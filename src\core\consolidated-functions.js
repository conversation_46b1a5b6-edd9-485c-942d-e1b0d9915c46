/**
 * Black Horse POS - Consolidated Functions
 * الدوال الموحدة بعد إزالة التكرار
 * Generated by: Duplicate Code Remover
 * Date: 2025-07-03
 */

// ===== CONSOLIDATED FUNCTIONS =====
// تم دمج وتوحيد الدوال المكررة من app-clean.js

// دالة التنقل الموحدة مع دعم NavigationManager
function showPage(pageId) {
    try {
        console.log('📄 Navigating to page:', pageId);
        
        // استخدام NavigationManager إذا كان متاحاً
        if (window.NavigationManager && typeof window.NavigationManager.showPage === 'function') {
            return window.NavigationManager.showPage(pageId);
        }
        
        // التنقل التقليدي كـ fallback
        document.querySelectorAll('.page').forEach(page => {
            page.style.display = 'none';
            page.classList.remove('active');
        });
        
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        const targetPage = document.getElementById(pageId);
        if (targetPage) {
            targetPage.style.display = 'block';
            targetPage.classList.add('active');
        }
        
        const activeLink = document.querySelector(`[onclick="showPage('${pageId}')"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
        
        // تحديث URL إذا أمكن
        if (window.history && window.history.pushState) {
            window.history.pushState({page: pageId}, '', `#${pageId}`);
        }
        
        // تحميل محتوى الصفحة
        loadPageContent(pageId);
        
        console.log(`✅ Successfully navigated to: ${pageId}`);
        
    } catch (error) {
        console.error('❌ Error in showPage:', error);
        if (window.Utils) {
            Utils.showMessage('خطأ في التنقل إلى الصفحة', 'error');
        }
    }
}

// دالة الحفظ الموحدة مع دعم UnifiedStorage
async function saveToLocalStorage(key, data) {
    try {
        console.log(`💾 Saving data to: ${key}`);
        
        // استخدام UnifiedStorage إذا كان متاحاً
        if (window.UnifiedStorage && typeof window.UnifiedStorage.save === 'function') {
            return await window.UnifiedStorage.save(key, data);
        }
        
        // الحفظ التقليدي كـ fallback
        if (typeof data === 'object') {
            localStorage.setItem(key, JSON.stringify(data));
        } else {
            localStorage.setItem(key, data);
        }
        
        console.log(`✅ Data saved successfully: ${key}`);
        return true;
        
    } catch (error) {
        console.error(`❌ Error saving data to ${key}:`, error);
        if (window.Utils) {
            Utils.showMessage('خطأ في حفظ البيانات', 'error');
        }
        return false;
    }
}

// دالة التحميل الموحدة مع دعم UnifiedStorage
async function loadFromLocalStorage(key, defaultValue = null) {
    try {
        console.log(`📂 Loading data from: ${key}`);
        
        // استخدام UnifiedStorage إذا كان متاحاً
        if (window.UnifiedStorage && typeof window.UnifiedStorage.load === 'function') {
            return await window.UnifiedStorage.load(key, defaultValue);
        }
        
        // التحميل التقليدي كـ fallback
        const data = localStorage.getItem(key);
        if (data === null) {
            return defaultValue;
        }
        
        try {
            return JSON.parse(data);
        } catch (parseError) {
            console.warn(`⚠️ Could not parse JSON for ${key}, returning raw data`);
            return data;
        }
        
    } catch (error) {
        console.error(`❌ Error loading data from ${key}:`, error);
        return defaultValue;
    }
}

// دالة تحميل محتوى الصفحات
function loadPageContent(pageId) {
    try {
        console.log(`🔄 Loading content for page: ${pageId}`);
        
        // استخدام المدراء المتخصصين
        switch (pageId) {
            case 'dashboard':
                if (typeof loadDashboardData === 'function') {
                    loadDashboardData();
                }
                break;
            case 'products':
                if (window.ProductsManager && typeof window.ProductsManager.loadProductsPage === 'function') {
                    window.ProductsManager.loadProductsPage();
                }
                break;
            case 'customers':
                if (window.CustomersManager && typeof window.CustomersManager.loadCustomersPage === 'function') {
                    window.CustomersManager.loadCustomersPage();
                }
                break;
            case 'sales':
                if (window.SalesManager && typeof window.SalesManager.loadSalesPage === 'function') {
                    window.SalesManager.loadSalesPage();
                }
                break;
            case 'reports':
                if (window.ReportsManager && typeof window.ReportsManager.loadReportsPage === 'function') {
                    window.ReportsManager.loadReportsPage();
                }
                break;
            default:
                console.log(`ℹ️ No specific loader for page: ${pageId}`);
        }
        
    } catch (error) {
        console.error(`❌ Error loading content for ${pageId}:`, error);
    }
}

// دوال إدارة المنتجات الموحدة
async function addProduct(productData) {
    try {
        console.log('➕ Adding new product...');
        
        // استخدام ProductsManager إذا كان متاحاً
        if (window.ProductsManager && typeof window.ProductsManager.addProduct === 'function') {
            return await window.ProductsManager.addProduct(productData);
        }
        
        // الطريقة التقليدية كـ fallback
        const products = await loadFromLocalStorage('products', []);
        
        // إنشاء ID جديد
        const newId = Date.now().toString();
        const newProduct = {
            id: newId,
            ...productData,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        
        products.push(newProduct);
        await saveToLocalStorage('products', products);
        
        console.log('✅ Product added successfully');
        return newProduct;
        
    } catch (error) {
        console.error('❌ Error adding product:', error);
        if (window.Utils) {
            Utils.showMessage('خطأ في إضافة المنتج', 'error');
        }
        return null;
    }
}

async function editProduct(productId, updatedData) {
    try {
        console.log(`✏️ Editing product: ${productId}`);
        
        // استخدام ProductsManager إذا كان متاحاً
        if (window.ProductsManager && typeof window.ProductsManager.editProduct === 'function') {
            return await window.ProductsManager.editProduct(productId, updatedData);
        }
        
        // الطريقة التقليدية كـ fallback
        const products = await loadFromLocalStorage('products', []);
        const productIndex = products.findIndex(p => p.id === productId);
        
        if (productIndex === -1) {
            throw new Error('المنتج غير موجود');
        }
        
        products[productIndex] = {
            ...products[productIndex],
            ...updatedData,
            updatedAt: new Date().toISOString()
        };
        
        await saveToLocalStorage('products', products);
        
        console.log('✅ Product updated successfully');
        return products[productIndex];
        
    } catch (error) {
        console.error('❌ Error editing product:', error);
        if (window.Utils) {
            Utils.showMessage('خطأ في تعديل المنتج', 'error');
        }
        return null;
    }
}

async function deleteProduct(productId) {
    try {
        console.log(`🗑️ Deleting product: ${productId}`);
        
        // استخدام ProductsManager إذا كان متاحاً
        if (window.ProductsManager && typeof window.ProductsManager.deleteProduct === 'function') {
            return await window.ProductsManager.deleteProduct(productId);
        }
        
        // الطريقة التقليدية كـ fallback
        const products = await loadFromLocalStorage('products', []);
        const productIndex = products.findIndex(p => p.id === productId);
        
        if (productIndex === -1) {
            throw new Error('المنتج غير موجود');
        }
        
        const deletedProduct = products.splice(productIndex, 1)[0];
        await saveToLocalStorage('products', products);
        
        console.log('✅ Product deleted successfully');
        return deletedProduct;
        
    } catch (error) {
        console.error('❌ Error deleting product:', error);
        if (window.Utils) {
            Utils.showMessage('خطأ في حذف المنتج', 'error');
        }
        return null;
    }
}

// دوال إدارة العملاء الموحدة
async function addCustomer(customerData) {
    try {
        console.log('👤 Adding new customer...');
        
        // استخدام CustomersManager إذا كان متاحاً
        if (window.CustomersManager && typeof window.CustomersManager.addCustomer === 'function') {
            return await window.CustomersManager.addCustomer(customerData);
        }
        
        // الطريقة التقليدية كـ fallback
        const customers = await loadFromLocalStorage('customers', []);
        
        const newId = Date.now().toString();
        const newCustomer = {
            id: newId,
            ...customerData,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        
        customers.push(newCustomer);
        await saveToLocalStorage('customers', customers);
        
        console.log('✅ Customer added successfully');
        return newCustomer;
        
    } catch (error) {
        console.error('❌ Error adding customer:', error);
        if (window.Utils) {
            Utils.showMessage('خطأ في إضافة العميل', 'error');
        }
        return null;
    }
}

// ===== INITIALIZATION =====
// تهيئة الدوال الموحدة

document.addEventListener('DOMContentLoaded', function() {
    console.log('✅ Consolidated functions loaded and ready');
    
    // التحقق من توفر النظام الجديد
    if (window.ModuleManager && window.ModuleManager.isInitialized) {
        console.log('🔧 Using new module system');
    } else {
        console.log('🔄 Using fallback functions');
    }
});

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.ConsolidatedFunctions = {
        showPage,
        saveToLocalStorage,
        loadFromLocalStorage,
        loadPageContent,
        addProduct,
        editProduct,
        deleteProduct,
        addCustomer
    };
}

console.log('✅ Consolidated Functions loaded successfully');
