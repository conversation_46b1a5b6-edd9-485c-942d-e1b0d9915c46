#!/bin/bash

# Black Horse ERP - خادم التطوير المحلي
# Local Development Server

# تعيين الترميز
export LANG=en_US.UTF-8

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# رسالة الترحيب
echo -e "${BLUE}================================================================${NC}"
echo -e "${WHITE}🐎 Black Horse ERP - نظام إدارة الأعمال المتكامل${NC}"
echo -e "${BLUE}================================================================${NC}"
echo ""

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo -e "${RED}❌ Python غير مثبت على النظام${NC}"
        echo ""
        echo -e "${YELLOW}💡 يرجى تثبيت Python باستخدام إحدى الطرق التالية:${NC}"
        echo ""
        
        # التحقق من نوع النظام
        if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            echo -e "${CYAN}📋 Ubuntu/Debian:${NC}"
            echo "   sudo apt update && sudo apt install python3 python3-pip"
            echo ""
            echo -e "${CYAN}📋 CentOS/RHEL/Fedora:${NC}"
            echo "   sudo yum install python3 python3-pip"
            echo "   # أو"
            echo "   sudo dnf install python3 python3-pip"
        elif [[ "$OSTYPE" == "darwin"* ]]; then
            echo -e "${CYAN}📋 macOS:${NC}"
            echo "   # باستخدام Homebrew"
            echo "   brew install python3"
            echo ""
            echo "   # أو قم بتحميله من:"
            echo "   https://www.python.org/downloads/"
        else
            echo -e "${CYAN}📋 تحميل من الموقع الرسمي:${NC}"
            echo "   https://www.python.org/downloads/"
        fi
        
        echo ""
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo -e "${GREEN}✅ تم العثور على Python${NC}"
$PYTHON_CMD --version

echo ""
echo -e "${BLUE}🚀 جاري بدء خادم التطوير...${NC}"
echo ""

# تشغيل الخادم
$PYTHON_CMD start-server.py

# في حالة حدوث خطأ
if [ $? -ne 0 ]; then
    echo ""
    echo -e "${RED}❌ حدث خطأ في تشغيل الخادم${NC}"
    echo ""
    echo -e "${YELLOW}🔧 حلول مقترحة:${NC}"
    echo "   1. تأكد من وجود جميع الملفات في نفس المجلد"
    echo "   2. تأكد من عدم استخدام المنفذ 8080 من برنامج آخر"
    echo "   3. جرب تشغيل الأمر التالي يدوياً:"
    echo -e "      ${CYAN}$PYTHON_CMD start-server.py${NC}"
    echo ""
    echo "   4. للتحقق من المنافذ المستخدمة:"
    echo -e "      ${CYAN}netstat -tulpn | grep :8080${NC}"
    echo ""
    echo "   5. لإيقاف العملية التي تستخدم المنفذ:"
    echo -e "      ${CYAN}sudo lsof -ti:8080 | xargs kill -9${NC}"
    echo ""
    exit 1
fi
