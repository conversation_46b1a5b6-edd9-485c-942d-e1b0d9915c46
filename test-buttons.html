<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أزرار Black Horse POS</title>
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- الأيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .test-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            max-width: 1200px;
        }
        
        .test-section {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            margin: 15px 0;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .test-result {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .test-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .test-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .test-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .btn-test {
            margin: 5px;
            min-width: 150px;
        }
        
        .console-output {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 8px;
        }
        
        .status-ok { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .status-loading { background: #17a2b8; animation: pulse 1s infinite; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container-fluid p-4">
        <div class="test-container p-4">
            <div class="text-center mb-4">
                <h1 class="text-primary">
                    <i class="fas fa-vials me-2"></i>
                    اختبار أزرار Black Horse POS
                </h1>
                <p class="text-muted">فحص شامل لوظائف الأزرار والتنقل في النظام</p>
            </div>
            
            <!-- حالة النظام -->
            <div class="test-section">
                <h3><i class="fas fa-heartbeat me-2"></i>حالة النظام</h3>
                <div class="row">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <span class="status-indicator status-loading" id="systemStatus"></span>
                                <h5 id="systemStatusText">جاري الفحص...</h5>
                                <p class="text-muted">حالة النظام العامة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <span class="status-indicator status-loading" id="modulesStatus"></span>
                                <h5 id="modulesStatusText">جاري الفحص...</h5>
                                <p class="text-muted">حالة الوحدات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <span class="status-indicator status-loading" id="storageStatus"></span>
                                <h5 id="storageStatusText">جاري الفحص...</h5>
                                <p class="text-muted">نظام التخزين</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <span class="status-indicator status-loading" id="uiStatus"></span>
                                <h5 id="uiStatusText">جاري الفحص...</h5>
                                <p class="text-muted">واجهة المستخدم</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- اختبار أزرار التنقل -->
            <div class="test-section">
                <h3><i class="fas fa-mouse-pointer me-2"></i>اختبار أزرار التنقل</h3>
                <div class="row">
                    <div class="col-md-6">
                        <h5>الصفحات الرئيسية:</h5>
                        <button class="btn btn-primary btn-test" onclick="testNavigation('dashboard')">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </button>
                        <button class="btn btn-success btn-test" onclick="testNavigation('products')">
                            <i class="fas fa-box me-2"></i>المنتجات
                        </button>
                        <button class="btn btn-info btn-test" onclick="testNavigation('customers')">
                            <i class="fas fa-users me-2"></i>العملاء
                        </button>
                        <button class="btn btn-warning btn-test" onclick="testNavigation('sales')">
                            <i class="fas fa-shopping-cart me-2"></i>المبيعات
                        </button>
                        <button class="btn btn-secondary btn-test" onclick="testNavigation('reports')">
                            <i class="fas fa-chart-bar me-2"></i>التقارير
                        </button>
                        <button class="btn btn-dark btn-test" onclick="testNavigation('expenses')">
                            <i class="fas fa-money-bill me-2"></i>المصروفات
                        </button>
                        <button class="btn btn-outline-primary btn-test" onclick="testNavigation('settings')">
                            <i class="fas fa-cog me-2"></i>الإعدادات
                        </button>
                    </div>
                    <div class="col-md-6">
                        <h5>النماذج والحوارات:</h5>
                        <button class="btn btn-success btn-test" onclick="testModal('showAddProductModal')">
                            <i class="fas fa-plus me-2"></i>إضافة منتج
                        </button>
                        <button class="btn btn-info btn-test" onclick="testModal('showAddCustomerModal')">
                            <i class="fas fa-user-plus me-2"></i>إضافة عميل
                        </button>
                        <button class="btn btn-warning btn-test" onclick="testModal('showAddExpenseModal')">
                            <i class="fas fa-plus-circle me-2"></i>إضافة مصروف
                        </button>
                        <button class="btn btn-primary btn-test" onclick="testFunction('exportData')">
                            <i class="fas fa-download me-2"></i>تصدير البيانات
                        </button>
                        <button class="btn btn-secondary btn-test" onclick="testFunction('runSystemDiagnostics')">
                            <i class="fas fa-stethoscope me-2"></i>تشخيص النظام
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- نتائج الاختبارات -->
            <div class="test-section">
                <h3><i class="fas fa-clipboard-check me-2"></i>نتائج الاختبارات</h3>
                <div id="testResults"></div>
            </div>
            
            <!-- سجل وحدة التحكم -->
            <div class="test-section">
                <h3><i class="fas fa-terminal me-2"></i>سجل وحدة التحكم</h3>
                <div class="console-output" id="consoleOutput">
جاري تحميل النظام...
                </div>
                <div class="mt-3">
                    <button class="btn btn-outline-secondary btn-sm" onclick="clearConsole()">
                        <i class="fas fa-trash me-2"></i>مسح السجل
                    </button>
                    <button class="btn btn-outline-primary btn-sm" onclick="runFullTest()">
                        <i class="fas fa-play me-2"></i>تشغيل اختبار شامل
                    </button>
                </div>
            </div>
            
            <!-- إحصائيات الاختبار -->
            <div class="test-section">
                <h3><i class="fas fa-chart-pie me-2"></i>إحصائيات الاختبار</h3>
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h2 id="successCount">0</h2>
                                <p>اختبارات ناجحة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <h2 id="errorCount">0</h2>
                                <p>أخطاء</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <h2 id="warningCount">0</h2>
                                <p>تحذيرات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h2 id="totalTests">0</h2>
                                <p>إجمالي الاختبارات</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let testResults = [];
        let successCount = 0;
        let errorCount = 0;
        let warningCount = 0;
        let totalTests = 0;
        
        // تحميل النظام الرئيسي في iframe مخفي للاختبار
        const iframe = document.createElement('iframe');
        iframe.src = 'src/index.html';
        iframe.style.display = 'none';
        document.body.appendChild(iframe);
        
        // انتظار تحميل النظام
        iframe.onload = function() {
            setTimeout(() => {
                checkSystemStatus();
                logToConsole('✅ تم تحميل النظام الرئيسي بنجاح');
            }, 2000);
        };
        
        iframe.onerror = function() {
            logToConsole('❌ فشل في تحميل النظام الرئيسي');
            updateStatus('systemStatus', 'error', 'خطأ في التحميل');
        };
        
        function logToConsole(message) {
            const console = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            console.textContent += `\n[${timestamp}] ${message}`;
            console.scrollTop = console.scrollHeight;
        }
        
        function updateStatus(elementId, status, text) {
            const indicator = document.getElementById(elementId);
            const textElement = document.getElementById(elementId + 'Text');
            
            indicator.className = `status-indicator status-${status}`;
            textElement.textContent = text;
        }
        
        function addTestResult(test, status, message) {
            totalTests++;
            
            if (status === 'success') successCount++;
            else if (status === 'error') errorCount++;
            else if (status === 'warning') warningCount++;
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result test-${status}`;
            resultDiv.innerHTML = `
                <i class="fas fa-${status === 'success' ? 'check' : status === 'error' ? 'times' : 'exclamation-triangle'} me-2"></i>
                <strong>${test}:</strong> ${message}
            `;
            
            document.getElementById('testResults').appendChild(resultDiv);
            updateCounters();
            logToConsole(`${status === 'success' ? '✅' : status === 'error' ? '❌' : '⚠️'} ${test}: ${message}`);
        }
        
        function updateCounters() {
            document.getElementById('successCount').textContent = successCount;
            document.getElementById('errorCount').textContent = errorCount;
            document.getElementById('warningCount').textContent = warningCount;
            document.getElementById('totalTests').textContent = totalTests;
        }
        
        function checkSystemStatus() {
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                // فحص النظام العام
                if (iframeDoc.title.includes('Black Horse POS')) {
                    updateStatus('systemStatus', 'ok', 'يعمل بشكل طبيعي');
                    addTestResult('تحميل النظام', 'success', 'تم تحميل النظام بنجاح');
                } else {
                    updateStatus('systemStatus', 'error', 'خطأ في التحميل');
                    addTestResult('تحميل النظام', 'error', 'فشل في تحميل النظام');
                }
                
                // فحص الوحدات
                const iframeWindow = iframe.contentWindow;
                let moduleCount = 0;
                const modules = ['Utils', 'UnifiedStorage', 'ProductsManager', 'CustomersManager', 'SalesManager'];
                
                modules.forEach(module => {
                    if (iframeWindow[module]) {
                        moduleCount++;
                        logToConsole(`✅ وحدة ${module} متاحة`);
                    } else {
                        logToConsole(`❌ وحدة ${module} غير متاحة`);
                    }
                });
                
                if (moduleCount >= 3) {
                    updateStatus('modulesStatus', 'ok', `${moduleCount}/${modules.length} متاح`);
                    addTestResult('فحص الوحدات', 'success', `${moduleCount} من ${modules.length} وحدة متاحة`);
                } else {
                    updateStatus('modulesStatus', 'warning', `${moduleCount}/${modules.length} متاح`);
                    addTestResult('فحص الوحدات', 'warning', `${moduleCount} من ${modules.length} وحدة متاحة فقط`);
                }
                
                // فحص التخزين
                if (typeof(Storage) !== "undefined") {
                    updateStatus('storageStatus', 'ok', 'متاح');
                    addTestResult('نظام التخزين', 'success', 'localStorage متاح ويعمل');
                } else {
                    updateStatus('storageStatus', 'error', 'غير متاح');
                    addTestResult('نظام التخزين', 'error', 'localStorage غير متاح');
                }
                
                // فحص واجهة المستخدم
                const pages = iframeDoc.querySelectorAll('.page');
                if (pages.length > 0) {
                    updateStatus('uiStatus', 'ok', `${pages.length} صفحة`);
                    addTestResult('واجهة المستخدم', 'success', `تم العثور على ${pages.length} صفحة`);
                } else {
                    updateStatus('uiStatus', 'error', 'لا توجد صفحات');
                    addTestResult('واجهة المستخدم', 'error', 'لم يتم العثور على صفحات');
                }
                
            } catch (error) {
                logToConsole(`❌ خطأ في فحص النظام: ${error.message}`);
                updateStatus('systemStatus', 'error', 'خطأ في الفحص');
                addTestResult('فحص النظام', 'error', error.message);
            }
        }
        
        function testNavigation(pageId) {
            try {
                const iframeWindow = iframe.contentWindow;
                
                if (typeof iframeWindow.showPage === 'function') {
                    iframeWindow.showPage(pageId);
                    
                    setTimeout(() => {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        const activePage = iframeDoc.querySelector(`#${pageId}.active`);
                        
                        if (activePage) {
                            addTestResult(`التنقل إلى ${pageId}`, 'success', 'تم التنقل بنجاح');
                        } else {
                            addTestResult(`التنقل إلى ${pageId}`, 'warning', 'التنقل تم لكن الصفحة غير مفعلة');
                        }
                    }, 500);
                } else {
                    addTestResult(`التنقل إلى ${pageId}`, 'error', 'دالة showPage غير متاحة');
                }
                
            } catch (error) {
                addTestResult(`التنقل إلى ${pageId}`, 'error', error.message);
            }
        }
        
        function testModal(modalFunction) {
            try {
                const iframeWindow = iframe.contentWindow;
                
                if (typeof iframeWindow[modalFunction] === 'function') {
                    iframeWindow[modalFunction]();
                    addTestResult(`نموذج ${modalFunction}`, 'success', 'تم استدعاء النموذج بنجاح');
                } else {
                    addTestResult(`نموذج ${modalFunction}`, 'error', `دالة ${modalFunction} غير متاحة`);
                }
                
            } catch (error) {
                addTestResult(`نموذج ${modalFunction}`, 'error', error.message);
            }
        }
        
        function testFunction(functionName) {
            try {
                const iframeWindow = iframe.contentWindow;
                
                if (typeof iframeWindow[functionName] === 'function') {
                    iframeWindow[functionName]();
                    addTestResult(`دالة ${functionName}`, 'success', 'تم تنفيذ الدالة بنجاح');
                } else {
                    addTestResult(`دالة ${functionName}`, 'error', `دالة ${functionName} غير متاحة`);
                }
                
            } catch (error) {
                addTestResult(`دالة ${functionName}`, 'error', error.message);
            }
        }
        
        function clearConsole() {
            document.getElementById('consoleOutput').textContent = 'تم مسح السجل...\n';
        }
        
        function runFullTest() {
            // إعادة تعيين العدادات
            successCount = 0;
            errorCount = 0;
            warningCount = 0;
            totalTests = 0;
            document.getElementById('testResults').innerHTML = '';
            
            logToConsole('🚀 بدء الاختبار الشامل...');
            
            // اختبار جميع الصفحات
            const pages = ['dashboard', 'products', 'customers', 'sales', 'reports', 'expenses', 'settings'];
            pages.forEach((page, index) => {
                setTimeout(() => testNavigation(page), index * 1000);
            });
            
            // اختبار النماذج
            setTimeout(() => {
                testModal('showAddProductModal');
                testModal('showAddCustomerModal');
                testModal('showAddExpenseModal');
            }, pages.length * 1000 + 1000);
            
            // اختبار الدوال
            setTimeout(() => {
                testFunction('exportData');
                testFunction('runSystemDiagnostics');
                logToConsole('✅ اكتمل الاختبار الشامل');
            }, pages.length * 1000 + 3000);
        }
        
        // بدء الفحص التلقائي
        setTimeout(() => {
            logToConsole('🔍 بدء الفحص التلقائي للنظام...');
        }, 1000);
    </script>
</body>
</html>
