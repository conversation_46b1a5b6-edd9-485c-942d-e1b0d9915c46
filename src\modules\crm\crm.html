<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء - Black Horse ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/custom.css" rel="stylesheet">
    <style>
        .customer-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-right: 4px solid #007bff;
        }
        
        .customer-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        
        .customer-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-blocked {
            background: #fff3cd;
            color: #856404;
        }
        
        .lead-pipeline {
            display: flex;
            gap: 20px;
            overflow-x: auto;
            padding: 20px 0;
            min-height: 500px;
        }
        
        .pipeline-stage {
            background: #f8f9fa;
            border-radius: 8px;
            min-width: 280px;
            max-width: 280px;
            padding: 15px;
        }
        
        .stage-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #dee2e6;
        }
        
        .lead-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .lead-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .lead-score {
            background: #007bff;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .activity-timeline {
            position: relative;
            padding-right: 30px;
        }
        
        .activity-timeline::before {
            content: '';
            position: absolute;
            right: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }
        
        .activity-item {
            position: relative;
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .activity-item::before {
            content: '';
            position: absolute;
            right: -23px;
            top: 20px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #007bff;
            border: 3px solid white;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
        }
        
        .stats-widget {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .stats-widget:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .stats-change {
            font-size: 0.8rem;
            margin-top: 5px;
        }
        
        .stats-up {
            color: #28a745;
        }
        
        .stats-down {
            color: #dc3545;
        }
        
        .opportunity-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-right: 4px solid #28a745;
        }
        
        .opportunity-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #28a745;
        }
        
        .probability-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
        }
        
        .probability-fill {
            background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
            height: 100%;
            transition: width 0.3s ease;
        }
        
        .campaign-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .campaign-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .campaign-stat {
            text-align: center;
        }
        
        .campaign-stat-number {
            font-size: 1.2rem;
            font-weight: 600;
            color: #007bff;
        }
        
        .campaign-stat-label {
            font-size: 0.8rem;
            color: #6c757d;
        }
        
        .filter-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .quick-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .action-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            background: #007bff;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .action-btn:hover {
            background: #0056b3;
            transform: translateY(-1px);
            color: white;
        }
        
        .action-btn.secondary {
            background: #6c757d;
        }
        
        .action-btn.success {
            background: #28a745;
        }
        
        .action-btn.warning {
            background: #ffc107;
            color: #212529;
        }
        
        .view-tabs {
            background: white;
            border-radius: 8px;
            padding: 5px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .view-tab {
            padding: 12px 20px;
            border: none;
            background: none;
            border-radius: 6px;
            transition: all 0.3s ease;
            color: #6c757d;
            font-weight: 500;
        }
        
        .view-tab.active {
            background: #007bff;
            color: white;
        }
        
        .search-box {
            position: relative;
        }
        
        .search-box .form-control {
            padding-right: 45px;
        }
        
        .search-box .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }
        
        .contact-info {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }
        
        .contact-info i {
            width: 16px;
            color: #6c757d;
        }
        
        .customer-tags {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
            margin-top: 10px;
        }
        
        .customer-tag {
            background: #e9ecef;
            color: #495057;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
        }
        
        .progress-circle {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: conic-gradient(#007bff 0deg, #e9ecef 0deg);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        
        .progress-circle::before {
            content: '';
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: white;
            position: absolute;
        }
        
        .progress-text {
            position: relative;
            z-index: 1;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px 8px 0 0;
        }
        
        .modal-header .btn-close {
            filter: invert(1);
        }
        
        .form-floating label {
            right: 1rem;
            left: auto;
        }
        
        .table-responsive {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .table th {
            background: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #495057;
        }
        
        .table td {
            border: none;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: middle;
        }
        
        .table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        .empty-state h4 {
            margin-bottom: 10px;
        }
        
        .empty-state p {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- رأس الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-users text-primary"></i> إدارة العملاء</h2>
                        <p class="text-muted">مركز إدارة العملاء والعملاء المحتملين</p>
                    </div>
                    <div class="quick-actions">
                        <button class="action-btn" onclick="showAddCustomerModal()">
                            <i class="fas fa-user-plus"></i> عميل جديد
                        </button>
                        <button class="action-btn secondary" onclick="showAddLeadModal()">
                            <i class="fas fa-user-tie"></i> عميل محتمل
                        </button>
                        <button class="action-btn success" onclick="showAddOpportunityModal()">
                            <i class="fas fa-handshake"></i> فرصة بيع
                        </button>
                        <button class="action-btn warning" onclick="showCreateCampaignModal()">
                            <i class="fas fa-bullhorn"></i> حملة تسويقية
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-widget">
                    <div class="stats-number text-primary" id="totalCustomers">0</div>
                    <div class="stats-label">إجمالي العملاء</div>
                    <div class="stats-change stats-up" id="customersChange">
                        <i class="fas fa-arrow-up"></i> +5.2%
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-widget">
                    <div class="stats-number text-success" id="totalLeads">0</div>
                    <div class="stats-label">العملاء المحتملين</div>
                    <div class="stats-change stats-up" id="leadsChange">
                        <i class="fas fa-arrow-up"></i> +12.8%
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-widget">
                    <div class="stats-number text-warning" id="totalOpportunities">0</div>
                    <div class="stats-label">فرص البيع</div>
                    <div class="stats-change stats-up" id="opportunitiesChange">
                        <i class="fas fa-arrow-up"></i> +8.4%
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-widget">
                    <div class="stats-number text-info" id="conversionRate">0%</div>
                    <div class="stats-label">معدل التحويل</div>
                    <div class="stats-change stats-up" id="conversionChange">
                        <i class="fas fa-arrow-up"></i> +2.1%
                    </div>
                </div>
            </div>
        </div>

        <!-- فلاتر البحث -->
        <div class="filter-panel">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="search-box">
                        <input type="text" class="form-control" id="searchInput" 
                               placeholder="البحث في العملاء..." onkeyup="searchCustomers()">
                        <i class="fas fa-search search-icon"></i>
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="statusFilter" onchange="applyFilters()">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                        <option value="blocked">محظور</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="categoryFilter" onchange="applyFilters()">
                        <option value="">جميع الفئات</option>
                        <option value="vip">VIP</option>
                        <option value="regular">عادي</option>
                        <option value="wholesale">جملة</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="sourceFilter" onchange="applyFilters()">
                        <option value="">جميع المصادر</option>
                        <option value="website">الموقع</option>
                        <option value="referral">إحالة</option>
                        <option value="social_media">وسائل التواصل</option>
                        <option value="direct">مباشر</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary" onclick="exportCustomers()">
                            <i class="fas fa-download"></i> تصدير
                        </button>
                        <button class="btn btn-outline-secondary" onclick="importCustomers()">
                            <i class="fas fa-upload"></i> استيراد
                        </button>
                        <button class="btn btn-outline-success" onclick="refreshData()">
                            <i class="fas fa-sync"></i> تحديث
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- تبويبات العرض -->
        <div class="view-tabs">
            <button class="view-tab active" onclick="setView('customers')" id="customersTab">
                <i class="fas fa-users"></i> العملاء
            </button>
            <button class="view-tab" onclick="setView('leads')" id="leadsTab">
                <i class="fas fa-user-tie"></i> العملاء المحتملين
            </button>
            <button class="view-tab" onclick="setView('opportunities')" id="opportunitiesTab">
                <i class="fas fa-handshake"></i> فرص البيع
            </button>
            <button class="view-tab" onclick="setView('activities')" id="activitiesTab">
                <i class="fas fa-tasks"></i> الأنشطة
            </button>
            <button class="view-tab" onclick="setView('campaigns')" id="campaignsTab">
                <i class="fas fa-bullhorn"></i> الحملات
            </button>
            <button class="view-tab" onclick="setView('reports')" id="reportsTab">
                <i class="fas fa-chart-bar"></i> التقارير
            </button>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="row">
            <!-- عرض العملاء -->
            <div id="customersView" class="col-12">
                <div class="row" id="customersGrid">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>

            <!-- عرض العملاء المحتملين -->
            <div id="leadsView" class="col-12" style="display: none;">
                <div class="lead-pipeline" id="leadsPipeline">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>

            <!-- عرض فرص البيع -->
            <div id="opportunitiesView" class="col-12" style="display: none;">
                <div id="opportunitiesList">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>

            <!-- عرض الأنشطة -->
            <div id="activitiesView" class="col-12" style="display: none;">
                <div class="row">
                    <div class="col-md-8">
                        <div class="activity-timeline" id="activitiesTimeline">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">إحصائيات الأنشطة</h6>
                            </div>
                            <div class="card-body" id="activitiesStats">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- عرض الحملات -->
            <div id="campaignsView" class="col-12" style="display: none;">
                <div id="campaignsList">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>

            <!-- عرض التقارير -->
            <div id="reportsView" class="col-12" style="display: none;">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">توزيع العملاء</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="customersChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">مسار المبيعات</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="salesFunnelChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
    <script src="../../core/database.js"></script>
    <script src="../../core/utils.js"></script>
    <script src="crm.js"></script>

    <script>
        // متغيرات عامة
        let crmManager;
        let currentView = 'customers';
        let currentFilters = {};
        let charts = {};

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeCRM();
            loadData();
            updateStatistics();
            setupEventListeners();
        });

        // تهيئة نظام CRM
        function initializeCRM() {
            crmManager = new CRMManager();
            console.log('✅ تم تهيئة واجهة إدارة العملاء');
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // تحديث الإحصائيات كل دقيقة
            setInterval(updateStatistics, 60000);
        }

        // تحميل البيانات
        function loadData() {
            switch(currentView) {
                case 'customers':
                    loadCustomers();
                    break;
                case 'leads':
                    loadLeads();
                    break;
                case 'opportunities':
                    loadOpportunities();
                    break;
                case 'activities':
                    loadActivities();
                    break;
                case 'campaigns':
                    loadCampaigns();
                    break;
                case 'reports':
                    loadReports();
                    break;
            }
        }

        // تحميل العملاء
        function loadCustomers() {
            const customers = Array.from(crmManager.customers.values());
            displayCustomers(customers);
        }

        // عرض العملاء
        function displayCustomers(customers) {
            const grid = document.getElementById('customersGrid');

            if (customers.length === 0) {
                grid.innerHTML = `
                    <div class="col-12">
                        <div class="empty-state">
                            <i class="fas fa-users"></i>
                            <h4>لا توجد عملاء</h4>
                            <p>ابدأ بإضافة عملاء جدد لإدارة قاعدة عملائك</p>
                            <button class="btn btn-primary" onclick="showAddCustomerModal()">
                                <i class="fas fa-user-plus"></i> إضافة عميل جديد
                            </button>
                        </div>
                    </div>
                `;
                return;
            }

            grid.innerHTML = customers.map(customer => createCustomerCard(customer)).join('');
        }

        // إنشاء بطاقة عميل
        function createCustomerCard(customer) {
            const fullName = `${customer.firstName} ${customer.lastName}`;
            const initials = `${customer.firstName.charAt(0)}${customer.lastName.charAt(0)}`;

            return `
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="customer-card">
                        <div class="p-3">
                            <div class="d-flex align-items-start mb-3">
                                <div class="customer-avatar me-3">
                                    ${initials}
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">${fullName}</h6>
                                    <p class="text-muted mb-1">${customer.company || 'لا توجد شركة'}</p>
                                    <span class="status-badge status-${customer.status}">
                                        ${getStatusLabel(customer.status)}
                                    </span>
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="viewCustomer('${customer.id}')">
                                            <i class="fas fa-eye"></i> عرض
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="editCustomer('${customer.id}')">
                                            <i class="fas fa-edit"></i> تعديل
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="addActivity('${customer.id}')">
                                            <i class="fas fa-plus"></i> إضافة نشاط
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#" onclick="deleteCustomer('${customer.id}')">
                                            <i class="fas fa-trash"></i> حذف
                                        </a></li>
                                    </ul>
                                </div>
                            </div>

                            <div class="contact-info">
                                <i class="fas fa-envelope"></i>
                                <span>${customer.email}</span>
                            </div>
                            <div class="contact-info">
                                <i class="fas fa-phone"></i>
                                <span>${customer.phone}</span>
                            </div>

                            <div class="row text-center mt-3 pt-3 border-top">
                                <div class="col-4">
                                    <div class="fw-bold text-primary">${customer.statistics.totalOrders}</div>
                                    <small class="text-muted">الطلبات</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-success">${formatCurrency(customer.statistics.totalSpent)}</div>
                                    <small class="text-muted">إجمالي الإنفاق</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-warning">${customer.statistics.loyaltyPoints}</div>
                                    <small class="text-muted">نقاط الولاء</small>
                                </div>
                            </div>

                            ${customer.tags.length > 0 ? `
                                <div class="customer-tags">
                                    ${customer.tags.map(tag => `<span class="customer-tag">${tag}</span>`).join('')}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;
        }

        // تحميل العملاء المحتملين
        function loadLeads() {
            const leads = Array.from(crmManager.leads.values());
            displayLeadsPipeline(leads);
        }

        // عرض مسار العملاء المحتملين
        function displayLeadsPipeline(leads) {
            const pipeline = document.getElementById('leadsPipeline');

            const stages = [
                { id: 'new', title: 'جديد', color: '#6c757d' },
                { id: 'contacted', title: 'تم التواصل', color: '#007bff' },
                { id: 'qualified', title: 'مؤهل', color: '#17a2b8' },
                { id: 'proposal', title: 'عرض سعر', color: '#ffc107' },
                { id: 'negotiation', title: 'تفاوض', color: '#fd7e14' },
                { id: 'won', title: 'فوز', color: '#28a745' },
                { id: 'lost', title: 'خسارة', color: '#dc3545' }
            ];

            pipeline.innerHTML = stages.map(stage => {
                const stageLeads = leads.filter(lead => lead.status === stage.id);

                return `
                    <div class="pipeline-stage">
                        <div class="stage-header">
                            <h6 class="mb-0" style="color: ${stage.color}">
                                <i class="fas fa-circle me-2"></i>
                                ${stage.title}
                            </h6>
                            <span class="badge bg-secondary">${stageLeads.length}</span>
                        </div>
                        <div class="stage-leads" id="leads-${stage.id}">
                            ${stageLeads.map(lead => createLeadCard(lead)).join('')}
                        </div>
                        <button class="btn btn-outline-primary btn-sm w-100 mt-2" onclick="showAddLeadModal('${stage.id}')">
                            <i class="fas fa-plus"></i> إضافة عميل محتمل
                        </button>
                    </div>
                `;
            }).join('');

            // إعداد السحب والإفلات
            setupLeadsDragDrop();
        }

        // إنشاء بطاقة عميل محتمل
        function createLeadCard(lead) {
            const fullName = `${lead.firstName} ${lead.lastName}`;

            return `
                <div class="lead-card" data-lead-id="${lead.id}" draggable="true">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="mb-0">${fullName}</h6>
                        <span class="lead-score">${lead.score}</span>
                    </div>
                    <p class="text-muted small mb-2">${lead.company || 'لا توجد شركة'}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-envelope"></i> ${lead.email}
                        </small>
                        <small class="text-muted">
                            ${lead.budget ? formatCurrency(lead.budget) : 'غير محدد'}
                        </small>
                    </div>
                    ${lead.nextFollowUp ? `
                        <div class="mt-2">
                            <small class="text-warning">
                                <i class="fas fa-clock"></i>
                                متابعة: ${new Date(lead.nextFollowUp).toLocaleDateString('ar-EG')}
                            </small>
                        </div>
                    ` : ''}
                </div>
            `;
        }

        // إعداد السحب والإفلات للعملاء المحتملين
        function setupLeadsDragDrop() {
            document.querySelectorAll('.stage-leads').forEach(stage => {
                new Sortable(stage, {
                    group: 'leads',
                    animation: 150,
                    ghostClass: 'sortable-ghost',
                    chosenClass: 'sortable-chosen',
                    onEnd: function(evt) {
                        const leadId = evt.item.dataset.leadId;
                        const newStatus = evt.to.id.replace('leads-', '');

                        updateLeadStatus(leadId, newStatus);
                    }
                });
            });
        }

        // تحديث حالة العميل المحتمل
        function updateLeadStatus(leadId, newStatus) {
            try {
                const lead = crmManager.leads.get(leadId);
                if (!lead) return;

                const oldStatus = lead.status;
                lead.status = newStatus;
                lead.modified = new Date().toISOString();

                crmManager.saveLeads();

                // إنشاء نشاط
                crmManager.createActivity({
                    type: 'status_change',
                    leadId: leadId,
                    description: `تم تغيير حالة العميل المحتمل من ${getStatusLabel(oldStatus)} إلى ${getStatusLabel(newStatus)}`,
                    userId: 'current_user'
                });

                updateStatistics();
                showNotification('تم تحديث حالة العميل المحتمل بنجاح', 'success');

            } catch (error) {
                showNotification('خطأ في تحديث حالة العميل المحتمل: ' + error.message, 'error');
            }
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            const reports = crmManager.getCRMReports();

            document.getElementById('totalCustomers').textContent = reports.customers.total;
            document.getElementById('totalLeads').textContent = reports.leads.total;
            document.getElementById('totalOpportunities').textContent = reports.opportunities.total;
            document.getElementById('conversionRate').textContent = reports.leads.conversionRate.toFixed(1) + '%';
        }

        // تعيين العرض
        function setView(view) {
            currentView = view;

            // إخفاء جميع العروض
            document.querySelectorAll('[id$="View"]').forEach(el => {
                el.style.display = 'none';
            });

            // إزالة الفئة النشطة من جميع التبويبات
            document.querySelectorAll('.view-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // عرض العرض المحدد
            document.getElementById(view + 'View').style.display = 'block';
            document.getElementById(view + 'Tab').classList.add('active');

            // تحميل البيانات
            loadData();
        }

        // البحث في العملاء
        function searchCustomers() {
            const query = document.getElementById('searchInput').value;
            if (!query.trim()) {
                loadCustomers();
                return;
            }

            const results = crmManager.searchCustomers(query, currentFilters);
            displayCustomers(results);
        }

        // تطبيق الفلاتر
        function applyFilters() {
            currentFilters = {
                status: document.getElementById('statusFilter').value,
                category: document.getElementById('categoryFilter').value,
                source: document.getElementById('sourceFilter').value
            };

            // إزالة الفلاتر الفارغة
            Object.keys(currentFilters).forEach(key => {
                if (!currentFilters[key]) {
                    delete currentFilters[key];
                }
            });

            searchCustomers();
        }

        // عرض نافذة إضافة عميل
        function showAddCustomerModal() {
            // سيتم تنفيذها لاحقاً
            showNotification('سيتم إضافة نافذة إضافة العميل قريباً', 'info');
        }

        // عرض نافذة إضافة عميل محتمل
        function showAddLeadModal(status = 'new') {
            // سيتم تنفيذها لاحقاً
            showNotification('سيتم إضافة نافذة إضافة العميل المحتمل قريباً', 'info');
        }

        // عرض نافذة إضافة فرصة بيع
        function showAddOpportunityModal() {
            // سيتم تنفيذها لاحقاً
            showNotification('سيتم إضافة نافذة إضافة فرصة البيع قريباً', 'info');
        }

        // عرض نافذة إنشاء حملة
        function showCreateCampaignModal() {
            // سيتم تنفيذها لاحقاً
            showNotification('سيتم إضافة نافذة إنشاء الحملة قريباً', 'info');
        }

        // الحصول على تسمية الحالة
        function getStatusLabel(status) {
            const labels = {
                'active': 'نشط',
                'inactive': 'غير نشط',
                'blocked': 'محظور',
                'new': 'جديد',
                'contacted': 'تم التواصل',
                'qualified': 'مؤهل',
                'proposal': 'عرض سعر',
                'negotiation': 'تفاوض',
                'won': 'فوز',
                'lost': 'خسارة'
            };
            return labels[status] || status;
        }

        // تنسيق العملة
        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: 'SAR'
            }).format(amount);
        }

        // عرض الإشعارات
        function showNotification(message, type = 'info') {
            const alertClass = {
                'success': 'alert-success',
                'error': 'alert-danger',
                'warning': 'alert-warning',
                'info': 'alert-info'
            }[type] || 'alert-info';

            const notification = document.createElement('div');
            notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // دوال إضافية (ستكتمل لاحقاً)
        function viewCustomer(customerId) {
            // عرض تفاصيل العميل
        }

        function editCustomer(customerId) {
            // تعديل العميل
        }

        function deleteCustomer(customerId) {
            if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
                try {
                    crmManager.customers.delete(customerId);
                    crmManager.saveCustomers();
                    loadCustomers();
                    updateStatistics();
                    showNotification('تم حذف العميل بنجاح', 'success');
                } catch (error) {
                    showNotification('خطأ في حذف العميل: ' + error.message, 'error');
                }
            }
        }

        function addActivity(customerId) {
            // إضافة نشاط للعميل
        }

        function loadOpportunities() {
            // تحميل فرص البيع
        }

        function loadActivities() {
            // تحميل الأنشطة
        }

        function loadCampaigns() {
            // تحميل الحملات
        }

        function loadReports() {
            // تحميل التقارير
        }

        function exportCustomers() {
            // تصدير العملاء
        }

        function importCustomers() {
            // استيراد العملاء
        }

        function refreshData() {
            loadData();
            updateStatistics();
            showNotification('تم تحديث البيانات بنجاح', 'success');
        }
    </script>
</body>
</html>
