/**
 * نظام إدارة المشاريع المتقدم
 * Black Horse ERP System
 */

class AdvancedProjectsManager {
    constructor() {
        this.projects = new Map();
        this.projectTemplates = new Map();
        this.projectPortfolios = new Map();
        this.projectResources = new Map();
        this.projectRisks = new Map();
        this.projectBudgets = new Map();
        this.projectTimelines = new Map();
        this.projectDeliverables = new Map();
        this.projectStakeholders = new Map();
        this.projectCommunications = new Map();
        this.projectQuality = new Map();
        this.projectIntegrations = new Map();

        this.settings = {
            defaultCurrency: 'SAR',
            workingHours: 8,
            workingDays: 5,
            riskThreshold: 0.7,
            budgetVarianceThreshold: 0.1,
            scheduleVarianceThreshold: 0.15,
            qualityThreshold: 0.8,
            autoNotifications: true,
            ganttViewEnabled: true,
            kanbanViewEnabled: true,
            calendarViewEnabled: true
        };

        this.initializeAdvancedProjects();
        this.loadData();
    }

    // تهيئة نظام المشاريع المتقدم
    initializeAdvancedProjects() {
        console.log('🚀 تهيئة نظام إدارة المشاريع المتقدم...');

        // إعداد قاعدة البيانات
        this.setupDatabase();

        // إعداد القوالب الافتراضية
        this.setupDefaultTemplates();

        // إعداد الأحداث
        this.setupEventListeners();

        console.log('✅ تم تهيئة نظام إدارة المشاريع المتقدم');
    }

    // إنشاء مشروع متقدم
    createAdvancedProject(projectData) {
        try {
            const projectId = this.generateId();
            const project = {
                id: projectId,
                code: projectData.code || this.generateProjectCode(),
                name: projectData.name,
                description: projectData.description || '',
                type: projectData.type || 'standard', // standard, agile, waterfall, hybrid
                methodology: projectData.methodology || 'traditional',
                priority: projectData.priority || 'medium', // low, medium, high, critical
                status: 'planning', // planning, active, on-hold, completed, cancelled
                phase: projectData.phase || 'initiation',

                // معلومات المشروع
                objectives: projectData.objectives || [],
                scope: projectData.scope || '',
                assumptions: projectData.assumptions || [],
                constraints: projectData.constraints || [],
                successCriteria: projectData.successCriteria || [],

                // التواريخ والجدولة
                dates: {
                    planned: {
                        start: projectData.plannedStart,
                        end: projectData.plannedEnd,
                        duration: projectData.plannedDuration || 0
                    },
                    actual: {
                        start: null,
                        end: null,
                        duration: 0
                    },
                    baseline: {
                        start: projectData.plannedStart,
                        end: projectData.plannedEnd,
                        duration: projectData.plannedDuration || 0
                    }
                },

                // الميزانية والتكاليف
                budget: {
                    planned: projectData.plannedBudget || 0,
                    actual: 0,
                    committed: 0,
                    remaining: projectData.plannedBudget || 0,
                    variance: 0,
                    currency: projectData.currency || this.settings.defaultCurrency,
                    breakdown: {
                        labor: 0,
                        materials: 0,
                        equipment: 0,
                        overhead: 0,
                        contingency: 0
                    }
                },

                // الفريق والموارد
                team: {
                    manager: projectData.managerId,
                    sponsor: projectData.sponsorId,
                    members: projectData.teamMembers || [],
                    stakeholders: projectData.stakeholders || [],
                    roles: projectData.roles || []
                },

                // التقدم والأداء
                progress: {
                    overall: 0,
                    tasks: 0,
                    milestones: 0,
                    deliverables: 0,
                    quality: 0,
                    schedule: 0,
                    budget: 0
                },

                // المخاطر والقضايا
                risks: {
                    total: 0,
                    high: 0,
                    medium: 0,
                    low: 0,
                    mitigated: 0,
                    score: 0
                },

                // الجودة
                quality: {
                    score: 0,
                    metrics: [],
                    reviews: [],
                    audits: [],
                    defects: 0,
                    rework: 0
                },

                // التواصل
                communications: {
                    plan: projectData.communicationPlan || {},
                    meetings: [],
                    reports: [],
                    notifications: []
                },

                // التكامل
                integrations: {
                    parentProject: projectData.parentProject || null,
                    subProjects: [],
                    dependencies: [],
                    linkedProjects: []
                },

                // الملفات والمرفقات
                documents: [],
                attachments: [],

                // الإعدادات
                settings: {
                    visibility: projectData.visibility || 'team', // public, team, private
                    notifications: projectData.notifications !== false,
                    autoReporting: projectData.autoReporting !== false,
                    riskMonitoring: projectData.riskMonitoring !== false
                },

                // التواريخ
                created: new Date().toISOString(),
                modified: new Date().toISOString(),
                createdBy: projectData.createdBy,

                // البيانات الوصفية
                tags: projectData.tags || [],
                category: projectData.category || '',
                location: projectData.location || '',
                client: projectData.client || '',
                contract: projectData.contract || ''
            };

            this.projects.set(projectId, project);
            this.saveProjects();

            // إنشاء الميزانية
            this.createProjectBudget(projectId, project.budget);

            // إنشاء الجدولة الزمنية
            this.createProjectTimeline(projectId, project.dates);

            console.log('✅ تم إنشاء المشروع المتقدم:', project.name);
            return project;

        } catch (error) {
            console.error('خطأ في إنشاء المشروع المتقدم:', error);
            throw error;
        }
    }

    // إنشاء محفظة مشاريع
    createProjectPortfolio(portfolioData) {
        try {
            const portfolioId = this.generateId();
            const portfolio = {
                id: portfolioId,
                name: portfolioData.name,
                description: portfolioData.description || '',
                manager: portfolioData.managerId,

                // المشاريع
                projects: portfolioData.projects || [],

                // الأهداف الاستراتيجية
                strategicGoals: portfolioData.strategicGoals || [],

                // الميزانية الإجمالية
                totalBudget: 0,
                allocatedBudget: 0,
                remainingBudget: 0,

                // الأداء
                performance: {
                    onTime: 0,
                    onBudget: 0,
                    quality: 0,
                    roi: 0,
                    npv: 0
                },

                // المخاطر
                riskProfile: {
                    low: 0,
                    medium: 0,
                    high: 0,
                    critical: 0
                },

                // الموارد
                resourceUtilization: {
                    allocated: 0,
                    available: 0,
                    overallocated: 0
                },

                created: new Date().toISOString(),
                modified: new Date().toISOString()
            };

            this.projectPortfolios.set(portfolioId, portfolio);
            this.savePortfolios();

            console.log('✅ تم إنشاء محفظة المشاريع:', portfolio.name);
            return portfolio;

        } catch (error) {
            console.error('خطأ في إنشاء محفظة المشاريع:', error);
            throw error;
        }
    }

    // إنشاء مخاطر المشروع
    createProjectRisk(riskData) {
        try {
            const riskId = this.generateId();
            const risk = {
                id: riskId,
                projectId: riskData.projectId,
                title: riskData.title,
                description: riskData.description || '',
                category: riskData.category || 'general',

                // تقييم المخاطر
                probability: riskData.probability || 0.5, // 0-1
                impact: riskData.impact || 0.5, // 0-1
                score: 0, // probability * impact
                level: 'medium', // low, medium, high, critical

                // الاستجابة للمخاطر
                response: riskData.response || 'monitor', // avoid, mitigate, transfer, accept, monitor
                mitigation: riskData.mitigation || '',
                contingency: riskData.contingency || '',

                // المسؤولية
                owner: riskData.ownerId,
                assignee: riskData.assigneeId,

                // الحالة
                status: 'identified', // identified, assessed, planned, monitored, closed

                // التواريخ
                identifiedDate: new Date().toISOString(),
                targetDate: riskData.targetDate,
                closedDate: null,

                // التكلفة
                estimatedCost: riskData.estimatedCost || 0,
                actualCost: 0,

                // المراجعات
                reviews: [],

                created: new Date().toISOString(),
                modified: new Date().toISOString()
            };

            // حساب النتيجة والمستوى
            risk.score = risk.probability * risk.impact;
            risk.level = this.calculateRiskLevel(risk.score);

            this.projectRisks.set(riskId, risk);
            this.saveRisks();

            // تحديث مخاطر المشروع
            this.updateProjectRisks(riskData.projectId);

            console.log('✅ تم إنشاء مخاطر المشروع');
            return risk;

        } catch (error) {
            console.error('خطأ في إنشاء مخاطر المشروع:', error);
            throw error;
        }
    }

    // إنشاء ميزانية المشروع
    createProjectBudget(projectId, budgetData) {
        try {
            const budgetId = this.generateId();
            const budget = {
                id: budgetId,
                projectId: projectId,

                // الميزانية المخططة
                planned: {
                    total: budgetData.planned || 0,
                    labor: budgetData.breakdown?.labor || 0,
                    materials: budgetData.breakdown?.materials || 0,
                    equipment: budgetData.breakdown?.equipment || 0,
                    overhead: budgetData.breakdown?.overhead || 0,
                    contingency: budgetData.breakdown?.contingency || 0
                },

                // الميزانية الفعلية
                actual: {
                    total: 0,
                    labor: 0,
                    materials: 0,
                    equipment: 0,
                    overhead: 0,
                    contingency: 0
                },

                // الميزانية الملتزم بها
                committed: {
                    total: 0,
                    labor: 0,
                    materials: 0,
                    equipment: 0,
                    overhead: 0,
                    contingency: 0
                },

                // التباين
                variance: {
                    total: 0,
                    labor: 0,
                    materials: 0,
                    equipment: 0,
                    overhead: 0,
                    contingency: 0,
                    percentage: 0
                },

                // التنبؤات
                forecast: {
                    total: budgetData.planned || 0,
                    completion: budgetData.planned || 0,
                    variance: 0
                },

                // المؤشرات
                indicators: {
                    cpi: 1.0, // Cost Performance Index
                    spi: 1.0, // Schedule Performance Index
                    eac: budgetData.planned || 0, // Estimate at Completion
                    etc: budgetData.planned || 0, // Estimate to Complete
                    tcpi: 1.0 // To Complete Performance Index
                },

                currency: budgetData.currency || this.settings.defaultCurrency,

                created: new Date().toISOString(),
                modified: new Date().toISOString()
            };

            this.projectBudgets.set(budgetId, budget);
            this.saveBudgets();

            console.log('✅ تم إنشاء ميزانية المشروع');
            return budget;

        } catch (error) {
            console.error('خطأ في إنشاء ميزانية المشروع:', error);
            throw error;
        }
    }

    // إنشاء الجدولة الزمنية للمشروع
    createProjectTimeline(projectId, timelineData) {
        try {
            const timelineId = this.generateId();
            const timeline = {
                id: timelineId,
                projectId: projectId,

                // المراحل الرئيسية
                phases: [
                    { name: 'البدء', start: timelineData.planned.start, duration: 5, progress: 0 },
                    { name: 'التخطيط', start: null, duration: 15, progress: 0 },
                    { name: 'التنفيذ', start: null, duration: 60, progress: 0 },
                    { name: 'المراقبة', start: null, duration: 10, progress: 0 },
                    { name: 'الإغلاق', start: null, duration: 10, progress: 0 }
                ],

                // المعالم الرئيسية
                milestones: [],

                // المسار الحرج
                criticalPath: [],

                // التبعيات
                dependencies: [],

                // الموارد
                resourceAllocation: [],

                // التقدم
                progress: {
                    overall: 0,
                    phases: {},
                    milestones: 0,
                    tasks: 0
                },

                // المؤشرات
                indicators: {
                    spi: 1.0, // Schedule Performance Index
                    sv: 0, // Schedule Variance
                    plannedValue: 0,
                    earnedValue: 0,
                    actualCost: 0
                },

                created: new Date().toISOString(),
                modified: new Date().toISOString()
            };

            this.projectTimelines.set(timelineId, timeline);
            this.saveTimelines();

            console.log('✅ تم إنشاء الجدولة الزمنية للمشروع');
            return timeline;

        } catch (error) {
            console.error('خطأ في إنشاء الجدولة الزمنية:', error);
            throw error;
        }
    }

    // إنشاء مخرجات المشروع
    createProjectDeliverable(deliverableData) {
        try {
            const deliverableId = this.generateId();
            const deliverable = {
                id: deliverableId,
                projectId: deliverableData.projectId,
                name: deliverableData.name,
                description: deliverableData.description || '',
                type: deliverableData.type || 'document', // document, software, hardware, service

                // معايير القبول
                acceptanceCriteria: deliverableData.acceptanceCriteria || [],

                // الجودة
                qualityStandards: deliverableData.qualityStandards || [],
                qualityChecks: [],

                // التواريخ
                plannedDate: deliverableData.plannedDate,
                actualDate: null,
                approvedDate: null,

                // الحالة
                status: 'planned', // planned, in-progress, review, approved, rejected
                progress: 0,

                // المسؤولية
                owner: deliverableData.ownerId,
                approver: deliverableData.approverId,

                // الملفات
                files: [],
                versions: [],

                // المراجعات
                reviews: [],

                created: new Date().toISOString(),
                modified: new Date().toISOString()
            };

            this.projectDeliverables.set(deliverableId, deliverable);
            this.saveDeliverables();

            console.log('✅ تم إنشاء مخرجات المشروع');
            return deliverable;

        } catch (error) {
            console.error('خطأ في إنشاء مخرجات المشروع:', error);
            throw error;
        }
    }

    // إنشاء أصحاب المصلحة
    createProjectStakeholder(stakeholderData) {
        try {
            const stakeholderId = this.generateId();
            const stakeholder = {
                id: stakeholderId,
                projectId: stakeholderData.projectId,
                name: stakeholderData.name,
                role: stakeholderData.role,
                organization: stakeholderData.organization || '',

                // معلومات الاتصال
                contact: {
                    email: stakeholderData.email,
                    phone: stakeholderData.phone || '',
                    address: stakeholderData.address || ''
                },

                // التأثير والاهتمام
                influence: stakeholderData.influence || 'medium', // low, medium, high
                interest: stakeholderData.interest || 'medium', // low, medium, high
                attitude: stakeholderData.attitude || 'neutral', // supporter, neutral, opponent

                // استراتيجية الإدارة
                managementStrategy: stakeholderData.managementStrategy || 'monitor',
                communicationFrequency: stakeholderData.communicationFrequency || 'weekly',
                preferredChannel: stakeholderData.preferredChannel || 'email',

                // التوقعات
                expectations: stakeholderData.expectations || [],
                concerns: stakeholderData.concerns || [],

                // التفاعلات
                interactions: [],

                created: new Date().toISOString(),
                modified: new Date().toISOString()
            };

            this.projectStakeholders.set(stakeholderId, stakeholder);
            this.saveStakeholders();

            console.log('✅ تم إنشاء أصحاب المصلحة');
            return stakeholder;

        } catch (error) {
            console.error('خطأ في إنشاء أصحاب المصلحة:', error);
            throw error;
        }
    }

    // تحديث تقدم المشروع
    updateProjectProgress(projectId) {
        try {
            const project = this.projects.get(projectId);
            if (!project) {
                throw new Error('المشروع غير موجود');
            }

            // حساب التقدم الإجمالي
            const timeline = this.getProjectTimeline(projectId);
            const budget = this.getProjectBudget(projectId);
            const deliverables = this.getProjectDeliverables(projectId);
            const risks = this.getProjectRisks(projectId);

            // تحديث التقدم
            if (timeline) {
                project.progress.schedule = timeline.progress.overall;
            }

            if (budget) {
                project.progress.budget = this.calculateBudgetProgress(budget);
            }

            if (deliverables.length > 0) {
                project.progress.deliverables = deliverables.reduce((sum, d) => sum + d.progress, 0) / deliverables.length;
            }

            // حساب التقدم الإجمالي
            project.progress.overall = (
                project.progress.schedule * 0.4 +
                project.progress.budget * 0.3 +
                project.progress.deliverables * 0.3
            );

            // تحديث المخاطر
            if (risks.length > 0) {
                project.risks.total = risks.length;
                project.risks.high = risks.filter(r => r.level === 'high' || r.level === 'critical').length;
                project.risks.medium = risks.filter(r => r.level === 'medium').length;
                project.risks.low = risks.filter(r => r.level === 'low').length;
                project.risks.score = risks.reduce((sum, r) => sum + r.score, 0) / risks.length;
            }

            project.modified = new Date().toISOString();
            this.saveProjects();

            console.log('✅ تم تحديث تقدم المشروع');
            return project;

        } catch (error) {
            console.error('خطأ في تحديث تقدم المشروع:', error);
            throw error;
        }
    }

    // تحليل أداء المحفظة
    analyzePortfolioPerformance(portfolioId) {
        try {
            const portfolio = this.projectPortfolios.get(portfolioId);
            if (!portfolio) {
                throw new Error('المحفظة غير موجودة');
            }

            const projects = portfolio.projects.map(id => this.projects.get(id)).filter(p => p);

            // حساب الأداء
            const performance = {
                totalProjects: projects.length,
                completedProjects: projects.filter(p => p.status === 'completed').length,
                onTimeProjects: projects.filter(p => this.isProjectOnTime(p)).length,
                onBudgetProjects: projects.filter(p => this.isProjectOnBudget(p)).length,

                totalBudget: projects.reduce((sum, p) => sum + p.budget.planned, 0),
                actualCost: projects.reduce((sum, p) => sum + p.budget.actual, 0),
                budgetVariance: 0,

                averageProgress: projects.reduce((sum, p) => sum + p.progress.overall, 0) / projects.length,

                riskDistribution: {
                    low: projects.reduce((sum, p) => sum + p.risks.low, 0),
                    medium: projects.reduce((sum, p) => sum + p.risks.medium, 0),
                    high: projects.reduce((sum, p) => sum + p.risks.high, 0)
                },

                qualityScore: projects.reduce((sum, p) => sum + p.quality.score, 0) / projects.length
            };

            performance.budgetVariance = (performance.actualCost - performance.totalBudget) / performance.totalBudget;
            performance.onTimePercentage = (performance.onTimeProjects / performance.totalProjects) * 100;
            performance.onBudgetPercentage = (performance.onBudgetProjects / performance.totalProjects) * 100;

            // تحديث المحفظة
            portfolio.performance = performance;
            portfolio.totalBudget = performance.totalBudget;
            portfolio.modified = new Date().toISOString();

            this.savePortfolios();

            console.log('✅ تم تحليل أداء المحفظة');
            return performance;

        } catch (error) {
            console.error('خطأ في تحليل أداء المحفظة:', error);
            throw error;
        }
    }

    // البحث في المشاريع المتقدم
    searchAdvancedProjects(query, filters = {}) {
        const results = [];
        const searchTerm = query.toLowerCase();

        this.projects.forEach(project => {
            const name = project.name.toLowerCase();
            const code = project.code.toLowerCase();
            const description = project.description.toLowerCase();

            if (name.includes(searchTerm) ||
                code.includes(searchTerm) ||
                description.includes(searchTerm)) {

                // تطبيق الفلاتر
                if (filters.status && project.status !== filters.status) return;
                if (filters.priority && project.priority !== filters.priority) return;
                if (filters.type && project.type !== filters.type) return;
                if (filters.phase && project.phase !== filters.phase) return;
                if (filters.manager && project.team.manager !== filters.manager) return;
                if (filters.portfolio && !this.isProjectInPortfolio(project.id, filters.portfolio)) return;

                results.push(project);
            }
        });

        return results;
    }

    // الحصول على تقارير المشاريع المتقدمة
    getAdvancedProjectsReports() {
        const projects = Array.from(this.projects.values());
        const portfolios = Array.from(this.projectPortfolios.values());
        const risks = Array.from(this.projectRisks.values());
        const budgets = Array.from(this.projectBudgets.values());

        return {
            projects: {
                total: projects.length,
                active: projects.filter(p => p.status === 'active').length,
                completed: projects.filter(p => p.status === 'completed').length,
                onHold: projects.filter(p => p.status === 'on-hold').length,
                cancelled: projects.filter(p => p.status === 'cancelled').length,
                averageProgress: projects.length > 0 ?
                    projects.reduce((sum, p) => sum + p.progress.overall, 0) / projects.length : 0
            },
            portfolios: {
                total: portfolios.length,
                totalValue: portfolios.reduce((sum, p) => sum + p.totalBudget, 0),
                averagePerformance: portfolios.length > 0 ?
                    portfolios.reduce((sum, p) => sum + (p.performance.onTimePercentage || 0), 0) / portfolios.length : 0
            },
            risks: {
                total: risks.length,
                high: risks.filter(r => r.level === 'high' || r.level === 'critical').length,
                medium: risks.filter(r => r.level === 'medium').length,
                low: risks.filter(r => r.level === 'low').length,
                averageScore: risks.length > 0 ?
                    risks.reduce((sum, r) => sum + r.score, 0) / risks.length : 0
            },
            budget: {
                totalPlanned: budgets.reduce((sum, b) => sum + b.planned.total, 0),
                totalActual: budgets.reduce((sum, b) => sum + b.actual.total, 0),
                totalVariance: budgets.reduce((sum, b) => sum + b.variance.total, 0),
                averageCPI: budgets.length > 0 ?
                    budgets.reduce((sum, b) => sum + b.indicators.cpi, 0) / budgets.length : 1.0
            }
        };
    }

    // دوال مساعدة
    calculateRiskLevel(score) {
        if (score >= 0.8) return 'critical';
        if (score >= 0.6) return 'high';
        if (score >= 0.4) return 'medium';
        return 'low';
    }

    calculateBudgetProgress(budget) {
        if (budget.planned.total === 0) return 0;
        return (budget.actual.total / budget.planned.total) * 100;
    }

    isProjectOnTime(project) {
        if (!project.dates.planned.end) return true;
        const plannedEnd = new Date(project.dates.planned.end);
        const today = new Date();
        return today <= plannedEnd || project.status === 'completed';
    }

    isProjectOnBudget(project) {
        return Math.abs(project.budget.variance) <= this.settings.budgetVarianceThreshold;
    }

    isProjectInPortfolio(projectId, portfolioId) {
        const portfolio = this.projectPortfolios.get(portfolioId);
        return portfolio && portfolio.projects.includes(projectId);
    }

    getProjectTimeline(projectId) {
        return Array.from(this.projectTimelines.values()).find(t => t.projectId === projectId);
    }

    getProjectBudget(projectId) {
        return Array.from(this.projectBudgets.values()).find(b => b.projectId === projectId);
    }

    getProjectDeliverables(projectId) {
        return Array.from(this.projectDeliverables.values()).filter(d => d.projectId === projectId);
    }

    getProjectRisks(projectId) {
        return Array.from(this.projectRisks.values()).filter(r => r.projectId === projectId);
    }

    getProjectStakeholders(projectId) {
        return Array.from(this.projectStakeholders.values()).filter(s => s.projectId === projectId);
    }

    updateProjectRisks(projectId) {
        const project = this.projects.get(projectId);
        const risks = this.getProjectRisks(projectId);

        if (project && risks.length > 0) {
            project.risks.total = risks.length;
            project.risks.high = risks.filter(r => r.level === 'high' || r.level === 'critical').length;
            project.risks.medium = risks.filter(r => r.level === 'medium').length;
            project.risks.low = risks.filter(r => r.level === 'low').length;
            project.risks.score = risks.reduce((sum, r) => sum + r.score, 0) / risks.length;

            this.saveProjects();
        }
    }

    // توليد رمز المشروع
    generateProjectCode() {
        const prefix = 'PRJ';
        const timestamp = Date.now().toString().slice(-6);
        const random = Math.random().toString(36).substr(2, 3).toUpperCase();
        return `${prefix}${timestamp}${random}`;
    }

    // إعداد القوالب الافتراضية
    setupDefaultTemplates() {
        const defaultTemplates = [
            {
                name: 'مشروع تطوير برمجيات',
                type: 'agile',
                methodology: 'scrum',
                phases: ['التخطيط', 'التطوير', 'الاختبار', 'النشر', 'الصيانة'],
                deliverables: ['متطلبات النظام', 'التصميم', 'الكود', 'الاختبارات', 'الوثائق']
            },
            {
                name: 'مشروع بناء',
                type: 'waterfall',
                methodology: 'traditional',
                phases: ['التصميم', 'التخطيط', 'البناء', 'التشطيب', 'التسليم'],
                deliverables: ['المخططات', 'التراخيص', 'المواد', 'العمالة', 'التقارير']
            },
            {
                name: 'مشروع تسويقي',
                type: 'hybrid',
                methodology: 'lean',
                phases: ['البحث', 'الاستراتيجية', 'التنفيذ', 'القياس', 'التحسين'],
                deliverables: ['خطة التسويق', 'المحتوى', 'الحملات', 'التقارير', 'التحليلات']
            }
        ];

        defaultTemplates.forEach(template => {
            if (!Array.from(this.projectTemplates.values()).find(t => t.name === template.name)) {
                const templateId = this.generateId();
                this.projectTemplates.set(templateId, {
                    id: templateId,
                    ...template,
                    created: new Date().toISOString()
                });
            }
        });
    }

    // توليد معرف فريد
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // إعداد قاعدة البيانات
    setupDatabase() {
        console.log('📊 إعداد قاعدة بيانات المشاريع المتقدمة');
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // سيتم إضافة مستمعي الأحداث هنا
    }

    // حفظ البيانات
    saveProjects() {
        try {
            const projectsObj = {};
            this.projects.forEach((project, id) => {
                projectsObj[id] = project;
            });
            localStorage.setItem('advanced_projects', JSON.stringify(projectsObj));
        } catch (error) {
            console.error('خطأ في حفظ المشاريع المتقدمة:', error);
        }
    }

    savePortfolios() {
        try {
            const portfoliosObj = {};
            this.projectPortfolios.forEach((portfolio, id) => {
                portfoliosObj[id] = portfolio;
            });
            localStorage.setItem('project_portfolios', JSON.stringify(portfoliosObj));
        } catch (error) {
            console.error('خطأ في حفظ محافظ المشاريع:', error);
        }
    }

    saveRisks() {
        try {
            const risksObj = {};
            this.projectRisks.forEach((risk, id) => {
                risksObj[id] = risk;
            });
            localStorage.setItem('project_risks', JSON.stringify(risksObj));
        } catch (error) {
            console.error('خطأ في حفظ مخاطر المشاريع:', error);
        }
    }

    saveBudgets() {
        try {
            const budgetsObj = {};
            this.projectBudgets.forEach((budget, id) => {
                budgetsObj[id] = budget;
            });
            localStorage.setItem('project_budgets', JSON.stringify(budgetsObj));
        } catch (error) {
            console.error('خطأ في حفظ ميزانيات المشاريع:', error);
        }
    }

    saveTimelines() {
        try {
            const timelinesObj = {};
            this.projectTimelines.forEach((timeline, id) => {
                timelinesObj[id] = timeline;
            });
            localStorage.setItem('project_timelines', JSON.stringify(timelinesObj));
        } catch (error) {
            console.error('خطأ في حفظ جداول المشاريع:', error);
        }
    }

    saveDeliverables() {
        try {
            const deliverablesObj = {};
            this.projectDeliverables.forEach((deliverable, id) => {
                deliverablesObj[id] = deliverable;
            });
            localStorage.setItem('project_deliverables', JSON.stringify(deliverablesObj));
        } catch (error) {
            console.error('خطأ في حفظ مخرجات المشاريع:', error);
        }
    }

    saveStakeholders() {
        try {
            const stakeholdersObj = {};
            this.projectStakeholders.forEach((stakeholder, id) => {
                stakeholdersObj[id] = stakeholder;
            });
            localStorage.setItem('project_stakeholders', JSON.stringify(stakeholdersObj));
        } catch (error) {
            console.error('خطأ في حفظ أصحاب المصلحة:', error);
        }
    }

    saveData() {
        this.saveProjects();
        this.savePortfolios();
        this.saveRisks();
        this.saveBudgets();
        this.saveTimelines();
        this.saveDeliverables();
        this.saveStakeholders();
    }

    // تحميل البيانات
    loadData() {
        try {
            // تحميل المشاريع
            const projects = JSON.parse(localStorage.getItem('advanced_projects') || '{}');
            Object.values(projects).forEach(project => {
                this.projects.set(project.id, project);
            });

            // تحميل المحافظ
            const portfolios = JSON.parse(localStorage.getItem('project_portfolios') || '{}');
            Object.values(portfolios).forEach(portfolio => {
                this.projectPortfolios.set(portfolio.id, portfolio);
            });

            // تحميل المخاطر
            const risks = JSON.parse(localStorage.getItem('project_risks') || '{}');
            Object.values(risks).forEach(risk => {
                this.projectRisks.set(risk.id, risk);
            });

            // تحميل الميزانيات
            const budgets = JSON.parse(localStorage.getItem('project_budgets') || '{}');
            Object.values(budgets).forEach(budget => {
                this.projectBudgets.set(budget.id, budget);
            });

            // تحميل الجداول
            const timelines = JSON.parse(localStorage.getItem('project_timelines') || '{}');
            Object.values(timelines).forEach(timeline => {
                this.projectTimelines.set(timeline.id, timeline);
            });

            // تحميل المخرجات
            const deliverables = JSON.parse(localStorage.getItem('project_deliverables') || '{}');
            Object.values(deliverables).forEach(deliverable => {
                this.projectDeliverables.set(deliverable.id, deliverable);
            });

            // تحميل أصحاب المصلحة
            const stakeholders = JSON.parse(localStorage.getItem('project_stakeholders') || '{}');
            Object.values(stakeholders).forEach(stakeholder => {
                this.projectStakeholders.set(stakeholder.id, stakeholder);
            });

            console.log(`🚀 تم تحميل ${this.projects.size} مشروع متقدم و ${this.projectPortfolios.size} محفظة`);

        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
        }
    }
}

// تصدير الكلاس
window.AdvancedProjectsManager = AdvancedProjectsManager;