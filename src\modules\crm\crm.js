/**
 * نظام إدارة علاقات العملاء (CRM)
 * Black Horse ERP System
 */

class CRMManager {
    constructor() {
        this.customers = new Map();
        this.leads = new Map();
        this.opportunities = new Map();
        this.activities = new Map();
        this.campaigns = new Map();
        this.contacts = new Map();
        
        this.settings = {
            autoAssignLeads: true,
            leadScoringEnabled: true,
            emailIntegration: true,
            smsIntegration: true,
            whatsappIntegration: true,
            defaultCurrency: 'SAR',
            defaultLanguage: 'ar'
        };
        
        this.initializeCRM();
        this.loadData();
    }

    // تهيئة نظام CRM
    initializeCRM() {
        console.log('👥 تهيئة نظام إدارة العملاء...');
        
        // إعداد قاعدة البيانات
        this.setupDatabase();
        
        // إعداد الأحداث
        this.setupEventListeners();
        
        console.log('✅ تم تهيئة نظام إدارة العملاء');
    }

    // إنشاء عميل جديد
    createCustomer(customerData) {
        try {
            const customerId = this.generateId();
            const customer = {
                id: customerId,
                type: 'customer', // customer, prospect, partner
                firstName: customerData.firstName,
                lastName: customerData.lastName,
                company: customerData.company || '',
                email: customerData.email,
                phone: customerData.phone,
                mobile: customerData.mobile || '',
                address: {
                    street: customerData.street || '',
                    city: customerData.city || '',
                    state: customerData.state || '',
                    country: customerData.country || 'السعودية',
                    postalCode: customerData.postalCode || ''
                },
                status: 'active', // active, inactive, blocked
                category: customerData.category || 'regular', // vip, regular, wholesale
                source: customerData.source || 'direct', // direct, website, referral, campaign
                assignedTo: customerData.assignedTo,
                tags: customerData.tags || [],
                customFields: customerData.customFields || {},
                preferences: {
                    language: customerData.language || 'ar',
                    currency: customerData.currency || 'SAR',
                    communicationMethod: customerData.communicationMethod || 'email',
                    newsletter: customerData.newsletter !== false
                },
                statistics: {
                    totalOrders: 0,
                    totalSpent: 0,
                    averageOrderValue: 0,
                    lastOrderDate: null,
                    lifetimeValue: 0,
                    loyaltyPoints: 0
                },
                created: new Date().toISOString(),
                modified: new Date().toISOString(),
                lastContact: null,
                notes: []
            };
            
            this.customers.set(customerId, customer);
            this.saveCustomers();
            
            // إنشاء نشاط
            this.createActivity({
                type: 'customer_created',
                customerId: customerId,
                description: `تم إنشاء عميل جديد: ${customer.firstName} ${customer.lastName}`,
                userId: 'current_user'
            });
            
            console.log('✅ تم إنشاء العميل:', customer.firstName, customer.lastName);
            return customer;
            
        } catch (error) {
            console.error('خطأ في إنشاء العميل:', error);
            throw error;
        }
    }

    // تحديث عميل
    updateCustomer(customerId, updates) {
        try {
            const customer = this.customers.get(customerId);
            if (!customer) {
                throw new Error('العميل غير موجود');
            }
            
            Object.assign(customer, updates);
            customer.modified = new Date().toISOString();
            
            this.saveCustomers();
            
            // إنشاء نشاط
            this.createActivity({
                type: 'customer_updated',
                customerId: customerId,
                description: `تم تحديث بيانات العميل: ${customer.firstName} ${customer.lastName}`,
                userId: 'current_user'
            });
            
            console.log('✅ تم تحديث العميل:', customer.firstName, customer.lastName);
            return customer;
            
        } catch (error) {
            console.error('خطأ في تحديث العميل:', error);
            throw error;
        }
    }

    // إنشاء عميل محتمل (Lead)
    createLead(leadData) {
        try {
            const leadId = this.generateId();
            const lead = {
                id: leadId,
                firstName: leadData.firstName,
                lastName: leadData.lastName,
                company: leadData.company || '',
                email: leadData.email,
                phone: leadData.phone,
                status: 'new', // new, contacted, qualified, proposal, negotiation, won, lost
                source: leadData.source || 'website',
                score: this.calculateLeadScore(leadData),
                assignedTo: leadData.assignedTo || this.autoAssignLead(),
                tags: leadData.tags || [],
                interests: leadData.interests || [],
                budget: leadData.budget || 0,
                timeline: leadData.timeline || '',
                notes: leadData.notes || '',
                customFields: leadData.customFields || {},
                created: new Date().toISOString(),
                modified: new Date().toISOString(),
                lastContact: null,
                nextFollowUp: leadData.nextFollowUp,
                convertedAt: null,
                convertedToCustomerId: null
            };
            
            this.leads.set(leadId, lead);
            this.saveLeads();
            
            // إنشاء نشاط
            this.createActivity({
                type: 'lead_created',
                leadId: leadId,
                description: `عميل محتمل جديد: ${lead.firstName} ${lead.lastName}`,
                userId: 'current_user'
            });
            
            console.log('✅ تم إنشاء العميل المحتمل:', lead.firstName, lead.lastName);
            return lead;
            
        } catch (error) {
            console.error('خطأ في إنشاء العميل المحتمل:', error);
            throw error;
        }
    }

    // تحويل عميل محتمل إلى عميل
    convertLeadToCustomer(leadId, customerData = {}) {
        try {
            const lead = this.leads.get(leadId);
            if (!lead) {
                throw new Error('العميل المحتمل غير موجود');
            }
            
            // إنشاء عميل جديد من بيانات العميل المحتمل
            const customer = this.createCustomer({
                firstName: lead.firstName,
                lastName: lead.lastName,
                company: lead.company,
                email: lead.email,
                phone: lead.phone,
                source: lead.source,
                assignedTo: lead.assignedTo,
                tags: lead.tags,
                ...customerData
            });
            
            // تحديث العميل المحتمل
            lead.status = 'won';
            lead.convertedAt = new Date().toISOString();
            lead.convertedToCustomerId = customer.id;
            lead.modified = new Date().toISOString();
            
            this.saveLeads();
            
            // إنشاء نشاط
            this.createActivity({
                type: 'lead_converted',
                leadId: leadId,
                customerId: customer.id,
                description: `تم تحويل العميل المحتمل إلى عميل: ${customer.firstName} ${customer.lastName}`,
                userId: 'current_user'
            });
            
            console.log('✅ تم تحويل العميل المحتمل إلى عميل');
            return customer;
            
        } catch (error) {
            console.error('خطأ في تحويل العميل المحتمل:', error);
            throw error;
        }
    }

    // إنشاء فرصة بيع
    createOpportunity(opportunityData) {
        try {
            const opportunityId = this.generateId();
            const opportunity = {
                id: opportunityId,
                name: opportunityData.name,
                customerId: opportunityData.customerId,
                leadId: opportunityData.leadId,
                value: opportunityData.value || 0,
                currency: opportunityData.currency || 'SAR',
                stage: 'prospecting', // prospecting, qualification, proposal, negotiation, closed-won, closed-lost
                probability: opportunityData.probability || 10,
                expectedCloseDate: opportunityData.expectedCloseDate,
                actualCloseDate: null,
                source: opportunityData.source || 'direct',
                assignedTo: opportunityData.assignedTo,
                products: opportunityData.products || [],
                competitors: opportunityData.competitors || [],
                description: opportunityData.description || '',
                tags: opportunityData.tags || [],
                customFields: opportunityData.customFields || {},
                created: new Date().toISOString(),
                modified: new Date().toISOString(),
                activities: [],
                documents: []
            };
            
            this.opportunities.set(opportunityId, opportunity);
            this.saveOpportunities();
            
            // إنشاء نشاط
            this.createActivity({
                type: 'opportunity_created',
                opportunityId: opportunityId,
                customerId: opportunityData.customerId,
                description: `فرصة بيع جديدة: ${opportunity.name}`,
                userId: 'current_user'
            });
            
            console.log('✅ تم إنشاء فرصة البيع:', opportunity.name);
            return opportunity;
            
        } catch (error) {
            console.error('خطأ في إنشاء فرصة البيع:', error);
            throw error;
        }
    }

    // إنشاء نشاط
    createActivity(activityData) {
        try {
            const activityId = this.generateId();
            const activity = {
                id: activityId,
                type: activityData.type, // call, email, meeting, task, note
                subject: activityData.subject || '',
                description: activityData.description || '',
                customerId: activityData.customerId,
                leadId: activityData.leadId,
                opportunityId: activityData.opportunityId,
                userId: activityData.userId,
                status: activityData.status || 'completed', // planned, in-progress, completed, cancelled
                priority: activityData.priority || 'medium',
                scheduledDate: activityData.scheduledDate,
                completedDate: activityData.status === 'completed' ? new Date().toISOString() : null,
                duration: activityData.duration || 0,
                outcome: activityData.outcome || '',
                nextAction: activityData.nextAction || '',
                attachments: activityData.attachments || [],
                created: new Date().toISOString(),
                modified: new Date().toISOString()
            };
            
            this.activities.set(activityId, activity);
            this.saveActivities();
            
            // تحديث تاريخ آخر اتصال
            if (activityData.customerId) {
                const customer = this.customers.get(activityData.customerId);
                if (customer) {
                    customer.lastContact = new Date().toISOString();
                    this.saveCustomers();
                }
            }
            
            if (activityData.leadId) {
                const lead = this.leads.get(activityData.leadId);
                if (lead) {
                    lead.lastContact = new Date().toISOString();
                    this.saveLeads();
                }
            }
            
            console.log('✅ تم إنشاء النشاط:', activity.type);
            return activity;
            
        } catch (error) {
            console.error('خطأ في إنشاء النشاط:', error);
            throw error;
        }
    }

    // حساب نقاط العميل المحتمل
    calculateLeadScore(leadData) {
        let score = 0;
        
        // نقاط حسب المصدر
        const sourceScores = {
            'website': 20,
            'referral': 30,
            'social_media': 15,
            'email_campaign': 25,
            'direct': 10
        };
        score += sourceScores[leadData.source] || 0;
        
        // نقاط حسب الشركة
        if (leadData.company) score += 15;
        
        // نقاط حسب الميزانية
        if (leadData.budget > 10000) score += 30;
        else if (leadData.budget > 5000) score += 20;
        else if (leadData.budget > 1000) score += 10;
        
        // نقاط حسب الاهتمامات
        if (leadData.interests && leadData.interests.length > 0) {
            score += leadData.interests.length * 5;
        }
        
        // نقاط حسب الجدول الزمني
        if (leadData.timeline === 'immediate') score += 25;
        else if (leadData.timeline === 'this_month') score += 20;
        else if (leadData.timeline === 'this_quarter') score += 15;
        
        return Math.min(score, 100); // الحد الأقصى 100
    }

    // تعيين تلقائي للعميل المحتمل
    autoAssignLead() {
        if (!this.settings.autoAssignLeads) return null;
        
        // منطق التعيين التلقائي (يمكن تخصيصه)
        const salesTeam = ['أحمد محمد', 'فاطمة علي', 'محمد حسن'];
        return salesTeam[Math.floor(Math.random() * salesTeam.length)];
    }

    // البحث في العملاء
    searchCustomers(query, filters = {}) {
        const results = [];
        const searchTerm = query.toLowerCase();
        
        this.customers.forEach(customer => {
            const fullName = `${customer.firstName} ${customer.lastName}`.toLowerCase();
            const company = customer.company.toLowerCase();
            const email = customer.email.toLowerCase();
            const phone = customer.phone;
            
            if (fullName.includes(searchTerm) ||
                company.includes(searchTerm) ||
                email.includes(searchTerm) ||
                phone.includes(searchTerm)) {
                
                // تطبيق الفلاتر
                if (filters.status && customer.status !== filters.status) return;
                if (filters.category && customer.category !== filters.category) return;
                if (filters.assignedTo && customer.assignedTo !== filters.assignedTo) return;
                
                results.push(customer);
            }
        });
        
        return results;
    }

    // الحصول على أنشطة العميل
    getCustomerActivities(customerId) {
        return Array.from(this.activities.values())
            .filter(activity => activity.customerId === customerId)
            .sort((a, b) => new Date(b.created) - new Date(a.created));
    }

    // الحصول على فرص البيع للعميل
    getCustomerOpportunities(customerId) {
        return Array.from(this.opportunities.values())
            .filter(opportunity => opportunity.customerId === customerId);
    }

    // تحديث إحصائيات العميل
    updateCustomerStatistics(customerId, orderData) {
        const customer = this.customers.get(customerId);
        if (!customer) return;
        
        customer.statistics.totalOrders += 1;
        customer.statistics.totalSpent += orderData.total;
        customer.statistics.averageOrderValue = customer.statistics.totalSpent / customer.statistics.totalOrders;
        customer.statistics.lastOrderDate = new Date().toISOString();
        customer.statistics.lifetimeValue = customer.statistics.totalSpent;
        
        // إضافة نقاط الولاء
        customer.statistics.loyaltyPoints += Math.floor(orderData.total / 10);
        
        this.saveCustomers();
    }

    // إرسال رسالة
    sendMessage(messageData) {
        try {
            const message = {
                id: this.generateId(),
                type: messageData.type, // email, sms, whatsapp
                to: messageData.to,
                subject: messageData.subject || '',
                content: messageData.content,
                customerId: messageData.customerId,
                leadId: messageData.leadId,
                campaignId: messageData.campaignId,
                status: 'sent',
                sentAt: new Date().toISOString(),
                deliveredAt: null,
                readAt: null,
                clickedAt: null
            };
            
            // محاكاة إرسال الرسالة
            console.log(`📧 إرسال ${message.type} إلى ${message.to}`);
            
            // إنشاء نشاط
            this.createActivity({
                type: message.type,
                subject: message.subject,
                description: `تم إرسال ${message.type} إلى ${message.to}`,
                customerId: message.customerId,
                leadId: message.leadId,
                userId: 'current_user'
            });
            
            return message;
            
        } catch (error) {
            console.error('خطأ في إرسال الرسالة:', error);
            throw error;
        }
    }

    // إنشاء حملة تسويقية
    createCampaign(campaignData) {
        try {
            const campaignId = this.generateId();
            const campaign = {
                id: campaignId,
                name: campaignData.name,
                type: campaignData.type, // email, sms, whatsapp, social
                status: 'draft', // draft, active, paused, completed
                startDate: campaignData.startDate,
                endDate: campaignData.endDate,
                targetAudience: campaignData.targetAudience || 'all',
                filters: campaignData.filters || {},
                content: {
                    subject: campaignData.subject || '',
                    message: campaignData.message || '',
                    template: campaignData.template || ''
                },
                budget: campaignData.budget || 0,
                goals: campaignData.goals || {},
                statistics: {
                    sent: 0,
                    delivered: 0,
                    opened: 0,
                    clicked: 0,
                    converted: 0,
                    unsubscribed: 0
                },
                created: new Date().toISOString(),
                modified: new Date().toISOString()
            };
            
            this.campaigns.set(campaignId, campaign);
            this.saveCampaigns();
            
            console.log('✅ تم إنشاء الحملة:', campaign.name);
            return campaign;
            
        } catch (error) {
            console.error('خطأ في إنشاء الحملة:', error);
            throw error;
        }
    }

    // تشغيل حملة
    runCampaign(campaignId) {
        try {
            const campaign = this.campaigns.get(campaignId);
            if (!campaign) {
                throw new Error('الحملة غير موجودة');
            }
            
            campaign.status = 'active';
            campaign.modified = new Date().toISOString();
            
            // الحصول على الجمهور المستهدف
            const audience = this.getCampaignAudience(campaign);
            
            // إرسال الرسائل
            audience.forEach(contact => {
                this.sendMessage({
                    type: campaign.type,
                    to: contact.email || contact.phone,
                    subject: campaign.content.subject,
                    content: campaign.content.message,
                    customerId: contact.id,
                    campaignId: campaignId
                });
                
                campaign.statistics.sent++;
            });
            
            this.saveCampaigns();
            
            console.log('✅ تم تشغيل الحملة:', campaign.name);
            return campaign;
            
        } catch (error) {
            console.error('خطأ في تشغيل الحملة:', error);
            throw error;
        }
    }

    // الحصول على جمهور الحملة
    getCampaignAudience(campaign) {
        let audience = Array.from(this.customers.values());
        
        // تطبيق الفلاتر
        if (campaign.filters.status) {
            audience = audience.filter(customer => customer.status === campaign.filters.status);
        }
        
        if (campaign.filters.category) {
            audience = audience.filter(customer => customer.category === campaign.filters.category);
        }
        
        if (campaign.filters.minSpent) {
            audience = audience.filter(customer => customer.statistics.totalSpent >= campaign.filters.minSpent);
        }
        
        if (campaign.filters.tags && campaign.filters.tags.length > 0) {
            audience = audience.filter(customer => 
                campaign.filters.tags.some(tag => customer.tags.includes(tag))
            );
        }
        
        return audience;
    }

    // الحصول على تقارير CRM
    getCRMReports() {
        const customers = Array.from(this.customers.values());
        const leads = Array.from(this.leads.values());
        const opportunities = Array.from(this.opportunities.values());
        const activities = Array.from(this.activities.values());
        
        return {
            customers: {
                total: customers.length,
                active: customers.filter(c => c.status === 'active').length,
                new: customers.filter(c => {
                    const created = new Date(c.created);
                    const monthAgo = new Date();
                    monthAgo.setMonth(monthAgo.getMonth() - 1);
                    return created > monthAgo;
                }).length,
                totalValue: customers.reduce((sum, c) => sum + c.statistics.totalSpent, 0)
            },
            leads: {
                total: leads.length,
                new: leads.filter(l => l.status === 'new').length,
                qualified: leads.filter(l => l.status === 'qualified').length,
                converted: leads.filter(l => l.status === 'won').length,
                conversionRate: leads.length > 0 ? 
                    (leads.filter(l => l.status === 'won').length / leads.length) * 100 : 0
            },
            opportunities: {
                total: opportunities.length,
                open: opportunities.filter(o => !['closed-won', 'closed-lost'].includes(o.stage)).length,
                won: opportunities.filter(o => o.stage === 'closed-won').length,
                totalValue: opportunities.reduce((sum, o) => sum + o.value, 0),
                avgValue: opportunities.length > 0 ? 
                    opportunities.reduce((sum, o) => sum + o.value, 0) / opportunities.length : 0
            },
            activities: {
                total: activities.length,
                thisWeek: activities.filter(a => {
                    const created = new Date(a.created);
                    const weekAgo = new Date();
                    weekAgo.setDate(weekAgo.getDate() - 7);
                    return created > weekAgo;
                }).length,
                byType: this.groupActivitiesByType(activities)
            }
        };
    }

    // تجميع الأنشطة حسب النوع
    groupActivitiesByType(activities) {
        const grouped = {};
        activities.forEach(activity => {
            grouped[activity.type] = (grouped[activity.type] || 0) + 1;
        });
        return grouped;
    }

    // توليد معرف فريد
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // إعداد قاعدة البيانات
    setupDatabase() {
        console.log('📊 إعداد قاعدة بيانات CRM');
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // سيتم إضافة مستمعي الأحداث هنا
    }

    // حفظ البيانات
    saveCustomers() {
        try {
            const customersObj = {};
            this.customers.forEach((customer, id) => {
                customersObj[id] = customer;
            });
            localStorage.setItem('crm_customers', JSON.stringify(customersObj));
        } catch (error) {
            console.error('خطأ في حفظ العملاء:', error);
        }
    }

    saveLeads() {
        try {
            const leadsObj = {};
            this.leads.forEach((lead, id) => {
                leadsObj[id] = lead;
            });
            localStorage.setItem('crm_leads', JSON.stringify(leadsObj));
        } catch (error) {
            console.error('خطأ في حفظ العملاء المحتملين:', error);
        }
    }

    saveOpportunities() {
        try {
            const opportunitiesObj = {};
            this.opportunities.forEach((opportunity, id) => {
                opportunitiesObj[id] = opportunity;
            });
            localStorage.setItem('crm_opportunities', JSON.stringify(opportunitiesObj));
        } catch (error) {
            console.error('خطأ في حفظ فرص البيع:', error);
        }
    }

    saveActivities() {
        try {
            const activitiesObj = {};
            this.activities.forEach((activity, id) => {
                activitiesObj[id] = activity;
            });
            localStorage.setItem('crm_activities', JSON.stringify(activitiesObj));
        } catch (error) {
            console.error('خطأ في حفظ الأنشطة:', error);
        }
    }

    saveCampaigns() {
        try {
            const campaignsObj = {};
            this.campaigns.forEach((campaign, id) => {
                campaignsObj[id] = campaign;
            });
            localStorage.setItem('crm_campaigns', JSON.stringify(campaignsObj));
        } catch (error) {
            console.error('خطأ في حفظ الحملات:', error);
        }
    }

    saveData() {
        this.saveCustomers();
        this.saveLeads();
        this.saveOpportunities();
        this.saveActivities();
        this.saveCampaigns();
    }

    // تحميل البيانات
    loadData() {
        try {
            // تحميل العملاء
            const customers = JSON.parse(localStorage.getItem('crm_customers') || '{}');
            Object.values(customers).forEach(customer => {
                this.customers.set(customer.id, customer);
            });
            
            // تحميل العملاء المحتملين
            const leads = JSON.parse(localStorage.getItem('crm_leads') || '{}');
            Object.values(leads).forEach(lead => {
                this.leads.set(lead.id, lead);
            });
            
            // تحميل فرص البيع
            const opportunities = JSON.parse(localStorage.getItem('crm_opportunities') || '{}');
            Object.values(opportunities).forEach(opportunity => {
                this.opportunities.set(opportunity.id, opportunity);
            });
            
            // تحميل الأنشطة
            const activities = JSON.parse(localStorage.getItem('crm_activities') || '{}');
            Object.values(activities).forEach(activity => {
                this.activities.set(activity.id, activity);
            });
            
            // تحميل الحملات
            const campaigns = JSON.parse(localStorage.getItem('crm_campaigns') || '{}');
            Object.values(campaigns).forEach(campaign => {
                this.campaigns.set(campaign.id, campaign);
            });
            
            console.log(`👥 تم تحميل ${this.customers.size} عميل و ${this.leads.size} عميل محتمل`);
            
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
        }
    }
}

// تصدير الكلاس
window.CRMManager = CRMManager;
