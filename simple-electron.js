const { app, BrowserWindow, Menu, dialog, shell, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

// إصلاح مشاكل GPU
app.commandLine.appendSwitch('--disable-gpu-sandbox');
app.commandLine.appendSwitch('--disable-software-rasterizer');
app.commandLine.appendSwitch('--disable-gpu');

let mainWindow;

function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        minWidth: 1200,
        minHeight: 700,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            webSecurity: true,
            enableRemoteModule: false,
            preload: path.join(__dirname, 'electron-preload.js')
        },
        icon: path.join(__dirname, 'assets', 'icon.png'),
        show: false,
        titleBarStyle: 'default'
    });

    // Load the main HTML file
    mainWindow.loadFile('main.html');

    // Show window when ready
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        mainWindow.maximize();

        // Show DevTools in development
        if (process.env.NODE_ENV === 'development') {
            mainWindow.webContents.openDevTools();
        }
    });

    mainWindow.on('closed', () => {
        mainWindow = null;
    });

    // Set Electron flag and APIs when ready
    mainWindow.webContents.once('dom-ready', () => {
        mainWindow.webContents.executeJavaScript(`
            window.isElectron = true;
            window.electronAPI = {
                saveFile: (data, filename) => {
                    return new Promise((resolve) => {
                        const { ipcRenderer } = require('electron');
                        ipcRenderer.invoke('save-file', data, filename).then(resolve);
                    });
                },
                openFile: () => {
                    return new Promise((resolve) => {
                        const { ipcRenderer } = require('electron');
                        ipcRenderer.invoke('open-file').then(resolve);
                    });
                },
                showMessageBox: (options) => {
                    return new Promise((resolve) => {
                        const { ipcRenderer } = require('electron');
                        ipcRenderer.invoke('show-message-box', options).then(resolve);
                    });
                }
            };
            console.log('🖥️ Electron app loaded with APIs');
        `);
    });

    // Handle external links
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });
}

// Create application menu
function createMenu() {
    const template = [
        {
            label: 'ملف',
            submenu: [
                {
                    label: 'حفظ',
                    accelerator: 'CmdOrCtrl+S',
                    click: () => {
                        mainWindow.webContents.executeJavaScript('if(typeof saveAllData === "function") saveAllData();');
                    }
                },
                {
                    label: 'تصدير البيانات',
                    click: () => {
                        mainWindow.webContents.executeJavaScript('if(typeof exportDataToFile === "function") exportDataToFile();');
                    }
                },
                {
                    label: 'استيراد البيانات',
                    click: () => {
                        mainWindow.webContents.executeJavaScript('if(typeof importDataFromFile === "function") importDataFromFile();');
                    }
                },
                { type: 'separator' },
                {
                    label: 'طباعة',
                    accelerator: 'CmdOrCtrl+P',
                    click: () => {
                        mainWindow.webContents.print();
                    }
                },
                { type: 'separator' },
                {
                    label: 'خروج',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'تحرير',
            submenu: [
                { label: 'تراجع', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
                { label: 'إعادة', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
                { type: 'separator' },
                { label: 'قص', accelerator: 'CmdOrCtrl+X', role: 'cut' },
                { label: 'نسخ', accelerator: 'CmdOrCtrl+C', role: 'copy' },
                { label: 'لصق', accelerator: 'CmdOrCtrl+V', role: 'paste' }
            ]
        },
        {
            label: 'عرض',
            submenu: [
                { label: 'إعادة تحميل', accelerator: 'CmdOrCtrl+R', role: 'reload' },
                { label: 'أدوات المطور', accelerator: 'F12', role: 'toggleDevTools' },
                { type: 'separator' },
                { label: 'تكبير', accelerator: 'CmdOrCtrl+Plus', role: 'zoomin' },
                { label: 'تصغير', accelerator: 'CmdOrCtrl+-', role: 'zoomout' },
                { label: 'حجم طبيعي', accelerator: 'CmdOrCtrl+0', role: 'resetzoom' },
                { type: 'separator' },
                { label: 'ملء الشاشة', accelerator: 'F11', role: 'togglefullscreen' }
            ]
        },
        {
            label: 'مساعدة',
            submenu: [
                {
                    label: 'حول البرنامج',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'حول البرنامج',
                            message: 'Black Horse POS',
                            detail: 'نظام نقاط البيع والمحاسبة المتكامل\nالإصدار 1.0.0\n\nمطور بواسطة: Ahmed El-Shorbagy\nالبريد الإلكتروني: <EMAIL>\nالهاتف: 01096359521',
                            buttons: ['موافق']
                        });
                    }
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

// IPC handlers for file operations
ipcMain.handle('save-file', async (event, data, filename) => {
    try {
        const result = await dialog.showSaveDialog(mainWindow, {
            title: 'حفظ الملف',
            defaultPath: filename || `black-horse-backup-${new Date().toISOString().split('T')[0]}.json`,
            filters: [
                { name: 'JSON Files', extensions: ['json'] },
                { name: 'All Files', extensions: ['*'] }
            ]
        });

        if (!result.canceled && result.filePath) {
            fs.writeFileSync(result.filePath, data, 'utf8');
            return { success: true, filePath: result.filePath };
        }

        return { success: false, canceled: true };
    } catch (error) {
        return { success: false, error: error.message };
    }
});

ipcMain.handle('open-file', async () => {
    try {
        const result = await dialog.showOpenDialog(mainWindow, {
            title: 'فتح الملف',
            filters: [
                { name: 'JSON Files', extensions: ['json'] },
                { name: 'All Files', extensions: ['*'] }
            ],
            properties: ['openFile']
        });

        if (!result.canceled && result.filePaths.length > 0) {
            const data = fs.readFileSync(result.filePaths[0], 'utf8');
            return { success: true, data, filePath: result.filePaths[0] };
        }

        return { success: false, canceled: true };
    } catch (error) {
        return { success: false, error: error.message };
    }
});

ipcMain.handle('show-message-box', async (event, options) => {
    try {
        const result = await dialog.showMessageBox(mainWindow, options);
        return result;
    } catch (error) {
        return { error: error.message };
    }
});

app.whenReady().then(() => {
    createWindow();
    createMenu();
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// Save data before quitting
app.on('before-quit', () => {
    if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.executeJavaScript('if(typeof saveAllData === "function") saveAllData();');
    }
});

console.log('🐎 Black Horse POS - Desktop Application Started');
