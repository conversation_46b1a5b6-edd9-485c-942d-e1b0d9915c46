/**
 * Black Horse POS - Navigation Fix
 * إصلاح مشاكل التنقل بين الصفحات
 * Developer: Augment Agent
 */

// ===== NAVIGATION SYSTEM FIX =====
const NavigationFix = {
    // قائمة الصفحات المتاحة
    availablePages: [
        'dashboard', 'pos', 'products', 'customers', 'suppliers', 
        'sales', 'reports', 'settings', 'inventory', 'orders',
        'expenses', 'debts', 'analytics', 'accounting', 'branches',
        'maintenance', 'sessions', 'permissions', 'loyalty',
        'appointments', 'subscriptions', 'fashion-reports',
        'purchase-invoices', 'purchase-returns', 'sales-returns'
    ],
    
    // الصفحات المكررة
    duplicatePages: ['users'], // صفحة users مكررة
    
    // الصفحات المفقودة
    missingPages: [],
    
    // تهيئة النظام
    init: function() {
        try {
            console.log('🧭 Initializing Navigation Fix...');
            
            // فحص الصفحات المتاحة
            this.checkAvailablePages();
            
            // إصلاح الصفحات المكررة
            this.fixDuplicatePages();
            
            // إصلاح الروابط المكسورة
            this.fixBrokenLinks();
            
            // إضافة معالجات الأحداث
            this.addEventHandlers();
            
            // تحسين التنقل
            this.enhanceNavigation();
            
            console.log('✅ Navigation system fixed successfully');
        } catch (error) {
            console.error('❌ Error fixing navigation:', error);
        }
    },
    
    // فحص الصفحات المتاحة
    checkAvailablePages: function() {
        try {
            console.log('🔍 Checking available pages...');
            
            this.availablePages.forEach(pageId => {
                const page = document.getElementById(pageId);
                if (!page) {
                    this.missingPages.push(pageId);
                    console.warn(`⚠️ Missing page: ${pageId}`);
                } else {
                    console.log(`✅ Found page: ${pageId}`);
                }
            });
            
            // فحص الصفحات المكررة
            this.duplicatePages.forEach(pageId => {
                const pages = document.querySelectorAll(`#${pageId}`);
                if (pages.length > 1) {
                    console.warn(`⚠️ Duplicate page found: ${pageId} (${pages.length} instances)`);
                }
            });
            
            console.log(`📊 Pages status: ${this.availablePages.length - this.missingPages.length}/${this.availablePages.length} available`);
        } catch (error) {
            console.error('❌ Error checking pages:', error);
        }
    },
    
    // إصلاح الصفحات المكررة
    fixDuplicatePages: function() {
        try {
            console.log('🔧 Fixing duplicate pages...');
            
            this.duplicatePages.forEach(pageId => {
                const pages = document.querySelectorAll(`#${pageId}`);
                if (pages.length > 1) {
                    // الاحتفاظ بالصفحة الأولى وإزالة الباقي
                    for (let i = 1; i < pages.length; i++) {
                        const duplicatePage = pages[i];
                        
                        // تغيير ID للصفحة المكررة
                        const newId = `${pageId}-${i}`;
                        duplicatePage.id = newId;
                        
                        // إخفاء الصفحة المكررة
                        duplicatePage.style.display = 'none';
                        
                        console.log(`🔄 Renamed duplicate page: ${pageId} → ${newId}`);
                    }
                }
            });
            
            console.log('✅ Duplicate pages fixed');
        } catch (error) {
            console.error('❌ Error fixing duplicate pages:', error);
        }
    },
    
    // إصلاح الروابط المكسورة
    fixBrokenLinks: function() {
        try {
            console.log('🔗 Fixing broken navigation links...');
            
            // العثور على جميع روابط التنقل
            const navLinks = document.querySelectorAll('[onclick*="showPage"]');
            
            navLinks.forEach(link => {
                const onclick = link.getAttribute('onclick');
                const pageMatch = onclick.match(/showPage\(['"]([^'"]+)['"]\)/);
                
                if (pageMatch) {
                    const pageId = pageMatch[1];
                    const targetPage = document.getElementById(pageId);
                    
                    if (!targetPage) {
                        // الصفحة غير موجودة - إصلاح الرابط
                        console.warn(`⚠️ Broken link to missing page: ${pageId}`);
                        
                        // محاولة العثور على صفحة بديلة
                        const alternativePage = this.findAlternativePage(pageId);
                        if (alternativePage) {
                            const newOnclick = onclick.replace(pageId, alternativePage);
                            link.setAttribute('onclick', newOnclick);
                            console.log(`🔄 Fixed broken link: ${pageId} → ${alternativePage}`);
                        } else {
                            // تعطيل الرابط المكسور
                            link.style.opacity = '0.5';
                            link.style.pointerEvents = 'none';
                            link.title = 'هذه الصفحة غير متاحة حالياً';
                            console.log(`❌ Disabled broken link: ${pageId}`);
                        }
                    }
                }
            });
            
            console.log('✅ Broken links fixed');
        } catch (error) {
            console.error('❌ Error fixing broken links:', error);
        }
    },
    
    // العثور على صفحة بديلة
    findAlternativePage: function(missingPageId) {
        const alternatives = {
            'fashion-reports': 'reports',
            'analytics': 'reports',
            'accounting': 'reports',
            'branches': 'settings',
            'maintenance': 'settings',
            'sessions': 'settings',
            'permissions': 'users',
            'loyalty': 'customers',
            'appointments': 'customers',
            'subscriptions': 'customers',
            'purchase-invoices': 'orders',
            'purchase-returns': 'orders',
            'sales-returns': 'sales'
        };
        
        return alternatives[missingPageId] || null;
    },
    
    // إضافة معالجات الأحداث
    addEventHandlers: function() {
        try {
            console.log('🎯 Adding navigation event handlers...');
            
            // معالج النقر على الروابط
            document.addEventListener('click', (e) => {
                const link = e.target.closest('[onclick*="showPage"]');
                if (link) {
                    e.preventDefault();
                    
                    const onclick = link.getAttribute('onclick');
                    const pageMatch = onclick.match(/showPage\(['"]([^'"]+)['"]\)/);
                    
                    if (pageMatch) {
                        const pageId = pageMatch[1];
                        this.navigateToPage(pageId);
                    }
                }
            });
            
            // معالج لوحة المفاتيح للتنقل السريع
            document.addEventListener('keydown', (e) => {
                if (e.ctrlKey || e.metaKey) {
                    switch (e.key) {
                        case '1':
                            e.preventDefault();
                            this.navigateToPage('dashboard');
                            break;
                        case '2':
                            e.preventDefault();
                            this.navigateToPage('pos');
                            break;
                        case '3':
                            e.preventDefault();
                            this.navigateToPage('products');
                            break;
                        case '4':
                            e.preventDefault();
                            this.navigateToPage('customers');
                            break;
                    }
                }
            });
            
            console.log('✅ Event handlers added');
        } catch (error) {
            console.error('❌ Error adding event handlers:', error);
        }
    },
    
    // التنقل إلى صفحة
    navigateToPage: function(pageId) {
        try {
            console.log(`🧭 Navigating to page: ${pageId}`);
            
            // التحقق من وجود الصفحة
            const targetPage = document.getElementById(pageId);
            if (!targetPage) {
                console.error(`❌ Page not found: ${pageId}`);
                
                // محاولة العثور على صفحة بديلة
                const alternative = this.findAlternativePage(pageId);
                if (alternative) {
                    console.log(`🔄 Using alternative page: ${alternative}`);
                    return this.navigateToPage(alternative);
                } else {
                    // إظهار رسالة خطأ
                    if (typeof showNotification === 'function') {
                        showNotification(`الصفحة "${pageId}" غير متاحة حالياً`, 'error');
                    }
                    return false;
                }
            }
            
            // إخفاء جميع الصفحات
            document.querySelectorAll('.page').forEach(page => {
                page.style.display = 'none';
                page.classList.remove('active');
            });
            
            // إظهار الصفحة المطلوبة
            targetPage.style.display = 'block';
            targetPage.classList.add('active');
            
            // تحديث التنقل النشط
            this.updateActiveNavigation(pageId);
            
            // تحميل بيانات الصفحة
            if (typeof FunctionConflictsResolver !== 'undefined' && 
                FunctionConflictsResolver.unifiedFunctions.loadPageData) {
                FunctionConflictsResolver.unifiedFunctions.loadPageData(pageId);
            }
            
            // تحديث URL (اختياري)
            if (window.history && window.history.pushState) {
                window.history.pushState({page: pageId}, '', `#${pageId}`);
            }
            
            console.log(`✅ Successfully navigated to: ${pageId}`);
            return true;
        } catch (error) {
            console.error('❌ Error navigating to page:', error);
            return false;
        }
    },
    
    // تحديث التنقل النشط
    updateActiveNavigation: function(pageId) {
        try {
            // إزالة الفئة النشطة من جميع الروابط
            document.querySelectorAll('.nav-link, .dropdown-item').forEach(link => {
                link.classList.remove('active');
            });
            
            // إضافة الفئة النشطة للرابط الحالي
            const activeLink = document.querySelector(`[onclick*="showPage('${pageId}')"]`);
            if (activeLink) {
                activeLink.classList.add('active');
                
                // إذا كان الرابط في قائمة منسدلة، تفعيل القائمة الرئيسية أيضاً
                const parentDropdown = activeLink.closest('.nav-dropdown');
                if (parentDropdown) {
                    const parentLink = parentDropdown.querySelector('.nav-link');
                    if (parentLink) {
                        parentLink.classList.add('active');
                    }
                }
            }
        } catch (error) {
            console.error('❌ Error updating active navigation:', error);
        }
    },
    
    // تحسين التنقل
    enhanceNavigation: function() {
        try {
            console.log('✨ Enhancing navigation...');
            
            // إضافة تأثيرات انتقال سلسة
            const style = document.createElement('style');
            style.textContent = `
                .page {
                    transition: opacity 0.3s ease, transform 0.3s ease;
                    opacity: 0;
                    transform: translateY(10px);
                }
                
                .page.active {
                    opacity: 1;
                    transform: translateY(0);
                }
                
                .nav-link.active {
                    background-color: var(--primary-color, #007bff);
                    color: white;
                }
                
                .dropdown-item.active {
                    background-color: var(--primary-color, #007bff);
                    color: white;
                }
            `;
            document.head.appendChild(style);
            
            // إضافة مؤشر التحميل للصفحات
            this.addLoadingIndicator();
            
            console.log('✅ Navigation enhanced');
        } catch (error) {
            console.error('❌ Error enhancing navigation:', error);
        }
    },
    
    // إضافة مؤشر التحميل
    addLoadingIndicator: function() {
        try {
            const loadingIndicator = document.createElement('div');
            loadingIndicator.id = 'navigation-loading';
            loadingIndicator.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 3px;
                background: linear-gradient(90deg, #007bff, #28a745);
                transform: scaleX(0);
                transform-origin: left;
                transition: transform 0.3s ease;
                z-index: 9999;
                display: none;
            `;
            document.body.appendChild(loadingIndicator);
        } catch (error) {
            console.error('❌ Error adding loading indicator:', error);
        }
    },
    
    // إظهار مؤشر التحميل
    showLoading: function() {
        const indicator = document.getElementById('navigation-loading');
        if (indicator) {
            indicator.style.display = 'block';
            indicator.style.transform = 'scaleX(1)';
            
            setTimeout(() => {
                indicator.style.transform = 'scaleX(0)';
                setTimeout(() => {
                    indicator.style.display = 'none';
                }, 300);
            }, 500);
        }
    }
};

// تهيئة تلقائية عند تحميل الملف
if (typeof window !== 'undefined') {
    window.NavigationFix = NavigationFix;
    
    // تهيئة عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            NavigationFix.init();
        }, 1500); // انتظار حتى تحميل جميع الأنظمة الأخرى
    });
}

console.log('📦 Navigation Fix loaded');
