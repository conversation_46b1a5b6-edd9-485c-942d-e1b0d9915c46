<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Black Horse ERP - نظام إدارة الأعمال الشامل</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="src/assets/css/custom.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --dark-color: #343a40;
            --light-color: #f8f9fa;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        .main-container {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar Styles */
        .sidebar {
            width: 280px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            position: relative;
        }

        .sidebar-header h3 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .sidebar-header .subtitle {
            font-size: 0.9rem;
            opacity: 0.9;
            margin-top: 5px;
        }

        .sidebar-toggle {
            position: absolute;
            top: 15px;
            left: 15px;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background: rgba(255,255,255,0.3);
        }

        .sidebar-menu {
            padding: 0;
            margin: 0;
            list-style: none;
        }

        .menu-item {
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }

        .menu-link {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            color: var(--dark-color);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
        }

        .menu-link:hover {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            transform: translateX(-5px);
        }

        .menu-link.active {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
        }

        .menu-link i {
            width: 20px;
            margin-left: 15px;
            text-align: center;
        }

        .menu-text {
            flex: 1;
            transition: opacity 0.3s ease;
        }

        .sidebar.collapsed .menu-text,
        .sidebar.collapsed .sidebar-header h3,
        .sidebar.collapsed .sidebar-header .subtitle {
            opacity: 0;
            pointer-events: none;
        }

        /* Main Content Styles */
        .main-content {
            flex: 1;
            margin-right: 280px;
            padding: 20px;
            transition: margin-right 0.3s ease;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            min-height: 100vh;
        }

        .main-content.expanded {
            margin-right: 70px;
        }

        .content-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .content-body {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            min-height: 600px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        /* Dashboard Cards */
        .dashboard-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            border: none;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
        }

        .stat-card {
            text-align: center;
            padding: 30px 20px;
        }

        .stat-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--dark-color);
            margin-bottom: 10px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 1.1rem;
        }

        /* Loading Animation */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 400px;
            flex-direction: column;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(102, 126, 234, 0.3);
            border-top: 5px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                transform: translateX(100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
            }
            
            .mobile-menu-toggle {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1001;
                background: var(--primary-color);
                color: white;
                border: none;
                padding: 12px;
                border-radius: 50%;
                box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            }
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(0,0,0,0.1);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Sidebar -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h3>Black Horse</h3>
                <div class="subtitle">نظام إدارة الأعمال</div>
            </div>
            
            <ul class="sidebar-menu">
                <li class="menu-item">
                    <a href="#" class="menu-link active" onclick="loadModule('dashboard')">
                        <i class="fas fa-tachometer-alt"></i>
                        <span class="menu-text">لوحة التحكم</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link" onclick="loadModule('inventory')">
                        <i class="fas fa-boxes"></i>
                        <span class="menu-text">إدارة المخزون</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link" onclick="loadModule('sales')">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="menu-text">المبيعات</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link" onclick="loadModule('pos')">
                        <i class="fas fa-cash-register"></i>
                        <span class="menu-text">نقطة البيع</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link" onclick="loadModule('purchases')">
                        <i class="fas fa-truck"></i>
                        <span class="menu-text">المشتريات</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link" onclick="loadModule('cashier')">
                        <i class="fas fa-user-tie"></i>
                        <span class="menu-text">إدارة الصندوق</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link" onclick="loadModule('hr')">
                        <i class="fas fa-users"></i>
                        <span class="menu-text">الموارد البشرية</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link" onclick="loadModule('reports')">
                        <i class="fas fa-chart-bar"></i>
                        <span class="menu-text">التقارير</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link" onclick="loadModule('advanced-finance')">
                        <i class="fas fa-chart-line"></i>
                        <span class="menu-text">الإدارة المالية المتقدمة</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link" onclick="loadModule('settings')">
                        <i class="fas fa-cog"></i>
                        <span class="menu-text">الإعدادات</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Mobile Menu Toggle -->
        <button class="mobile-menu-toggle d-md-none" onclick="toggleMobileSidebar()">
            <i class="fas fa-bars"></i>
        </button>

        <!-- Main Content -->
        <main class="main-content" id="mainContent">
            <div class="content-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 id="pageTitle">لوحة التحكم الرئيسية</h2>
                        <p class="mb-0 text-muted" id="pageSubtitle">مرحباً بك في نظام Black Horse ERP</p>
                    </div>
                    <div class="d-flex align-items-center gap-3">
                        <div class="text-end">
                            <small class="text-muted d-block">تاريخ اليوم</small>
                            <strong id="currentDate"></strong>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-2"></i>المدير
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#"><i class="fas fa-user-cog me-2"></i>الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="content-body">
                <div id="moduleContainer">
                    <!-- Module content will be loaded here -->
                </div>
            </div>
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Core Scripts -->
    <script src="src/assets/js/error-handler.js"></script>
    <script src="src/core/database.js"></script>
    <script src="src/core/utils.js"></script>
    <script src="src/assets/js/performance.js"></script>
    <script src="src/assets/js/app.js"></script>
    
    <!-- Module Scripts -->
    <script src="src/modules/inventory/inventory.js"></script>
    <script src="src/modules/sales/sales.js"></script>
    <script src="src/modules/pos/pos.js"></script>
    <script src="src/modules/purchases/purchases.js"></script>
    <script src="src/modules/cashier/cashier.js"></script>
    <script src="src/modules/hr/hr.js"></script>
    <script src="src/modules/advanced-finance/advanced-finance.js"></script>

    <!-- Main Application Script -->
    <script>
        let currentModule = 'dashboard';
        let sidebarCollapsed = false;

        // Initialize application
        document.addEventListener('DOMContentLoaded', async function() {
            updateCurrentDate();
            await initializeDatabase();
            loadModule('dashboard');
            
            // Update date every minute
            setInterval(updateCurrentDate, 60000);
        });

        // Update current date display
        function updateCurrentDate() {
            const now = new Date();
            const options = { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            };
            document.getElementById('currentDate').textContent = 
                now.toLocaleDateString('ar-EG', options);
        }

        // Initialize database
        async function initializeDatabase() {
            try {
                if (typeof Database !== 'undefined' && Database.initialize) {
                    await Database.initialize();
                    console.log('Database initialized successfully');
                }
            } catch (error) {
                console.error('Error initializing database:', error);
            }
        }

        // Load script dynamically
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                // Check if script is already loaded
                const existingScript = document.querySelector(`script[src="${src}"]`);
                if (existingScript) {
                    resolve();
                    return;
                }

                const script = document.createElement('script');
                script.src = src;
                script.onload = () => resolve();
                script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
                document.head.appendChild(script);
            });
        }

        // Toggle sidebar
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            
            sidebarCollapsed = !sidebarCollapsed;
            
            if (sidebarCollapsed) {
                sidebar.classList.add('collapsed');
                mainContent.classList.add('expanded');
            } else {
                sidebar.classList.remove('collapsed');
                mainContent.classList.remove('expanded');
            }
        }

        // Toggle mobile sidebar
        function toggleMobileSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }

        // Load module
        async function loadModule(moduleName) {
            try {
                // Update active menu item
                document.querySelectorAll('.menu-link').forEach(link => {
                    link.classList.remove('active');
                });
                event?.target.closest('.menu-link')?.classList.add('active');

                // Show loading
                showLoading();
                
                // Update page title
                updatePageTitle(moduleName);
                
                currentModule = moduleName;

                // Load module content
                switch (moduleName) {
                    case 'dashboard':
                        await loadDashboard();
                        break;
                    case 'inventory':
                        if (typeof Inventory !== 'undefined') {
                            await Inventory.initialize();
                            await Inventory.show();
                        }
                        break;
                    case 'sales':
                        if (typeof Sales !== 'undefined') {
                            await Sales.initialize();
                            await Sales.show();
                        }
                        break;
                    case 'pos':
                        if (typeof POS !== 'undefined') {
                            await POS.initialize();
                            await POS.show();
                        }
                        break;
                    case 'purchases':
                        if (typeof Purchases !== 'undefined') {
                            await Purchases.initialize();
                            await Purchases.show();
                        }
                        break;
                    case 'cashier':
                        if (typeof Cashier !== 'undefined') {
                            await Cashier.initialize();
                            await Cashier.show();
                        }
                        break;
                    case 'hr':
                        if (typeof HR !== 'undefined') {
                            await HR.initialize();
                            await HR.show();
                        }
                        break;
                    case 'reports':
                        await loadReports();
                        break;
                    case 'advanced-finance':
                        await loadAdvancedFinance();
                        break;
                    case 'settings':
                        await loadSettings();
                        break;
                    default:
                        await loadDashboard();
                }

                // Hide mobile sidebar after selection
                if (window.innerWidth < 768) {
                    document.getElementById('sidebar').classList.remove('show');
                }

            } catch (error) {
                console.error('Error loading module:', error);
                showError('حدث خطأ أثناء تحميل الوحدة');
            }
        }

        // Update page title
        function updatePageTitle(moduleName) {
            const titles = {
                'dashboard': 'لوحة التحكم الرئيسية',
                'inventory': 'إدارة المخزون',
                'sales': 'إدارة المبيعات',
                'pos': 'نقطة البيع',
                'purchases': 'إدارة المشتريات',
                'cashier': 'إدارة الصندوق',
                'hr': 'الموارد البشرية',
                'reports': 'التقارير والإحصائيات',
                'advanced-finance': 'الإدارة المالية المتقدمة',
                'settings': 'إعدادات النظام'
            };

            const subtitles = {
                'dashboard': 'نظرة عامة على أداء الأعمال',
                'inventory': 'إدارة المنتجات والمخزون',
                'sales': 'إدارة المبيعات والعملاء',
                'pos': 'نقطة البيع السريعة',
                'purchases': 'إدارة المشتريات والموردين',
                'cashier': 'إدارة الصناديق والمعاملات',
                'hr': 'إدارة الموظفين والموارد البشرية',
                'reports': 'تقارير وتحليلات الأعمال',
                'advanced-finance': 'إدارة مالية شاملة على مستوى المؤسسات',
                'settings': 'إعدادات وتكوين النظام'
            };

            document.getElementById('pageTitle').textContent = titles[moduleName] || 'Black Horse ERP';
            document.getElementById('pageSubtitle').textContent = subtitles[moduleName] || 'نظام إدارة الأعمال الشامل';
        }

        // Show loading
        function showLoading() {
            document.getElementById('moduleContainer').innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <h5>جاري التحميل...</h5>
                    <p class="text-muted">يرجى الانتظار</p>
                </div>
            `;
        }

        // Show error
        function showError(message) {
            document.getElementById('moduleContainer').innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h4>حدث خطأ</h4>
                    <p class="text-muted">${message}</p>
                    <button class="btn btn-primary" onclick="loadModule('dashboard')">
                        <i class="fas fa-home me-2"></i>العودة للرئيسية
                    </button>
                </div>
            `;
        }

        // Load dashboard
        async function loadDashboard() {
            const container = document.getElementById('moduleContainer');

            container.innerHTML = `
                <div class="p-4">
                    <!-- Quick Stats -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="dashboard-card stat-card">
                                <i class="fas fa-boxes stat-icon"></i>
                                <div class="stat-number" id="totalProducts">0</div>
                                <div class="stat-label">إجمالي المنتجات</div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="dashboard-card stat-card">
                                <i class="fas fa-shopping-cart stat-icon"></i>
                                <div class="stat-number" id="todaySales">0</div>
                                <div class="stat-label">مبيعات اليوم</div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="dashboard-card stat-card">
                                <i class="fas fa-users stat-icon"></i>
                                <div class="stat-number" id="totalEmployees">0</div>
                                <div class="stat-label">عدد الموظفين</div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="dashboard-card stat-card">
                                <i class="fas fa-money-bill-wave stat-icon"></i>
                                <div class="stat-number" id="monthlyRevenue">0</div>
                                <div class="stat-label">إيرادات الشهر</div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="dashboard-card">
                                <h5 class="mb-3">
                                    <i class="fas fa-bolt me-2"></i>الإجراءات السريعة
                                </h5>
                                <div class="row">
                                    <div class="col-md-2 col-6 mb-3">
                                        <button class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3" onclick="loadModule('pos')">
                                            <i class="fas fa-cash-register fa-2x mb-2"></i>
                                            <span>بيع سريع</span>
                                        </button>
                                    </div>
                                    <div class="col-md-2 col-6 mb-3">
                                        <button class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3" onclick="loadModule('inventory')">
                                            <i class="fas fa-plus fa-2x mb-2"></i>
                                            <span>إضافة منتج</span>
                                        </button>
                                    </div>
                                    <div class="col-md-2 col-6 mb-3">
                                        <button class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3" onclick="loadModule('sales')">
                                            <i class="fas fa-file-invoice fa-2x mb-2"></i>
                                            <span>فاتورة جديدة</span>
                                        </button>
                                    </div>
                                    <div class="col-md-2 col-6 mb-3">
                                        <button class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3" onclick="loadModule('purchases')">
                                            <i class="fas fa-truck fa-2x mb-2"></i>
                                            <span>أمر شراء</span>
                                        </button>
                                    </div>
                                    <div class="col-md-2 col-6 mb-3">
                                        <button class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3" onclick="loadModule('hr')">
                                            <i class="fas fa-user-plus fa-2x mb-2"></i>
                                            <span>موظف جديد</span>
                                        </button>
                                    </div>
                                    <div class="col-md-2 col-6 mb-3">
                                        <button class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3" onclick="loadModule('reports')">
                                            <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                            <span>التقارير</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <h6 class="mb-3 text-muted">
                                            <i class="fas fa-star me-2"></i>الأنظمة المتقدمة
                                        </h6>
                                    </div>
                                    <div class="col-md-3 col-6 mb-3">
                                        <button class="btn btn-gradient-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3" onclick="loadModule('advanced-finance')" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; color: white;">
                                            <i class="fas fa-chart-line fa-2x mb-2"></i>
                                            <span>الإدارة المالية المتقدمة</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activities -->
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="dashboard-card">
                                <h5 class="mb-3">
                                    <i class="fas fa-clock me-2"></i>الأنشطة الأخيرة
                                </h5>
                                <div id="recentActivities">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="bg-success rounded-circle p-2 me-3">
                                            <i class="fas fa-shopping-cart text-white"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold">فاتورة مبيعات جديدة</div>
                                            <small class="text-muted">منذ 5 دقائق</small>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="bg-info rounded-circle p-2 me-3">
                                            <i class="fas fa-box text-white"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold">تم إضافة منتج جديد</div>
                                            <small class="text-muted">منذ 15 دقيقة</small>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <div class="bg-warning rounded-circle p-2 me-3">
                                            <i class="fas fa-exclamation-triangle text-white"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold">تنبيه مخزون منخفض</div>
                                            <small class="text-muted">منذ ساعة</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="dashboard-card">
                                <h5 class="mb-3">
                                    <i class="fas fa-chart-line me-2"></i>إحصائيات سريعة
                                </h5>
                                <div class="row text-center">
                                    <div class="col-6 mb-3">
                                        <div class="border-end">
                                            <h4 class="text-success mb-1">85%</h4>
                                            <small class="text-muted">معدل الحضور</small>
                                        </div>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <h4 class="text-info mb-1">12</h4>
                                        <small class="text-muted">منتجات نفدت</small>
                                    </div>
                                    <div class="col-6">
                                        <div class="border-end">
                                            <h4 class="text-warning mb-1">45</h4>
                                            <small class="text-muted">فواتير معلقة</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <h4 class="text-primary mb-1">156</h4>
                                        <small class="text-muted">عملاء جدد</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Load dashboard statistics
            await loadDashboardStats();
        }

        // Load dashboard statistics
        async function loadDashboardStats() {
            try {
                // This would typically load real data from the database
                document.getElementById('totalProducts').textContent = '1,234';
                document.getElementById('todaySales').textContent = '45,678 ج.م';
                document.getElementById('totalEmployees').textContent = '25';
                document.getElementById('monthlyRevenue').textContent = '1.2M ج.م';
            } catch (error) {
                console.error('Error loading dashboard stats:', error);
            }
        }

        // Load reports module
        async function loadReports() {
            try {
                showLoading();
                updatePageTitle('reports');

                // Load reports script if not already loaded
                if (!window.Reports) {
                    await loadScript('src/modules/reports/reports.js');
                }

                // Initialize and show reports module
                if (window.Reports) {
                    await window.Reports.initialize();
                    await window.Reports.show();
                } else {
                    throw new Error('Reports module not found');
                }

            } catch (error) {
                console.error('Error loading reports module:', error);
                showError('حدث خطأ أثناء تحميل وحدة التقارير');
            }
        }

        // Load advanced finance module
        async function loadAdvancedFinance() {
            try {
                showLoading();
                updatePageTitle('advanced-finance');

                // Load the advanced finance HTML file
                const response = await fetch('src/modules/advanced-finance/advanced-finance.html');
                if (!response.ok) {
                    throw new Error('Failed to load advanced finance module');
                }

                const htmlContent = await response.text();

                // Extract the body content (everything between <body> and </body>)
                const bodyMatch = htmlContent.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
                if (bodyMatch) {
                    document.getElementById('moduleContainer').innerHTML = bodyMatch[1];
                } else {
                    // If no body tags found, use the entire content
                    document.getElementById('moduleContainer').innerHTML = htmlContent;
                }

                // Load and execute the JavaScript from the HTML file
                const scriptMatches = htmlContent.match(/<script[^>]*>([\s\S]*?)<\/script>/gi);
                if (scriptMatches) {
                    scriptMatches.forEach(scriptTag => {
                        const scriptContent = scriptTag.replace(/<script[^>]*>|<\/script>/gi, '');
                        if (scriptContent.trim()) {
                            try {
                                eval(scriptContent);
                            } catch (error) {
                                console.error('Error executing script:', error);
                            }
                        }
                    });
                }

                // Initialize the advanced finance module if available
                if (typeof initializeAdvancedFinance === 'function') {
                    await initializeAdvancedFinance();
                }

            } catch (error) {
                console.error('Error loading advanced finance module:', error);
                showError('حدث خطأ أثناء تحميل وحدة الإدارة المالية المتقدمة');
            }
        }

        // Load settings module
        async function loadSettings() {
            try {
                showLoading();
                updatePageTitle('settings');

                // Load settings script if not already loaded
                if (!window.Settings) {
                    await loadScript('src/modules/settings/settings.js');
                }

                // Initialize and show settings module
                if (window.Settings) {
                    await window.Settings.initialize();
                    await window.Settings.show();
                } else {
                    throw new Error('Settings module not found');
                }

            } catch (error) {
                console.error('Error loading settings module:', error);
                showError('حدث خطأ أثناء تحميل وحدة الإعدادات');
            }
        }

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 768) {
                document.getElementById('sidebar').classList.remove('show');
            }
        });

        // Handle clicks outside sidebar on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const mobileToggle = document.querySelector('.mobile-menu-toggle');

            if (window.innerWidth < 768 &&
                !sidebar.contains(event.target) &&
                !mobileToggle.contains(event.target)) {
                sidebar.classList.remove('show');
            }
        });
    </script>
</body>
</html>
