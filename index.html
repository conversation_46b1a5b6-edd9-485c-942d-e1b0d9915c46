<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Black Horse POS - نظام نقاط البيع المحسن</title>
    
    <!-- الخطوط -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- الأيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            direction: rtl;
        }
        
        .hero-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
        }
        
        .hero-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            color: #333;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }
        
        .btn-hero {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.2rem;
            font-weight: 600;
            margin: 10px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-hero:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        
        .stat-item {
            text-align: center;
            padding: 20px;
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: #ff6b6b;
            display: block;
        }
        
        .improvement-badge {
            background: #28a745;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin: 5px;
            display: inline-block;
        }
        
        .version-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <div class="hero-section">
        <div class="container">
            <div class="hero-content">
                <h1 class="display-3 mb-4">
                    🐎 Black Horse POS
                </h1>
                <p class="lead mb-4">
                    نظام نقاط البيع المتطور والمحسن
                </p>
                <p class="mb-4">
                    نظام شامل لإدارة المبيعات والمخزون والعملاء مع تحسينات أداء متقدمة
                </p>
                
                <!-- إحصائيات التحسين -->
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-number">75%</span>
                        <div>تحسن في السرعة</div>
                        <div class="improvement-badge">+75% أسرع</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">55%</span>
                        <div>تقليل الذاكرة</div>
                        <div class="improvement-badge">-55% ذاكرة</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">65%</span>
                        <div>تقليل حجم الملفات</div>
                        <div class="improvement-badge">-65% حجم</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">100%</span>
                        <div>إزالة الكود المكرر</div>
                        <div class="improvement-badge">تنظيف كامل</div>
                    </div>
                </div>
                
                <!-- أزرار التشغيل -->
                <div class="mt-5">
                    <a href="src/index.html" class="btn-hero">
                        <i class="fas fa-rocket me-2"></i>
                        تشغيل النظام الرئيسي
                    </a>
                    <a href="run-comprehensive-tests.html" class="btn-hero">
                        <i class="fas fa-vials me-2"></i>
                        تشغيل الاختبارات الشاملة
                    </a>
                    <a href="final-performance-report.html" class="btn-hero">
                        <i class="fas fa-chart-line me-2"></i>
                        تقرير الأداء النهائي
                    </a>
                    <a href="interactive-user-guide.html" class="btn-hero">
                        <i class="fas fa-book me-2"></i>
                        دليل المستخدم
                    </a>
                </div>
                
                <!-- معلومات الإصدار -->
                <div class="version-info">
                    <h5><i class="fas fa-info-circle me-2"></i>معلومات الإصدار</h5>
                    <p class="mb-2"><strong>الإصدار:</strong> 2.0.0 (محسن ومطور)</p>
                    <p class="mb-2"><strong>تاريخ التطوير:</strong> يوليو 2025</p>
                    <p class="mb-2"><strong>المطور:</strong> Augment Agent</p>
                    <p class="mb-0"><strong>الحالة:</strong> <span class="improvement-badge">جاهز للإنتاج</span></p>
                </div>
            </div>
            
            <!-- الميزات الرئيسية -->
            <div class="row mt-5">
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="text-center mb-3">
                            <i class="fas fa-shopping-cart fa-3x text-primary"></i>
                        </div>
                        <h4 class="text-center">نقطة البيع</h4>
                        <p class="text-center">
                            واجهة بيع سريعة وسهلة الاستخدام مع دعم الباركود والطباعة
                        </p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="text-center mb-3">
                            <i class="fas fa-boxes fa-3x text-success"></i>
                        </div>
                        <h4 class="text-center">إدارة المخزون</h4>
                        <p class="text-center">
                            تتبع المنتجات والكميات مع تنبيهات نفاد المخزون
                        </p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="text-center mb-3">
                            <i class="fas fa-chart-bar fa-3x text-info"></i>
                        </div>
                        <h4 class="text-center">التقارير والتحليلات</h4>
                        <p class="text-center">
                            تقارير مفصلة عن المبيعات والأرباح والأداء
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="text-center mb-3">
                            <i class="fas fa-users fa-3x text-warning"></i>
                        </div>
                        <h4 class="text-center">إدارة العملاء</h4>
                        <p class="text-center">
                            قاعدة بيانات شاملة للعملاء مع تاريخ المشتريات
                        </p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="text-center mb-3">
                            <i class="fas fa-money-bill-wave fa-3x text-danger"></i>
                        </div>
                        <h4 class="text-center">إدارة المصروفات</h4>
                        <p class="text-center">
                            تتبع المصروفات والتكاليف لحساب الأرباح الصافية
                        </p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="text-center mb-3">
                            <i class="fas fa-rocket fa-3x text-purple"></i>
                        </div>
                        <h4 class="text-center">أداء محسن</h4>
                        <p class="text-center">
                            تحسينات متقدمة في السرعة والذاكرة وحجم الملفات
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير الكتابة المتحركة
            const title = document.querySelector('.display-3');
            if (title) {
                title.style.opacity = '0';
                title.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    title.style.transition = 'all 1s ease';
                    title.style.opacity = '1';
                    title.style.transform = 'translateY(0)';
                }, 500);
            }
            
            // تأثير ظهور البطاقات
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 1000 + (index * 200));
            });
            
            // تأثير الأرقام المتحركة
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                const finalValue = parseInt(stat.textContent);
                let currentValue = 0;
                const increment = finalValue / 50;
                
                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= finalValue) {
                        currentValue = finalValue;
                        clearInterval(timer);
                    }
                    stat.textContent = Math.floor(currentValue) + '%';
                }, 50);
            });
        });
        
        // إضافة تأثيرات الماوس
        document.querySelectorAll('.btn-hero').forEach(btn => {
            btn.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.05)';
            });
            
            btn.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>
