/**
 * نظام الإشعارات المتقدم
 * Black Horse ERP System
 */

class NotificationManager {
    constructor() {
        this.notifications = [];
        this.settings = {
            enabled: true,
            sound: true,
            desktop: true,
            email: false,
            sms: false,
            whatsapp: false,
            autoMarkRead: true,
            maxNotifications: 100
        };
        
        this.templates = new Map();
        this.channels = new Map();
        this.subscribers = new Map();
        
        this.initializeNotifications();
        this.setupTemplates();
        this.loadSettings();
        this.requestPermissions();
    }

    // تهيئة نظام الإشعارات
    initializeNotifications() {
        console.log('🔔 تهيئة نظام الإشعارات...');
        
        // إعداد قنوات الإشعارات
        this.setupChannels();
        
        // تحميل الإشعارات المحفوظة
        this.loadNotifications();
        
        // إعداد المستمعين
        this.setupEventListeners();
        
        console.log('✅ تم تهيئة نظام الإشعارات');
    }

    // إعداد قنوات الإشعارات
    setupChannels() {
        // قناة المبيعات
        this.channels.set('sales', {
            name: 'المبيعات',
            icon: 'fas fa-shopping-cart',
            color: '#28a745',
            enabled: true,
            priority: 'high'
        });

        // قناة المخزون
        this.channels.set('inventory', {
            name: 'المخزون',
            icon: 'fas fa-boxes',
            color: '#ffc107',
            enabled: true,
            priority: 'medium'
        });

        // قناة الموارد البشرية
        this.channels.set('hr', {
            name: 'الموارد البشرية',
            icon: 'fas fa-users',
            color: '#17a2b8',
            enabled: true,
            priority: 'medium'
        });

        // قناة النظام
        this.channels.set('system', {
            name: 'النظام',
            icon: 'fas fa-cog',
            color: '#6c757d',
            enabled: true,
            priority: 'low'
        });

        // قناة الأمان
        this.channels.set('security', {
            name: 'الأمان',
            icon: 'fas fa-shield-alt',
            color: '#dc3545',
            enabled: true,
            priority: 'critical'
        });
    }

    // إعداد قوالب الإشعارات
    setupTemplates() {
        // قالب مبيعة جديدة
        this.templates.set('new_sale', {
            title: 'مبيعة جديدة',
            body: 'تم إتمام مبيعة جديدة بقيمة {amount} ج.م للعميل {customer}',
            icon: 'fas fa-shopping-cart',
            channel: 'sales',
            priority: 'high',
            actions: [
                { label: 'عرض التفاصيل', action: 'viewSale' },
                { label: 'طباعة الفاتورة', action: 'printInvoice' }
            ]
        });

        // قالب نفاد المخزون
        this.templates.set('low_stock', {
            title: 'تحذير مخزون منخفض',
            body: 'المنتج {product} أوشك على النفاد. الكمية المتبقية: {quantity}',
            icon: 'fas fa-exclamation-triangle',
            channel: 'inventory',
            priority: 'medium',
            actions: [
                { label: 'إضافة مخزون', action: 'addStock' },
                { label: 'طلب شراء', action: 'createPurchaseOrder' }
            ]
        });

        // قالب حضور الموظف
        this.templates.set('employee_attendance', {
            title: 'حضور موظف',
            body: 'الموظف {employee} سجل {action} في {time}',
            icon: 'fas fa-clock',
            channel: 'hr',
            priority: 'low',
            actions: [
                { label: 'عرض التفاصيل', action: 'viewAttendance' }
            ]
        });

        // قالب خطأ في النظام
        this.templates.set('system_error', {
            title: 'خطأ في النظام',
            body: 'حدث خطأ في {module}: {error}',
            icon: 'fas fa-exclamation-circle',
            channel: 'system',
            priority: 'high',
            actions: [
                { label: 'عرض التفاصيل', action: 'viewError' },
                { label: 'إرسال تقرير', action: 'sendReport' }
            ]
        });

        // قالب تسجيل دخول مشبوه
        this.templates.set('suspicious_login', {
            title: 'تسجيل دخول مشبوه',
            body: 'محاولة تسجيل دخول من موقع غير معتاد: {location}',
            icon: 'fas fa-shield-alt',
            channel: 'security',
            priority: 'critical',
            actions: [
                { label: 'حظر IP', action: 'blockIP' },
                { label: 'تغيير كلمة المرور', action: 'changePassword' }
            ]
        });
    }

    // إرسال إشعار
    async send(templateId, data = {}, options = {}) {
        try {
            const template = this.templates.get(templateId);
            if (!template) {
                throw new Error(`قالب الإشعار ${templateId} غير موجود`);
            }

            const channel = this.channels.get(template.channel);
            if (!channel || !channel.enabled) {
                console.log(`قناة ${template.channel} معطلة`);
                return;
            }

            // إنشاء الإشعار
            const notification = {
                id: Date.now() + Math.random(),
                templateId,
                title: this.processTemplate(template.title, data),
                body: this.processTemplate(template.body, data),
                icon: template.icon,
                channel: template.channel,
                priority: template.priority,
                timestamp: new Date().toISOString(),
                read: false,
                data: data,
                actions: template.actions || [],
                ...options
            };

            // إضافة للقائمة
            this.notifications.unshift(notification);

            // تحديد الحد الأقصى للإشعارات
            if (this.notifications.length > this.settings.maxNotifications) {
                this.notifications = this.notifications.slice(0, this.settings.maxNotifications);
            }

            // حفظ الإشعارات
            this.saveNotifications();

            // إرسال عبر القنوات المختلفة
            await this.deliverNotification(notification);

            // تحديث العداد
            this.updateNotificationCount();

            // إطلاق حدث
            this.emit('notificationSent', notification);

            console.log('✅ تم إرسال الإشعار:', notification.title);
            return notification;

        } catch (error) {
            console.error('خطأ في إرسال الإشعار:', error);
            throw error;
        }
    }

    // معالجة قالب النص
    processTemplate(template, data) {
        let processed = template;
        
        for (const [key, value] of Object.entries(data)) {
            const placeholder = `{${key}}`;
            processed = processed.replace(new RegExp(placeholder, 'g'), value);
        }
        
        return processed;
    }

    // توصيل الإشعار عبر القنوات
    async deliverNotification(notification) {
        const promises = [];

        // إشعار في المتصفح
        if (this.settings.desktop && 'Notification' in window && Notification.permission === 'granted') {
            promises.push(this.sendDesktopNotification(notification));
        }

        // صوت الإشعار
        if (this.settings.sound) {
            promises.push(this.playNotificationSound(notification));
        }

        // إشعار في الواجهة
        promises.push(this.showInAppNotification(notification));

        // إشعار عبر البريد الإلكتروني
        if (this.settings.email) {
            promises.push(this.sendEmailNotification(notification));
        }

        // إشعار عبر SMS
        if (this.settings.sms) {
            promises.push(this.sendSMSNotification(notification));
        }

        // إشعار عبر واتساب
        if (this.settings.whatsapp) {
            promises.push(this.sendWhatsAppNotification(notification));
        }

        await Promise.allSettled(promises);
    }

    // إشعار سطح المكتب
    async sendDesktopNotification(notification) {
        try {
            const desktopNotification = new Notification(notification.title, {
                body: notification.body,
                icon: '/assets/images/logo.png',
                badge: '/assets/images/badge.png',
                tag: notification.id,
                requireInteraction: notification.priority === 'critical'
            });

            desktopNotification.onclick = () => {
                window.focus();
                this.handleNotificationClick(notification);
                desktopNotification.close();
            };

            // إغلاق تلقائي بعد 5 ثوان (إلا إذا كان حرج)
            if (notification.priority !== 'critical') {
                setTimeout(() => {
                    desktopNotification.close();
                }, 5000);
            }

        } catch (error) {
            console.error('خطأ في إشعار سطح المكتب:', error);
        }
    }

    // تشغيل صوت الإشعار
    async playNotificationSound(notification) {
        try {
            const audio = new Audio();
            
            // اختيار الصوت حسب الأولوية
            switch (notification.priority) {
                case 'critical':
                    audio.src = '/assets/sounds/critical.mp3';
                    break;
                case 'high':
                    audio.src = '/assets/sounds/high.mp3';
                    break;
                case 'medium':
                    audio.src = '/assets/sounds/medium.mp3';
                    break;
                default:
                    audio.src = '/assets/sounds/default.mp3';
            }
            
            audio.volume = 0.5;
            await audio.play();
            
        } catch (error) {
            console.warn('لا يمكن تشغيل صوت الإشعار:', error);
        }
    }

    // إشعار في التطبيق
    async showInAppNotification(notification) {
        const container = this.getNotificationContainer();
        const element = this.createNotificationElement(notification);
        
        container.appendChild(element);
        
        // تأثير الظهور
        setTimeout(() => {
            element.classList.add('show');
        }, 100);
        
        // إزالة تلقائية
        if (notification.priority !== 'critical') {
            setTimeout(() => {
                this.removeNotificationElement(element);
            }, 5000);
        }
    }

    // إنشاء عنصر الإشعار
    createNotificationElement(notification) {
        const element = document.createElement('div');
        element.className = `notification-toast notification-${notification.priority}`;
        element.dataset.notificationId = notification.id;
        
        const channel = this.channels.get(notification.channel);
        
        element.innerHTML = `
            <div class="notification-content">
                <div class="notification-header">
                    <i class="${notification.icon}" style="color: ${channel.color}"></i>
                    <strong>${notification.title}</strong>
                    <button type="button" class="btn-close" onclick="notificationManager.dismissNotification('${notification.id}')"></button>
                </div>
                <div class="notification-body">
                    ${notification.body}
                </div>
                ${notification.actions.length > 0 ? `
                    <div class="notification-actions">
                        ${notification.actions.map(action => `
                            <button class="btn btn-sm btn-outline-primary" onclick="notificationManager.handleAction('${notification.id}', '${action.action}')">
                                ${action.label}
                            </button>
                        `).join('')}
                    </div>
                ` : ''}
                <div class="notification-time">
                    ${new Date(notification.timestamp).toLocaleTimeString('ar-EG')}
                </div>
            </div>
        `;
        
        return element;
    }

    // الحصول على حاوي الإشعارات
    getNotificationContainer() {
        let container = document.getElementById('notification-container');
        
        if (!container) {
            container = document.createElement('div');
            container.id = 'notification-container';
            container.className = 'notification-container';
            document.body.appendChild(container);
        }
        
        return container;
    }

    // إزالة عنصر الإشعار
    removeNotificationElement(element) {
        element.classList.add('hide');
        setTimeout(() => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
        }, 300);
    }

    // رفض الإشعار
    dismissNotification(notificationId) {
        // إزالة من الواجهة
        const element = document.querySelector(`[data-notification-id="${notificationId}"]`);
        if (element) {
            this.removeNotificationElement(element);
        }
        
        // تحديث حالة القراءة
        this.markAsRead(notificationId);
    }

    // تحديد كمقروء
    markAsRead(notificationId) {
        const notification = this.notifications.find(n => n.id == notificationId);
        if (notification && !notification.read) {
            notification.read = true;
            this.saveNotifications();
            this.updateNotificationCount();
            this.emit('notificationRead', notification);
        }
    }

    // تحديد الكل كمقروء
    markAllAsRead() {
        let updated = 0;
        this.notifications.forEach(notification => {
            if (!notification.read) {
                notification.read = true;
                updated++;
            }
        });
        
        if (updated > 0) {
            this.saveNotifications();
            this.updateNotificationCount();
            this.emit('allNotificationsRead', updated);
        }
        
        return updated;
    }

    // معالجة النقر على الإشعار
    handleNotificationClick(notification) {
        this.markAsRead(notification.id);
        this.emit('notificationClicked', notification);
        
        // تنفيذ الإجراء الافتراضي إذا وجد
        if (notification.actions.length > 0) {
            this.handleAction(notification.id, notification.actions[0].action);
        }
    }

    // معالجة إجراء الإشعار
    handleAction(notificationId, action) {
        const notification = this.notifications.find(n => n.id == notificationId);
        if (!notification) return;
        
        this.markAsRead(notificationId);
        this.emit('notificationAction', { notification, action });
        
        // تنفيذ الإجراءات المحددة مسبقاً
        switch (action) {
            case 'viewSale':
                // فتح صفحة المبيعات
                if (window.loadModule) {
                    window.loadModule('sales');
                }
                break;
                
            case 'addStock':
                // فتح صفحة إضافة مخزون
                if (window.loadModule) {
                    window.loadModule('inventory');
                }
                break;
                
            case 'viewAttendance':
                // فتح صفحة الحضور
                if (window.loadModule) {
                    window.loadModule('hr');
                }
                break;
                
            case 'viewError':
                // عرض تفاصيل الخطأ
                console.log('تفاصيل الخطأ:', notification.data);
                break;
                
            default:
                console.log('إجراء غير معروف:', action);
        }
    }

    // طلب أذونات الإشعارات
    async requestPermissions() {
        if ('Notification' in window && Notification.permission === 'default') {
            try {
                const permission = await Notification.requestPermission();
                console.log('إذن الإشعارات:', permission);
            } catch (error) {
                console.error('خطأ في طلب إذن الإشعارات:', error);
            }
        }
    }

    // تحديث عداد الإشعارات
    updateNotificationCount() {
        const unreadCount = this.getUnreadCount();
        
        // تحديث شارة الإشعارات
        const badge = document.querySelector('.notification-badge');
        if (badge) {
            badge.textContent = unreadCount;
            badge.style.display = unreadCount > 0 ? 'inline' : 'none';
        }
        
        // تحديث عنوان الصفحة
        if (unreadCount > 0) {
            document.title = `(${unreadCount}) Black Horse ERP`;
        } else {
            document.title = 'Black Horse ERP';
        }
    }

    // الحصول على عدد الإشعارات غير المقروءة
    getUnreadCount() {
        return this.notifications.filter(n => !n.read).length;
    }

    // الحصول على الإشعارات حسب القناة
    getByChannel(channel) {
        return this.notifications.filter(n => n.channel === channel);
    }

    // الحصول على الإشعارات حسب الأولوية
    getByPriority(priority) {
        return this.notifications.filter(n => n.priority === priority);
    }

    // حذف إشعار
    delete(notificationId) {
        const index = this.notifications.findIndex(n => n.id == notificationId);
        if (index !== -1) {
            const notification = this.notifications.splice(index, 1)[0];
            this.saveNotifications();
            this.updateNotificationCount();
            this.emit('notificationDeleted', notification);
            return true;
        }
        return false;
    }

    // حذف جميع الإشعارات
    deleteAll() {
        const count = this.notifications.length;
        this.notifications = [];
        this.saveNotifications();
        this.updateNotificationCount();
        this.emit('allNotificationsDeleted', count);
        return count;
    }

    // حفظ الإشعارات
    saveNotifications() {
        try {
            localStorage.setItem('notifications', JSON.stringify(this.notifications));
        } catch (error) {
            console.error('خطأ في حفظ الإشعارات:', error);
        }
    }

    // تحميل الإشعارات
    loadNotifications() {
        try {
            const saved = localStorage.getItem('notifications');
            if (saved) {
                this.notifications = JSON.parse(saved);
                this.updateNotificationCount();
            }
        } catch (error) {
            console.error('خطأ في تحميل الإشعارات:', error);
            this.notifications = [];
        }
    }

    // حفظ الإعدادات
    saveSettings() {
        try {
            localStorage.setItem('notification_settings', JSON.stringify(this.settings));
        } catch (error) {
            console.error('خطأ في حفظ إعدادات الإشعارات:', error);
        }
    }

    // تحميل الإعدادات
    loadSettings() {
        try {
            const saved = localStorage.getItem('notification_settings');
            if (saved) {
                this.settings = { ...this.settings, ...JSON.parse(saved) };
            }
        } catch (error) {
            console.error('خطأ في تحميل إعدادات الإشعارات:', error);
        }
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        this.eventListeners = {};
    }

    // إضافة مستمع حدث
    on(event, callback) {
        if (!this.eventListeners[event]) {
            this.eventListeners[event] = [];
        }
        this.eventListeners[event].push(callback);
    }

    // إزالة مستمع حدث
    off(event, callback) {
        if (this.eventListeners[event]) {
            const index = this.eventListeners[event].indexOf(callback);
            if (index !== -1) {
                this.eventListeners[event].splice(index, 1);
            }
        }
    }

    // إطلاق حدث
    emit(event, data) {
        if (this.eventListeners[event]) {
            this.eventListeners[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error('خطأ في معالج الحدث:', error);
                }
            });
        }
    }
}

// تصدير الكلاس
window.NotificationManager = NotificationManager;
