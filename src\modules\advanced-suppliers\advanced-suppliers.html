<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الموردين المتقدمة - Black Horse ERP</title>
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }

        .main-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
            margin: 0 auto;
            max-width: 1400px;
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255,255,255,0.05) 10px,
                rgba(255,255,255,0.05) 20px
            );
            animation: movePattern 20s linear infinite;
        }

        @keyframes movePattern {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        .page-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .page-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        /* إحصائيات سريعة */
        .stats-section {
            padding: 30px;
            background: #f8f9fa;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 1.5rem;
            color: white;
        }

        .stat-icon.suppliers { background: linear-gradient(135deg, #3498db, #2980b9); }
        .stat-icon.contracts { background: linear-gradient(135deg, #27ae60, #229954); }
        .stat-icon.evaluations { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .stat-icon.payments { background: linear-gradient(135deg, #e74c3c, #c0392b); }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-weight: 500;
        }

        /* تبويبات العرض */
        .view-tabs {
            display: flex;
            background: white;
            border-radius: 15px;
            padding: 10px;
            margin: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            overflow-x: auto;
        }

        .view-tab {
            flex: 1;
            padding: 15px 20px;
            border: none;
            background: transparent;
            border-radius: 10px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            min-width: 120px;
        }

        .view-tab:hover {
            background: rgba(44, 62, 80, 0.1);
        }

        .view-tab.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 3px 10px rgba(44, 62, 80, 0.3);
        }

        .view-tab i {
            margin-left: 8px;
        }

        /* محتوى العرض */
        .view-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 0 30px 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        /* لوحة الفلاتر */
        .filters-panel {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
        }

        .filters-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--primary-color);
        }

        .form-control, .form-select {
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
            outline: none;
        }

        /* الإجراءات السريعة */
        .quick-actions {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
            flex-wrap: wrap;
        }

        .quick-action-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(44, 62, 80, 0.3);
        }

        /* شبكة الموردين */
        .suppliers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 25px;
        }

        .supplier-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .supplier-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .supplier-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .supplier-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .supplier-code {
            font-size: 0.9rem;
            color: #6c757d;
            font-family: 'Courier New', monospace;
        }

        .supplier-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-active {
            background: rgba(39, 174, 96, 0.1);
            color: var(--success-color);
        }

        .status-inactive {
            background: rgba(149, 165, 166, 0.1);
            color: #95a5a6;
        }

        .status-suspended {
            background: rgba(243, 156, 18, 0.1);
            color: var(--warning-color);
        }

        .status-blacklisted {
            background: rgba(231, 76, 60, 0.1);
            color: var(--danger-color);
        }

        .supplier-rating {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 15px 0;
        }

        .rating-stars {
            color: #f39c12;
        }

        .rating-value {
            font-weight: 600;
            color: var(--primary-color);
        }

        .supplier-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-item .stat-value {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--primary-color);
        }

        .stat-item .stat-label {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 2px;
        }

        .supplier-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .action-btn {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            background: white;
            color: var(--primary-color);
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .action-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* حالة فارغة */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h4 {
            margin-bottom: 15px;
            color: var(--primary-color);
        }

        .empty-state p {
            margin-bottom: 25px;
            font-size: 1.1rem;
        }

        @media (max-width: 768px) {
            .page-header h1 {
                font-size: 2rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                padding: 20px;
            }
            
            .view-content {
                padding: 20px;
                margin: 0 20px 20px;
            }
            
            .suppliers-grid {
                grid-template-columns: 1fr;
            }
            
            .quick-actions {
                justify-content: center;
            }

            .view-tabs {
                margin: 20px;
                padding: 5px;
            }

            .view-tab {
                padding: 10px 15px;
                font-size: 0.9rem;
            }

            .filters-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <h1><i class="fas fa-truck"></i> إدارة الموردين المتقدمة</h1>
            <p>نظام شامل لإدارة الموردين وتقييم الأداء والعقود</p>
        </div>

        <!-- قسم الإحصائيات -->
        <div class="stats-section">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon suppliers">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="stat-value" id="totalSuppliers">0</div>
                    <div class="stat-label">إجمالي الموردين</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon contracts">
                        <i class="fas fa-file-contract"></i>
                    </div>
                    <div class="stat-value" id="activeContracts">0</div>
                    <div class="stat-label">العقود النشطة</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon evaluations">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-value" id="averageRating">0</div>
                    <div class="stat-label">متوسط التقييم</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon payments">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="stat-value" id="totalSpent">0</div>
                    <div class="stat-label">إجمالي المدفوعات</div>
                </div>
            </div>
        </div>

        <!-- تبويبات العرض -->
        <div class="view-tabs">
            <button class="view-tab active" onclick="setView('suppliers')" id="suppliersTab">
                <i class="fas fa-truck"></i> الموردين
            </button>
            <button class="view-tab" onclick="setView('contracts')" id="contractsTab">
                <i class="fas fa-file-contract"></i> العقود
            </button>
            <button class="view-tab" onclick="setView('evaluations')" id="evaluationsTab">
                <i class="fas fa-star"></i> التقييمات
            </button>
            <button class="view-tab" onclick="setView('payments')" id="paymentsTab">
                <i class="fas fa-money-bill-wave"></i> المدفوعات
            </button>
            <button class="view-tab" onclick="setView('performance')" id="performanceTab">
                <i class="fas fa-chart-line"></i> الأداء
            </button>
            <button class="view-tab" onclick="setView('reports')" id="reportsTab">
                <i class="fas fa-chart-bar"></i> التقارير
            </button>
        </div>

        <!-- محتوى العرض -->
        <div class="view-content">
            <!-- عرض الموردين -->
            <div id="suppliersView">
                <!-- لوحة الفلاتر -->
                <div class="filters-panel">
                    <div class="filters-row">
                        <div class="filter-group">
                            <label for="searchInput">البحث</label>
                            <input type="text" class="form-control" id="searchInput" 
                                   placeholder="ابحث في الموردين..." onkeyup="searchSuppliers()">
                        </div>
                        <div class="filter-group">
                            <label for="categoryFilter">الفئة</label>
                            <select class="form-select" id="categoryFilter" onchange="applyFilters()">
                                <option value="">جميع الفئات</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="statusFilter">الحالة</label>
                            <select class="form-select" id="statusFilter" onchange="applyFilters()">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                                <option value="suspended">معلق</option>
                                <option value="blacklisted">محظور</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="ratingFilter">التقييم</label>
                            <select class="form-select" id="ratingFilter" onchange="applyFilters()">
                                <option value="">جميع التقييمات</option>
                                <option value="5">5 نجوم</option>
                                <option value="4">4 نجوم فأكثر</option>
                                <option value="3">3 نجوم فأكثر</option>
                                <option value="2">2 نجوم فأكثر</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <button class="btn btn-outline-secondary" onclick="clearFilters()">
                                <i class="fas fa-times"></i> مسح الفلاتر
                            </button>
                        </div>
                    </div>
                </div>

                <!-- الإجراءات السريعة -->
                <div class="quick-actions">
                    <button class="quick-action-btn" onclick="showCreateSupplierModal()">
                        <i class="fas fa-plus"></i> مورد جديد
                    </button>
                    <button class="quick-action-btn" onclick="showImportSuppliersModal()">
                        <i class="fas fa-upload"></i> استيراد موردين
                    </button>
                    <button class="quick-action-btn" onclick="exportSuppliers()">
                        <i class="fas fa-download"></i> تصدير البيانات
                    </button>
                    <button class="quick-action-btn" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                </div>

                <!-- شبكة الموردين -->
                <div class="suppliers-grid" id="suppliersGrid">
                    <!-- سيتم ملء الموردين هنا -->
                </div>
            </div>

            <!-- عرض العقود -->
            <div id="contractsView" style="display: none;">
                <div class="quick-actions">
                    <button class="quick-action-btn" onclick="showCreateContractModal()">
                        <i class="fas fa-plus"></i> عقد جديد
                    </button>
                    <button class="quick-action-btn" onclick="showContractTemplates()">
                        <i class="fas fa-file-alt"></i> قوالب العقود
                    </button>
                    <button class="quick-action-btn" onclick="generateContractReport()">
                        <i class="fas fa-chart-bar"></i> تقرير العقود
                    </button>
                </div>

                <div id="contractsList">
                    <!-- سيتم ملء العقود هنا -->
                </div>
            </div>

            <!-- عرض التقييمات -->
            <div id="evaluationsView" style="display: none;">
                <div class="quick-actions">
                    <button class="quick-action-btn" onclick="showCreateEvaluationModal()">
                        <i class="fas fa-plus"></i> تقييم جديد
                    </button>
                    <button class="quick-action-btn" onclick="showEvaluationCriteria()">
                        <i class="fas fa-list"></i> معايير التقييم
                    </button>
                    <button class="quick-action-btn" onclick="generateEvaluationReport()">
                        <i class="fas fa-chart-line"></i> تقرير التقييمات
                    </button>
                </div>

                <div id="evaluationsList">
                    <!-- سيتم ملء التقييمات هنا -->
                </div>
            </div>

            <!-- عرض المدفوعات -->
            <div id="paymentsView" style="display: none;">
                <div class="quick-actions">
                    <button class="quick-action-btn" onclick="showCreatePaymentModal()">
                        <i class="fas fa-plus"></i> دفعة جديدة
                    </button>
                    <button class="quick-action-btn" onclick="showPaymentSchedule()">
                        <i class="fas fa-calendar"></i> جدولة المدفوعات
                    </button>
                    <button class="quick-action-btn" onclick="generatePaymentReport()">
                        <i class="fas fa-money-bill-wave"></i> تقرير المدفوعات
                    </button>
                </div>

                <div id="paymentsList">
                    <!-- سيتم ملء المدفوعات هنا -->
                </div>
            </div>

            <!-- عرض الأداء -->
            <div id="performanceView" style="display: none;">
                <div class="quick-actions">
                    <button class="quick-action-btn" onclick="analyzeAllSuppliers()">
                        <i class="fas fa-analytics"></i> تحليل شامل
                    </button>
                    <button class="quick-action-btn" onclick="showPerformanceTrends()">
                        <i class="fas fa-chart-line"></i> اتجاهات الأداء
                    </button>
                    <button class="quick-action-btn" onclick="generatePerformanceReport()">
                        <i class="fas fa-file-chart"></i> تقرير الأداء
                    </button>
                </div>

                <div id="performanceAnalysis">
                    <!-- سيتم ملء تحليل الأداء هنا -->
                </div>
            </div>

            <!-- عرض التقارير -->
            <div id="reportsView" style="display: none;">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-pie"></i> توزيع الموردين</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="suppliersDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-bar"></i> أداء الموردين</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="suppliersPerformanceChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-line"></i> اتجاهات المدفوعات</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="paymentsTrendChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-exclamation-triangle"></i> تحليل المخاطر</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="riskAnalysisChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../core/database.js"></script>
    <script src="../../core/utils.js"></script>
    <script src="advanced-suppliers.js"></script>

    <script>
        // متغيرات عامة
        let advancedSuppliersManager;
        let currentView = 'suppliers';
        let currentFilters = {};
        let charts = {};

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeAdvancedSuppliers();
            loadData();
            updateStatistics();
            setupEventListeners();
            loadCategories();
        });

        // تهيئة نظام الموردين المتقدم
        function initializeAdvancedSuppliers() {
            advancedSuppliersManager = new AdvancedSuppliersManager();
            console.log('✅ تم تهيئة واجهة إدارة الموردين المتقدمة');
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // تحديث الإحصائيات كل دقيقة
            setInterval(updateStatistics, 60000);
        }

        // تحميل الفئات
        function loadCategories() {
            const categoryFilter = document.getElementById('categoryFilter');
            const categories = Array.from(advancedSuppliersManager.supplierCategories.values());

            categoryFilter.innerHTML = '<option value="">جميع الفئات</option>';
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                categoryFilter.appendChild(option);
            });
        }

        // تحميل البيانات
        function loadData() {
            switch(currentView) {
                case 'suppliers':
                    loadSuppliers();
                    break;
                case 'contracts':
                    loadContracts();
                    break;
                case 'evaluations':
                    loadEvaluations();
                    break;
                case 'payments':
                    loadPayments();
                    break;
                case 'performance':
                    loadPerformance();
                    break;
                case 'reports':
                    loadReports();
                    break;
            }
        }

        // تحميل الموردين
        function loadSuppliers() {
            const suppliers = Array.from(advancedSuppliersManager.suppliers.values());
            displaySuppliers(suppliers);
        }

        // عرض الموردين
        function displaySuppliers(suppliers) {
            const grid = document.getElementById('suppliersGrid');

            if (suppliers.length === 0) {
                grid.innerHTML = `
                    <div class="col-12">
                        <div class="empty-state">
                            <i class="fas fa-truck"></i>
                            <h4>لا توجد موردين</h4>
                            <p>ابدأ بإضافة موردين جدد لإدارة سلسلة التوريد</p>
                            <button class="btn btn-primary" onclick="showCreateSupplierModal()">
                                <i class="fas fa-plus"></i> إضافة مورد جديد
                            </button>
                        </div>
                    </div>
                `;
                return;
            }

            grid.innerHTML = suppliers.map(supplier => createSupplierCard(supplier)).join('');
        }

        // إنشاء بطاقة مورد
        function createSupplierCard(supplier) {
            const rating = supplier.performance.rating || 0;
            const stars = Math.round(rating / 20); // تحويل من 100 إلى 5 نجوم

            return `
                <div class="supplier-card">
                    <div class="supplier-header">
                        <div>
                            <div class="supplier-title">${supplier.name}</div>
                            <div class="supplier-code">${supplier.code}</div>
                        </div>
                        <span class="supplier-status status-${supplier.status}">
                            ${getStatusLabel(supplier.status)}
                        </span>
                    </div>

                    <div class="supplier-rating">
                        <div class="rating-stars">
                            ${'★'.repeat(stars)}${'☆'.repeat(5-stars)}
                        </div>
                        <div class="rating-value">${rating.toFixed(1)}%</div>
                    </div>

                    <div class="supplier-stats">
                        <div class="stat-item">
                            <div class="stat-value">${formatCurrency(supplier.financial.totalPurchases)}</div>
                            <div class="stat-label">إجمالي المشتريات</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${supplier.performance.totalOrders}</div>
                            <div class="stat-label">عدد الطلبات</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${supplier.risk.level}</div>
                            <div class="stat-label">مستوى المخاطر</div>
                        </div>
                    </div>

                    <div class="supplier-actions">
                        <button class="action-btn" onclick="viewSupplier('${supplier.id}')">
                            <i class="fas fa-eye"></i> عرض
                        </button>
                        <button class="action-btn" onclick="editSupplier('${supplier.id}')">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="action-btn" onclick="evaluateSupplier('${supplier.id}')">
                            <i class="fas fa-star"></i> تقييم
                        </button>
                    </div>
                </div>
            `;
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            const reports = advancedSuppliersManager.getSuppliersReports();

            document.getElementById('totalSuppliers').textContent = reports.summary.totalSuppliers;
            document.getElementById('activeContracts').textContent = reports.contracts.activeContracts;
            document.getElementById('averageRating').textContent = reports.summary.averageRating.toFixed(1);
            document.getElementById('totalSpent').textContent = formatCurrency(reports.financial.totalSpent);
        }

        // تعيين العرض
        function setView(view) {
            currentView = view;

            // إخفاء جميع العروض
            document.querySelectorAll('[id$="View"]').forEach(el => {
                el.style.display = 'none';
            });

            // إزالة الفئة النشطة من جميع التبويبات
            document.querySelectorAll('.view-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // عرض العرض المحدد
            document.getElementById(view + 'View').style.display = 'block';
            document.getElementById(view + 'Tab').classList.add('active');

            // تحميل البيانات
            loadData();
        }

        // البحث في الموردين
        function searchSuppliers() {
            const query = document.getElementById('searchInput').value;
            if (!query.trim()) {
                loadSuppliers();
                return;
            }

            const results = advancedSuppliersManager.searchSuppliers(query, currentFilters);
            displaySuppliers(results);
        }

        // تطبيق الفلاتر
        function applyFilters() {
            currentFilters = {
                category: document.getElementById('categoryFilter').value,
                status: document.getElementById('statusFilter').value,
                minRating: document.getElementById('ratingFilter').value ? parseInt(document.getElementById('ratingFilter').value) * 20 : null
            };

            // إزالة الفلاتر الفارغة
            Object.keys(currentFilters).forEach(key => {
                if (!currentFilters[key]) {
                    delete currentFilters[key];
                }
            });

            searchSuppliers();
        }

        // مسح الفلاتر
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('categoryFilter').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('ratingFilter').value = '';
            currentFilters = {};
            loadSuppliers();
        }

        // الحصول على تسمية الحالة
        function getStatusLabel(status) {
            const labels = {
                'active': 'نشط',
                'inactive': 'غير نشط',
                'suspended': 'معلق',
                'blacklisted': 'محظور'
            };
            return labels[status] || status;
        }

        // تنسيق العملة
        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: 'SAR'
            }).format(amount);
        }

        // عرض مورد
        function viewSupplier(supplierId) {
            const supplier = advancedSuppliersManager.suppliers.get(supplierId);
            if (!supplier) return;

            // إنشاء نافذة منبثقة لعرض تفاصيل المورد
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل المورد: ${supplier.name}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>المعلومات الأساسية</h6>
                                    <p><strong>الكود:</strong> ${supplier.code}</p>
                                    <p><strong>النوع:</strong> ${supplier.type}</p>
                                    <p><strong>الفئة:</strong> ${supplier.category}</p>
                                    <p><strong>الحالة:</strong> ${getStatusLabel(supplier.status)}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6>معلومات الاتصال</h6>
                                    <p><strong>الهاتف:</strong> ${supplier.contact.phone}</p>
                                    <p><strong>الجوال:</strong> ${supplier.contact.mobile}</p>
                                    <p><strong>البريد:</strong> ${supplier.contact.email}</p>
                                    <p><strong>الموقع:</strong> ${supplier.contact.website}</p>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <h6>المعلومات المالية</h6>
                                    <p><strong>الرصيد الحالي:</strong> ${formatCurrency(supplier.financial.currentBalance)}</p>
                                    <p><strong>إجمالي المشتريات:</strong> ${formatCurrency(supplier.financial.totalPurchases)}</p>
                                    <p><strong>حد الائتمان:</strong> ${formatCurrency(supplier.financial.creditLimit)}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6>الأداء</h6>
                                    <p><strong>التقييم:</strong> ${supplier.performance.rating.toFixed(1)}%</p>
                                    <p><strong>عدد الطلبات:</strong> ${supplier.performance.totalOrders}</p>
                                    <p><strong>مستوى المخاطر:</strong> ${supplier.risk.level}</p>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="editSupplier('${supplier.id}')">تعديل</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();

            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        }

        // تعديل مورد
        function editSupplier(supplierId) {
            const supplier = advancedSuppliersManager.suppliers.get(supplierId);
            if (!supplier) return;

            // إنشاء نافذة تعديل المورد
            showCreateSupplierModal(supplier);
        }

        // تقييم مورد
        function evaluateSupplier(supplierId) {
            const supplier = advancedSuppliersManager.suppliers.get(supplierId);
            if (!supplier) return;

            // إنشاء نافذة تقييم المورد
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تقييم المورد: ${supplier.name}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="evaluationForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">تقييم التسليم (0-100)</label>
                                            <input type="number" class="form-control" id="deliveryScore" min="0" max="100" required>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">تقييم الجودة (0-100)</label>
                                            <input type="number" class="form-control" id="qualityScore" min="0" max="100" required>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">تقييم الخدمة (0-100)</label>
                                            <input type="number" class="form-control" id="serviceScore" min="0" max="100" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">تقييم السعر (0-100)</label>
                                            <input type="number" class="form-control" id="priceScore" min="0" max="100" required>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">تقييم التواصل (0-100)</label>
                                            <input type="number" class="form-control" id="communicationScore" min="0" max="100" required>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">فترة التقييم</label>
                                            <select class="form-select" id="evaluationPeriod" required>
                                                <option value="monthly">شهري</option>
                                                <option value="quarterly">ربع سنوي</option>
                                                <option value="yearly">سنوي</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">التوصيات</label>
                                    <textarea class="form-control" id="recommendations" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="submitEvaluation('${supplierId}')">حفظ التقييم</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();

            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        }

        // إرسال التقييم
        function submitEvaluation(supplierId) {
            const evaluationData = {
                supplierId: supplierId,
                period: document.getElementById('evaluationPeriod').value,
                deliveryScore: parseInt(document.getElementById('deliveryScore').value),
                qualityScore: parseInt(document.getElementById('qualityScore').value),
                serviceScore: parseInt(document.getElementById('serviceScore').value),
                priceScore: parseInt(document.getElementById('priceScore').value),
                communicationScore: parseInt(document.getElementById('communicationScore').value),
                recommendations: document.getElementById('recommendations').value.split('\n').filter(r => r.trim()),
                evaluatedBy: 'المستخدم الحالي'
            };

            try {
                const evaluation = advancedSuppliersManager.evaluateSupplier(evaluationData);

                // إغلاق النافذة
                const modal = document.querySelector('.modal.show');
                if (modal) {
                    bootstrap.Modal.getInstance(modal).hide();
                }

                // تحديث العرض
                loadData();
                updateStatistics();

                // إظهار رسالة نجاح
                showNotification('تم حفظ التقييم بنجاح', 'success');

            } catch (error) {
                console.error('خطأ في حفظ التقييم:', error);
                showNotification('حدث خطأ في حفظ التقييم', 'error');
            }
        }

        // عرض نافذة إنشاء مورد
        function showCreateSupplierModal(supplier = null) {
            const isEdit = supplier !== null;
            const title = isEdit ? 'تعديل المورد' : 'مورد جديد';

            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="supplierForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>المعلومات الأساسية</h6>
                                        <div class="mb-3">
                                            <label class="form-label">اسم المورد *</label>
                                            <input type="text" class="form-control" id="supplierName" value="${supplier?.name || ''}" required>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">الاسم بالإنجليزية</label>
                                            <input type="text" class="form-control" id="supplierNameEn" value="${supplier?.nameEn || ''}">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">النوع</label>
                                            <select class="form-select" id="supplierType">
                                                <option value="individual" ${supplier?.type === 'individual' ? 'selected' : ''}>فرد</option>
                                                <option value="company" ${supplier?.type === 'company' ? 'selected' : ''}>شركة</option>
                                                <option value="government" ${supplier?.type === 'government' ? 'selected' : ''}>جهة حكومية</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">الفئة</label>
                                            <select class="form-select" id="supplierCategory">
                                                <!-- سيتم ملؤها من الجافا سكريبت -->
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>معلومات الاتصال</h6>
                                        <div class="mb-3">
                                            <label class="form-label">الهاتف</label>
                                            <input type="tel" class="form-control" id="supplierPhone" value="${supplier?.contact?.phone || ''}">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">الجوال</label>
                                            <input type="tel" class="form-control" id="supplierMobile" value="${supplier?.contact?.mobile || ''}">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">البريد الإلكتروني</label>
                                            <input type="email" class="form-control" id="supplierEmail" value="${supplier?.contact?.email || ''}">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">الموقع الإلكتروني</label>
                                            <input type="url" class="form-control" id="supplierWebsite" value="${supplier?.contact?.website || ''}">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>العنوان</h6>
                                        <div class="mb-3">
                                            <label class="form-label">المدينة</label>
                                            <input type="text" class="form-control" id="supplierCity" value="${supplier?.address?.city || ''}">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">الحي</label>
                                            <input type="text" class="form-control" id="supplierDistrict" value="${supplier?.address?.district || ''}">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">الشارع</label>
                                            <input type="text" class="form-control" id="supplierStreet" value="${supplier?.address?.street || ''}">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>المعلومات المالية</h6>
                                        <div class="mb-3">
                                            <label class="form-label">حد الائتمان</label>
                                            <input type="number" class="form-control" id="supplierCreditLimit" value="${supplier?.financial?.creditLimit || 0}" min="0">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">شروط الدفع</label>
                                            <select class="form-select" id="supplierPaymentTerms">
                                                <option value="نقدي" ${supplier?.financial?.paymentTerms === 'نقدي' ? 'selected' : ''}>نقدي</option>
                                                <option value="آجل 30 يوم" ${supplier?.financial?.paymentTerms === 'آجل 30 يوم' ? 'selected' : ''}>آجل 30 يوم</option>
                                                <option value="آجل 60 يوم" ${supplier?.financial?.paymentTerms === 'آجل 60 يوم' ? 'selected' : ''}>آجل 60 يوم</option>
                                                <option value="آجل 90 يوم" ${supplier?.financial?.paymentTerms === 'آجل 90 يوم' ? 'selected' : ''}>آجل 90 يوم</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">الرقم الضريبي</label>
                                            <input type="text" class="form-control" id="supplierTaxNumber" value="${supplier?.financial?.taxNumber || ''}">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="supplierNotes" rows="3">${supplier?.notes || ''}</textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="submitSupplier(${isEdit ? `'${supplier.id}'` : 'null'})">${isEdit ? 'تحديث' : 'حفظ'}</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // ملء فئات الموردين
            const categorySelect = modal.querySelector('#supplierCategory');
            const categories = Array.from(advancedSuppliersManager.supplierCategories.values());
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                option.selected = supplier?.category === category.id;
                categorySelect.appendChild(option);
            });

            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();

            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        }

        // إرسال بيانات المورد
        function submitSupplier(supplierId) {
            const supplierData = {
                name: document.getElementById('supplierName').value,
                nameEn: document.getElementById('supplierNameEn').value,
                type: document.getElementById('supplierType').value,
                category: document.getElementById('supplierCategory').value,
                phone: document.getElementById('supplierPhone').value,
                mobile: document.getElementById('supplierMobile').value,
                email: document.getElementById('supplierEmail').value,
                website: document.getElementById('supplierWebsite').value,
                city: document.getElementById('supplierCity').value,
                district: document.getElementById('supplierDistrict').value,
                street: document.getElementById('supplierStreet').value,
                creditLimit: parseFloat(document.getElementById('supplierCreditLimit').value) || 0,
                paymentTerms: document.getElementById('supplierPaymentTerms').value,
                taxNumber: document.getElementById('supplierTaxNumber').value,
                notes: document.getElementById('supplierNotes').value
            };

            try {
                if (supplierId) {
                    // تحديث مورد موجود
                    advancedSuppliersManager.updateSupplier(supplierId, supplierData);
                    showNotification('تم تحديث المورد بنجاح', 'success');
                } else {
                    // إنشاء مورد جديد
                    advancedSuppliersManager.createSupplier(supplierData);
                    showNotification('تم إنشاء المورد بنجاح', 'success');
                }

                // إغلاق النافذة
                const modal = document.querySelector('.modal.show');
                if (modal) {
                    bootstrap.Modal.getInstance(modal).hide();
                }

                // تحديث العرض
                loadData();
                updateStatistics();

            } catch (error) {
                console.error('خطأ في حفظ المورد:', error);
                showNotification('حدث خطأ في حفظ المورد', 'error');
            }
        }

        // إظهار الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }

        // تحديث البيانات
        function refreshData() {
            loadData();
            updateStatistics();
            showNotification('تم تحديث البيانات', 'success');
        }

        // دوال العروض الأخرى (ستكون بسيطة للآن)
        function loadContracts() {
            document.getElementById('contractsList').innerHTML = '<p class="text-center">قريباً...</p>';
        }

        function loadEvaluations() {
            document.getElementById('evaluationsList').innerHTML = '<p class="text-center">قريباً...</p>';
        }

        function loadPayments() {
            document.getElementById('paymentsList').innerHTML = '<p class="text-center">قريباً...</p>';
        }

        function loadPerformance() {
            document.getElementById('performanceAnalysis').innerHTML = '<p class="text-center">قريباً...</p>';
        }

        function loadReports() {
            // تحميل الرسوم البيانية
            setTimeout(() => {
                createChartsIfNeeded();
            }, 100);
        }

        function createChartsIfNeeded() {
            // رسم بياني لتوزيع الموردين
            const ctx1 = document.getElementById('suppliersDistributionChart');
            if (ctx1 && !charts.distribution) {
                const reports = advancedSuppliersManager.getSuppliersReports();
                charts.distribution = new Chart(ctx1, {
                    type: 'pie',
                    data: {
                        labels: ['نشط', 'غير نشط', 'معلق', 'محظور'],
                        datasets: [{
                            data: [
                                reports.summary.activeSuppliers,
                                reports.summary.totalSuppliers - reports.summary.activeSuppliers,
                                0, 0
                            ],
                            backgroundColor: ['#27ae60', '#95a5a6', '#f39c12', '#e74c3c']
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
        }

        // دوال الإجراءات السريعة (ستكون بسيطة للآن)
        function showImportSuppliersModal() {
            showNotification('ميزة الاستيراد قيد التطوير', 'info');
        }

        function exportSuppliers() {
            showNotification('ميزة التصدير قيد التطوير', 'info');
        }

        function showCreateContractModal() {
            showNotification('ميزة إنشاء العقود قيد التطوير', 'info');
        }

        function showCreateEvaluationModal() {
            showNotification('ميزة إنشاء التقييمات قيد التطوير', 'info');
        }

        function showCreatePaymentModal() {
            showNotification('ميزة إنشاء المدفوعات قيد التطوير', 'info');
        }
    </script>
</body>
</html>
