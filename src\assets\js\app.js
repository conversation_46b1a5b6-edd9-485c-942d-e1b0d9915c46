/**
 * Main Application JavaScript
 * Enhanced functionality for Black Horse ERP
 */

// Global application state
window.BlackHorseApp = {
    currentModule: null,
    isLoading: false,
    modules: new Map(),
    cache: new Map(),
    
    // Initialize application
    async init() {
        console.log('Initializing Black Horse ERP...');
        
        // Setup global error handling
        this.setupErrorHandling();
        
        // Setup performance monitoring
        this.setupPerformanceMonitoring();
        
        // Setup keyboard shortcuts
        this.setupKeyboardShortcuts();
        
        // Setup auto-save functionality
        this.setupAutoSave();
        
        console.log('Black Horse ERP initialized successfully');
    },
    
    // Setup global error handling
    setupErrorHandling() {
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            this.showErrorNotification('حدث خطأ غير متوقع في النظام');
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            this.showErrorNotification('حدث خطأ في معالجة البيانات');
        });
    },
    
    // Setup performance monitoring
    setupPerformanceMonitoring() {
        // Monitor page load time
        window.addEventListener('load', () => {
            const loadTime = performance.now();
            console.log(`Page loaded in ${loadTime.toFixed(2)}ms`);
            
            if (loadTime > 3000) {
                console.warn('Slow page load detected');
            }
        });
        
        // Monitor memory usage
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                if (memory.usedJSHeapSize > 50 * 1024 * 1024) { // 50MB
                    console.warn('High memory usage detected');
                }
            }, 30000); // Check every 30 seconds
        }
    },
    
    // Setup keyboard shortcuts
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Ctrl/Cmd + S for save
            if ((event.ctrlKey || event.metaKey) && event.key === 's') {
                event.preventDefault();
                this.saveCurrentForm();
            }
            
            // Ctrl/Cmd + N for new
            if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
                event.preventDefault();
                this.createNew();
            }
            
            // Escape to close modals
            if (event.key === 'Escape') {
                this.closeModals();
            }
            
            // F1 for help
            if (event.key === 'F1') {
                event.preventDefault();
                this.showHelp();
            }
        });
    },
    
    // Setup auto-save functionality
    setupAutoSave() {
        setInterval(() => {
            this.autoSave();
        }, 30000); // Auto-save every 30 seconds
    },
    
    // Show error notification
    showErrorNotification(message) {
        if (typeof Utils !== 'undefined' && Utils.showAlert) {
            Utils.showAlert(message, 'error');
        } else {
            alert(message);
        }
    },
    
    // Save current form
    saveCurrentForm() {
        const activeForm = document.querySelector('form:focus-within');
        if (activeForm) {
            const submitButton = activeForm.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.click();
            }
        }
    },
    
    // Create new item
    createNew() {
        const addButton = document.querySelector('.btn[onclick*="add"], .btn[onclick*="new"]');
        if (addButton) {
            addButton.click();
        }
    },
    
    // Close modals
    closeModals() {
        const modals = document.querySelectorAll('.modal.show');
        modals.forEach(modal => {
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
            }
        });
    },
    
    // Show help
    showHelp() {
        const helpModal = new bootstrap.Modal(document.getElementById('helpModal') || this.createHelpModal());
        helpModal.show();
    },
    
    // Create help modal
    createHelpModal() {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'helpModal';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">مساعدة النظام</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <h6>اختصارات لوحة المفاتيح:</h6>
                        <ul>
                            <li><kbd>Ctrl + S</kbd> - حفظ النموذج الحالي</li>
                            <li><kbd>Ctrl + N</kbd> - إنشاء عنصر جديد</li>
                            <li><kbd>Esc</kbd> - إغلاق النوافذ المنبثقة</li>
                            <li><kbd>F1</kbd> - عرض المساعدة</li>
                        </ul>
                        <h6>ميزات النظام:</h6>
                        <ul>
                            <li>حفظ تلقائي كل 30 ثانية</li>
                            <li>مراقبة الأداء والذاكرة</li>
                            <li>معالجة الأخطاء التلقائية</li>
                            <li>واجهة متجاوبة مع الأجهزة المختلفة</li>
                        </ul>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
        return modal;
    },
    
    // Auto-save functionality
    autoSave() {
        const forms = document.querySelectorAll('form[data-auto-save="true"]');
        forms.forEach(form => {
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            
            // Save to localStorage as backup
            localStorage.setItem(`autosave_${form.id}`, JSON.stringify({
                data: data,
                timestamp: Date.now()
            }));
        });
    },
    
    // Load auto-saved data
    loadAutoSave(formId) {
        const saved = localStorage.getItem(`autosave_${formId}`);
        if (saved) {
            try {
                const { data, timestamp } = JSON.parse(saved);
                
                // Only load if saved within last hour
                if (Date.now() - timestamp < 3600000) {
                    return data;
                }
            } catch (error) {
                console.error('Error loading auto-save data:', error);
            }
        }
        return null;
    },
    
    // Clear auto-save data
    clearAutoSave(formId) {
        localStorage.removeItem(`autosave_${formId}`);
    },
    
    // Module management
    registerModule(name, module) {
        this.modules.set(name, module);
    },
    
    getModule(name) {
        return this.modules.get(name);
    },
    
    // Cache management
    setCache(key, value, ttl = 300000) { // 5 minutes default TTL
        this.cache.set(key, {
            value: value,
            expires: Date.now() + ttl
        });
    },
    
    getCache(key) {
        const cached = this.cache.get(key);
        if (cached && cached.expires > Date.now()) {
            return cached.value;
        }
        this.cache.delete(key);
        return null;
    },
    
    clearCache() {
        this.cache.clear();
    },
    
    // Utility functions
    formatNumber(number, decimals = 2) {
        return new Intl.NumberFormat('ar-EG', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(number);
    },
    
    formatDate(date, format = 'short') {
        const options = {
            short: { year: 'numeric', month: '2-digit', day: '2-digit' },
            long: { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' },
            time: { hour: '2-digit', minute: '2-digit', second: '2-digit' }
        };
        
        return new Intl.DateTimeFormat('ar-EG', options[format] || options.short).format(new Date(date));
    },
    
    // Validation helpers
    validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },
    
    validatePhone(phone) {
        const re = /^[\+]?[1-9][\d]{0,15}$/;
        return re.test(phone.replace(/\s/g, ''));
    },
    
    validateRequired(value) {
        return value !== null && value !== undefined && value.toString().trim() !== '';
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.BlackHorseApp.init();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.BlackHorseApp;
}
