/**
 * Database Management System
 * IndexedDB wrapper for Black Horse ERP
 */

class DatabaseManager {
    constructor() {
        this.dbName = 'BlackHorseERP';
        this.version = 1;
        this.db = null;
        this.stores = [
            'products',
            'categories', 
            'customers',
            'suppliers',
            'sales',
            'purchases',
            'inventory_transactions',
            'employees',
            'departments',
            'positions',
            'attendance',
            'payroll',
            'evaluations',
            'cashiers',
            'cash_transactions',
            'settings'
        ];
    }

    /**
     * Initialize database
     */
    async initialize() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);

            request.onerror = () => {
                console.error('Database error:', request.error);
                reject(request.error);
            };

            request.onsuccess = () => {
                this.db = request.result;
                console.log('Database initialized successfully');
                resolve(this.db);
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // Create object stores
                this.stores.forEach(storeName => {
                    if (!db.objectStoreNames.contains(storeName)) {
                        const store = db.createObjectStore(storeName, { keyPath: 'id' });
                        
                        // Create indexes based on store type
                        switch (storeName) {
                            case 'products':
                                store.createIndex('barcode', 'barcode', { unique: false });
                                store.createIndex('category_id', 'category_id', { unique: false });
                                store.createIndex('name', 'name', { unique: false });
                                break;
                            case 'customers':
                                store.createIndex('phone', 'phone', { unique: false });
                                store.createIndex('email', 'email', { unique: false });
                                break;
                            case 'employees':
                                store.createIndex('employee_id', 'employee_id', { unique: true });
                                store.createIndex('department_id', 'department_id', { unique: false });
                                store.createIndex('national_id', 'national_id', { unique: true });
                                break;
                            case 'attendance':
                                store.createIndex('employee_id', 'employee_id', { unique: false });
                                store.createIndex('date', 'date', { unique: false });
                                break;
                            case 'sales':
                                store.createIndex('customer_id', 'customer_id', { unique: false });
                                store.createIndex('date', 'date', { unique: false });
                                break;
                        }
                    }
                });
            };
        });
    }

    /**
     * Generic CRUD operations
     */
    createStore(storeName) {
        return {
            async add(data) {
                const transaction = this.db.transaction([storeName], 'readwrite');
                const store = transaction.objectStore(storeName);
                
                if (!data.id) {
                    data.id = this.generateId();
                }
                
                data.created_at = data.created_at || new Date().toISOString();
                data.updated_at = new Date().toISOString();
                
                return new Promise((resolve, reject) => {
                    const request = store.add(data);
                    request.onsuccess = () => resolve(data);
                    request.onerror = () => reject(request.error);
                });
            },

            async getAll() {
                const transaction = this.db.transaction([storeName], 'readonly');
                const store = transaction.objectStore(storeName);
                
                return new Promise((resolve, reject) => {
                    const request = store.getAll();
                    request.onsuccess = () => resolve(request.result);
                    request.onerror = () => reject(request.error);
                });
            },

            async getById(id) {
                const transaction = this.db.transaction([storeName], 'readonly');
                const store = transaction.objectStore(storeName);
                
                return new Promise((resolve, reject) => {
                    const request = store.get(id);
                    request.onsuccess = () => resolve(request.result);
                    request.onerror = () => reject(request.error);
                });
            },

            async update(id, data) {
                const transaction = this.db.transaction([storeName], 'readwrite');
                const store = transaction.objectStore(storeName);
                
                return new Promise((resolve, reject) => {
                    const getRequest = store.get(id);
                    getRequest.onsuccess = () => {
                        const existingData = getRequest.result;
                        if (existingData) {
                            const updatedData = { ...existingData, ...data };
                            updatedData.updated_at = new Date().toISOString();
                            
                            const putRequest = store.put(updatedData);
                            putRequest.onsuccess = () => resolve(updatedData);
                            putRequest.onerror = () => reject(putRequest.error);
                        } else {
                            reject(new Error('Record not found'));
                        }
                    };
                    getRequest.onerror = () => reject(getRequest.error);
                });
            },

            async delete(id) {
                const transaction = this.db.transaction([storeName], 'readwrite');
                const store = transaction.objectStore(storeName);
                
                return new Promise((resolve, reject) => {
                    const request = store.delete(id);
                    request.onsuccess = () => resolve(true);
                    request.onerror = () => reject(request.error);
                });
            },

            async search(indexName, value) {
                const transaction = this.db.transaction([storeName], 'readonly');
                const store = transaction.objectStore(storeName);
                const index = store.index(indexName);
                
                return new Promise((resolve, reject) => {
                    const request = index.getAll(value);
                    request.onsuccess = () => resolve(request.result);
                    request.onerror = () => reject(request.error);
                });
            },

            async count() {
                const transaction = this.db.transaction([storeName], 'readonly');
                const store = transaction.objectStore(storeName);
                
                return new Promise((resolve, reject) => {
                    const request = store.count();
                    request.onsuccess = () => resolve(request.result);
                    request.onerror = () => reject(request.error);
                });
            }
        };
    }

    /**
     * Generate unique ID
     */
    generateId() {
        return 'id_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    }

    /**
     * Clear all data
     */
    async clearAllData() {
        const transaction = this.db.transaction(this.stores, 'readwrite');
        
        return Promise.all(
            this.stores.map(storeName => {
                return new Promise((resolve, reject) => {
                    const store = transaction.objectStore(storeName);
                    const request = store.clear();
                    request.onsuccess = () => resolve();
                    request.onerror = () => reject(request.error);
                });
            })
        );
    }

    /**
     * Export data
     */
    async exportData() {
        const data = {};
        
        for (const storeName of this.stores) {
            const store = this.createStore(storeName);
            data[storeName] = await store.getAll();
        }
        
        return data;
    }

    /**
     * Import data
     */
    async importData(data) {
        for (const [storeName, records] of Object.entries(data)) {
            if (this.stores.includes(storeName)) {
                const store = this.createStore(storeName);
                
                for (const record of records) {
                    try {
                        await store.add(record);
                    } catch (error) {
                        console.warn(`Failed to import record in ${storeName}:`, error);
                    }
                }
            }
        }
    }
}

// Create global database instance
const dbManager = new DatabaseManager();

// Create store instances
window.Database = {
    initialize: () => dbManager.initialize(),
    
    // Store instances
    products: dbManager.createStore('products'),
    categories: dbManager.createStore('categories'),
    customers: dbManager.createStore('customers'),
    suppliers: dbManager.createStore('suppliers'),
    sales: dbManager.createStore('sales'),
    purchases: dbManager.createStore('purchases'),
    inventory_transactions: dbManager.createStore('inventory_transactions'),
    employees: dbManager.createStore('employees'),
    departments: dbManager.createStore('departments'),
    positions: dbManager.createStore('positions'),
    attendance: dbManager.createStore('attendance'),
    payroll: dbManager.createStore('payroll'),
    evaluations: dbManager.createStore('evaluations'),
    cashiers: dbManager.createStore('cashiers'),
    cash_transactions: dbManager.createStore('cash_transactions'),
    settings: dbManager.createStore('settings'),
    
    // Utility methods
    generateId: () => dbManager.generateId(),
    clearAllData: () => dbManager.clearAllData(),
    exportData: () => dbManager.exportData(),
    importData: (data) => dbManager.importData(data)
};

console.log('Database module loaded successfully');
