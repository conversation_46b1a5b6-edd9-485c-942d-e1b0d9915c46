<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Black Horse POS - نظام نقاط البيع</title>
    
    <!-- الخطوط -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- الأيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- الأنماط المخصصة -->
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            direction: rtl;
        }
        
        .main-container {
            display: none;
            min-height: 100vh;
        }
        
        .sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .content-area {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            min-height: 100vh;
            padding: 20px;
        }
        
        .nav-link {
            color: #333;
            padding: 15px 20px;
            margin: 5px 0;
            border-radius: 10px;
            transition: all 0.3s;
        }
        
        .nav-link:hover, .nav-link.active {
            background: #007bff;
            color: white;
            transform: translateX(-5px);
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.9);
        }
        
        .btn {
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
        
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
        }
        
        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .page {
            display: none;
        }
        
        .page.active {
            display: block;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .quick-action {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .quick-action:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        
        .theme-light {
            /* الثيم الفاتح - افتراضي */
        }
        
        .theme-dark {
            background: #1a1a1a;
            color: white;
        }
        
        .theme-dark .card {
            background: rgba(40, 40, 40, 0.9);
            color: white;
        }
        
        .theme-dark .sidebar {
            background: rgba(30, 30, 30, 0.95);
            color: white;
        }
        
        .theme-dark .nav-link {
            color: #ccc;
        }
        
        .theme-dark .nav-link:hover, .theme-dark .nav-link.active {
            background: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <!-- شاشة التحميل -->
    <div id="loading-screen" class="loading-screen">
        <div class="text-center">
            <div class="loading-spinner"></div>
            <h2>🐎 Black Horse POS</h2>
            <p>جاري تحميل النظام...</p>
            <small>نظام نقاط البيع المتطور</small>
        </div>
    </div>

    <!-- التطبيق الرئيسي -->
    <div id="main-app" class="main-container">
        <div class="container-fluid">
            <div class="row">
                <!-- الشريط الجانبي -->
                <div class="col-md-3 col-lg-2 sidebar">
                    <div class="p-3">
                        <h4 class="text-center mb-4">
                            🐎 Black Horse POS
                        </h4>
                        
                        <nav class="nav flex-column">
                            <a href="#" class="nav-link active" onclick="showPage('dashboard')">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                            <a href="#" class="nav-link" onclick="showPage('products')">
                                <i class="fas fa-box me-2"></i>
                                المنتجات
                            </a>
                            <a href="#" class="nav-link" onclick="showPage('customers')">
                                <i class="fas fa-users me-2"></i>
                                العملاء
                            </a>
                            <a href="#" class="nav-link" onclick="showPage('sales')">
                                <i class="fas fa-shopping-cart me-2"></i>
                                المبيعات
                            </a>
                            <a href="#" class="nav-link" onclick="showPage('reports')">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                            <a href="#" class="nav-link" onclick="showPage('expenses')">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                المصروفات
                            </a>
                            <a href="#" class="nav-link" onclick="showPage('settings')">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات
                            </a>
                        </nav>
                    </div>
                </div>

                <!-- المحتوى الرئيسي -->
                <div class="col-md-9 col-lg-10 content-area">
                    <!-- لوحة التحكم -->
                    <div id="dashboard" class="page active">
                        <h2 class="mb-4">لوحة التحكم</h2>
                        
                        <!-- الإحصائيات -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <h5>إجمالي المبيعات</h5>
                                    <h3 id="total-sales">0 ج.م</h3>
                                    <small>هذا الشهر</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <h5>عدد المنتجات</h5>
                                    <h3 id="total-products">0</h3>
                                    <small>في المخزون</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <h5>عدد العملاء</h5>
                                    <h3 id="total-customers">0</h3>
                                    <small>مسجل</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <h5>المعاملات اليوم</h5>
                                    <h3 id="today-transactions">0</h3>
                                    <small>معاملة</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- الإجراءات السريعة -->
                        <div class="row">
                            <div class="col-md-4">
                                <div class="quick-action" onclick="showPage('sales')">
                                    <i class="fas fa-plus-circle fa-3x text-primary mb-3"></i>
                                    <h5>بيع جديد</h5>
                                    <p>إنشاء فاتورة بيع جديدة</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="quick-action" onclick="showPage('products')">
                                    <i class="fas fa-box-open fa-3x text-success mb-3"></i>
                                    <h5>إضافة منتج</h5>
                                    <p>إضافة منتج جديد للمخزون</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="quick-action" onclick="showPage('reports')">
                                    <i class="fas fa-chart-line fa-3x text-info mb-3"></i>
                                    <h5>عرض التقارير</h5>
                                    <p>مراجعة تقارير المبيعات</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صفحة المنتجات -->
                    <div id="products" class="page">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2>إدارة المنتجات</h2>
                            <button class="btn btn-primary" onclick="showAddProductModal()">
                                <i class="fas fa-plus me-2"></i>
                                إضافة منتج جديد
                            </button>
                        </div>
                        
                        <div class="card">
                            <div class="card-body">
                                <div id="products-content">
                                    <p class="text-center text-muted">جاري تحميل المنتجات...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صفحة العملاء -->
                    <div id="customers" class="page">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2>إدارة العملاء</h2>
                            <button class="btn btn-primary" onclick="showAddCustomerModal()">
                                <i class="fas fa-plus me-2"></i>
                                إضافة عميل جديد
                            </button>
                        </div>
                        
                        <div class="card">
                            <div class="card-body">
                                <div id="customers-content">
                                    <p class="text-center text-muted">جاري تحميل العملاء...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صفحة المبيعات -->
                    <div id="sales" class="page">
                        <h2 class="mb-4">نقطة البيع</h2>
                        
                        <div class="card">
                            <div class="card-body">
                                <div id="sales-content">
                                    <p class="text-center text-muted">جاري تحميل نقطة البيع...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صفحة التقارير -->
                    <div id="reports" class="page">
                        <h2 class="mb-4">التقارير والإحصائيات</h2>
                        
                        <div class="card">
                            <div class="card-body">
                                <div id="reports-content">
                                    <p class="text-center text-muted">جاري تحميل التقارير...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صفحة المصروفات -->
                    <div id="expenses" class="page">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2>إدارة المصروفات</h2>
                            <button class="btn btn-primary" onclick="showAddExpenseModal()">
                                <i class="fas fa-plus me-2"></i>
                                إضافة مصروف جديد
                            </button>
                        </div>
                        
                        <div class="card">
                            <div class="card-body">
                                <div id="expenses-content">
                                    <p class="text-center text-muted">جاري تحميل المصروفات...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صفحة الإعدادات -->
                    <div id="settings" class="page">
                        <h2 class="mb-4">إعدادات النظام</h2>
                        
                        <div class="card">
                            <div class="card-body">
                                <div id="settings-content">
                                    <p class="text-center text-muted">جاري تحميل الإعدادات...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- تحميل الوحدات الأساسية -->
    <script src="src/utils/helpers.js"></script>
    <script src="src/utils/validators.js"></script>
    <script src="src/database/database-optimizer.js"></script>
    <script src="src/database/unified-storage.js"></script>
    <script src="src/database/data-migration.js"></script>
    <script src="src/core/module-manager.js"></script>
    <script src="src/core/consolidated-functions.js"></script>
    <script src="src/core/system-health-checker.js"></script>
    <script src="src/core/error-handler.js"></script>
    <script src="src/core/performance-optimizer.js"></script>
    <script src="src/ui/ui-optimizer.js"></script>
    <script src="src/core/asset-optimizer.js"></script>

    <!-- تحميل الوحدات المحسنة -->
    <script src="src/modules/products/products-manager.js"></script>
    <script src="src/modules/customers/customers-manager.js"></script>
    <script src="src/modules/sales/sales-manager.js"></script>
    <script src="src/modules/reports/reports-manager.js"></script>
    <script src="src/modules/expenses/expenses-manager.js"></script>

    <!-- تحميل لوحة مراقبة الأداء -->
    <script src="src/ui/performance-dashboard.js"></script>

    <!-- تحميل نظام الاختبارات -->
    <script src="src/tests/performance-tests.js"></script>

    <script src="src/core/app-initializer.js"></script>
    
    <!-- نظام التحقق من الصحة ومعالجة الأخطاء -->
    <script>
        // نظام التحقق من صحة النظام
        class SystemHealthChecker {
            constructor() {
                this.requiredModules = [
                    'Utils', 'UnifiedStorage', 'DatabaseOptimizer', 'ModuleManager',
                    'AssetOptimizer', 'PerformanceOptimizer', 'UIOptimizer',
                    'ProductsManager', 'CustomersManager', 'SalesManager',
                    'ReportsManager', 'ExpensesManager', 'PerformanceDashboard',
                    'PerformanceTests', 'AppInitializer'
                ];
                this.healthStatus = {};
                this.errors = [];
            }

            async checkSystemHealth() {
                console.log('🔍 بدء فحص صحة النظام...');

                // فحص الوحدات المطلوبة
                this.checkRequiredModules();

                // فحص قاعدة البيانات
                await this.checkDatabase();

                // فحص الأداء
                await this.checkPerformance();

                // إنشاء التقرير
                this.generateHealthReport();

                return this.healthStatus;
            }

            checkRequiredModules() {
                console.log('📦 فحص الوحدات المطلوبة...');

                this.requiredModules.forEach(moduleName => {
                    const isAvailable = window[moduleName] !== undefined;
                    this.healthStatus[moduleName] = {
                        status: isAvailable ? 'OK' : 'ERROR',
                        message: isAvailable ? 'متاح' : 'غير متاح'
                    };

                    if (!isAvailable) {
                        this.errors.push(`الوحدة ${moduleName} غير متاحة`);
                    }
                });
            }

            async checkDatabase() {
                console.log('🗄️ فحص قاعدة البيانات...');

                try {
                    if (window.UnifiedStorage) {
                        await window.UnifiedStorage.init();
                        this.healthStatus.Database = {
                            status: 'OK',
                            message: 'قاعدة البيانات تعمل بشكل صحيح'
                        };
                    } else {
                        throw new Error('UnifiedStorage غير متاح');
                    }
                } catch (error) {
                    this.healthStatus.Database = {
                        status: 'ERROR',
                        message: `خطأ في قاعدة البيانات: ${error.message}`
                    };
                    this.errors.push(`خطأ في قاعدة البيانات: ${error.message}`);
                }
            }

            async checkPerformance() {
                console.log('⚡ فحص الأداء...');

                try {
                    const startTime = performance.now();

                    // محاكاة عملية
                    await new Promise(resolve => setTimeout(resolve, 100));

                    const endTime = performance.now();
                    const responseTime = endTime - startTime;

                    this.healthStatus.Performance = {
                        status: responseTime < 200 ? 'OK' : 'WARNING',
                        message: `وقت الاستجابة: ${responseTime.toFixed(2)}ms`,
                        responseTime: responseTime
                    };
                } catch (error) {
                    this.healthStatus.Performance = {
                        status: 'ERROR',
                        message: `خطأ في فحص الأداء: ${error.message}`
                    };
                    this.errors.push(`خطأ في فحص الأداء: ${error.message}`);
                }
            }

            generateHealthReport() {
                const totalChecks = Object.keys(this.healthStatus).length;
                const okChecks = Object.values(this.healthStatus).filter(status => status.status === 'OK').length;
                const errorChecks = Object.values(this.healthStatus).filter(status => status.status === 'ERROR').length;

                console.log(`📊 تقرير صحة النظام:`);
                console.log(`✅ فحوصات ناجحة: ${okChecks}/${totalChecks}`);
                console.log(`❌ فحوصات فاشلة: ${errorChecks}/${totalChecks}`);

                if (this.errors.length > 0) {
                    console.log('🚨 الأخطاء المكتشفة:');
                    this.errors.forEach(error => console.log(`  - ${error}`));
                }
            }

            getHealthSummary() {
                const totalChecks = Object.keys(this.healthStatus).length;
                const okChecks = Object.values(this.healthStatus).filter(status => status.status === 'OK').length;

                return {
                    total: totalChecks,
                    ok: okChecks,
                    errors: this.errors.length,
                    healthPercentage: Math.round((okChecks / totalChecks) * 100),
                    status: this.healthStatus,
                    errors: this.errors
                };
            }
        }

        // نظام معالجة الأخطاء المتقدم
        class ErrorHandler {
            constructor() {
                this.errors = [];
                this.setupGlobalErrorHandling();
            }

            setupGlobalErrorHandling() {
                // معالجة أخطاء JavaScript
                window.addEventListener('error', (event) => {
                    this.logError('JavaScript Error', event.error, {
                        filename: event.filename,
                        lineno: event.lineno,
                        colno: event.colno
                    });
                });

                // معالجة أخطاء Promise
                window.addEventListener('unhandledrejection', (event) => {
                    this.logError('Unhandled Promise Rejection', event.reason);
                });
            }

            logError(type, error, details = {}) {
                const errorInfo = {
                    type: type,
                    message: error?.message || error,
                    stack: error?.stack,
                    timestamp: new Date().toISOString(),
                    details: details,
                    userAgent: navigator.userAgent,
                    url: window.location.href
                };

                this.errors.push(errorInfo);

                console.error(`🚨 ${type}:`, errorInfo);

                // إرسال إلى نظام المراقبة إذا كان متاحاً
                if (window.PerformanceDashboard) {
                    window.PerformanceDashboard.reportError(errorInfo);
                }
            }

            getErrorReport() {
                return {
                    totalErrors: this.errors.length,
                    recentErrors: this.errors.slice(-10),
                    errorsByType: this.groupErrorsByType(),
                    timestamp: new Date().toISOString()
                };
            }

            groupErrorsByType() {
                const grouped = {};
                this.errors.forEach(error => {
                    grouped[error.type] = (grouped[error.type] || 0) + 1;
                });
                return grouped;
            }

            clearErrors() {
                this.errors = [];
                console.log('🧹 تم مسح سجل الأخطاء');
            }
        }

        // إنشاء مثيلات النظام
        window.SystemHealthChecker = new SystemHealthChecker();
        window.ErrorHandler = new ErrorHandler();

        // دالة التنقل بين الصفحات
        function showPage(pageId) {
            try {
                console.log(`🔄 Navigating to page: ${pageId}`);

                // إخفاء جميع الصفحات
                document.querySelectorAll('.page').forEach(page => {
                    page.classList.remove('active');
                });

                // إزالة active من جميع روابط التنقل
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.classList.remove('active');
                });

                // إظهار الصفحة المطلوبة
                const targetPage = document.getElementById(pageId);
                if (targetPage) {
                    targetPage.classList.add('active');
                    console.log(`✅ Page ${pageId} activated`);
                } else {
                    console.error(`❌ Page ${pageId} not found`);
                    return;
                }

                // تفعيل رابط التنقل
                const activeLink = document.querySelector(`[onclick="showPage('${pageId}')"]`);
                if (activeLink) {
                    activeLink.classList.add('active');
                }

                // تحديث محتوى الصفحة إذا لزم الأمر
                loadPageContent(pageId);

                // إضافة تأثير انتقال سلس
                targetPage.style.opacity = '0';
                targetPage.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    targetPage.style.transition = 'all 0.3s ease';
                    targetPage.style.opacity = '1';
                    targetPage.style.transform = 'translateY(0)';
                }, 50);

            } catch (error) {
                console.error('❌ Error in showPage:', error);
                if (window.ErrorHandler) {
                    window.ErrorHandler.logError('Navigation Error', error);
                }
            }
        }
        
        // تحميل محتوى الصفحة
        function loadPageContent(pageId) {
            try {
                console.log(`📄 Loading content for page: ${pageId}`);

                const contentDiv = document.getElementById(`${pageId}-content`);
                if (!contentDiv && pageId !== 'dashboard') {
                    console.warn(`⚠️ Content div not found for page: ${pageId}`);
                    return;
                }

                // عرض رسالة تحميل
                if (contentDiv) {
                    contentDiv.innerHTML = `
                        <div class="text-center p-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                            <p class="mt-2 text-muted">جاري تحميل ${getPageTitle(pageId)}...</p>
                        </div>
                    `;
                }

                // استخدام المدراء المناسبين لكل صفحة
                switch (pageId) {
                    case 'dashboard':
                        loadDashboardData();
                        break;
                    case 'products':
                        if (window.ProductsManager) {
                            window.ProductsManager.loadProductsPage();
                        } else {
                            showModuleNotAvailable(contentDiv, 'إدارة المنتجات', 'ProductsManager');
                        }
                        break;
                    case 'customers':
                        if (window.CustomersManager) {
                            window.CustomersManager.loadCustomersPage();
                        } else {
                            showModuleNotAvailable(contentDiv, 'إدارة العملاء', 'CustomersManager');
                        }
                        break;
                    case 'sales':
                        if (window.SalesManager) {
                            window.SalesManager.loadSalesPage();
                        } else {
                            showModuleNotAvailable(contentDiv, 'نقطة البيع', 'SalesManager');
                        }
                        break;
                    case 'reports':
                        if (window.ReportsManager) {
                            window.ReportsManager.loadReportsPage();
                        } else {
                            showModuleNotAvailable(contentDiv, 'التقارير', 'ReportsManager');
                        }
                        break;
                    case 'expenses':
                        if (window.ExpensesManager) {
                            window.ExpensesManager.loadExpensesPage();
                        } else {
                            showModuleNotAvailable(contentDiv, 'إدارة المصروفات', 'ExpensesManager');
                        }
                        break;
                    case 'settings':
                        loadSettingsPage(contentDiv);
                        break;
                    default:
                        if (contentDiv) {
                            contentDiv.innerHTML = `
                                <div class="alert alert-warning text-center">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    الصفحة المطلوبة غير متاحة حالياً
                                </div>
                            `;
                        }
                }

            } catch (error) {
                console.error(`❌ Error loading page content for ${pageId}:`, error);
                if (window.ErrorHandler) {
                    window.ErrorHandler.logError('Page Content Loading Error', error, { pageId });
                }
            }
        }

        // الحصول على عنوان الصفحة
        function getPageTitle(pageId) {
            const titles = {
                'dashboard': 'لوحة التحكم',
                'products': 'المنتجات',
                'customers': 'العملاء',
                'sales': 'المبيعات',
                'reports': 'التقارير',
                'expenses': 'المصروفات',
                'settings': 'الإعدادات'
            };
            return titles[pageId] || 'المحتوى';
        }

        // عرض رسالة عدم توفر الوحدة
        function showModuleNotAvailable(contentDiv, moduleName, managerName) {
            if (!contentDiv) return;

            contentDiv.innerHTML = `
                <div class="alert alert-warning text-center">
                    <i class="fas fa-exclamation-triangle fa-2x mb-3 text-warning"></i>
                    <h5>وحدة ${moduleName} غير متاحة</h5>
                    <p class="mb-3">الوحدة ${managerName} لم يتم تحميلها بعد.</p>
                    <button class="btn btn-primary" onclick="retryLoadModule('${managerName}', '${moduleName}')">
                        <i class="fas fa-redo me-2"></i>
                        إعادة المحاولة
                    </button>
                </div>
            `;
        }

        // إعادة محاولة تحميل الوحدة
        function retryLoadModule(managerName, moduleName) {
            console.log(`🔄 Retrying to load ${managerName}...`);

            if (window.Utils) {
                window.Utils.showMessage(`جاري إعادة تحميل وحدة ${moduleName}...`, 'info');
            }

            // إعادة تحميل الصفحة بعد ثانيتين
            setTimeout(() => {
                location.reload();
            }, 2000);
        }
        
        // تحميل بيانات لوحة التحكم
        async function loadDashboardData() {
            try {
                if (window.UnifiedStorage) {
                    const products = await window.UnifiedStorage.load('products') || [];
                    const customers = await window.UnifiedStorage.load('customers') || [];
                    const sales = await window.UnifiedStorage.load('sales') || [];
                    
                    // تحديث الإحصائيات
                    document.getElementById('total-products').textContent = products.length;
                    document.getElementById('total-customers').textContent = customers.length;
                    
                    // حساب إجمالي المبيعات
                    const totalSales = sales.reduce((sum, sale) => sum + (parseFloat(sale.total) || 0), 0);
                    document.getElementById('total-sales').textContent = Utils.formatCurrency(totalSales);
                    
                    // حساب معاملات اليوم
                    const today = new Date().toDateString();
                    const todayTransactions = sales.filter(sale => 
                        new Date(sale.date).toDateString() === today
                    ).length;
                    document.getElementById('today-transactions').textContent = todayTransactions;
                }
            } catch (error) {
                console.error('❌ Error loading dashboard data:', error);
            }
        }
        
        // دوال النماذج المحسنة
        function showAddProductModal() {
            try {
                console.log('🛍️ Opening add product modal...');

                if (window.ProductsManager && window.ProductsManager.showAddProductModal) {
                    window.ProductsManager.showAddProductModal();
                } else {
                    // إنشاء نموذج مؤقت
                    createTemporaryModal('إضافة منتج جديد', `
                        <form id="addProductForm">
                            <div class="mb-3">
                                <label class="form-label">اسم المنتج</label>
                                <input type="text" class="form-control" id="productName" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">السعر</label>
                                <input type="number" class="form-control" id="productPrice" step="0.01" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الكمية</label>
                                <input type="number" class="form-control" id="productQuantity" required>
                            </div>
                        </form>
                    `, 'حفظ المنتج', saveTemporaryProduct);
                }
            } catch (error) {
                console.error('❌ Error opening add product modal:', error);
                if (window.Utils) {
                    window.Utils.showMessage('خطأ في فتح نموذج إضافة المنتج', 'error');
                }
            }
        }

        function showAddCustomerModal() {
            try {
                console.log('👤 Opening add customer modal...');

                if (window.CustomersManager && window.CustomersManager.showAddCustomerModal) {
                    window.CustomersManager.showAddCustomerModal();
                } else {
                    createTemporaryModal('إضافة عميل جديد', `
                        <form id="addCustomerForm">
                            <div class="mb-3">
                                <label class="form-label">اسم العميل</label>
                                <input type="text" class="form-control" id="customerName" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="customerPhone">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">العنوان</label>
                                <textarea class="form-control" id="customerAddress" rows="2"></textarea>
                            </div>
                        </form>
                    `, 'حفظ العميل', saveTemporaryCustomer);
                }
            } catch (error) {
                console.error('❌ Error opening add customer modal:', error);
                if (window.Utils) {
                    window.Utils.showMessage('خطأ في فتح نموذج إضافة العميل', 'error');
                }
            }
        }

        function showAddExpenseModal() {
            try {
                console.log('💰 Opening add expense modal...');

                if (window.ExpensesManager && window.ExpensesManager.showAddExpenseModal) {
                    window.ExpensesManager.showAddExpenseModal();
                } else {
                    createTemporaryModal('إضافة مصروف جديد', `
                        <form id="addExpenseForm">
                            <div class="mb-3">
                                <label class="form-label">وصف المصروف</label>
                                <input type="text" class="form-control" id="expenseDescription" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">المبلغ</label>
                                <input type="number" class="form-control" id="expenseAmount" step="0.01" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">التاريخ</label>
                                <input type="date" class="form-control" id="expenseDate" required>
                            </div>
                        </form>
                    `, 'حفظ المصروف', saveTemporaryExpense);
                }
            } catch (error) {
                console.error('❌ Error opening add expense modal:', error);
                if (window.Utils) {
                    window.Utils.showMessage('خطأ في فتح نموذج إضافة المصروف', 'error');
                }
            }
        }

        // إنشاء نموذج مؤقت
        function createTemporaryModal(title, content, buttonText, saveFunction) {
            const modalId = 'temporaryModal';

            // إزالة النموذج السابق إن وجد
            const existingModal = document.getElementById(modalId);
            if (existingModal) {
                existingModal.remove();
            }

            const modalHTML = `
                <div class="modal fade" id="${modalId}" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">${title}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                ${content}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-primary" onclick="${saveFunction.name}()">${buttonText}</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHTML);

            // عرض النموذج
            const modal = new bootstrap.Modal(document.getElementById(modalId));
            modal.show();

            // إزالة النموذج عند الإغلاق
            document.getElementById(modalId).addEventListener('hidden.bs.modal', function() {
                this.remove();
            });
        }

        // دوال الحفظ المؤقتة
        async function saveTemporaryProduct() {
            try {
                const name = document.getElementById('productName').value;
                const price = parseFloat(document.getElementById('productPrice').value);
                const quantity = parseInt(document.getElementById('productQuantity').value);

                if (!name || !price || !quantity) {
                    if (window.Utils) {
                        window.Utils.showMessage('يرجى ملء جميع الحقول المطلوبة', 'warning');
                    }
                    return;
                }

                const product = {
                    id: Date.now().toString(),
                    name: name,
                    price: price,
                    quantity: quantity,
                    createdAt: new Date().toISOString()
                };

                if (window.UnifiedStorage) {
                    const products = await window.UnifiedStorage.load('products', []);
                    products.push(product);
                    await window.UnifiedStorage.save('products', products);

                    if (window.Utils) {
                        window.Utils.showMessage('تم إضافة المنتج بنجاح', 'success');
                    }

                    // إغلاق النموذج
                    const modal = bootstrap.Modal.getInstance(document.getElementById('temporaryModal'));
                    if (modal) modal.hide();

                    // تحديث الصفحة إذا كانت صفحة المنتجات مفتوحة
                    if (document.getElementById('products').classList.contains('active')) {
                        loadPageContent('products');
                    }

                    // تحديث لوحة التحكم
                    loadDashboardData();
                }

            } catch (error) {
                console.error('❌ Error saving product:', error);
                if (window.Utils) {
                    window.Utils.showMessage('خطأ في حفظ المنتج', 'error');
                }
            }
        }

        async function saveTemporaryCustomer() {
            try {
                const name = document.getElementById('customerName').value;
                const phone = document.getElementById('customerPhone').value;
                const address = document.getElementById('customerAddress').value;

                if (!name) {
                    if (window.Utils) {
                        window.Utils.showMessage('يرجى إدخال اسم العميل', 'warning');
                    }
                    return;
                }

                const customer = {
                    id: Date.now().toString(),
                    name: name,
                    phone: phone || '',
                    address: address || '',
                    createdAt: new Date().toISOString()
                };

                if (window.UnifiedStorage) {
                    const customers = await window.UnifiedStorage.load('customers', []);
                    customers.push(customer);
                    await window.UnifiedStorage.save('customers', customers);

                    if (window.Utils) {
                        window.Utils.showMessage('تم إضافة العميل بنجاح', 'success');
                    }

                    // إغلاق النموذج
                    const modal = bootstrap.Modal.getInstance(document.getElementById('temporaryModal'));
                    if (modal) modal.hide();

                    // تحديث الصفحة إذا كانت صفحة العملاء مفتوحة
                    if (document.getElementById('customers').classList.contains('active')) {
                        loadPageContent('customers');
                    }

                    // تحديث لوحة التحكم
                    loadDashboardData();
                }

            } catch (error) {
                console.error('❌ Error saving customer:', error);
                if (window.Utils) {
                    window.Utils.showMessage('خطأ في حفظ العميل', 'error');
                }
            }
        }

        async function saveTemporaryExpense() {
            try {
                const description = document.getElementById('expenseDescription').value;
                const amount = parseFloat(document.getElementById('expenseAmount').value);
                const date = document.getElementById('expenseDate').value;

                if (!description || !amount || !date) {
                    if (window.Utils) {
                        window.Utils.showMessage('يرجى ملء جميع الحقول المطلوبة', 'warning');
                    }
                    return;
                }

                const expense = {
                    id: Date.now().toString(),
                    description: description,
                    amount: amount,
                    date: date,
                    createdAt: new Date().toISOString()
                };

                if (window.UnifiedStorage) {
                    const expenses = await window.UnifiedStorage.load('expenses', []);
                    expenses.push(expense);
                    await window.UnifiedStorage.save('expenses', expenses);

                    if (window.Utils) {
                        window.Utils.showMessage('تم إضافة المصروف بنجاح', 'success');
                    }

                    // إغلاق النموذج
                    const modal = bootstrap.Modal.getInstance(document.getElementById('temporaryModal'));
                    if (modal) modal.hide();

                    // تحديث الصفحة إذا كانت صفحة المصروفات مفتوحة
                    if (document.getElementById('expenses').classList.contains('active')) {
                        loadPageContent('expenses');
                    }
                }

            } catch (error) {
                console.error('❌ Error saving expense:', error);
                if (window.Utils) {
                    window.Utils.showMessage('خطأ في حفظ المصروف', 'error');
                }
            }
        }

        // تحميل صفحة الإعدادات
        function loadSettingsPage(contentDiv) {
            if (!contentDiv) return;

            contentDiv.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-palette me-2"></i>إعدادات المظهر</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">نمط المظهر</label>
                                    <select class="form-select" id="themeSelect" onchange="changeTheme()">
                                        <option value="light">فاتح</option>
                                        <option value="dark">داكن</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">حجم الخط</label>
                                    <select class="form-select" id="fontSizeSelect" onchange="changeFontSize()">
                                        <option value="small">صغير</option>
                                        <option value="medium" selected>متوسط</option>
                                        <option value="large">كبير</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-database me-2"></i>إدارة البيانات</h5>
                            </div>
                            <div class="card-body">
                                <button class="btn btn-primary me-2 mb-2" onclick="exportData()">
                                    <i class="fas fa-download me-2"></i>تصدير البيانات
                                </button>
                                <button class="btn btn-warning me-2 mb-2" onclick="importData()">
                                    <i class="fas fa-upload me-2"></i>استيراد البيانات
                                </button>
                                <button class="btn btn-info me-2 mb-2" onclick="backupData()">
                                    <i class="fas fa-save me-2"></i>نسخ احتياطي
                                </button>
                                <button class="btn btn-danger mb-2" onclick="clearAllData()">
                                    <i class="fas fa-trash me-2"></i>مسح جميع البيانات
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-cogs me-2"></i>إعدادات النظام</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">اسم المتجر</label>
                                    <input type="text" class="form-control" id="storeName" placeholder="اسم المتجر">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">عملة النظام</label>
                                    <select class="form-select" id="currencySelect">
                                        <option value="EGP" selected>جنيه مصري (ج.م)</option>
                                        <option value="USD">دولار أمريكي ($)</option>
                                        <option value="EUR">يورو (€)</option>
                                        <option value="SAR">ريال سعودي (ر.س)</option>
                                    </select>
                                </div>
                                <button class="btn btn-success" onclick="saveSystemSettings()">
                                    <i class="fas fa-save me-2"></i>حفظ الإعدادات
                                </button>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-info-circle me-2"></i>معلومات النظام</h5>
                            </div>
                            <div class="card-body">
                                <p><strong>إصدار النظام:</strong> 2.0.0 (محسن)</p>
                                <p><strong>تاريخ آخر تحديث:</strong> ${new Date().toLocaleDateString('ar-EG')}</p>
                                <p><strong>حالة النظام:</strong> <span class="badge bg-success">يعمل بشكل طبيعي</span></p>
                                <button class="btn btn-info" onclick="runSystemDiagnostics()">
                                    <i class="fas fa-stethoscope me-2"></i>تشخيص النظام
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // دالة التهيئة الشاملة للنظام
        async function initializeSystem() {
            try {
                console.log('🚀 بدء تهيئة نظام Black Horse POS...');

                // عرض شاشة التحميل
                showLoadingScreen();

                // فحص صحة النظام
                const healthCheck = await window.SystemHealthChecker.checkSystemHealth();
                const healthSummary = window.SystemHealthChecker.getHealthSummary();

                console.log(`📊 صحة النظام: ${healthSummary.healthPercentage}%`);

                // إذا كانت هناك أخطاء حرجة، عرض تحذير
                if (healthSummary.errors > 0) {
                    console.warn('⚠️ تم اكتشاف بعض المشاكل في النظام');
                    if (window.Utils) {
                        window.Utils.showMessage(
                            `تم اكتشاف ${healthSummary.errors} مشكلة في النظام. يرجى مراجعة وحدة التحكم للتفاصيل.`,
                            'warning'
                        );
                    }
                }

                // تهيئة مدير التطبيق
                if (window.AppInitializer) {
                    await window.AppInitializer.initialize();
                } else {
                    console.error('❌ AppInitializer غير متاح');
                }

                // تهيئة محسن الأصول
                if (window.AssetOptimizer) {
                    await window.AssetOptimizer.initialize();
                    console.log('✅ تم تهيئة محسن الأصول');
                }

                // تهيئة محسن الأداء
                if (window.PerformanceOptimizer) {
                    await window.PerformanceOptimizer.initialize();
                    console.log('✅ تم تهيئة محسن الأداء');
                }

                // تهيئة لوحة مراقبة الأداء
                if (window.PerformanceDashboard) {
                    window.PerformanceDashboard.initialize();
                    console.log('✅ تم تهيئة لوحة مراقبة الأداء');
                }

                // تحميل البيانات الأولية
                await loadDashboardData();

                // إخفاء شاشة التحميل وعرض التطبيق
                hideLoadingScreen();
                showMainApplication();

                console.log('🎉 تم تهيئة النظام بنجاح!');

                // عرض رسالة ترحيب
                if (window.Utils) {
                    setTimeout(() => {
                        window.Utils.showMessage(
                            `مرحباً بك في Black Horse POS المحسن! صحة النظام: ${healthSummary.healthPercentage}%`,
                            'success'
                        );
                    }, 1000);
                }

            } catch (error) {
                console.error('❌ خطأ في تهيئة النظام:', error);
                window.ErrorHandler.logError('System Initialization Error', error);

                // عرض رسالة خطأ للمستخدم
                hideLoadingScreen();
                showErrorScreen(error);
            }
        }

        // عرض شاشة التحميل
        function showLoadingScreen() {
            const loadingHTML = `
                <div id="loading-screen" style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                    color: white;
                    font-family: 'Cairo', sans-serif;
                ">
                    <div style="text-align: center;">
                        <div style="
                            width: 80px;
                            height: 80px;
                            border: 4px solid rgba(255,255,255,0.3);
                            border-top: 4px solid white;
                            border-radius: 50%;
                            animation: spin 1s linear infinite;
                            margin: 0 auto 20px;
                        "></div>
                        <h2 style="margin: 0 0 10px 0;">🐎 Black Horse POS</h2>
                        <p style="margin: 0; opacity: 0.8;">جاري تحميل النظام المحسن...</p>
                        <div id="loading-progress" style="
                            width: 300px;
                            height: 4px;
                            background: rgba(255,255,255,0.3);
                            border-radius: 2px;
                            margin: 20px auto;
                            overflow: hidden;
                        ">
                            <div style="
                                width: 0%;
                                height: 100%;
                                background: white;
                                border-radius: 2px;
                                animation: progress 3s ease-in-out infinite;
                            "></div>
                        </div>
                    </div>
                    <style>
                        @keyframes spin {
                            0% { transform: rotate(0deg); }
                            100% { transform: rotate(360deg); }
                        }
                        @keyframes progress {
                            0% { width: 0%; }
                            50% { width: 70%; }
                            100% { width: 100%; }
                        }
                    </style>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', loadingHTML);
        }

        // إخفاء شاشة التحميل
        function hideLoadingScreen() {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                loadingScreen.style.opacity = '0';
                loadingScreen.style.transition = 'opacity 0.5s ease-out';
                setTimeout(() => {
                    loadingScreen.remove();
                }, 500);
            }
        }

        // عرض التطبيق الرئيسي
        function showMainApplication() {
            const mainContainer = document.querySelector('.main-container');
            if (mainContainer) {
                mainContainer.style.display = 'flex';
                mainContainer.style.opacity = '0';
                mainContainer.style.transition = 'opacity 0.5s ease-in';
                setTimeout(() => {
                    mainContainer.style.opacity = '1';
                }, 100);
            }
        }

        // عرض شاشة الخطأ
        function showErrorScreen(error) {
            const errorHTML = `
                <div id="error-screen" style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                    color: white;
                    font-family: 'Cairo', sans-serif;
                    text-align: center;
                    padding: 20px;
                ">
                    <div style="max-width: 600px;">
                        <h1 style="font-size: 4rem; margin: 0 0 20px 0;">⚠️</h1>
                        <h2 style="margin: 0 0 20px 0;">خطأ في تهيئة النظام</h2>
                        <p style="margin: 0 0 20px 0; opacity: 0.9;">
                            عذراً، حدث خطأ أثناء تحميل النظام. يرجى إعادة تحميل الصفحة أو الاتصال بالدعم التقني.
                        </p>
                        <div style="
                            background: rgba(255,255,255,0.1);
                            padding: 15px;
                            border-radius: 8px;
                            margin: 20px 0;
                            text-align: right;
                            font-family: monospace;
                            font-size: 0.9rem;
                        ">
                            ${error.message || error}
                        </div>
                        <button onclick="location.reload()" style="
                            background: white;
                            color: #ee5a24;
                            border: none;
                            padding: 12px 24px;
                            border-radius: 6px;
                            font-family: 'Cairo', sans-serif;
                            font-weight: 600;
                            cursor: pointer;
                            font-size: 1rem;
                        ">
                            🔄 إعادة تحميل الصفحة
                        </button>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', errorHTML);
        }

        // دوال الإعدادات
        function changeTheme() {
            const theme = document.getElementById('themeSelect').value;
            document.body.className = theme === 'dark' ? 'theme-dark' : 'theme-light';
            localStorage.setItem('theme', theme);

            if (window.Utils) {
                window.Utils.showMessage(`تم تغيير المظهر إلى ${theme === 'dark' ? 'الداكن' : 'الفاتح'}`, 'success');
            }
        }

        function changeFontSize() {
            const fontSize = document.getElementById('fontSizeSelect').value;
            const sizes = { small: '14px', medium: '16px', large: '18px' };
            document.body.style.fontSize = sizes[fontSize];
            localStorage.setItem('fontSize', fontSize);

            if (window.Utils) {
                window.Utils.showMessage('تم تغيير حجم الخط', 'success');
            }
        }

        async function exportData() {
            try {
                if (!window.UnifiedStorage) {
                    throw new Error('نظام التخزين غير متاح');
                }

                const data = {
                    products: await window.UnifiedStorage.load('products', []),
                    customers: await window.UnifiedStorage.load('customers', []),
                    sales: await window.UnifiedStorage.load('sales', []),
                    expenses: await window.UnifiedStorage.load('expenses', []),
                    exportDate: new Date().toISOString(),
                    version: '2.0.0'
                };

                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `black-horse-pos-backup-${new Date().toISOString().split('T')[0]}.json`;
                a.click();
                URL.revokeObjectURL(url);

                if (window.Utils) {
                    window.Utils.showMessage('تم تصدير البيانات بنجاح', 'success');
                }

            } catch (error) {
                console.error('❌ Error exporting data:', error);
                if (window.Utils) {
                    window.Utils.showMessage('خطأ في تصدير البيانات', 'error');
                }
            }
        }

        function importData() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = async (e) => {
                try {
                    const file = e.target.files[0];
                    if (!file) return;

                    const text = await file.text();
                    const data = JSON.parse(text);

                    if (!data.products || !data.customers || !data.sales) {
                        throw new Error('ملف البيانات غير صحيح');
                    }

                    if (window.UnifiedStorage) {
                        await window.UnifiedStorage.save('products', data.products);
                        await window.UnifiedStorage.save('customers', data.customers);
                        await window.UnifiedStorage.save('sales', data.sales);
                        if (data.expenses) {
                            await window.UnifiedStorage.save('expenses', data.expenses);
                        }

                        if (window.Utils) {
                            window.Utils.showMessage('تم استيراد البيانات بنجاح', 'success');
                        }

                        // تحديث لوحة التحكم
                        loadDashboardData();
                    }

                } catch (error) {
                    console.error('❌ Error importing data:', error);
                    if (window.Utils) {
                        window.Utils.showMessage('خطأ في استيراد البيانات', 'error');
                    }
                }
            };
            input.click();
        }

        async function backupData() {
            try {
                await exportData();
                if (window.Utils) {
                    window.Utils.showMessage('تم إنشاء النسخة الاحتياطية', 'success');
                }
            } catch (error) {
                console.error('❌ Error creating backup:', error);
                if (window.Utils) {
                    window.Utils.showMessage('خطأ في إنشاء النسخة الاحتياطية', 'error');
                }
            }
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                if (confirm('تأكيد أخير: سيتم مسح جميع المنتجات والعملاء والمبيعات والمصروفات!')) {
                    try {
                        localStorage.clear();
                        if (window.Utils) {
                            window.Utils.showMessage('تم مسح جميع البيانات', 'warning');
                        }
                        setTimeout(() => location.reload(), 2000);
                    } catch (error) {
                        console.error('❌ Error clearing data:', error);
                        if (window.Utils) {
                            window.Utils.showMessage('خطأ في مسح البيانات', 'error');
                        }
                    }
                }
            }
        }

        function saveSystemSettings() {
            try {
                const storeName = document.getElementById('storeName').value;
                const currency = document.getElementById('currencySelect').value;

                const settings = {
                    storeName: storeName || 'Black Horse POS',
                    currency: currency,
                    updatedAt: new Date().toISOString()
                };

                localStorage.setItem('systemSettings', JSON.stringify(settings));

                if (window.Utils) {
                    window.Utils.showMessage('تم حفظ إعدادات النظام', 'success');
                }

            } catch (error) {
                console.error('❌ Error saving settings:', error);
                if (window.Utils) {
                    window.Utils.showMessage('خطأ في حفظ الإعدادات', 'error');
                }
            }
        }

        async function runSystemDiagnostics() {
            try {
                if (window.Utils) {
                    window.Utils.showMessage('جاري تشغيل تشخيص النظام...', 'info');
                }

                if (window.SystemHealthChecker) {
                    const healthCheck = await window.SystemHealthChecker.checkSystemHealth();
                    const summary = window.SystemHealthChecker.getHealthSummary();

                    const message = `
                        تشخيص النظام مكتمل:
                        - صحة النظام: ${summary.healthPercentage}%
                        - الفحوصات الناجحة: ${summary.ok}/${summary.total}
                        - الأخطاء: ${summary.errors}
                    `;

                    if (window.Utils) {
                        window.Utils.showMessage(message, summary.healthPercentage > 80 ? 'success' : 'warning');
                    }
                } else {
                    if (window.Utils) {
                        window.Utils.showMessage('نظام التشخيص غير متاح', 'warning');
                    }
                }

            } catch (error) {
                console.error('❌ Error running diagnostics:', error);
                if (window.Utils) {
                    window.Utils.showMessage('خطأ في تشغيل التشخيص', 'error');
                }
            }
        }

        // تحميل الإعدادات المحفوظة عند بدء التشغيل
        function loadSavedSettings() {
            try {
                // تحميل المظهر
                const savedTheme = localStorage.getItem('theme') || 'light';
                document.body.className = savedTheme === 'dark' ? 'theme-dark' : 'theme-light';

                // تحميل حجم الخط
                const savedFontSize = localStorage.getItem('fontSize') || 'medium';
                const sizes = { small: '14px', medium: '16px', large: '18px' };
                document.body.style.fontSize = sizes[savedFontSize];

                // تحميل إعدادات النظام
                const systemSettings = localStorage.getItem('systemSettings');
                if (systemSettings) {
                    const settings = JSON.parse(systemSettings);
                    // يمكن استخدام هذه الإعدادات في أجزاء أخرى من النظام
                    window.systemSettings = settings;
                }

            } catch (error) {
                console.error('❌ Error loading saved settings:', error);
            }
        }

        // بدء التهيئة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            loadSavedSettings();
            initializeSystem();
        });
    </script>
</body>
</html>
