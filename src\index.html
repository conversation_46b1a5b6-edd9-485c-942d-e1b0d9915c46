<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Black Horse POS - نظام نقاط البيع</title>
    
    <!-- الخطوط -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- الأيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- الأنماط المخصصة -->
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            direction: rtl;
        }
        
        .main-container {
            display: none;
            min-height: 100vh;
        }
        
        .sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .content-area {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            min-height: 100vh;
            padding: 20px;
        }
        
        .nav-link {
            color: #333;
            padding: 15px 20px;
            margin: 5px 0;
            border-radius: 10px;
            transition: all 0.3s;
        }
        
        .nav-link:hover, .nav-link.active {
            background: #007bff;
            color: white;
            transform: translateX(-5px);
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.9);
        }
        
        .btn {
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
        
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
        }
        
        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .page {
            display: none;
        }
        
        .page.active {
            display: block;
        }

        /* أنماط إضافية للصفحات الجديدة */
        .avatar-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
        }

        .permission-group {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }

        .permission-group h6 {
            color: #495057;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .permissions-grid {
            max-height: 400px;
            overflow-y: auto;
        }

        .form-check {
            margin-bottom: 8px;
        }

        .table th {
            background-color: #343a40;
            color: white;
            border: none;
            font-weight: 600;
        }

        .badge {
            font-size: 0.75em;
            padding: 0.375rem 0.75rem;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        /* أنماط القوائم المنسدلة في الشريط الجانبي */
        .nav-item.dropdown {
            margin-bottom: 5px;
        }

        .nav-link.dropdown-toggle {
            position: relative;
            padding: 10px 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
            color: #6c757d;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .nav-link.dropdown-toggle:hover {
            background-color: #f8f9fa;
            color: #495057;
        }

        .nav-link.dropdown-toggle::after {
            content: '\f107';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            border: none;
            font-size: 12px;
            transition: transform 0.3s ease;
        }

        .nav-link.dropdown-toggle[aria-expanded="true"]::after {
            transform: rotate(180deg);
        }

        .nav-submenu {
            padding: 5px 0 10px 20px;
            border-right: 2px solid #e9ecef;
            margin-right: 15px;
        }

        .submenu-link {
            padding: 8px 15px;
            font-size: 0.9em;
            color: #6c757d;
            border-radius: 6px;
            transition: all 0.3s ease;
            margin-bottom: 2px;
            display: block;
        }

        .submenu-link:hover {
            background-color: #e3f2fd;
            color: #1976d2;
            text-decoration: none;
            transform: translateX(-3px);
        }

        .submenu-link.active {
            background-color: #2196f3;
            color: white;
        }

        .nav-link.active {
            background-color: #007bff;
            color: white;
        }

        .nav-link.active:hover {
            background-color: #0056b3;
            color: white;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .quick-action {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .quick-action:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        
        .theme-light {
            /* الثيم الفاتح - افتراضي */
        }
        
        .theme-dark {
            background: #1a1a1a;
            color: white;
        }
        
        .theme-dark .card {
            background: rgba(40, 40, 40, 0.9);
            color: white;
        }
        
        .theme-dark .sidebar {
            background: rgba(30, 30, 30, 0.95);
            color: white;
        }
        
        .theme-dark .nav-link {
            color: #ccc;
        }
        
        .theme-dark .nav-link:hover, .theme-dark .nav-link.active {
            background: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <!-- شاشة التحميل -->
    <div id="loading-screen" class="loading-screen">
        <div class="text-center">
            <div class="loading-spinner"></div>
            <h2>🐎 Black Horse POS</h2>
            <p>جاري تحميل النظام...</p>
            <small>نظام نقاط البيع المتطور</small>
        </div>
    </div>

    <!-- التطبيق الرئيسي -->
    <div id="main-app" class="main-container">
        <div class="container-fluid">
            <div class="row">
                <!-- الشريط الجانبي -->
                <div class="col-md-3 col-lg-2 sidebar">
                    <div class="p-3">
                        <h4 class="text-center mb-4">
                            🐎 Black Horse POS
                        </h4>
                        
                        <nav class="nav flex-column">
                            <!-- لوحة التحكم -->
                            <a href="#" class="nav-link active" onclick="showPage('dashboard')">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>

                            <!-- إدارة المخزون -->
                            <div class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#inventoryMenu" aria-expanded="false">
                                    <i class="fas fa-warehouse me-2"></i>
                                    إدارة المخزون
                                </a>
                                <div class="collapse" id="inventoryMenu">
                                    <div class="nav-submenu">
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('products')">
                                            <i class="fas fa-box me-2"></i>
                                            المنتجات
                                        </a>
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('categories')">
                                            <i class="fas fa-tags me-2"></i>
                                            الفئات
                                        </a>
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('inventory')">
                                            <i class="fas fa-clipboard-list me-2"></i>
                                            حركة المخزون
                                        </a>
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('stock-alerts')">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            تنبيهات المخزون
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- المبيعات -->
                            <div class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#salesMenu" aria-expanded="false">
                                    <i class="fas fa-shopping-cart me-2"></i>
                                    المبيعات
                                </a>
                                <div class="collapse" id="salesMenu">
                                    <div class="nav-submenu">
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('sales')">
                                            <i class="fas fa-cash-register me-2"></i>
                                            نقطة البيع
                                        </a>
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('sales-history')">
                                            <i class="fas fa-history me-2"></i>
                                            تاريخ المبيعات
                                        </a>
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('invoices')">
                                            <i class="fas fa-file-invoice me-2"></i>
                                            الفواتير
                                        </a>
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('returns')">
                                            <i class="fas fa-undo me-2"></i>
                                            المرتجعات
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- إدارة العملاء -->
                            <div class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#customersMenu" aria-expanded="false">
                                    <i class="fas fa-users me-2"></i>
                                    إدارة العملاء
                                </a>
                                <div class="collapse" id="customersMenu">
                                    <div class="nav-submenu">
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('customers')">
                                            <i class="fas fa-user-friends me-2"></i>
                                            قائمة العملاء
                                        </a>
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('customer-groups')">
                                            <i class="fas fa-layer-group me-2"></i>
                                            مجموعات العملاء
                                        </a>
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('loyalty-program')">
                                            <i class="fas fa-star me-2"></i>
                                            برنامج الولاء
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- المشتريات والموردين -->
                            <div class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#purchasesMenu" aria-expanded="false">
                                    <i class="fas fa-truck me-2"></i>
                                    المشتريات والموردين
                                </a>
                                <div class="collapse" id="purchasesMenu">
                                    <div class="nav-submenu">
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('suppliers')">
                                            <i class="fas fa-industry me-2"></i>
                                            الموردين
                                        </a>
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('purchase-orders')">
                                            <i class="fas fa-shopping-bag me-2"></i>
                                            أوامر الشراء
                                        </a>
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('purchase-invoices')">
                                            <i class="fas fa-file-invoice-dollar me-2"></i>
                                            فواتير الشراء
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- التقارير والإحصائيات -->
                            <div class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#reportsMenu" aria-expanded="false">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    التقارير والإحصائيات
                                </a>
                                <div class="collapse" id="reportsMenu">
                                    <div class="nav-submenu">
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('reports')">
                                            <i class="fas fa-chart-line me-2"></i>
                                            تقارير المبيعات
                                        </a>
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('financial-reports')">
                                            <i class="fas fa-calculator me-2"></i>
                                            التقارير المالية
                                        </a>
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('inventory-reports')">
                                            <i class="fas fa-boxes me-2"></i>
                                            تقارير المخزون
                                        </a>
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('customer-reports')">
                                            <i class="fas fa-user-chart me-2"></i>
                                            تقارير العملاء
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- المحاسبة والمالية -->
                            <div class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#accountingMenu" aria-expanded="false">
                                    <i class="fas fa-money-bill-wave me-2"></i>
                                    المحاسبة والمالية
                                </a>
                                <div class="collapse" id="accountingMenu">
                                    <div class="nav-submenu">
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('expenses')">
                                            <i class="fas fa-receipt me-2"></i>
                                            المصروفات
                                        </a>
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('accounts')">
                                            <i class="fas fa-university me-2"></i>
                                            الحسابات
                                        </a>
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('payments')">
                                            <i class="fas fa-credit-card me-2"></i>
                                            المدفوعات
                                        </a>
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('taxes')">
                                            <i class="fas fa-percentage me-2"></i>
                                            الضرائب
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- إدارة النظام -->
                            <div class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#systemMenu" aria-expanded="false">
                                    <i class="fas fa-cogs me-2"></i>
                                    إدارة النظام
                                </a>
                                <div class="collapse" id="systemMenu">
                                    <div class="nav-submenu">
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('users')">
                                            <i class="fas fa-users-cog me-2"></i>
                                            إدارة المستخدمين
                                        </a>
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('permissions')">
                                            <i class="fas fa-shield-alt me-2"></i>
                                            الصلاحيات
                                        </a>
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('backup')">
                                            <i class="fas fa-database me-2"></i>
                                            النسخ الاحتياطي
                                        </a>
                                        <a href="#" class="nav-link submenu-link" onclick="showPage('settings')">
                                            <i class="fas fa-cog me-2"></i>
                                            الإعدادات العامة
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </nav>
                    </div>
                </div>

                <!-- المحتوى الرئيسي -->
                <div class="col-md-9 col-lg-10 content-area">
                    <!-- لوحة التحكم -->
                    <div id="dashboard" class="page active">
                        <h2 class="mb-4">لوحة التحكم</h2>
                        
                        <!-- الإحصائيات -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <h5>إجمالي المبيعات</h5>
                                    <h3 id="total-sales">0 ج.م</h3>
                                    <small>هذا الشهر</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <h5>عدد المنتجات</h5>
                                    <h3 id="total-products">0</h3>
                                    <small>في المخزون</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <h5>عدد العملاء</h5>
                                    <h3 id="total-customers">0</h3>
                                    <small>مسجل</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <h5>المعاملات اليوم</h5>
                                    <h3 id="today-transactions">0</h3>
                                    <small>معاملة</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- الإجراءات السريعة -->
                        <div class="row">
                            <div class="col-md-4">
                                <div class="quick-action" onclick="showPage('sales')">
                                    <i class="fas fa-plus-circle fa-3x text-primary mb-3"></i>
                                    <h5>بيع جديد</h5>
                                    <p>إنشاء فاتورة بيع جديدة</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="quick-action" onclick="showPage('products')">
                                    <i class="fas fa-box-open fa-3x text-success mb-3"></i>
                                    <h5>إضافة منتج</h5>
                                    <p>إضافة منتج جديد للمخزون</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="quick-action" onclick="showPage('reports')">
                                    <i class="fas fa-chart-line fa-3x text-info mb-3"></i>
                                    <h5>عرض التقارير</h5>
                                    <p>مراجعة تقارير المبيعات</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صفحة المنتجات -->
                    <div id="products" class="page">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2>إدارة المنتجات</h2>
                            <button class="btn btn-primary" onclick="showAddProductModal()">
                                <i class="fas fa-plus me-2"></i>
                                إضافة منتج جديد
                            </button>
                        </div>
                        
                        <div class="card">
                            <div class="card-body">
                                <div id="products-content">
                                    <!-- شريط البحث والفلترة -->
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i class="fas fa-search"></i>
                                                </span>
                                                <input type="text" class="form-control" id="searchProducts" placeholder="البحث في المنتجات...">
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <select class="form-select" id="categoryFilter">
                                                <option value="">جميع الفئات</option>
                                                <option value="electronics">إلكترونيات</option>
                                                <option value="clothing">ملابس</option>
                                                <option value="food">أطعمة</option>
                                                <option value="books">كتب</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-outline-primary w-100" onclick="exportProducts()">
                                                <i class="fas fa-download me-2"></i>تصدير
                                            </button>
                                        </div>
                                    </div>

                                    <!-- جدول المنتجات -->
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>الكود</th>
                                                    <th>اسم المنتج</th>
                                                    <th>الفئة</th>
                                                    <th>السعر</th>
                                                    <th>الكمية</th>
                                                    <th>الحالة</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody id="productsTableBody">
                                                <tr>
                                                    <td colspan="7" class="text-center text-muted">
                                                        <i class="fas fa-box fa-2x mb-2"></i><br>
                                                        لا توجد منتجات مضافة بعد
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- إحصائيات المنتجات -->
                                    <div class="row mt-4">
                                        <div class="col-md-3">
                                            <div class="card bg-primary text-white">
                                                <div class="card-body text-center">
                                                    <h4 id="totalProductsCount">0</h4>
                                                    <small>إجمالي المنتجات</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="card bg-success text-white">
                                                <div class="card-body text-center">
                                                    <h4 id="inStockCount">0</h4>
                                                    <small>متوفر في المخزون</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="card bg-warning text-white">
                                                <div class="card-body text-center">
                                                    <h4 id="lowStockCount">0</h4>
                                                    <small>مخزون منخفض</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="card bg-danger text-white">
                                                <div class="card-body text-center">
                                                    <h4 id="outOfStockCount">0</h4>
                                                    <small>نفد من المخزون</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صفحة العملاء -->
                    <div id="customers" class="page">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2>إدارة العملاء</h2>
                            <button class="btn btn-primary" onclick="showAddCustomerModal()">
                                <i class="fas fa-plus me-2"></i>
                                إضافة عميل جديد
                            </button>
                        </div>
                        
                        <div class="card">
                            <div class="card-body">
                                <div id="customers-content">
                                    <!-- شريط البحث والفلترة -->
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i class="fas fa-search"></i>
                                                </span>
                                                <input type="text" class="form-control" id="searchCustomers" placeholder="البحث في العملاء...">
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <select class="form-select" id="customerTypeFilter">
                                                <option value="">جميع الأنواع</option>
                                                <option value="regular">عادي</option>
                                                <option value="vip">مميز</option>
                                                <option value="wholesale">جملة</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-outline-primary w-100" onclick="exportCustomers()">
                                                <i class="fas fa-download me-2"></i>تصدير
                                            </button>
                                        </div>
                                    </div>

                                    <!-- جدول العملاء -->
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>الكود</th>
                                                    <th>اسم العميل</th>
                                                    <th>الهاتف</th>
                                                    <th>البريد الإلكتروني</th>
                                                    <th>النوع</th>
                                                    <th>إجمالي المشتريات</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody id="customersTableBody">
                                                <tr>
                                                    <td colspan="7" class="text-center text-muted">
                                                        <i class="fas fa-users fa-2x mb-2"></i><br>
                                                        لا يوجد عملاء مسجلون بعد
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- إحصائيات العملاء -->
                                    <div class="row mt-4">
                                        <div class="col-md-3">
                                            <div class="card bg-info text-white">
                                                <div class="card-body text-center">
                                                    <h4 id="totalCustomersCount">0</h4>
                                                    <small>إجمالي العملاء</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="card bg-success text-white">
                                                <div class="card-body text-center">
                                                    <h4 id="activeCustomersCount">0</h4>
                                                    <small>عملاء نشطون</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="card bg-warning text-white">
                                                <div class="card-body text-center">
                                                    <h4 id="vipCustomersCount">0</h4>
                                                    <small>عملاء مميزون</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="card bg-primary text-white">
                                                <div class="card-body text-center">
                                                    <h4 id="newCustomersCount">0</h4>
                                                    <small>عملاء جدد هذا الشهر</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صفحة المبيعات -->
                    <div id="sales" class="page">
                        <h2 class="mb-4">نقطة البيع</h2>
                        
                        <div class="card">
                            <div class="card-body">
                                <div id="sales-content">
                                    <div class="row">
                                        <!-- قائمة المنتجات -->
                                        <div class="col-md-8">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h5><i class="fas fa-shopping-cart me-2"></i>المنتجات</h5>
                                                </div>
                                                <div class="card-body">
                                                    <!-- شريط البحث -->
                                                    <div class="mb-3">
                                                        <div class="input-group">
                                                            <span class="input-group-text">
                                                                <i class="fas fa-search"></i>
                                                            </span>
                                                            <input type="text" class="form-control" id="productSearch" placeholder="البحث عن منتج أو مسح الباركود...">
                                                        </div>
                                                    </div>

                                                    <!-- شبكة المنتجات -->
                                                    <div class="row" id="productsGrid">
                                                        <div class="col-12 text-center text-muted">
                                                            <i class="fas fa-box fa-3x mb-3"></i>
                                                            <p>لا توجد منتجات متاحة</p>
                                                            <button class="btn btn-primary" onclick="showPage('products')">
                                                                إضافة منتجات
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- سلة المشتريات -->
                                        <div class="col-md-4">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h5><i class="fas fa-receipt me-2"></i>الفاتورة</h5>
                                                </div>
                                                <div class="card-body">
                                                    <!-- معلومات العميل -->
                                                    <div class="mb-3">
                                                        <label class="form-label">العميل</label>
                                                        <select class="form-select" id="customerSelect">
                                                            <option value="">عميل نقدي</option>
                                                        </select>
                                                    </div>

                                                    <!-- عناصر الفاتورة -->
                                                    <div class="mb-3">
                                                        <div id="cartItems" style="max-height: 300px; overflow-y: auto;">
                                                            <div class="text-center text-muted py-4">
                                                                <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                                                                <p>السلة فارغة</p>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- الإجمالي -->
                                                    <div class="border-top pt-3">
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>المجموع الفرعي:</span>
                                                            <span id="subtotal">0.00 ج.م</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between mb-2">
                                                            <span>الضريبة:</span>
                                                            <span id="tax">0.00 ج.م</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between mb-3 fw-bold">
                                                            <span>الإجمالي:</span>
                                                            <span id="total">0.00 ج.م</span>
                                                        </div>

                                                        <!-- أزرار العمليات -->
                                                        <div class="d-grid gap-2">
                                                            <button class="btn btn-success" id="checkoutBtn" disabled onclick="processCheckout()">
                                                                <i class="fas fa-credit-card me-2"></i>إتمام البيع
                                                            </button>
                                                            <button class="btn btn-outline-secondary" onclick="clearCart()">
                                                                <i class="fas fa-trash me-2"></i>مسح السلة
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صفحة التقارير -->
                    <div id="reports" class="page">
                        <h2 class="mb-4">التقارير والإحصائيات</h2>
                        
                        <div class="card">
                            <div class="card-body">
                                <div id="reports-content">
                                    <!-- فلاتر التقارير -->
                                    <div class="row mb-4">
                                        <div class="col-md-3">
                                            <label class="form-label">نوع التقرير</label>
                                            <select class="form-select" id="reportType">
                                                <option value="sales">تقرير المبيعات</option>
                                                <option value="products">تقرير المنتجات</option>
                                                <option value="customers">تقرير العملاء</option>
                                                <option value="expenses">تقرير المصروفات</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">من تاريخ</label>
                                            <input type="date" class="form-control" id="fromDate">
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">إلى تاريخ</label>
                                            <input type="date" class="form-control" id="toDate">
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">&nbsp;</label>
                                            <div class="d-grid">
                                                <button class="btn btn-primary" onclick="generateReport()">
                                                    <i class="fas fa-chart-bar me-2"></i>إنشاء التقرير
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- إحصائيات سريعة -->
                                    <div class="row mb-4">
                                        <div class="col-md-3">
                                            <div class="card bg-primary text-white">
                                                <div class="card-body text-center">
                                                    <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                                                    <h4 id="todaySales">0 ج.م</h4>
                                                    <small>مبيعات اليوم</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="card bg-success text-white">
                                                <div class="card-body text-center">
                                                    <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                                                    <h4 id="todayOrders">0</h4>
                                                    <small>طلبات اليوم</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="card bg-info text-white">
                                                <div class="card-body text-center">
                                                    <i class="fas fa-users fa-2x mb-2"></i>
                                                    <h4 id="todayCustomers">0</h4>
                                                    <small>عملاء جدد اليوم</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="card bg-warning text-white">
                                                <div class="card-body text-center">
                                                    <i class="fas fa-box fa-2x mb-2"></i>
                                                    <h4 id="lowStockItems">0</h4>
                                                    <small>منتجات مخزون منخفض</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- منطقة عرض التقرير -->
                                    <div class="card">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h5 class="mb-0">
                                                <i class="fas fa-chart-line me-2"></i>
                                                <span id="reportTitle">تقرير المبيعات</span>
                                            </h5>
                                            <div>
                                                <button class="btn btn-outline-primary btn-sm" onclick="exportReport('pdf')">
                                                    <i class="fas fa-file-pdf me-1"></i>PDF
                                                </button>
                                                <button class="btn btn-outline-success btn-sm" onclick="exportReport('excel')">
                                                    <i class="fas fa-file-excel me-1"></i>Excel
                                                </button>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div id="reportChart" style="height: 300px;">
                                                <div class="text-center text-muted py-5">
                                                    <i class="fas fa-chart-bar fa-3x mb-3"></i>
                                                    <p>اختر نوع التقرير والفترة الزمنية لعرض البيانات</p>
                                                </div>
                                            </div>

                                            <div class="table-responsive mt-4">
                                                <table class="table table-striped" id="reportTable">
                                                    <thead class="table-dark">
                                                        <tr id="reportTableHeader">
                                                            <th>التاريخ</th>
                                                            <th>الوصف</th>
                                                            <th>المبلغ</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="reportTableBody">
                                                        <tr>
                                                            <td colspan="3" class="text-center text-muted">
                                                                لا توجد بيانات لعرضها
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صفحة المصروفات -->
                    <div id="expenses" class="page">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2>إدارة المصروفات</h2>
                            <button class="btn btn-primary" onclick="showAddExpenseModal()">
                                <i class="fas fa-plus me-2"></i>
                                إضافة مصروف جديد
                            </button>
                        </div>
                        
                        <div class="card">
                            <div class="card-body">
                                <div id="expenses-content">
                                    <!-- فلاتر المصروفات -->
                                    <div class="row mb-3">
                                        <div class="col-md-4">
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i class="fas fa-search"></i>
                                                </span>
                                                <input type="text" class="form-control" id="searchExpenses" placeholder="البحث في المصروفات...">
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <select class="form-select" id="expenseCategoryFilter">
                                                <option value="">جميع الفئات</option>
                                                <option value="rent">إيجار</option>
                                                <option value="utilities">مرافق</option>
                                                <option value="supplies">مستلزمات</option>
                                                <option value="marketing">تسويق</option>
                                                <option value="maintenance">صيانة</option>
                                                <option value="other">أخرى</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <input type="date" class="form-control" id="expenseDateFilter">
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-outline-primary w-100" onclick="exportExpenses()">
                                                <i class="fas fa-download me-2"></i>تصدير
                                            </button>
                                        </div>
                                    </div>

                                    <!-- جدول المصروفات -->
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>التاريخ</th>
                                                    <th>الوصف</th>
                                                    <th>الفئة</th>
                                                    <th>المبلغ</th>
                                                    <th>طريقة الدفع</th>
                                                    <th>الملاحظات</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody id="expensesTableBody">
                                                <tr>
                                                    <td colspan="7" class="text-center text-muted">
                                                        <i class="fas fa-receipt fa-2x mb-2"></i><br>
                                                        لا توجد مصروفات مسجلة بعد
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- إحصائيات المصروفات -->
                                    <div class="row mt-4">
                                        <div class="col-md-3">
                                            <div class="card bg-danger text-white">
                                                <div class="card-body text-center">
                                                    <h4 id="totalExpensesAmount">0 ج.م</h4>
                                                    <small>إجمالي المصروفات</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="card bg-warning text-white">
                                                <div class="card-body text-center">
                                                    <h4 id="monthlyExpenses">0 ج.م</h4>
                                                    <small>مصروفات هذا الشهر</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="card bg-info text-white">
                                                <div class="card-body text-center">
                                                    <h4 id="todayExpenses">0 ج.م</h4>
                                                    <small>مصروفات اليوم</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="card bg-secondary text-white">
                                                <div class="card-body text-center">
                                                    <h4 id="expensesCount">0</h4>
                                                    <small>عدد المصروفات</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صفحة الإعدادات -->
                    <div id="settings" class="page">
                        <h2 class="mb-4">إعدادات النظام</h2>
                        
                        <div class="card">
                            <div class="card-body">
                                <div id="settings-content">
                                    <div class="row">
                                        <!-- إعدادات عامة -->
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h5><i class="fas fa-cog me-2"></i>الإعدادات العامة</h5>
                                                </div>
                                                <div class="card-body">
                                                    <div class="mb-3">
                                                        <label class="form-label">اسم المتجر</label>
                                                        <input type="text" class="form-control" id="storeName" value="Black Horse Store">
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">عنوان المتجر</label>
                                                        <textarea class="form-control" id="storeAddress" rows="3"></textarea>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">هاتف المتجر</label>
                                                        <input type="tel" class="form-control" id="storePhone">
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">البريد الإلكتروني</label>
                                                        <input type="email" class="form-control" id="storeEmail">
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">العملة</label>
                                                        <select class="form-select" id="currency">
                                                            <option value="EGP">جنيه مصري (ج.م)</option>
                                                            <option value="USD">دولار أمريكي ($)</option>
                                                            <option value="EUR">يورو (€)</option>
                                                            <option value="SAR">ريال سعودي (ر.س)</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- إعدادات الضرائب والخصومات -->
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h5><i class="fas fa-percentage me-2"></i>الضرائب والخصومات</h5>
                                                </div>
                                                <div class="card-body">
                                                    <div class="mb-3">
                                                        <label class="form-label">معدل الضريبة (%)</label>
                                                        <input type="number" class="form-control" id="taxRate" value="14" min="0" max="100" step="0.01">
                                                    </div>
                                                    <div class="mb-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="enableTax" checked>
                                                            <label class="form-check-label" for="enableTax">
                                                                تفعيل الضريبة
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">حد التنبيه للمخزون المنخفض</label>
                                                        <input type="number" class="form-control" id="lowStockAlert" value="10" min="1">
                                                    </div>
                                                    <div class="mb-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="enableLowStockAlert" checked>
                                                            <label class="form-check-label" for="enableLowStockAlert">
                                                                تفعيل تنبيهات المخزون المنخفض
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row mt-4">
                                        <!-- إعدادات الطباعة -->
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h5><i class="fas fa-print me-2"></i>إعدادات الطباعة</h5>
                                                </div>
                                                <div class="card-body">
                                                    <div class="mb-3">
                                                        <label class="form-label">حجم الورق</label>
                                                        <select class="form-select" id="paperSize">
                                                            <option value="80mm">80mm (حراري)</option>
                                                            <option value="A4">A4</option>
                                                            <option value="A5">A5</option>
                                                        </select>
                                                    </div>
                                                    <div class="mb-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="autoPrint">
                                                            <label class="form-check-label" for="autoPrint">
                                                                طباعة تلقائية للفواتير
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="mb-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="printLogo" checked>
                                                            <label class="form-check-label" for="printLogo">
                                                                طباعة شعار المتجر
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- إعدادات النسخ الاحتياطي -->
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h5><i class="fas fa-database me-2"></i>النسخ الاحتياطي</h5>
                                                </div>
                                                <div class="card-body">
                                                    <div class="mb-3">
                                                        <button class="btn btn-success w-100" onclick="createBackup()">
                                                            <i class="fas fa-download me-2"></i>إنشاء نسخة احتياطية
                                                        </button>
                                                    </div>
                                                    <div class="mb-3">
                                                        <button class="btn btn-warning w-100" onclick="restoreBackup()">
                                                            <i class="fas fa-upload me-2"></i>استعادة نسخة احتياطية
                                                        </button>
                                                    </div>
                                                    <div class="mb-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="autoBackup">
                                                            <label class="form-check-label" for="autoBackup">
                                                                نسخ احتياطي تلقائي يومي
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="mb-3">
                                                        <small class="text-muted">
                                                            آخر نسخة احتياطية: <span id="lastBackup">لم يتم إنشاء نسخة بعد</span>
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- أزرار الحفظ -->
                                    <div class="row mt-4">
                                        <div class="col-12">
                                            <div class="card">
                                                <div class="card-body text-center">
                                                    <button class="btn btn-primary btn-lg me-3" onclick="saveSettings()">
                                                        <i class="fas fa-save me-2"></i>حفظ الإعدادات
                                                    </button>
                                                    <button class="btn btn-outline-secondary btn-lg" onclick="resetSettings()">
                                                        <i class="fas fa-undo me-2"></i>إعادة تعيين
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

                    <!-- صفحة إدارة المستخدمين -->
                    <div id="users" class="page">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2>إدارة المستخدمين</h2>
                            <button class="btn btn-primary" onclick="showAddUserModal()">
                                <i class="fas fa-user-plus me-2"></i>
                                إضافة مستخدم جديد
                            </button>
                        </div>

                        <div class="card">
                            <div class="card-body">
                                <!-- إحصائيات المستخدمين -->
                                <div class="row mb-4">
                                    <div class="col-md-3">
                                        <div class="card bg-primary text-white">
                                            <div class="card-body text-center">
                                                <h4 id="totalUsersCount">0</h4>
                                                <small>إجمالي المستخدمين</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-success text-white">
                                            <div class="card-body text-center">
                                                <h4 id="activeUsersCount">0</h4>
                                                <small>المستخدمين النشطين</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-warning text-white">
                                            <div class="card-body text-center">
                                                <h4 id="adminUsersCount">0</h4>
                                                <small>المديرين</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-info text-white">
                                            <div class="card-body text-center">
                                                <h4 id="onlineUsersCount">0</h4>
                                                <small>متصل الآن</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- شريط البحث والفلترة -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="fas fa-search"></i>
                                            </span>
                                            <input type="text" class="form-control" id="searchUsers" placeholder="البحث في المستخدمين...">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select" id="filterUserRole">
                                            <option value="">جميع الأدوار</option>
                                            <option value="admin">مدير</option>
                                            <option value="manager">مدير فرع</option>
                                            <option value="cashier">كاشير</option>
                                            <option value="employee">موظف</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select" id="filterUserStatus">
                                            <option value="">جميع الحالات</option>
                                            <option value="active">نشط</option>
                                            <option value="inactive">غير نشط</option>
                                            <option value="suspended">موقوف</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- جدول المستخدمين -->
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>الصورة</th>
                                                <th>الاسم</th>
                                                <th>اسم المستخدم</th>
                                                <th>البريد الإلكتروني</th>
                                                <th>الدور</th>
                                                <th>الحالة</th>
                                                <th>آخر دخول</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="usersTableBody">
                                            <!-- سيتم ملء البيانات هنا -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صفحة الصلاحيات -->
                    <div id="permissions" class="page">
                        <h2 class="mb-4">إدارة الصلاحيات</h2>

                        <div class="card">
                            <div class="card-body">
                                <div class="row">
                                    <!-- قائمة الأدوار -->
                                    <div class="col-md-4">
                                        <h5>الأدوار</h5>
                                        <div class="list-group" id="rolesList">
                                            <a href="#" class="list-group-item list-group-item-action active" data-role="admin">
                                                <i class="fas fa-crown me-2"></i>
                                                مدير النظام
                                            </a>
                                            <a href="#" class="list-group-item list-group-item-action" data-role="manager">
                                                <i class="fas fa-user-tie me-2"></i>
                                                مدير فرع
                                            </a>
                                            <a href="#" class="list-group-item list-group-item-action" data-role="cashier">
                                                <i class="fas fa-cash-register me-2"></i>
                                                كاشير
                                            </a>
                                            <a href="#" class="list-group-item list-group-item-action" data-role="employee">
                                                <i class="fas fa-user me-2"></i>
                                                موظف
                                            </a>
                                        </div>

                                        <button class="btn btn-outline-primary btn-sm mt-3" onclick="showAddRoleModal()">
                                            <i class="fas fa-plus me-2"></i>
                                            إضافة دور جديد
                                        </button>
                                    </div>

                                    <!-- صلاحيات الدور المحدد -->
                                    <div class="col-md-8">
                                        <h5>صلاحيات الدور: <span id="currentRoleName">مدير النظام</span></h5>

                                        <div class="permissions-grid">
                                            <!-- صلاحيات المنتجات -->
                                            <div class="permission-group">
                                                <h6><i class="fas fa-box me-2"></i>المنتجات</h6>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="products_view" checked>
                                                    <label class="form-check-label" for="products_view">عرض المنتجات</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="products_add" checked>
                                                    <label class="form-check-label" for="products_add">إضافة منتج</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="products_edit" checked>
                                                    <label class="form-check-label" for="products_edit">تعديل منتج</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="products_delete" checked>
                                                    <label class="form-check-label" for="products_delete">حذف منتج</label>
                                                </div>
                                            </div>

                                            <!-- صلاحيات المبيعات -->
                                            <div class="permission-group">
                                                <h6><i class="fas fa-shopping-cart me-2"></i>المبيعات</h6>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="sales_view" checked>
                                                    <label class="form-check-label" for="sales_view">عرض المبيعات</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="sales_process" checked>
                                                    <label class="form-check-label" for="sales_process">معالجة البيع</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="sales_refund" checked>
                                                    <label class="form-check-label" for="sales_refund">استرداد</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="sales_discount" checked>
                                                    <label class="form-check-label" for="sales_discount">تطبيق خصم</label>
                                                </div>
                                            </div>
                                        </div>

                                        <button class="btn btn-success mt-3" onclick="savePermissions()">
                                            <i class="fas fa-save me-2"></i>
                                            حفظ الصلاحيات
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صفحة إدارة المخزون -->
                    <div id="inventory" class="page">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2>إدارة المخزون</h2>
                            <div>
                                <button class="btn btn-success me-2" onclick="showStockInModal()">
                                    <i class="fas fa-plus me-2"></i>
                                    إدخال مخزون
                                </button>
                                <button class="btn btn-warning" onclick="showStockOutModal()">
                                    <i class="fas fa-minus me-2"></i>
                                    إخراج مخزون
                                </button>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-body">
                                <!-- إحصائيات المخزون -->
                                <div class="row mb-4">
                                    <div class="col-md-3">
                                        <div class="card bg-primary text-white">
                                            <div class="card-body text-center">
                                                <h4 id="totalInventoryValue">0 ج.م</h4>
                                                <small>قيمة المخزون الإجمالية</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-success text-white">
                                            <div class="card-body text-center">
                                                <h4 id="availableItemsCount">0</h4>
                                                <small>الأصناف المتوفرة</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-warning text-white">
                                            <div class="card-body text-center">
                                                <h4 id="lowStockItemsCount">0</h4>
                                                <small>أصناف منخفضة المخزون</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-danger text-white">
                                            <div class="card-body text-center">
                                                <h4 id="outOfStockItemsCount">0</h4>
                                                <small>أصناف نفدت</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- فلاتر المخزون -->
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="fas fa-search"></i>
                                            </span>
                                            <input type="text" class="form-control" id="searchInventory" placeholder="البحث في المخزون...">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select" id="filterInventoryCategory">
                                            <option value="">جميع الفئات</option>
                                            <option value="electronics">إلكترونيات</option>
                                            <option value="clothing">ملابس</option>
                                            <option value="food">أطعمة</option>
                                            <option value="books">كتب</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select" id="filterInventoryStatus">
                                            <option value="">جميع الحالات</option>
                                            <option value="available">متوفر</option>
                                            <option value="low">منخفض</option>
                                            <option value="out">نفد</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-outline-primary w-100" onclick="exportInventoryReport()">
                                            <i class="fas fa-download me-2"></i>
                                            تصدير
                                        </button>
                                    </div>
                                </div>

                                <!-- جدول المخزون -->
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>كود المنتج</th>
                                                <th>اسم المنتج</th>
                                                <th>الفئة</th>
                                                <th>الكمية الحالية</th>
                                                <th>الحد الأدنى</th>
                                                <th>سعر التكلفة</th>
                                                <th>سعر البيع</th>
                                                <th>قيمة المخزون</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="inventoryTableBody">
                                            <!-- سيتم ملء البيانات هنا -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صفحة الموردين -->
                    <div id="suppliers" class="page">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2>إدارة الموردين</h2>
                            <button class="btn btn-primary" onclick="showAddSupplierModal()">
                                <i class="fas fa-plus me-2"></i>
                                إضافة مورد جديد
                            </button>
                        </div>

                        <div class="card">
                            <div class="card-body">
                                <!-- إحصائيات الموردين -->
                                <div class="row mb-4">
                                    <div class="col-md-3">
                                        <div class="card bg-primary text-white">
                                            <div class="card-body text-center">
                                                <h4 id="totalSuppliersCount">0</h4>
                                                <small>إجمالي الموردين</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-success text-white">
                                            <div class="card-body text-center">
                                                <h4 id="activeSuppliersCount">0</h4>
                                                <small>الموردين النشطين</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-warning text-white">
                                            <div class="card-body text-center">
                                                <h4 id="totalPurchasesAmount">0 ج.م</h4>
                                                <small>إجمالي المشتريات</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-info text-white">
                                            <div class="card-body text-center">
                                                <h4 id="pendingOrdersCount">0</h4>
                                                <small>طلبات معلقة</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- شريط البحث والفلترة -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="fas fa-search"></i>
                                            </span>
                                            <input type="text" class="form-control" id="searchSuppliers" placeholder="البحث في الموردين...">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select" id="filterSupplierStatus">
                                            <option value="">جميع الحالات</option>
                                            <option value="active">نشط</option>
                                            <option value="inactive">غير نشط</option>
                                            <option value="suspended">موقوف</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-outline-primary w-100" onclick="exportSuppliersReport()">
                                            <i class="fas fa-download me-2"></i>
                                            تصدير قائمة الموردين
                                        </button>
                                    </div>
                                </div>

                                <!-- جدول الموردين -->
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>الكود</th>
                                                <th>اسم المورد</th>
                                                <th>الهاتف</th>
                                                <th>البريد الإلكتروني</th>
                                                <th>العنوان</th>
                                                <th>إجمالي المشتريات</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="suppliersTableBody">
                                            <!-- سيتم ملء البيانات هنا -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صفحة الفئات -->
                    <div id="categories" class="page">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2>إدارة الفئات</h2>
                            <button class="btn btn-primary" onclick="showAddCategoryModal()">
                                <i class="fas fa-plus me-2"></i>
                                إضافة فئة جديدة
                            </button>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h4 id="totalCategoriesCount">0</h4>
                                        <small>إجمالي الفئات</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h4 id="activeCategoriesCount">0</h4>
                                        <small>الفئات النشطة</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h4 id="categoriesWithProductsCount">0</h4>
                                        <small>فئات بها منتجات</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h4 id="emptyCategoriesCount">0</h4>
                                        <small>فئات فارغة</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h5 class="mb-0">قائمة الفئات</h5>
                                    </div>
                                    <div class="col-auto">
                                        <div class="input-group">
                                            <input type="text" class="form-control" placeholder="البحث في الفئات..." id="categoriesSearch">
                                            <button class="btn btn-outline-secondary" type="button">
                                                <i class="fas fa-search"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>الكود</th>
                                                <th>اسم الفئة</th>
                                                <th>الوصف</th>
                                                <th>عدد المنتجات</th>
                                                <th>الحالة</th>
                                                <th>تاريخ الإنشاء</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="categoriesTableBody">
                                            <!-- سيتم ملء البيانات هنا -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صفحة تنبيهات المخزون -->
                    <div id="stock-alerts" class="page">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2>تنبيهات المخزون</h2>
                            <button class="btn btn-primary" onclick="refreshStockAlerts()">
                                <i class="fas fa-sync me-2"></i>
                                تحديث التنبيهات
                            </button>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body text-center">
                                        <h4 id="outOfStockCount">0</h4>
                                        <small>منتجات نفدت</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h4 id="lowStockCount">0</h4>
                                        <small>مخزون منخفض</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h4 id="criticalStockCount">0</h4>
                                        <small>مخزون حرج</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h4 id="goodStockCount">0</h4>
                                        <small>مخزون جيد</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">تنبيهات المخزون</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>كود المنتج</th>
                                                <th>اسم المنتج</th>
                                                <th>الكمية الحالية</th>
                                                <th>الحد الأدنى</th>
                                                <th>حالة التنبيه</th>
                                                <th>آخر تحديث</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="stockAlertsTableBody">
                                            <!-- سيتم ملء البيانات هنا -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صفحة تاريخ المبيعات -->
                    <div id="sales-history" class="page">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2>تاريخ المبيعات</h2>
                            <div>
                                <button class="btn btn-success me-2" onclick="exportSalesHistory()">
                                    <i class="fas fa-file-excel me-2"></i>
                                    تصدير Excel
                                </button>
                                <button class="btn btn-primary" onclick="refreshSalesHistory()">
                                    <i class="fas fa-sync me-2"></i>
                                    تحديث
                                </button>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h4 id="totalSalesCount">0</h4>
                                        <small>إجمالي المبيعات</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h4 id="todaySalesAmount">0 ج.م</h4>
                                        <small>مبيعات اليوم</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h4 id="monthSalesAmount">0 ج.م</h4>
                                        <small>مبيعات الشهر</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h4 id="averageSaleAmount">0 ج.م</h4>
                                        <small>متوسط البيع</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h5 class="mb-0">سجل المبيعات</h5>
                                    </div>
                                    <div class="col-auto">
                                        <div class="row g-2">
                                            <div class="col-auto">
                                                <input type="date" class="form-control" id="salesFromDate">
                                            </div>
                                            <div class="col-auto">
                                                <input type="date" class="form-control" id="salesToDate">
                                            </div>
                                            <div class="col-auto">
                                                <button class="btn btn-outline-primary" onclick="filterSalesHistory()">
                                                    <i class="fas fa-filter"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>رقم الفاتورة</th>
                                                <th>التاريخ</th>
                                                <th>العميل</th>
                                                <th>المبلغ الإجمالي</th>
                                                <th>طريقة الدفع</th>
                                                <th>الكاشير</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="salesHistoryTableBody">
                                            <!-- سيتم ملء البيانات هنا -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

    <!-- النوافذ المنبثقة (Modals) -->

    <!-- نافذة إضافة منتج -->
    <div class="modal fade" id="addProductModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة منتج جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="productForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم المنتج *</label>
                                    <input type="text" class="form-control" id="productName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">كود المنتج</label>
                                    <input type="text" class="form-control" id="productCode">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الفئة *</label>
                                    <select class="form-select" id="productCategory" required>
                                        <option value="">اختر الفئة</option>
                                        <option value="electronics">إلكترونيات</option>
                                        <option value="clothing">ملابس</option>
                                        <option value="food">أطعمة</option>
                                        <option value="books">كتب</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">السعر *</label>
                                    <input type="number" class="form-control" id="productPrice" step="0.01" min="0" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الكمية *</label>
                                    <input type="number" class="form-control" id="productQuantity" min="0" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الحد الأدنى للمخزون</label>
                                    <input type="number" class="form-control" id="productMinStock" min="0" value="10">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الوصف</label>
                            <textarea class="form-control" id="productDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveProduct()">حفظ المنتج</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة عميل -->
    <div class="modal fade" id="addCustomerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة عميل جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="customerForm">
                        <div class="mb-3">
                            <label class="form-label">اسم العميل *</label>
                            <input type="text" class="form-control" id="customerName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">رقم الهاتف *</label>
                            <input type="tel" class="form-control" id="customerPhone" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="customerEmail">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-control" id="customerAddress" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">نوع العميل</label>
                            <select class="form-select" id="customerType">
                                <option value="regular">عادي</option>
                                <option value="vip">مميز</option>
                                <option value="wholesale">جملة</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveCustomer()">حفظ العميل</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة مصروف -->
    <div class="modal fade" id="addExpenseModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مصروف جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="expenseForm">
                        <div class="mb-3">
                            <label class="form-label">وصف المصروف *</label>
                            <input type="text" class="form-control" id="expenseDescription" required>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المبلغ *</label>
                                    <input type="number" class="form-control" id="expenseAmount" step="0.01" min="0" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">التاريخ *</label>
                                    <input type="date" class="form-control" id="expenseDate" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الفئة *</label>
                                    <select class="form-select" id="expenseCategory" required>
                                        <option value="">اختر الفئة</option>
                                        <option value="rent">إيجار</option>
                                        <option value="utilities">مرافق</option>
                                        <option value="supplies">مستلزمات</option>
                                        <option value="marketing">تسويق</option>
                                        <option value="maintenance">صيانة</option>
                                        <option value="other">أخرى</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">طريقة الدفع</label>
                                    <select class="form-select" id="expensePaymentMethod">
                                        <option value="cash">نقدي</option>
                                        <option value="card">بطاقة</option>
                                        <option value="bank">تحويل بنكي</option>
                                        <option value="check">شيك</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="expenseNotes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveExpense()">حفظ المصروف</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة مستخدم -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مستخدم جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="userForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الاسم الكامل *</label>
                                    <input type="text" class="form-control" id="userName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم المستخدم *</label>
                                    <input type="text" class="form-control" id="userUsername" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني *</label>
                                    <input type="email" class="form-control" id="userEmail" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الهاتف</label>
                                    <input type="tel" class="form-control" id="userPhone">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور *</label>
                                    <input type="password" class="form-control" id="userPassword" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تأكيد كلمة المرور *</label>
                                    <input type="password" class="form-control" id="userPasswordConfirm" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الدور *</label>
                                    <select class="form-select" id="userRole" required>
                                        <option value="">اختر الدور</option>
                                        <option value="admin">مدير النظام</option>
                                        <option value="manager">مدير فرع</option>
                                        <option value="cashier">كاشير</option>
                                        <option value="employee">موظف</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الحالة</label>
                                    <select class="form-select" id="userStatus">
                                        <option value="active">نشط</option>
                                        <option value="inactive">غير نشط</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-control" id="userAddress" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveUser()">حفظ المستخدم</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة فئة -->
    <div class="modal fade" id="addCategoryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة فئة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="categoryForm">
                        <div class="mb-3">
                            <label class="form-label">كود الفئة</label>
                            <input type="text" class="form-control" id="categoryCode" placeholder="سيتم توليده تلقائياً">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">اسم الفئة *</label>
                            <input type="text" class="form-control" id="categoryName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الوصف</label>
                            <textarea class="form-control" id="categoryDescription" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الفئة الأب</label>
                            <select class="form-control" id="categoryParent">
                                <option value="">لا يوجد (فئة رئيسية)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الحالة</label>
                            <select class="form-control" id="categoryStatus">
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ترتيب العرض</label>
                            <input type="number" class="form-control" id="categoryOrder" value="0">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveCategory()">حفظ الفئة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة مورد -->
    <div class="modal fade" id="addSupplierModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مورد جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="supplierForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم المورد *</label>
                                    <input type="text" class="form-control" id="supplierName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">كود المورد</label>
                                    <input type="text" class="form-control" id="supplierCode">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الهاتف *</label>
                                    <input type="tel" class="form-control" id="supplierPhone" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="supplierEmail">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-control" id="supplierAddress" rows="2"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الحالة</label>
                                    <select class="form-select" id="supplierStatus">
                                        <option value="active">نشط</option>
                                        <option value="inactive">غير نشط</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">شروط الدفع</label>
                                    <select class="form-select" id="supplierPaymentTerms">
                                        <option value="cash">نقدي</option>
                                        <option value="30days">30 يوم</option>
                                        <option value="60days">60 يوم</option>
                                        <option value="90days">90 يوم</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="supplierNotes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveSupplier()">حفظ المورد</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- تحميل الوحدات الأساسية -->
    <script src="utils/helpers.js"></script>
    <script src="utils/validators.js"></script>
    <script src="database/database-optimizer.js"></script>
    <script src="database/unified-storage.js"></script>
    <script src="database/data-migration.js"></script>
    <script src="core/module-manager.js"></script>
    <script src="core/consolidated-functions.js"></script>
    <script src="core/system-health-checker.js"></script>
    <script src="core/error-handler.js"></script>
    <script src="core/performance-optimizer.js"></script>
    <script src="ui/ui-optimizer.js"></script>
    <script src="core/asset-optimizer.js"></script>

    <!-- تحميل الوحدات المحسنة -->
    <script src="modules/products/products-manager.js"></script>
    <script src="modules/customers/customers-manager.js"></script>
    <script src="modules/sales/sales-manager.js"></script>
    <script src="modules/reports/reports-manager.js"></script>
    <script src="modules/expenses/expenses-manager.js"></script>

    <!-- تحميل لوحة مراقبة الأداء -->
    <script src="ui/performance-dashboard.js"></script>

    <!-- تحميل نظام الاختبارات -->
    <script src="tests/performance-tests.js"></script>

    <script src="core/app-initializer.js"></script>
    
    <!-- نظام التحقق من الصحة ومعالجة الأخطاء -->
    <script>
        // نظام التحقق من صحة النظام
        class SystemHealthChecker {
            constructor() {
                this.requiredModules = [
                    'Utils', 'UnifiedStorage', 'DatabaseOptimizer', 'ModuleManager',
                    'AssetOptimizer', 'PerformanceOptimizer', 'UIOptimizer',
                    'ProductsManager', 'CustomersManager', 'SalesManager',
                    'ReportsManager', 'ExpensesManager', 'PerformanceDashboard',
                    'PerformanceTests', 'AppInitializer'
                ];
                this.healthStatus = {};
                this.errors = [];
            }

            async checkSystemHealth() {
                console.log('🔍 بدء فحص صحة النظام...');

                // فحص الوحدات المطلوبة
                this.checkRequiredModules();

                // فحص قاعدة البيانات
                await this.checkDatabase();

                // فحص الأداء
                await this.checkPerformance();

                // إنشاء التقرير
                this.generateHealthReport();

                return this.healthStatus;
            }

            checkRequiredModules() {
                console.log('📦 فحص الوحدات المطلوبة...');

                this.requiredModules.forEach(moduleName => {
                    const isAvailable = window[moduleName] !== undefined;
                    this.healthStatus[moduleName] = {
                        status: isAvailable ? 'OK' : 'ERROR',
                        message: isAvailable ? 'متاح' : 'غير متاح'
                    };

                    if (!isAvailable) {
                        this.errors.push(`الوحدة ${moduleName} غير متاحة`);
                    }
                });
            }

            async checkDatabase() {
                console.log('🗄️ فحص قاعدة البيانات...');

                try {
                    if (window.UnifiedStorage) {
                        await window.UnifiedStorage.init();
                        this.healthStatus.Database = {
                            status: 'OK',
                            message: 'قاعدة البيانات تعمل بشكل صحيح'
                        };
                    } else {
                        throw new Error('UnifiedStorage غير متاح');
                    }
                } catch (error) {
                    this.healthStatus.Database = {
                        status: 'ERROR',
                        message: `خطأ في قاعدة البيانات: ${error.message}`
                    };
                    this.errors.push(`خطأ في قاعدة البيانات: ${error.message}`);
                }
            }

            async checkPerformance() {
                console.log('⚡ فحص الأداء...');

                try {
                    const startTime = performance.now();

                    // محاكاة عملية
                    await new Promise(resolve => setTimeout(resolve, 100));

                    const endTime = performance.now();
                    const responseTime = endTime - startTime;

                    this.healthStatus.Performance = {
                        status: responseTime < 200 ? 'OK' : 'WARNING',
                        message: `وقت الاستجابة: ${responseTime.toFixed(2)}ms`,
                        responseTime: responseTime
                    };
                } catch (error) {
                    this.healthStatus.Performance = {
                        status: 'ERROR',
                        message: `خطأ في فحص الأداء: ${error.message}`
                    };
                    this.errors.push(`خطأ في فحص الأداء: ${error.message}`);
                }
            }

            generateHealthReport() {
                const totalChecks = Object.keys(this.healthStatus).length;
                const okChecks = Object.values(this.healthStatus).filter(status => status.status === 'OK').length;
                const errorChecks = Object.values(this.healthStatus).filter(status => status.status === 'ERROR').length;

                console.log(`📊 تقرير صحة النظام:`);
                console.log(`✅ فحوصات ناجحة: ${okChecks}/${totalChecks}`);
                console.log(`❌ فحوصات فاشلة: ${errorChecks}/${totalChecks}`);

                if (this.errors.length > 0) {
                    console.log('🚨 الأخطاء المكتشفة:');
                    this.errors.forEach(error => console.log(`  - ${error}`));
                }
            }

            getHealthSummary() {
                const totalChecks = Object.keys(this.healthStatus).length;
                const okChecks = Object.values(this.healthStatus).filter(status => status.status === 'OK').length;

                return {
                    total: totalChecks,
                    ok: okChecks,
                    errors: this.errors.length,
                    healthPercentage: Math.round((okChecks / totalChecks) * 100),
                    status: this.healthStatus,
                    errors: this.errors
                };
            }
        }

        // نظام معالجة الأخطاء المتقدم
        class ErrorHandler {
            constructor() {
                this.errors = [];
                this.setupGlobalErrorHandling();
            }

            setupGlobalErrorHandling() {
                // معالجة أخطاء JavaScript
                window.addEventListener('error', (event) => {
                    this.logError('JavaScript Error', event.error, {
                        filename: event.filename,
                        lineno: event.lineno,
                        colno: event.colno
                    });
                });

                // معالجة أخطاء Promise
                window.addEventListener('unhandledrejection', (event) => {
                    this.logError('Unhandled Promise Rejection', event.reason);
                });
            }

            logError(type, error, details = {}) {
                const errorInfo = {
                    type: type,
                    message: error?.message || error,
                    stack: error?.stack,
                    timestamp: new Date().toISOString(),
                    details: details,
                    userAgent: navigator.userAgent,
                    url: window.location.href
                };

                this.errors.push(errorInfo);

                console.error(`🚨 ${type}:`, errorInfo);

                // إرسال إلى نظام المراقبة إذا كان متاحاً
                if (window.PerformanceDashboard) {
                    window.PerformanceDashboard.reportError(errorInfo);
                }
            }

            getErrorReport() {
                return {
                    totalErrors: this.errors.length,
                    recentErrors: this.errors.slice(-10),
                    errorsByType: this.groupErrorsByType(),
                    timestamp: new Date().toISOString()
                };
            }

            groupErrorsByType() {
                const grouped = {};
                this.errors.forEach(error => {
                    grouped[error.type] = (grouped[error.type] || 0) + 1;
                });
                return grouped;
            }

            clearErrors() {
                this.errors = [];
                console.log('🧹 تم مسح سجل الأخطاء');
            }
        }

        // إنشاء مثيلات النظام
        window.SystemHealthChecker = new SystemHealthChecker();
        window.ErrorHandler = new ErrorHandler();

        // دالة التنقل بين الصفحات
        function showPage(pageId) {
            try {
                console.log(`🔄 Navigating to page: ${pageId}`);

                // إخفاء جميع الصفحات
                document.querySelectorAll('.page').forEach(page => {
                    page.classList.remove('active');
                });

                // إزالة active من جميع روابط التنقل
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.classList.remove('active');
                });

                // إظهار الصفحة المطلوبة
                const targetPage = document.getElementById(pageId);
                if (targetPage) {
                    targetPage.classList.add('active');
                    console.log(`✅ Page ${pageId} activated`);
                } else {
                    console.error(`❌ Page ${pageId} not found`);
                    return;
                }

                // تفعيل رابط التنقل
                const activeLink = document.querySelector(`[onclick="showPage('${pageId}')"]`);
                if (activeLink) {
                    activeLink.classList.add('active');
                }

                // تحديث محتوى الصفحة إذا لزم الأمر
                loadPageContent(pageId);

                // إضافة تأثير انتقال سلس
                targetPage.style.opacity = '0';
                targetPage.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    targetPage.style.transition = 'all 0.3s ease';
                    targetPage.style.opacity = '1';
                    targetPage.style.transform = 'translateY(0)';
                }, 50);

            } catch (error) {
                console.error('❌ Error in showPage:', error);
                if (window.ErrorHandler) {
                    window.ErrorHandler.logError('Navigation Error', error);
                }
            }
        }
        
        // تحميل محتوى الصفحة
        function loadPageContent(pageId) {
            try {
                console.log(`📄 Loading content for page: ${pageId}`);

                const contentDiv = document.getElementById(`${pageId}-content`);
                if (!contentDiv && pageId !== 'dashboard') {
                    console.warn(`⚠️ Content div not found for page: ${pageId}`);
                    return;
                }

                // عرض رسالة تحميل
                if (contentDiv) {
                    contentDiv.innerHTML = `
                        <div class="text-center p-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                            <p class="mt-2 text-muted">جاري تحميل ${getPageTitle(pageId)}...</p>
                        </div>
                    `;
                }

                // استخدام المدراء المناسبين لكل صفحة
                switch (pageId) {
                    case 'dashboard':
                        loadDashboardData();
                        break;
                    case 'products':
                        if (window.ProductsManager) {
                            window.ProductsManager.loadProductsPage();
                        } else {
                            showModuleNotAvailable(contentDiv, 'إدارة المنتجات', 'ProductsManager');
                        }
                        break;
                    case 'customers':
                        if (window.CustomersManager) {
                            window.CustomersManager.loadCustomersPage();
                        } else {
                            showModuleNotAvailable(contentDiv, 'إدارة العملاء', 'CustomersManager');
                        }
                        break;
                    case 'sales':
                        if (window.SalesManager) {
                            window.SalesManager.loadSalesPage();
                        } else {
                            showModuleNotAvailable(contentDiv, 'نقطة البيع', 'SalesManager');
                        }
                        break;
                    case 'reports':
                        if (window.ReportsManager) {
                            window.ReportsManager.loadReportsPage();
                        } else {
                            showModuleNotAvailable(contentDiv, 'التقارير', 'ReportsManager');
                        }
                        break;
                    case 'expenses':
                        if (window.ExpensesManager) {
                            window.ExpensesManager.loadExpensesPage();
                        } else {
                            showModuleNotAvailable(contentDiv, 'إدارة المصروفات', 'ExpensesManager');
                        }
                        break;
                    case 'settings':
                        loadSettingsPage(contentDiv);
                        break;
                    case 'users':
                        loadUsers();
                        break;
                    case 'permissions':
                        // الصفحة جاهزة، لا حاجة لتحميل إضافي
                        break;
                    case 'inventory':
                        loadInventoryData();
                        break;
                    case 'suppliers':
                        loadSuppliers();
                        break;
                    case 'categories':
                        loadCategories();
                        break;
                    case 'stock-alerts':
                        loadStockAlerts();
                        break;
                    case 'sales-history':
                        loadSalesHistory();
                        break;
                    case 'invoices':
                        // سيتم تنفيذها لاحقاً
                        if (contentDiv) {
                            contentDiv.innerHTML = `
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle me-2"></i>
                                    صفحة الفواتير قيد التطوير
                                </div>
                            `;
                        }
                        break;
                    case 'returns':
                        // سيتم تنفيذها لاحقاً
                        if (contentDiv) {
                            contentDiv.innerHTML = `
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle me-2"></i>
                                    صفحة المرتجعات قيد التطوير
                                </div>
                            `;
                        }
                        break;
                    case 'customer-groups':
                        // سيتم تنفيذها لاحقاً
                        if (contentDiv) {
                            contentDiv.innerHTML = `
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle me-2"></i>
                                    صفحة مجموعات العملاء قيد التطوير
                                </div>
                            `;
                        }
                        break;
                    case 'loyalty-program':
                        // سيتم تنفيذها لاحقاً
                        if (contentDiv) {
                            contentDiv.innerHTML = `
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle me-2"></i>
                                    صفحة برنامج الولاء قيد التطوير
                                </div>
                            `;
                        }
                        break;
                    case 'purchase-orders':
                        // سيتم تنفيذها لاحقاً
                        if (contentDiv) {
                            contentDiv.innerHTML = `
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle me-2"></i>
                                    صفحة أوامر الشراء قيد التطوير
                                </div>
                            `;
                        }
                        break;
                    case 'purchase-invoices':
                        // سيتم تنفيذها لاحقاً
                        if (contentDiv) {
                            contentDiv.innerHTML = `
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle me-2"></i>
                                    صفحة فواتير الشراء قيد التطوير
                                </div>
                            `;
                        }
                        break;
                    case 'financial-reports':
                        // سيتم تنفيذها لاحقاً
                        if (contentDiv) {
                            contentDiv.innerHTML = `
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle me-2"></i>
                                    صفحة التقارير المالية قيد التطوير
                                </div>
                            `;
                        }
                        break;
                    case 'inventory-reports':
                        // سيتم تنفيذها لاحقاً
                        if (contentDiv) {
                            contentDiv.innerHTML = `
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle me-2"></i>
                                    صفحة تقارير المخزون قيد التطوير
                                </div>
                            `;
                        }
                        break;
                    case 'customer-reports':
                        // سيتم تنفيذها لاحقاً
                        if (contentDiv) {
                            contentDiv.innerHTML = `
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle me-2"></i>
                                    صفحة تقارير العملاء قيد التطوير
                                </div>
                            `;
                        }
                        break;
                    case 'accounts':
                        // سيتم تنفيذها لاحقاً
                        if (contentDiv) {
                            contentDiv.innerHTML = `
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle me-2"></i>
                                    صفحة الحسابات قيد التطوير
                                </div>
                            `;
                        }
                        break;
                    case 'payments':
                        // سيتم تنفيذها لاحقاً
                        if (contentDiv) {
                            contentDiv.innerHTML = `
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle me-2"></i>
                                    صفحة المدفوعات قيد التطوير
                                </div>
                            `;
                        }
                        break;
                    case 'taxes':
                        // سيتم تنفيذها لاحقاً
                        if (contentDiv) {
                            contentDiv.innerHTML = `
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle me-2"></i>
                                    صفحة الضرائب قيد التطوير
                                </div>
                            `;
                        }
                        break;
                    case 'backup':
                        // سيتم تنفيذها لاحقاً
                        if (contentDiv) {
                            contentDiv.innerHTML = `
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle me-2"></i>
                                    صفحة النسخ الاحتياطي قيد التطوير
                                </div>
                            `;
                        }
                        break;
                    default:
                        if (contentDiv) {
                            contentDiv.innerHTML = `
                                <div class="alert alert-warning text-center">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    الصفحة المطلوبة غير متاحة حالياً
                                </div>
                            `;
                        }
                }

            } catch (error) {
                console.error(`❌ Error loading page content for ${pageId}:`, error);
                if (window.ErrorHandler) {
                    window.ErrorHandler.logError('Page Content Loading Error', error, { pageId });
                }
            }
        }

        // الحصول على عنوان الصفحة
        function getPageTitle(pageId) {
            const titles = {
                'dashboard': 'لوحة التحكم',
                'products': 'المنتجات',
                'customers': 'العملاء',
                'sales': 'المبيعات',
                'reports': 'التقارير',
                'expenses': 'المصروفات',
                'settings': 'الإعدادات'
            };
            return titles[pageId] || 'المحتوى';
        }

        // عرض رسالة عدم توفر الوحدة
        function showModuleNotAvailable(contentDiv, moduleName, managerName) {
            if (!contentDiv) return;

            contentDiv.innerHTML = `
                <div class="alert alert-warning text-center">
                    <i class="fas fa-exclamation-triangle fa-2x mb-3 text-warning"></i>
                    <h5>وحدة ${moduleName} غير متاحة</h5>
                    <p class="mb-3">الوحدة ${managerName} لم يتم تحميلها بعد.</p>
                    <button class="btn btn-primary" onclick="retryLoadModule('${managerName}', '${moduleName}')">
                        <i class="fas fa-redo me-2"></i>
                        إعادة المحاولة
                    </button>
                </div>
            `;
        }

        // إعادة محاولة تحميل الوحدة
        function retryLoadModule(managerName, moduleName) {
            console.log(`🔄 Retrying to load ${managerName}...`);

            if (window.Utils) {
                window.Utils.showMessage(`جاري إعادة تحميل وحدة ${moduleName}...`, 'info');
            }

            // إعادة تحميل الصفحة بعد ثانيتين
            setTimeout(() => {
                location.reload();
            }, 2000);
        }
        
        // تحميل بيانات لوحة التحكم
        async function loadDashboardData() {
            try {
                if (window.UnifiedStorage) {
                    const products = await window.UnifiedStorage.load('products') || [];
                    const customers = await window.UnifiedStorage.load('customers') || [];
                    const sales = await window.UnifiedStorage.load('sales') || [];
                    
                    // تحديث الإحصائيات
                    document.getElementById('total-products').textContent = products.length;
                    document.getElementById('total-customers').textContent = customers.length;
                    
                    // حساب إجمالي المبيعات
                    const totalSales = sales.reduce((sum, sale) => sum + (parseFloat(sale.total) || 0), 0);
                    document.getElementById('total-sales').textContent = Utils.formatCurrency(totalSales);
                    
                    // حساب معاملات اليوم
                    const today = new Date().toDateString();
                    const todayTransactions = sales.filter(sale => 
                        new Date(sale.date).toDateString() === today
                    ).length;
                    document.getElementById('today-transactions').textContent = todayTransactions;
                }
            } catch (error) {
                console.error('❌ Error loading dashboard data:', error);
            }
        }
        
        // دوال النماذج المحسنة
        function showAddProductModal() {
            try {
                console.log('🛍️ Opening add product modal...');

                if (window.ProductsManager && window.ProductsManager.showAddProductModal) {
                    window.ProductsManager.showAddProductModal();
                } else {
                    // استخدام النافذة المنبثقة المدمجة
                    document.getElementById('productForm').reset();
                    const modal = new bootstrap.Modal(document.getElementById('addProductModal'));
                    modal.show();
                    console.log('✅ Product modal opened successfully');
                }
            } catch (error) {
                console.error('❌ Error opening add product modal:', error);
                if (window.Utils) {
                    window.Utils.showMessage('خطأ في فتح نموذج إضافة المنتج', 'error');
                }
            }
        }

        function showAddCustomerModal() {
            try {
                console.log('👤 Opening add customer modal...');

                if (window.CustomersManager && window.CustomersManager.showAddCustomerModal) {
                    window.CustomersManager.showAddCustomerModal();
                } else {
                    createTemporaryModal('إضافة عميل جديد', `
                        <form id="addCustomerForm">
                            <div class="mb-3">
                                <label class="form-label">اسم العميل</label>
                                <input type="text" class="form-control" id="customerName" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="customerPhone">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">العنوان</label>
                                <textarea class="form-control" id="customerAddress" rows="2"></textarea>
                            </div>
                        </form>
                    `, 'حفظ العميل', saveTemporaryCustomer);
                }
            } catch (error) {
                console.error('❌ Error opening add customer modal:', error);
                if (window.Utils) {
                    window.Utils.showMessage('خطأ في فتح نموذج إضافة العميل', 'error');
                }
            }
        }

        function showAddExpenseModal() {
            try {
                console.log('💰 Opening add expense modal...');

                if (window.ExpensesManager && window.ExpensesManager.showAddExpenseModal) {
                    window.ExpensesManager.showAddExpenseModal();
                } else {
                    createTemporaryModal('إضافة مصروف جديد', `
                        <form id="addExpenseForm">
                            <div class="mb-3">
                                <label class="form-label">وصف المصروف</label>
                                <input type="text" class="form-control" id="expenseDescription" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">المبلغ</label>
                                <input type="number" class="form-control" id="expenseAmount" step="0.01" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">التاريخ</label>
                                <input type="date" class="form-control" id="expenseDate" required>
                            </div>
                        </form>
                    `, 'حفظ المصروف', saveTemporaryExpense);
                }
            } catch (error) {
                console.error('❌ Error opening add expense modal:', error);
                if (window.Utils) {
                    window.Utils.showMessage('خطأ في فتح نموذج إضافة المصروف', 'error');
                }
            }
        }

        // إنشاء نموذج مؤقت
        function createTemporaryModal(title, content, buttonText, saveFunction) {
            const modalId = 'temporaryModal';

            // إزالة النموذج السابق إن وجد
            const existingModal = document.getElementById(modalId);
            if (existingModal) {
                existingModal.remove();
            }

            const modalHTML = `
                <div class="modal fade" id="${modalId}" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">${title}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                ${content}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-primary" onclick="${saveFunction.name}()">${buttonText}</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHTML);

            // عرض النموذج
            const modal = new bootstrap.Modal(document.getElementById(modalId));
            modal.show();

            // إزالة النموذج عند الإغلاق
            document.getElementById(modalId).addEventListener('hidden.bs.modal', function() {
                this.remove();
            });
        }

        // دوال الحفظ المؤقتة
        async function saveTemporaryProduct() {
            try {
                const name = document.getElementById('productName').value;
                const price = parseFloat(document.getElementById('productPrice').value);
                const quantity = parseInt(document.getElementById('productQuantity').value);

                if (!name || !price || !quantity) {
                    if (window.Utils) {
                        window.Utils.showMessage('يرجى ملء جميع الحقول المطلوبة', 'warning');
                    }
                    return;
                }

                const product = {
                    id: Date.now().toString(),
                    name: name,
                    price: price,
                    quantity: quantity,
                    createdAt: new Date().toISOString()
                };

                if (window.UnifiedStorage) {
                    const products = await window.UnifiedStorage.load('products', []);
                    products.push(product);
                    await window.UnifiedStorage.save('products', products);

                    if (window.Utils) {
                        window.Utils.showMessage('تم إضافة المنتج بنجاح', 'success');
                    }

                    // إغلاق النموذج
                    const modal = bootstrap.Modal.getInstance(document.getElementById('temporaryModal'));
                    if (modal) modal.hide();

                    // تحديث الصفحة إذا كانت صفحة المنتجات مفتوحة
                    if (document.getElementById('products').classList.contains('active')) {
                        loadPageContent('products');
                    }

                    // تحديث لوحة التحكم
                    loadDashboardData();
                }

            } catch (error) {
                console.error('❌ Error saving product:', error);
                if (window.Utils) {
                    window.Utils.showMessage('خطأ في حفظ المنتج', 'error');
                }
            }
        }

        async function saveTemporaryCustomer() {
            try {
                const name = document.getElementById('customerName').value;
                const phone = document.getElementById('customerPhone').value;
                const address = document.getElementById('customerAddress').value;

                if (!name) {
                    if (window.Utils) {
                        window.Utils.showMessage('يرجى إدخال اسم العميل', 'warning');
                    }
                    return;
                }

                const customer = {
                    id: Date.now().toString(),
                    name: name,
                    phone: phone || '',
                    address: address || '',
                    createdAt: new Date().toISOString()
                };

                if (window.UnifiedStorage) {
                    const customers = await window.UnifiedStorage.load('customers', []);
                    customers.push(customer);
                    await window.UnifiedStorage.save('customers', customers);

                    if (window.Utils) {
                        window.Utils.showMessage('تم إضافة العميل بنجاح', 'success');
                    }

                    // إغلاق النموذج
                    const modal = bootstrap.Modal.getInstance(document.getElementById('temporaryModal'));
                    if (modal) modal.hide();

                    // تحديث الصفحة إذا كانت صفحة العملاء مفتوحة
                    if (document.getElementById('customers').classList.contains('active')) {
                        loadPageContent('customers');
                    }

                    // تحديث لوحة التحكم
                    loadDashboardData();
                }

            } catch (error) {
                console.error('❌ Error saving customer:', error);
                if (window.Utils) {
                    window.Utils.showMessage('خطأ في حفظ العميل', 'error');
                }
            }
        }

        async function saveTemporaryExpense() {
            try {
                const description = document.getElementById('expenseDescription').value;
                const amount = parseFloat(document.getElementById('expenseAmount').value);
                const date = document.getElementById('expenseDate').value;

                if (!description || !amount || !date) {
                    if (window.Utils) {
                        window.Utils.showMessage('يرجى ملء جميع الحقول المطلوبة', 'warning');
                    }
                    return;
                }

                const expense = {
                    id: Date.now().toString(),
                    description: description,
                    amount: amount,
                    date: date,
                    createdAt: new Date().toISOString()
                };

                if (window.UnifiedStorage) {
                    const expenses = await window.UnifiedStorage.load('expenses', []);
                    expenses.push(expense);
                    await window.UnifiedStorage.save('expenses', expenses);

                    if (window.Utils) {
                        window.Utils.showMessage('تم إضافة المصروف بنجاح', 'success');
                    }

                    // إغلاق النموذج
                    const modal = bootstrap.Modal.getInstance(document.getElementById('temporaryModal'));
                    if (modal) modal.hide();

                    // تحديث الصفحة إذا كانت صفحة المصروفات مفتوحة
                    if (document.getElementById('expenses').classList.contains('active')) {
                        loadPageContent('expenses');
                    }
                }

            } catch (error) {
                console.error('❌ Error saving expense:', error);
                if (window.Utils) {
                    window.Utils.showMessage('خطأ في حفظ المصروف', 'error');
                }
            }
        }

        // تحميل صفحة الإعدادات
        function loadSettingsPage(contentDiv) {
            if (!contentDiv) return;

            contentDiv.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-palette me-2"></i>إعدادات المظهر</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">نمط المظهر</label>
                                    <select class="form-select" id="themeSelect" onchange="changeTheme()">
                                        <option value="light">فاتح</option>
                                        <option value="dark">داكن</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">حجم الخط</label>
                                    <select class="form-select" id="fontSizeSelect" onchange="changeFontSize()">
                                        <option value="small">صغير</option>
                                        <option value="medium" selected>متوسط</option>
                                        <option value="large">كبير</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-database me-2"></i>إدارة البيانات</h5>
                            </div>
                            <div class="card-body">
                                <button class="btn btn-primary me-2 mb-2" onclick="exportData()">
                                    <i class="fas fa-download me-2"></i>تصدير البيانات
                                </button>
                                <button class="btn btn-warning me-2 mb-2" onclick="importData()">
                                    <i class="fas fa-upload me-2"></i>استيراد البيانات
                                </button>
                                <button class="btn btn-info me-2 mb-2" onclick="backupData()">
                                    <i class="fas fa-save me-2"></i>نسخ احتياطي
                                </button>
                                <button class="btn btn-danger mb-2" onclick="clearAllData()">
                                    <i class="fas fa-trash me-2"></i>مسح جميع البيانات
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-cogs me-2"></i>إعدادات النظام</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">اسم المتجر</label>
                                    <input type="text" class="form-control" id="storeName" placeholder="اسم المتجر">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">عملة النظام</label>
                                    <select class="form-select" id="currencySelect">
                                        <option value="EGP" selected>جنيه مصري (ج.م)</option>
                                        <option value="USD">دولار أمريكي ($)</option>
                                        <option value="EUR">يورو (€)</option>
                                        <option value="SAR">ريال سعودي (ر.س)</option>
                                    </select>
                                </div>
                                <button class="btn btn-success" onclick="saveSystemSettings()">
                                    <i class="fas fa-save me-2"></i>حفظ الإعدادات
                                </button>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-info-circle me-2"></i>معلومات النظام</h5>
                            </div>
                            <div class="card-body">
                                <p><strong>إصدار النظام:</strong> 2.0.0 (محسن)</p>
                                <p><strong>تاريخ آخر تحديث:</strong> ${new Date().toLocaleDateString('ar-EG')}</p>
                                <p><strong>حالة النظام:</strong> <span class="badge bg-success">يعمل بشكل طبيعي</span></p>
                                <button class="btn btn-info" onclick="runSystemDiagnostics()">
                                    <i class="fas fa-stethoscope me-2"></i>تشخيص النظام
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // دالة التهيئة الشاملة للنظام
        async function initializeSystem() {
            try {
                console.log('🚀 بدء تهيئة نظام Black Horse POS...');

                // عرض شاشة التحميل
                showLoadingScreen();

                // فحص صحة النظام
                const healthCheck = await window.SystemHealthChecker.checkSystemHealth();
                const healthSummary = window.SystemHealthChecker.getHealthSummary();

                console.log(`📊 صحة النظام: ${healthSummary.healthPercentage}%`);

                // إذا كانت هناك أخطاء حرجة، عرض تحذير
                if (healthSummary.errors > 0) {
                    console.warn('⚠️ تم اكتشاف بعض المشاكل في النظام');
                    if (window.Utils) {
                        window.Utils.showMessage(
                            `تم اكتشاف ${healthSummary.errors} مشكلة في النظام. يرجى مراجعة وحدة التحكم للتفاصيل.`,
                            'warning'
                        );
                    }
                }

                // تهيئة مدير التطبيق
                if (window.AppInitializer) {
                    await window.AppInitializer.initialize();
                } else {
                    console.error('❌ AppInitializer غير متاح');
                }

                // تهيئة محسن الأصول
                if (window.AssetOptimizer) {
                    await window.AssetOptimizer.initialize();
                    console.log('✅ تم تهيئة محسن الأصول');
                }

                // تهيئة محسن الأداء
                if (window.PerformanceOptimizer) {
                    await window.PerformanceOptimizer.initialize();
                    console.log('✅ تم تهيئة محسن الأداء');
                }

                // تهيئة لوحة مراقبة الأداء
                if (window.PerformanceDashboard) {
                    window.PerformanceDashboard.initialize();
                    console.log('✅ تم تهيئة لوحة مراقبة الأداء');
                }

                // تحميل البيانات الأولية
                await loadDashboardData();

                // إخفاء شاشة التحميل وعرض التطبيق
                hideLoadingScreen();
                showMainApplication();

                console.log('🎉 تم تهيئة النظام بنجاح!');

                // عرض رسالة ترحيب
                if (window.Utils) {
                    setTimeout(() => {
                        window.Utils.showMessage(
                            `مرحباً بك في Black Horse POS المحسن! صحة النظام: ${healthSummary.healthPercentage}%`,
                            'success'
                        );
                    }, 1000);
                }

            } catch (error) {
                console.error('❌ خطأ في تهيئة النظام:', error);
                window.ErrorHandler.logError('System Initialization Error', error);

                // عرض رسالة خطأ للمستخدم
                hideLoadingScreen();
                showErrorScreen(error);
            }
        }

        // عرض شاشة التحميل
        function showLoadingScreen() {
            const loadingHTML = `
                <div id="loading-screen" style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                    color: white;
                    font-family: 'Cairo', sans-serif;
                ">
                    <div style="text-align: center;">
                        <div style="
                            width: 80px;
                            height: 80px;
                            border: 4px solid rgba(255,255,255,0.3);
                            border-top: 4px solid white;
                            border-radius: 50%;
                            animation: spin 1s linear infinite;
                            margin: 0 auto 20px;
                        "></div>
                        <h2 style="margin: 0 0 10px 0;">🐎 Black Horse POS</h2>
                        <p style="margin: 0; opacity: 0.8;">جاري تحميل النظام المحسن...</p>
                        <div id="loading-progress" style="
                            width: 300px;
                            height: 4px;
                            background: rgba(255,255,255,0.3);
                            border-radius: 2px;
                            margin: 20px auto;
                            overflow: hidden;
                        ">
                            <div style="
                                width: 0%;
                                height: 100%;
                                background: white;
                                border-radius: 2px;
                                animation: progress 3s ease-in-out infinite;
                            "></div>
                        </div>
                    </div>
                    <style>
                        @keyframes spin {
                            0% { transform: rotate(0deg); }
                            100% { transform: rotate(360deg); }
                        }
                        @keyframes progress {
                            0% { width: 0%; }
                            50% { width: 70%; }
                            100% { width: 100%; }
                        }
                    </style>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', loadingHTML);
        }

        // إخفاء شاشة التحميل
        function hideLoadingScreen() {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                loadingScreen.style.opacity = '0';
                loadingScreen.style.transition = 'opacity 0.5s ease-out';
                setTimeout(() => {
                    loadingScreen.remove();
                }, 500);
            }
        }

        // عرض التطبيق الرئيسي
        function showMainApplication() {
            const mainContainer = document.querySelector('.main-container');
            if (mainContainer) {
                mainContainer.style.display = 'flex';
                mainContainer.style.opacity = '0';
                mainContainer.style.transition = 'opacity 0.5s ease-in';
                setTimeout(() => {
                    mainContainer.style.opacity = '1';
                }, 100);
            }
        }

        // عرض شاشة الخطأ
        function showErrorScreen(error) {
            const errorHTML = `
                <div id="error-screen" style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                    color: white;
                    font-family: 'Cairo', sans-serif;
                    text-align: center;
                    padding: 20px;
                ">
                    <div style="max-width: 600px;">
                        <h1 style="font-size: 4rem; margin: 0 0 20px 0;">⚠️</h1>
                        <h2 style="margin: 0 0 20px 0;">خطأ في تهيئة النظام</h2>
                        <p style="margin: 0 0 20px 0; opacity: 0.9;">
                            عذراً، حدث خطأ أثناء تحميل النظام. يرجى إعادة تحميل الصفحة أو الاتصال بالدعم التقني.
                        </p>
                        <div style="
                            background: rgba(255,255,255,0.1);
                            padding: 15px;
                            border-radius: 8px;
                            margin: 20px 0;
                            text-align: right;
                            font-family: monospace;
                            font-size: 0.9rem;
                        ">
                            ${error.message || error}
                        </div>
                        <button onclick="location.reload()" style="
                            background: white;
                            color: #ee5a24;
                            border: none;
                            padding: 12px 24px;
                            border-radius: 6px;
                            font-family: 'Cairo', sans-serif;
                            font-weight: 600;
                            cursor: pointer;
                            font-size: 1rem;
                        ">
                            🔄 إعادة تحميل الصفحة
                        </button>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', errorHTML);
        }

        // دوال الإعدادات
        function changeTheme() {
            const theme = document.getElementById('themeSelect').value;
            document.body.className = theme === 'dark' ? 'theme-dark' : 'theme-light';
            localStorage.setItem('theme', theme);

            if (window.Utils) {
                window.Utils.showMessage(`تم تغيير المظهر إلى ${theme === 'dark' ? 'الداكن' : 'الفاتح'}`, 'success');
            }
        }

        function changeFontSize() {
            const fontSize = document.getElementById('fontSizeSelect').value;
            const sizes = { small: '14px', medium: '16px', large: '18px' };
            document.body.style.fontSize = sizes[fontSize];
            localStorage.setItem('fontSize', fontSize);

            if (window.Utils) {
                window.Utils.showMessage('تم تغيير حجم الخط', 'success');
            }
        }

        async function exportData() {
            try {
                if (!window.UnifiedStorage) {
                    throw new Error('نظام التخزين غير متاح');
                }

                const data = {
                    products: await window.UnifiedStorage.load('products', []),
                    customers: await window.UnifiedStorage.load('customers', []),
                    sales: await window.UnifiedStorage.load('sales', []),
                    expenses: await window.UnifiedStorage.load('expenses', []),
                    exportDate: new Date().toISOString(),
                    version: '2.0.0'
                };

                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `black-horse-pos-backup-${new Date().toISOString().split('T')[0]}.json`;
                a.click();
                URL.revokeObjectURL(url);

                if (window.Utils) {
                    window.Utils.showMessage('تم تصدير البيانات بنجاح', 'success');
                }

            } catch (error) {
                console.error('❌ Error exporting data:', error);
                if (window.Utils) {
                    window.Utils.showMessage('خطأ في تصدير البيانات', 'error');
                }
            }
        }

        function importData() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = async (e) => {
                try {
                    const file = e.target.files[0];
                    if (!file) return;

                    const text = await file.text();
                    const data = JSON.parse(text);

                    if (!data.products || !data.customers || !data.sales) {
                        throw new Error('ملف البيانات غير صحيح');
                    }

                    if (window.UnifiedStorage) {
                        await window.UnifiedStorage.save('products', data.products);
                        await window.UnifiedStorage.save('customers', data.customers);
                        await window.UnifiedStorage.save('sales', data.sales);
                        if (data.expenses) {
                            await window.UnifiedStorage.save('expenses', data.expenses);
                        }

                        if (window.Utils) {
                            window.Utils.showMessage('تم استيراد البيانات بنجاح', 'success');
                        }

                        // تحديث لوحة التحكم
                        loadDashboardData();
                    }

                } catch (error) {
                    console.error('❌ Error importing data:', error);
                    if (window.Utils) {
                        window.Utils.showMessage('خطأ في استيراد البيانات', 'error');
                    }
                }
            };
            input.click();
        }

        async function backupData() {
            try {
                await exportData();
                if (window.Utils) {
                    window.Utils.showMessage('تم إنشاء النسخة الاحتياطية', 'success');
                }
            } catch (error) {
                console.error('❌ Error creating backup:', error);
                if (window.Utils) {
                    window.Utils.showMessage('خطأ في إنشاء النسخة الاحتياطية', 'error');
                }
            }
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                if (confirm('تأكيد أخير: سيتم مسح جميع المنتجات والعملاء والمبيعات والمصروفات!')) {
                    try {
                        localStorage.clear();
                        if (window.Utils) {
                            window.Utils.showMessage('تم مسح جميع البيانات', 'warning');
                        }
                        setTimeout(() => location.reload(), 2000);
                    } catch (error) {
                        console.error('❌ Error clearing data:', error);
                        if (window.Utils) {
                            window.Utils.showMessage('خطأ في مسح البيانات', 'error');
                        }
                    }
                }
            }
        }

        function saveSystemSettings() {
            try {
                const storeName = document.getElementById('storeName').value;
                const currency = document.getElementById('currencySelect').value;

                const settings = {
                    storeName: storeName || 'Black Horse POS',
                    currency: currency,
                    updatedAt: new Date().toISOString()
                };

                localStorage.setItem('systemSettings', JSON.stringify(settings));

                if (window.Utils) {
                    window.Utils.showMessage('تم حفظ إعدادات النظام', 'success');
                }

            } catch (error) {
                console.error('❌ Error saving settings:', error);
                if (window.Utils) {
                    window.Utils.showMessage('خطأ في حفظ الإعدادات', 'error');
                }
            }
        }

        async function runSystemDiagnostics() {
            try {
                if (window.Utils) {
                    window.Utils.showMessage('جاري تشغيل تشخيص النظام...', 'info');
                }

                if (window.SystemHealthChecker) {
                    const healthCheck = await window.SystemHealthChecker.checkSystemHealth();
                    const summary = window.SystemHealthChecker.getHealthSummary();

                    const message = `
                        تشخيص النظام مكتمل:
                        - صحة النظام: ${summary.healthPercentage}%
                        - الفحوصات الناجحة: ${summary.ok}/${summary.total}
                        - الأخطاء: ${summary.errors}
                    `;

                    if (window.Utils) {
                        window.Utils.showMessage(message, summary.healthPercentage > 80 ? 'success' : 'warning');
                    }
                } else {
                    if (window.Utils) {
                        window.Utils.showMessage('نظام التشخيص غير متاح', 'warning');
                    }
                }

            } catch (error) {
                console.error('❌ Error running diagnostics:', error);
                if (window.Utils) {
                    window.Utils.showMessage('خطأ في تشغيل التشخيص', 'error');
                }
            }
        }

        // تحميل الإعدادات المحفوظة عند بدء التشغيل
        function loadSavedSettings() {
            try {
                // تحميل المظهر
                const savedTheme = localStorage.getItem('theme') || 'light';
                document.body.className = savedTheme === 'dark' ? 'theme-dark' : 'theme-light';

                // تحميل حجم الخط
                const savedFontSize = localStorage.getItem('fontSize') || 'medium';
                const sizes = { small: '14px', medium: '16px', large: '18px' };
                document.body.style.fontSize = sizes[savedFontSize];

                // تحميل إعدادات النظام
                const systemSettings = localStorage.getItem('systemSettings');
                if (systemSettings) {
                    const settings = JSON.parse(systemSettings);
                    // يمكن استخدام هذه الإعدادات في أجزاء أخرى من النظام
                    window.systemSettings = settings;
                }

            } catch (error) {
                console.error('❌ Error loading saved settings:', error);
            }
        }

        // ===== وظائف إدارة المنتجات المحسنة =====

        function saveProduct() {
            try {
                const productData = {
                    id: Date.now().toString(),
                    name: document.getElementById('productName').value,
                    code: document.getElementById('productCode').value || Date.now().toString(),
                    category: document.getElementById('productCategory').value,
                    price: parseFloat(document.getElementById('productPrice').value),
                    quantity: parseInt(document.getElementById('productQuantity').value),
                    minStock: parseInt(document.getElementById('productMinStock').value),
                    description: document.getElementById('productDescription').value,
                    createdAt: new Date().toISOString()
                };

                // التحقق من صحة البيانات
                if (!productData.name || !productData.category || !productData.price || productData.quantity < 0) {
                    if (window.Utils) {
                        window.Utils.showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
                    } else {
                        alert('يرجى ملء جميع الحقول المطلوبة');
                    }
                    return;
                }

                // حفظ المنتج
                if (window.ProductsManager) {
                    window.ProductsManager.addProduct(productData);
                } else {
                    // حفظ مؤقت في localStorage
                    const products = JSON.parse(localStorage.getItem('products') || '[]');
                    products.push(productData);
                    localStorage.setItem('products', JSON.stringify(products));
                }

                if (window.Utils) {
                    window.Utils.showMessage('تم حفظ المنتج بنجاح', 'success');
                } else {
                    alert('تم حفظ المنتج بنجاح');
                }

                // إغلاق النافذة وتحديث القائمة
                bootstrap.Modal.getInstance(document.getElementById('addProductModal')).hide();
                document.getElementById('productForm').reset();
                loadProducts();

            } catch (error) {
                console.error('Error saving product:', error);
                if (window.Utils) {
                    window.Utils.showMessage('خطأ في حفظ المنتج', 'error');
                } else {
                    alert('خطأ في حفظ المنتج');
                }
            }
        }

        function loadProducts() {
            try {
                const products = JSON.parse(localStorage.getItem('products') || '[]');
                const tbody = document.getElementById('productsTableBody');

                if (products.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="7" class="text-center text-muted">
                                <i class="fas fa-box fa-2x mb-2"></i><br>
                                لا توجد منتجات مضافة بعد
                            </td>
                        </tr>
                    `;
                } else {
                    tbody.innerHTML = products.map(product => `
                        <tr>
                            <td>${product.code}</td>
                            <td>${product.name}</td>
                            <td>${getCategoryName(product.category)}</td>
                            <td>${product.price} ج.م</td>
                            <td>${product.quantity}</td>
                            <td>
                                <span class="badge ${product.quantity > product.minStock ? 'bg-success' : product.quantity > 0 ? 'bg-warning' : 'bg-danger'}">
                                    ${product.quantity > product.minStock ? 'متوفر' : product.quantity > 0 ? 'مخزون منخفض' : 'نفد'}
                                </span>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="editProduct('${product.id}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteProduct('${product.id}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `).join('');
                }

                // تحديث الإحصائيات
                updateProductStats(products);

            } catch (error) {
                console.error('Error loading products:', error);
            }
        }

        function getCategoryName(category) {
            const categories = {
                'electronics': 'إلكترونيات',
                'clothing': 'ملابس',
                'food': 'أطعمة',
                'books': 'كتب'
            };
            return categories[category] || category;
        }

        function updateProductStats(products) {
            document.getElementById('totalProductsCount').textContent = products.length;
            document.getElementById('inStockCount').textContent = products.filter(p => p.quantity > p.minStock).length;
            document.getElementById('lowStockCount').textContent = products.filter(p => p.quantity <= p.minStock && p.quantity > 0).length;
            document.getElementById('outOfStockCount').textContent = products.filter(p => p.quantity === 0).length;
        }

        // ===== وظائف إدارة العملاء =====
        function showAddCustomerModal() {
            const modal = new bootstrap.Modal(document.getElementById('addCustomerModal'));
            modal.show();
        }

        function saveCustomer() {
            try {
                const customerData = {
                    id: Date.now().toString(),
                    name: document.getElementById('customerName').value,
                    phone: document.getElementById('customerPhone').value,
                    email: document.getElementById('customerEmail').value,
                    address: document.getElementById('customerAddress').value,
                    type: document.getElementById('customerType').value,
                    totalPurchases: 0,
                    createdAt: new Date().toISOString()
                };

                // التحقق من صحة البيانات
                if (!customerData.name || !customerData.phone) {
                    Utils.showMessage('يرجى ملء الحقول المطلوبة', 'error');
                    return;
                }

                // حفظ العميل
                const customers = JSON.parse(localStorage.getItem('customers') || '[]');
                customers.push(customerData);
                localStorage.setItem('customers', JSON.stringify(customers));

                Utils.showMessage('تم حفظ العميل بنجاح', 'success');

                // إغلاق النافذة وتحديث القائمة
                bootstrap.Modal.getInstance(document.getElementById('addCustomerModal')).hide();
                document.getElementById('customerForm').reset();
                loadCustomers();

            } catch (error) {
                console.error('Error saving customer:', error);
                Utils.showMessage('خطأ في حفظ العميل', 'error');
            }
        }

        function loadCustomers() {
            try {
                const customers = JSON.parse(localStorage.getItem('customers') || '[]');
                const tbody = document.getElementById('customersTableBody');

                if (customers.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="7" class="text-center text-muted">
                                <i class="fas fa-users fa-2x mb-2"></i><br>
                                لا يوجد عملاء مسجلون بعد
                            </td>
                        </tr>
                    `;
                } else {
                    tbody.innerHTML = customers.map((customer, index) => `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${customer.name}</td>
                            <td>${customer.phone}</td>
                            <td>${customer.email || '-'}</td>
                            <td>
                                <span class="badge ${customer.type === 'vip' ? 'bg-warning' : customer.type === 'wholesale' ? 'bg-info' : 'bg-secondary'}">
                                    ${getCustomerTypeName(customer.type)}
                                </span>
                            </td>
                            <td>${customer.totalPurchases} ج.م</td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="editCustomer('${customer.id}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteCustomer('${customer.id}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `).join('');
                }

                // تحديث الإحصائيات
                updateCustomerStats(customers);

            } catch (error) {
                console.error('Error loading customers:', error);
            }
        }

        function getCustomerTypeName(type) {
            const types = {
                'regular': 'عادي',
                'vip': 'مميز',
                'wholesale': 'جملة'
            };
            return types[type] || type;
        }

        function updateCustomerStats(customers) {
            document.getElementById('totalCustomersCount').textContent = customers.length;
            document.getElementById('activeCustomersCount').textContent = customers.filter(c => c.totalPurchases > 0).length;
            document.getElementById('vipCustomersCount').textContent = customers.filter(c => c.type === 'vip').length;

            const thisMonth = new Date().getMonth();
            const newCustomers = customers.filter(c => new Date(c.createdAt).getMonth() === thisMonth);
            document.getElementById('newCustomersCount').textContent = newCustomers.length;
        }

        // ===== وظائف إدارة المصروفات =====
        function showAddExpenseModal() {
            // تعيين التاريخ الحالي
            document.getElementById('expenseDate').value = new Date().toISOString().split('T')[0];

            const modal = new bootstrap.Modal(document.getElementById('addExpenseModal'));
            modal.show();
        }

        function saveExpense() {
            try {
                const expenseData = {
                    id: Date.now().toString(),
                    description: document.getElementById('expenseDescription').value,
                    amount: parseFloat(document.getElementById('expenseAmount').value),
                    date: document.getElementById('expenseDate').value,
                    category: document.getElementById('expenseCategory').value,
                    paymentMethod: document.getElementById('expensePaymentMethod').value,
                    notes: document.getElementById('expenseNotes').value,
                    createdAt: new Date().toISOString()
                };

                // التحقق من صحة البيانات
                if (!expenseData.description || !expenseData.amount || !expenseData.date || !expenseData.category) {
                    Utils.showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
                    return;
                }

                // حفظ المصروف
                const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
                expenses.push(expenseData);
                localStorage.setItem('expenses', JSON.stringify(expenses));

                Utils.showMessage('تم حفظ المصروف بنجاح', 'success');

                // إغلاق النافذة وتحديث القائمة
                bootstrap.Modal.getInstance(document.getElementById('addExpenseModal')).hide();
                document.getElementById('expenseForm').reset();
                loadExpenses();

            } catch (error) {
                console.error('Error saving expense:', error);
                Utils.showMessage('خطأ في حفظ المصروف', 'error');
            }
        }

        function loadExpenses() {
            try {
                const expenses = JSON.parse(localStorage.getItem('expenses') || '[]');
                const tbody = document.getElementById('expensesTableBody');

                if (expenses.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="7" class="text-center text-muted">
                                <i class="fas fa-receipt fa-2x mb-2"></i><br>
                                لا توجد مصروفات مسجلة بعد
                            </td>
                        </tr>
                    `;
                } else {
                    tbody.innerHTML = expenses.map(expense => `
                        <tr>
                            <td>${new Date(expense.date).toLocaleDateString('ar-EG')}</td>
                            <td>${expense.description}</td>
                            <td>${getExpenseCategoryName(expense.category)}</td>
                            <td>${expense.amount} ج.م</td>
                            <td>${getPaymentMethodName(expense.paymentMethod)}</td>
                            <td>${expense.notes || '-'}</td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="editExpense('${expense.id}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteExpense('${expense.id}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `).join('');
                }

                // تحديث الإحصائيات
                updateExpenseStats(expenses);

            } catch (error) {
                console.error('Error loading expenses:', error);
            }
        }

        function getExpenseCategoryName(category) {
            const categories = {
                'rent': 'إيجار',
                'utilities': 'مرافق',
                'supplies': 'مستلزمات',
                'marketing': 'تسويق',
                'maintenance': 'صيانة',
                'other': 'أخرى'
            };
            return categories[category] || category;
        }

        function getPaymentMethodName(method) {
            const methods = {
                'cash': 'نقدي',
                'card': 'بطاقة',
                'bank': 'تحويل بنكي',
                'check': 'شيك'
            };
            return methods[method] || method;
        }

        function updateExpenseStats(expenses) {
            const total = expenses.reduce((sum, exp) => sum + exp.amount, 0);
            document.getElementById('totalExpensesAmount').textContent = total + ' ج.م';

            const today = new Date().toISOString().split('T')[0];
            const todayExpenses = expenses.filter(exp => exp.date === today);
            const todayTotal = todayExpenses.reduce((sum, exp) => sum + exp.amount, 0);
            document.getElementById('todayExpenses').textContent = todayTotal + ' ج.م';

            const thisMonth = new Date().getMonth();
            const monthlyExpenses = expenses.filter(exp => new Date(exp.date).getMonth() === thisMonth);
            const monthlyTotal = monthlyExpenses.reduce((sum, exp) => sum + exp.amount, 0);
            document.getElementById('monthlyExpenses').textContent = monthlyTotal + ' ج.م';

            document.getElementById('expensesCount').textContent = expenses.length;
        }

        // بدء التهيئة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            loadSavedSettings();
            initializeSystem();

            // تحميل البيانات
            loadProducts();
            loadCustomers();
            loadExpenses();
            loadUsers();
            loadSuppliers();
        });

        // ===== دوال إدارة المستخدمين =====
        function showAddUserModal() {
            document.getElementById('userForm').reset();
            new bootstrap.Modal(document.getElementById('addUserModal')).show();
        }

        async function saveUser() {
            const name = document.getElementById('userName').value.trim();
            const username = document.getElementById('userUsername').value.trim();
            const email = document.getElementById('userEmail').value.trim();
            const phone = document.getElementById('userPhone').value.trim();
            const password = document.getElementById('userPassword').value;
            const passwordConfirm = document.getElementById('userPasswordConfirm').value;
            const role = document.getElementById('userRole').value;
            const status = document.getElementById('userStatus').value;
            const address = document.getElementById('userAddress').value.trim();

            if (!name || !username || !email || !password || !role) {
                if (window.Utils) {
                    window.Utils.showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
                } else {
                    alert('يرجى ملء جميع الحقول المطلوبة');
                }
                return;
            }

            if (password !== passwordConfirm) {
                if (window.Utils) {
                    window.Utils.showMessage('كلمة المرور غير متطابقة', 'error');
                } else {
                    alert('كلمة المرور غير متطابقة');
                }
                return;
            }

            const user = {
                id: Date.now().toString(),
                name,
                username,
                email,
                phone,
                password: btoa(password), // تشفير بسيط
                role,
                status,
                address,
                createdAt: new Date().toISOString(),
                lastLogin: null
            };

            try {
                const users = JSON.parse(localStorage.getItem('users') || '[]');

                // التحقق من عدم تكرار اسم المستخدم
                if (users.some(u => u.username === username)) {
                    if (window.Utils) {
                        window.Utils.showMessage('اسم المستخدم موجود بالفعل', 'error');
                    } else {
                        alert('اسم المستخدم موجود بالفعل');
                    }
                    return;
                }

                users.push(user);
                localStorage.setItem('users', JSON.stringify(users));

                if (window.Utils) {
                    window.Utils.showMessage('تم إضافة المستخدم بنجاح', 'success');
                } else {
                    alert('تم إضافة المستخدم بنجاح');
                }

                bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();
                loadUsers();
            } catch (error) {
                console.error('خطأ في حفظ المستخدم:', error);
                if (window.Utils) {
                    window.Utils.showMessage('حدث خطأ في حفظ المستخدم', 'error');
                } else {
                    alert('حدث خطأ في حفظ المستخدم');
                }
            }
        }

        function loadUsers() {
            try {
                const users = JSON.parse(localStorage.getItem('users') || '[]');
                const tbody = document.getElementById('usersTableBody');

                if (!tbody) return;

                if (users.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="8" class="text-center">لا توجد مستخدمين</td></tr>';
                    return;
                }

                tbody.innerHTML = users.map(user => `
                    <tr>
                        <td>
                            <div class="avatar-circle">
                                ${user.name.charAt(0)}
                            </div>
                        </td>
                        <td>${user.name}</td>
                        <td>${user.username}</td>
                        <td>${user.email}</td>
                        <td>
                            <span class="badge bg-${getRoleBadgeColor(user.role)}">
                                ${getRoleDisplayName(user.role)}
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-${user.status === 'active' ? 'success' : 'secondary'}">
                                ${user.status === 'active' ? 'نشط' : 'غير نشط'}
                            </span>
                        </td>
                        <td>${user.lastLogin ? new Date(user.lastLogin).toLocaleDateString('ar-EG') : 'لم يسجل دخول'}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="editUser('${user.id}')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteUser('${user.id}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `).join('');

                updateUserStats(users);
            } catch (error) {
                console.error('خطأ في تحميل المستخدمين:', error);
            }
        }

        function getRoleBadgeColor(role) {
            const colors = {
                'admin': 'danger',
                'manager': 'warning',
                'cashier': 'info',
                'employee': 'secondary'
            };
            return colors[role] || 'secondary';
        }

        function getRoleDisplayName(role) {
            const names = {
                'admin': 'مدير النظام',
                'manager': 'مدير فرع',
                'cashier': 'كاشير',
                'employee': 'موظف'
            };
            return names[role] || role;
        }

        function updateUserStats(users) {
            document.getElementById('totalUsersCount').textContent = users.length;
            document.getElementById('activeUsersCount').textContent = users.filter(u => u.status === 'active').length;
            document.getElementById('adminUsersCount').textContent = users.filter(u => u.role === 'admin').length;
            document.getElementById('onlineUsersCount').textContent = '0'; // يحتاج تطبيق نظام الجلسات
        }

        function editUser(userId) {
            // TODO: تنفيذ تعديل المستخدم
            if (window.Utils) {
                window.Utils.showMessage('سيتم تنفيذ هذه الميزة قريباً', 'info');
            } else {
                alert('سيتم تنفيذ هذه الميزة قريباً');
            }
        }

        function deleteUser(userId) {
            if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
                try {
                    const users = JSON.parse(localStorage.getItem('users') || '[]');
                    const updatedUsers = users.filter(user => user.id !== userId);
                    localStorage.setItem('users', JSON.stringify(updatedUsers));

                    if (window.Utils) {
                        window.Utils.showMessage('تم حذف المستخدم بنجاح', 'success');
                    } else {
                        alert('تم حذف المستخدم بنجاح');
                    }

                    loadUsers();
                } catch (error) {
                    console.error('خطأ في حذف المستخدم:', error);
                    if (window.Utils) {
                        window.Utils.showMessage('حدث خطأ في حذف المستخدم', 'error');
                    } else {
                        alert('حدث خطأ في حذف المستخدم');
                    }
                }
            }
        }

        // ===== دوال إدارة الموردين =====
        function showAddSupplierModal() {
            document.getElementById('supplierForm').reset();
            // توليد كود مورد تلقائي
            document.getElementById('supplierCode').value = 'SUP' + Date.now().toString().slice(-6);
            new bootstrap.Modal(document.getElementById('addSupplierModal')).show();
        }

        async function saveSupplier() {
            const name = document.getElementById('supplierName').value.trim();
            const code = document.getElementById('supplierCode').value.trim();
            const phone = document.getElementById('supplierPhone').value.trim();
            const email = document.getElementById('supplierEmail').value.trim();
            const address = document.getElementById('supplierAddress').value.trim();
            const status = document.getElementById('supplierStatus').value;
            const paymentTerms = document.getElementById('supplierPaymentTerms').value;
            const notes = document.getElementById('supplierNotes').value.trim();

            if (!name || !phone) {
                if (window.Utils) {
                    window.Utils.showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
                } else {
                    alert('يرجى ملء جميع الحقول المطلوبة');
                }
                return;
            }

            const supplier = {
                id: Date.now().toString(),
                name,
                code: code || 'SUP' + Date.now().toString().slice(-6),
                phone,
                email,
                address,
                status,
                paymentTerms,
                notes,
                totalPurchases: 0,
                createdAt: new Date().toISOString()
            };

            try {
                const suppliers = JSON.parse(localStorage.getItem('suppliers') || '[]');

                // التحقق من عدم تكرار الكود
                if (suppliers.some(s => s.code === supplier.code)) {
                    if (window.Utils) {
                        window.Utils.showMessage('كود المورد موجود بالفعل', 'error');
                    } else {
                        alert('كود المورد موجود بالفعل');
                    }
                    return;
                }

                suppliers.push(supplier);
                localStorage.setItem('suppliers', JSON.stringify(suppliers));

                if (window.Utils) {
                    window.Utils.showMessage('تم إضافة المورد بنجاح', 'success');
                } else {
                    alert('تم إضافة المورد بنجاح');
                }

                bootstrap.Modal.getInstance(document.getElementById('addSupplierModal')).hide();
                loadSuppliers();
            } catch (error) {
                console.error('خطأ في حفظ المورد:', error);
                if (window.Utils) {
                    window.Utils.showMessage('حدث خطأ في حفظ المورد', 'error');
                } else {
                    alert('حدث خطأ في حفظ المورد');
                }
            }
        }

        function loadSuppliers() {
            try {
                const suppliers = JSON.parse(localStorage.getItem('suppliers') || '[]');
                const tbody = document.getElementById('suppliersTableBody');

                if (!tbody) return;

                if (suppliers.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="8" class="text-center">لا توجد موردين</td></tr>';
                    return;
                }

                tbody.innerHTML = suppliers.map(supplier => `
                    <tr>
                        <td>${supplier.code}</td>
                        <td>${supplier.name}</td>
                        <td>${supplier.phone}</td>
                        <td>${supplier.email || '-'}</td>
                        <td>${supplier.address || '-'}</td>
                        <td>${supplier.totalPurchases.toLocaleString()} ج.م</td>
                        <td>
                            <span class="badge bg-${supplier.status === 'active' ? 'success' : 'secondary'}">
                                ${supplier.status === 'active' ? 'نشط' : 'غير نشط'}
                            </span>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="editSupplier('${supplier.id}')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteSupplier('${supplier.id}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `).join('');

                updateSupplierStats(suppliers);
            } catch (error) {
                console.error('خطأ في تحميل الموردين:', error);
            }
        }

        function updateSupplierStats(suppliers) {
            document.getElementById('totalSuppliersCount').textContent = suppliers.length;
            document.getElementById('activeSuppliersCount').textContent = suppliers.filter(s => s.status === 'active').length;
            document.getElementById('totalPurchasesAmount').textContent = suppliers.reduce((sum, s) => sum + s.totalPurchases, 0).toLocaleString() + ' ج.م';
            document.getElementById('pendingOrdersCount').textContent = '0'; // يحتاج تطبيق نظام الطلبات
        }

        function editSupplier(supplierId) {
            // TODO: تنفيذ تعديل المورد
            if (window.Utils) {
                window.Utils.showMessage('سيتم تنفيذ هذه الميزة قريباً', 'info');
            } else {
                alert('سيتم تنفيذ هذه الميزة قريباً');
            }
        }

        function deleteSupplier(supplierId) {
            if (confirm('هل أنت متأكد من حذف هذا المورد؟')) {
                try {
                    const suppliers = JSON.parse(localStorage.getItem('suppliers') || '[]');
                    const updatedSuppliers = suppliers.filter(supplier => supplier.id !== supplierId);
                    localStorage.setItem('suppliers', JSON.stringify(updatedSuppliers));

                    if (window.Utils) {
                        window.Utils.showMessage('تم حذف المورد بنجاح', 'success');
                    } else {
                        alert('تم حذف المورد بنجاح');
                    }

                    loadSuppliers();
                } catch (error) {
                    console.error('خطأ في حذف المورد:', error);
                    if (window.Utils) {
                        window.Utils.showMessage('حدث خطأ في حذف المورد', 'error');
                    } else {
                        alert('حدث خطأ في حذف المورد');
                    }
                }
            }
        }

        // ===== دوال الصلاحيات =====
        function savePermissions() {
            if (window.Utils) {
                window.Utils.showMessage('تم حفظ الصلاحيات بنجاح', 'success');
            } else {
                alert('تم حفظ الصلاحيات بنجاح');
            }
        }

        function showAddRoleModal() {
            if (window.Utils) {
                window.Utils.showMessage('سيتم تنفيذ هذه الميزة قريباً', 'info');
            } else {
                alert('سيتم تنفيذ هذه الميزة قريباً');
            }
        }

        // ===== دوال إدارة المخزون =====
        function showStockInModal() {
            if (window.Utils) {
                window.Utils.showMessage('سيتم تنفيذ هذه الميزة قريباً', 'info');
            } else {
                alert('سيتم تنفيذ هذه الميزة قريباً');
            }
        }

        function showStockOutModal() {
            if (window.Utils) {
                window.Utils.showMessage('سيتم تنفيذ هذه الميزة قريباً', 'info');
            } else {
                alert('سيتم تنفيذ هذه الميزة قريباً');
            }
        }

        function exportInventoryReport() {
            if (window.Utils) {
                window.Utils.showMessage('سيتم تنفيذ هذه الميزة قريباً', 'info');
            } else {
                alert('سيتم تنفيذ هذه الميزة قريباً');
            }
        }

        function exportSuppliersReport() {
            if (window.Utils) {
                window.Utils.showMessage('سيتم تنفيذ هذه الميزة قريباً', 'info');
            } else {
                alert('سيتم تنفيذ هذه الميزة قريباً');
            }
        }

        // ===== دوال إدارة المخزون =====
        function loadInventoryData() {
            try {
                const products = JSON.parse(localStorage.getItem('products') || '[]');
                const tbody = document.getElementById('inventoryTableBody');

                if (!tbody) return;

                if (products.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="10" class="text-center">لا توجد منتجات في المخزون</td></tr>';
                    updateInventoryStats([]);
                    return;
                }

                tbody.innerHTML = products.map(product => {
                    const stockValue = (product.quantity || 0) * (product.price || 0);
                    const status = getStockStatus(product.quantity, product.minStock);

                    return `
                        <tr>
                            <td>${product.code || product.id}</td>
                            <td>${product.name}</td>
                            <td>${product.category || '-'}</td>
                            <td>
                                <span class="badge bg-${status.color}">
                                    ${product.quantity || 0}
                                </span>
                            </td>
                            <td>${product.minStock || 0}</td>
                            <td>${(product.costPrice || product.price || 0).toLocaleString()} ج.م</td>
                            <td>${(product.price || 0).toLocaleString()} ج.م</td>
                            <td>${stockValue.toLocaleString()} ج.م</td>
                            <td>
                                <span class="badge bg-${status.color}">
                                    ${status.text}
                                </span>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-success me-1" onclick="adjustStock('${product.id}', 'in')" title="إدخال مخزون">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-warning" onclick="adjustStock('${product.id}', 'out')" title="إخراج مخزون">
                                    <i class="fas fa-minus"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                }).join('');

                updateInventoryStats(products);
            } catch (error) {
                console.error('خطأ في تحميل بيانات المخزون:', error);
            }
        }

        function getStockStatus(quantity, minStock) {
            const qty = quantity || 0;
            const min = minStock || 0;

            if (qty === 0) {
                return { color: 'danger', text: 'نفد' };
            } else if (qty <= min) {
                return { color: 'warning', text: 'منخفض' };
            } else {
                return { color: 'success', text: 'متوفر' };
            }
        }

        function updateInventoryStats(products) {
            const totalValue = products.reduce((sum, p) => sum + ((p.quantity || 0) * (p.price || 0)), 0);
            const availableItems = products.filter(p => (p.quantity || 0) > 0).length;
            const lowStockItems = products.filter(p => (p.quantity || 0) <= (p.minStock || 0) && (p.quantity || 0) > 0).length;
            const outOfStockItems = products.filter(p => (p.quantity || 0) === 0).length;

            document.getElementById('totalInventoryValue').textContent = totalValue.toLocaleString() + ' ج.م';
            document.getElementById('availableItemsCount').textContent = availableItems;
            document.getElementById('lowStockItemsCount').textContent = lowStockItems;
            document.getElementById('outOfStockItemsCount').textContent = outOfStockItems;
        }

        function adjustStock(productId, type) {
            const quantity = prompt(`كم الكمية التي تريد ${type === 'in' ? 'إدخالها' : 'إخراجها'}؟`);

            if (!quantity || isNaN(quantity) || parseInt(quantity) <= 0) {
                if (window.Utils) {
                    window.Utils.showMessage('يرجى إدخال كمية صحيحة', 'error');
                } else {
                    alert('يرجى إدخال كمية صحيحة');
                }
                return;
            }

            try {
                const products = JSON.parse(localStorage.getItem('products') || '[]');
                const productIndex = products.findIndex(p => p.id === productId);

                if (productIndex === -1) {
                    if (window.Utils) {
                        window.Utils.showMessage('المنتج غير موجود', 'error');
                    } else {
                        alert('المنتج غير موجود');
                    }
                    return;
                }

                const product = products[productIndex];
                const currentQuantity = product.quantity || 0;
                const adjustmentQuantity = parseInt(quantity);

                if (type === 'in') {
                    product.quantity = currentQuantity + adjustmentQuantity;
                } else {
                    if (currentQuantity < adjustmentQuantity) {
                        if (window.Utils) {
                            window.Utils.showMessage('الكمية المطلوب إخراجها أكبر من المتوفر', 'error');
                        } else {
                            alert('الكمية المطلوب إخراجها أكبر من المتوفر');
                        }
                        return;
                    }
                    product.quantity = currentQuantity - adjustmentQuantity;
                }

                products[productIndex] = product;
                localStorage.setItem('products', JSON.stringify(products));

                if (window.Utils) {
                    window.Utils.showMessage(`تم ${type === 'in' ? 'إدخال' : 'إخراج'} المخزون بنجاح`, 'success');
                } else {
                    alert(`تم ${type === 'in' ? 'إدخال' : 'إخراج'} المخزون بنجاح`);
                }

                loadInventoryData();
                loadProducts(); // تحديث صفحة المنتجات أيضاً
            } catch (error) {
                console.error('خطأ في تعديل المخزون:', error);
                if (window.Utils) {
                    window.Utils.showMessage('حدث خطأ في تعديل المخزون', 'error');
                } else {
                    alert('حدث خطأ في تعديل المخزون');
                }
            }
        }

        // ===== دوال إدارة الفئات =====
        function showAddCategoryModal() {
            document.getElementById('categoryForm').reset();
            // توليد كود فئة تلقائي
            document.getElementById('categoryCode').value = 'CAT' + Date.now().toString().slice(-6);
            loadParentCategories();
            new bootstrap.Modal(document.getElementById('addCategoryModal')).show();
        }

        function loadParentCategories() {
            try {
                const categories = JSON.parse(localStorage.getItem('categories') || '[]');
                const select = document.getElementById('categoryParent');
                select.innerHTML = '<option value="">لا يوجد (فئة رئيسية)</option>';

                categories.forEach(category => {
                    if (category.status === 'active') {
                        select.innerHTML += `<option value="${category.id}">${category.name}</option>`;
                    }
                });
            } catch (error) {
                console.error('خطأ في تحميل الفئات الأب:', error);
            }
        }

        function saveCategory() {
            const name = document.getElementById('categoryName').value.trim();
            const code = document.getElementById('categoryCode').value.trim();
            const description = document.getElementById('categoryDescription').value.trim();
            const parent = document.getElementById('categoryParent').value;
            const status = document.getElementById('categoryStatus').value;
            const order = parseInt(document.getElementById('categoryOrder').value) || 0;

            if (!name) {
                if (window.Utils) {
                    window.Utils.showMessage('يرجى إدخال اسم الفئة', 'error');
                } else {
                    alert('يرجى إدخال اسم الفئة');
                }
                return;
            }

            const category = {
                id: Date.now().toString(),
                name,
                code: code || 'CAT' + Date.now().toString().slice(-6),
                description,
                parent,
                status,
                order,
                createdAt: new Date().toISOString()
            };

            try {
                const categories = JSON.parse(localStorage.getItem('categories') || '[]');

                // التحقق من عدم تكرار الاسم
                if (categories.some(c => c.name === category.name)) {
                    if (window.Utils) {
                        window.Utils.showMessage('اسم الفئة موجود بالفعل', 'error');
                    } else {
                        alert('اسم الفئة موجود بالفعل');
                    }
                    return;
                }

                categories.push(category);
                localStorage.setItem('categories', JSON.stringify(categories));

                if (window.Utils) {
                    window.Utils.showMessage('تم إضافة الفئة بنجاح', 'success');
                } else {
                    alert('تم إضافة الفئة بنجاح');
                }

                bootstrap.Modal.getInstance(document.getElementById('addCategoryModal')).hide();
                loadCategories();
            } catch (error) {
                console.error('خطأ في حفظ الفئة:', error);
                if (window.Utils) {
                    window.Utils.showMessage('حدث خطأ في حفظ الفئة', 'error');
                } else {
                    alert('حدث خطأ في حفظ الفئة');
                }
            }
        }

        function loadCategories() {
            try {
                const categories = JSON.parse(localStorage.getItem('categories') || '[]');
                const products = JSON.parse(localStorage.getItem('products') || '[]');
                const tbody = document.getElementById('categoriesTableBody');

                if (!tbody) return;

                if (categories.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="7" class="text-center">لا توجد فئات</td></tr>';
                    updateCategoriesStats([]);
                    return;
                }

                tbody.innerHTML = categories.map(category => {
                    const productCount = products.filter(p => p.category === category.name).length;
                    const parentName = category.parent ?
                        categories.find(c => c.id === category.parent)?.name || 'غير محدد' :
                        'فئة رئيسية';

                    return `
                        <tr>
                            <td>${category.code}</td>
                            <td>${category.name}</td>
                            <td>${category.description || '-'}</td>
                            <td>${productCount}</td>
                            <td>
                                <span class="badge bg-${category.status === 'active' ? 'success' : 'secondary'}">
                                    ${category.status === 'active' ? 'نشط' : 'غير نشط'}
                                </span>
                            </td>
                            <td>${new Date(category.createdAt).toLocaleDateString('ar-EG')}</td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary me-1" onclick="editCategory('${category.id}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteCategory('${category.id}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                }).join('');

                updateCategoriesStats(categories, products);
            } catch (error) {
                console.error('خطأ في تحميل الفئات:', error);
            }
        }

        function updateCategoriesStats(categories, products = null) {
            if (!products) {
                products = JSON.parse(localStorage.getItem('products') || '[]');
            }

            const totalCategories = categories.length;
            const activeCategories = categories.filter(c => c.status === 'active').length;
            const categoriesWithProducts = categories.filter(c =>
                products.some(p => p.category === c.name)
            ).length;
            const emptyCategories = totalCategories - categoriesWithProducts;

            document.getElementById('totalCategoriesCount').textContent = totalCategories;
            document.getElementById('activeCategoriesCount').textContent = activeCategories;
            document.getElementById('categoriesWithProductsCount').textContent = categoriesWithProducts;
            document.getElementById('emptyCategoriesCount').textContent = emptyCategories;
        }

        function editCategory(categoryId) {
            if (window.Utils) {
                window.Utils.showMessage('سيتم تنفيذ هذه الميزة قريباً', 'info');
            } else {
                alert('سيتم تنفيذ هذه الميزة قريباً');
            }
        }

        function deleteCategory(categoryId) {
            if (confirm('هل أنت متأكد من حذف هذه الفئة؟')) {
                try {
                    const categories = JSON.parse(localStorage.getItem('categories') || '[]');
                    const updatedCategories = categories.filter(category => category.id !== categoryId);
                    localStorage.setItem('categories', JSON.stringify(updatedCategories));

                    if (window.Utils) {
                        window.Utils.showMessage('تم حذف الفئة بنجاح', 'success');
                    } else {
                        alert('تم حذف الفئة بنجاح');
                    }

                    loadCategories();
                } catch (error) {
                    console.error('خطأ في حذف الفئة:', error);
                    if (window.Utils) {
                        window.Utils.showMessage('حدث خطأ في حذف الفئة', 'error');
                    } else {
                        alert('حدث خطأ في حذف الفئة');
                    }
                }
            }
        }

        // ===== دوال تنبيهات المخزون =====
        function refreshStockAlerts() {
            loadStockAlerts();
            if (window.Utils) {
                window.Utils.showMessage('تم تحديث تنبيهات المخزون', 'success');
            } else {
                alert('تم تحديث تنبيهات المخزون');
            }
        }

        function loadStockAlerts() {
            try {
                const products = JSON.parse(localStorage.getItem('products') || '[]');
                const tbody = document.getElementById('stockAlertsTableBody');

                if (!tbody) return;

                // تصنيف المنتجات حسب حالة المخزون
                const alerts = products.map(product => {
                    const quantity = product.quantity || 0;
                    const minStock = product.minStock || 0;
                    let alertType = 'good';
                    let alertText = 'مخزون جيد';
                    let alertColor = 'success';

                    if (quantity === 0) {
                        alertType = 'out';
                        alertText = 'نفد المخزون';
                        alertColor = 'danger';
                    } else if (quantity <= minStock * 0.5) {
                        alertType = 'critical';
                        alertText = 'مخزون حرج';
                        alertColor = 'danger';
                    } else if (quantity <= minStock) {
                        alertType = 'low';
                        alertText = 'مخزون منخفض';
                        alertColor = 'warning';
                    }

                    return {
                        ...product,
                        alertType,
                        alertText,
                        alertColor
                    };
                }).filter(product => product.alertType !== 'good'); // عرض التنبيهات فقط

                if (alerts.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="7" class="text-center text-success">لا توجد تنبيهات مخزون! جميع المنتجات في حالة جيدة</td></tr>';
                } else {
                    tbody.innerHTML = alerts.map(product => `
                        <tr>
                            <td>${product.code || product.id}</td>
                            <td>${product.name}</td>
                            <td>
                                <span class="badge bg-${product.alertColor}">
                                    ${product.quantity || 0}
                                </span>
                            </td>
                            <td>${product.minStock || 0}</td>
                            <td>
                                <span class="badge bg-${product.alertColor}">
                                    ${product.alertText}
                                </span>
                            </td>
                            <td>${new Date().toLocaleDateString('ar-EG')}</td>
                            <td>
                                <button class="btn btn-sm btn-outline-success me-1" onclick="adjustStock('${product.id}', 'in')" title="إضافة مخزون">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-primary" onclick="showPage('products')" title="تعديل المنتج">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </td>
                        </tr>
                    `).join('');
                }

                updateStockAlertsStats(products);
            } catch (error) {
                console.error('خطأ في تحميل تنبيهات المخزون:', error);
            }
        }

        function updateStockAlertsStats(products) {
            let outOfStock = 0, lowStock = 0, criticalStock = 0, goodStock = 0;

            products.forEach(product => {
                const quantity = product.quantity || 0;
                const minStock = product.minStock || 0;

                if (quantity === 0) {
                    outOfStock++;
                } else if (quantity <= minStock * 0.5) {
                    criticalStock++;
                } else if (quantity <= minStock) {
                    lowStock++;
                } else {
                    goodStock++;
                }
            });

            document.getElementById('outOfStockCount').textContent = outOfStock;
            document.getElementById('lowStockCount').textContent = lowStock;
            document.getElementById('criticalStockCount').textContent = criticalStock;
            document.getElementById('goodStockCount').textContent = goodStock;
        }

        // ===== دوال تاريخ المبيعات =====
        function refreshSalesHistory() {
            loadSalesHistory();
            if (window.Utils) {
                window.Utils.showMessage('تم تحديث تاريخ المبيعات', 'success');
            } else {
                alert('تم تحديث تاريخ المبيعات');
            }
        }

        function loadSalesHistory() {
            try {
                const sales = JSON.parse(localStorage.getItem('sales') || '[]');
                const tbody = document.getElementById('salesHistoryTableBody');

                if (!tbody) return;

                if (sales.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="8" class="text-center">لا توجد مبيعات</td></tr>';
                    updateSalesHistoryStats([]);
                    return;
                }

                // ترتيب المبيعات حسب التاريخ (الأحدث أولاً)
                const sortedSales = sales.sort((a, b) => new Date(b.date) - new Date(a.date));

                tbody.innerHTML = sortedSales.map(sale => `
                    <tr>
                        <td>${sale.invoiceNumber || sale.id}</td>
                        <td>${new Date(sale.date).toLocaleDateString('ar-EG')}</td>
                        <td>${sale.customerName || 'عميل نقدي'}</td>
                        <td>${(sale.total || 0).toFixed(2)} ج.م</td>
                        <td>
                            <span class="badge bg-${sale.paymentMethod === 'cash' ? 'success' : 'primary'}">
                                ${sale.paymentMethod === 'cash' ? 'نقدي' : 'آجل'}
                            </span>
                        </td>
                        <td>${sale.cashier || 'غير محدد'}</td>
                        <td>
                            <span class="badge bg-${sale.status === 'completed' ? 'success' : sale.status === 'pending' ? 'warning' : 'danger'}">
                                ${sale.status === 'completed' ? 'مكتملة' : sale.status === 'pending' ? 'معلقة' : 'ملغية'}
                            </span>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="viewSaleDetails('${sale.id}')" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-success me-1" onclick="printInvoice('${sale.id}')" title="طباعة الفاتورة">
                                <i class="fas fa-print"></i>
                            </button>
                            ${sale.status === 'completed' ? `
                                <button class="btn btn-sm btn-outline-warning" onclick="returnSale('${sale.id}')" title="إرجاع">
                                    <i class="fas fa-undo"></i>
                                </button>
                            ` : ''}
                        </td>
                    </tr>
                `).join('');

                updateSalesHistoryStats(sortedSales);
            } catch (error) {
                console.error('خطأ في تحميل تاريخ المبيعات:', error);
            }
        }

        function updateSalesHistoryStats(sales) {
            const today = new Date();
            const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());

            const totalSales = sales.length;
            const todaySales = sales.filter(sale => new Date(sale.date) >= startOfDay);
            const monthSales = sales.filter(sale => new Date(sale.date) >= startOfMonth);

            const todayAmount = todaySales.reduce((sum, sale) => sum + (sale.total || 0), 0);
            const monthAmount = monthSales.reduce((sum, sale) => sum + (sale.total || 0), 0);
            const averageAmount = totalSales > 0 ? sales.reduce((sum, sale) => sum + (sale.total || 0), 0) / totalSales : 0;

            document.getElementById('totalSalesCount').textContent = totalSales;
            document.getElementById('todaySalesAmount').textContent = todayAmount.toFixed(2) + ' ج.م';
            document.getElementById('monthSalesAmount').textContent = monthAmount.toFixed(2) + ' ج.م';
            document.getElementById('averageSaleAmount').textContent = averageAmount.toFixed(2) + ' ج.م';
        }

        function filterSalesHistory() {
            const fromDate = document.getElementById('salesFromDate').value;
            const toDate = document.getElementById('salesToDate').value;

            if (!fromDate || !toDate) {
                if (window.Utils) {
                    window.Utils.showMessage('يرجى تحديد تاريخ البداية والنهاية', 'warning');
                } else {
                    alert('يرجى تحديد تاريخ البداية والنهاية');
                }
                return;
            }

            try {
                const sales = JSON.parse(localStorage.getItem('sales') || '[]');
                const filteredSales = sales.filter(sale => {
                    const saleDate = new Date(sale.date);
                    return saleDate >= new Date(fromDate) && saleDate <= new Date(toDate + 'T23:59:59');
                });

                const tbody = document.getElementById('salesHistoryTableBody');
                if (filteredSales.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="8" class="text-center">لا توجد مبيعات في هذه الفترة</td></tr>';
                } else {
                    // إعادة عرض النتائج المفلترة
                    const sortedSales = filteredSales.sort((a, b) => new Date(b.date) - new Date(a.date));
                    tbody.innerHTML = sortedSales.map(sale => `
                        <tr>
                            <td>${sale.invoiceNumber || sale.id}</td>
                            <td>${new Date(sale.date).toLocaleDateString('ar-EG')}</td>
                            <td>${sale.customerName || 'عميل نقدي'}</td>
                            <td>${(sale.total || 0).toFixed(2)} ج.م</td>
                            <td>
                                <span class="badge bg-${sale.paymentMethod === 'cash' ? 'success' : 'primary'}">
                                    ${sale.paymentMethod === 'cash' ? 'نقدي' : 'آجل'}
                                </span>
                            </td>
                            <td>${sale.cashier || 'غير محدد'}</td>
                            <td>
                                <span class="badge bg-${sale.status === 'completed' ? 'success' : sale.status === 'pending' ? 'warning' : 'danger'}">
                                    ${sale.status === 'completed' ? 'مكتملة' : sale.status === 'pending' ? 'معلقة' : 'ملغية'}
                                </span>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary me-1" onclick="viewSaleDetails('${sale.id}')" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-success me-1" onclick="printInvoice('${sale.id}')" title="طباعة الفاتورة">
                                    <i class="fas fa-print"></i>
                                </button>
                                ${sale.status === 'completed' ? `
                                    <button class="btn btn-sm btn-outline-warning" onclick="returnSale('${sale.id}')" title="إرجاع">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                ` : ''}
                            </td>
                        </tr>
                    `).join('');
                }

                updateSalesHistoryStats(filteredSales);

                if (window.Utils) {
                    window.Utils.showMessage(`تم العثور على ${filteredSales.length} عملية بيع في الفترة المحددة`, 'info');
                } else {
                    alert(`تم العثور على ${filteredSales.length} عملية بيع في الفترة المحددة`);
                }
            } catch (error) {
                console.error('خطأ في فلترة المبيعات:', error);
                if (window.Utils) {
                    window.Utils.showMessage('حدث خطأ في فلترة المبيعات', 'error');
                } else {
                    alert('حدث خطأ في فلترة المبيعات');
                }
            }
        }

        function exportSalesHistory() {
            if (window.Utils) {
                window.Utils.showMessage('سيتم تنفيذ ميزة التصدير قريباً', 'info');
            } else {
                alert('سيتم تنفيذ ميزة التصدير قريباً');
            }
        }

        function viewSaleDetails(saleId) {
            if (window.Utils) {
                window.Utils.showMessage('سيتم تنفيذ هذه الميزة قريباً', 'info');
            } else {
                alert('سيتم تنفيذ هذه الميزة قريباً');
            }
        }

        function printInvoice(saleId) {
            if (window.Utils) {
                window.Utils.showMessage('سيتم تنفيذ ميزة الطباعة قريباً', 'info');
            } else {
                alert('سيتم تنفيذ ميزة الطباعة قريباً');
            }
        }

        function returnSale(saleId) {
            if (confirm('هل أنت متأكد من إرجاع هذه العملية؟')) {
                if (window.Utils) {
                    window.Utils.showMessage('سيتم تنفيذ ميزة الإرجاع قريباً', 'info');
                } else {
                    alert('سيتم تنفيذ ميزة الإرجاع قريباً');
                }
            }
        }
    </script>
</body>
</html>
