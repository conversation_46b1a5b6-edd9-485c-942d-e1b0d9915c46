<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Black Horse POS - نظام نقاط البيع</title>
    
    <!-- الخطوط -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- الأيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- الأنماط المخصصة -->
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            direction: rtl;
        }
        
        .main-container {
            display: none;
            min-height: 100vh;
        }
        
        .sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .content-area {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            min-height: 100vh;
            padding: 20px;
        }
        
        .nav-link {
            color: #333;
            padding: 15px 20px;
            margin: 5px 0;
            border-radius: 10px;
            transition: all 0.3s;
        }
        
        .nav-link:hover, .nav-link.active {
            background: #007bff;
            color: white;
            transform: translateX(-5px);
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.9);
        }
        
        .btn {
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
        
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
        }
        
        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .page {
            display: none;
        }
        
        .page.active {
            display: block;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .quick-action {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .quick-action:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        
        .theme-light {
            /* الثيم الفاتح - افتراضي */
        }
        
        .theme-dark {
            background: #1a1a1a;
            color: white;
        }
        
        .theme-dark .card {
            background: rgba(40, 40, 40, 0.9);
            color: white;
        }
        
        .theme-dark .sidebar {
            background: rgba(30, 30, 30, 0.95);
            color: white;
        }
        
        .theme-dark .nav-link {
            color: #ccc;
        }
        
        .theme-dark .nav-link:hover, .theme-dark .nav-link.active {
            background: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <!-- شاشة التحميل -->
    <div id="loading-screen" class="loading-screen">
        <div class="text-center">
            <div class="loading-spinner"></div>
            <h2>🐎 Black Horse POS</h2>
            <p>جاري تحميل النظام...</p>
            <small>نظام نقاط البيع المتطور</small>
        </div>
    </div>

    <!-- التطبيق الرئيسي -->
    <div id="main-app" class="main-container">
        <div class="container-fluid">
            <div class="row">
                <!-- الشريط الجانبي -->
                <div class="col-md-3 col-lg-2 sidebar">
                    <div class="p-3">
                        <h4 class="text-center mb-4">
                            🐎 Black Horse POS
                        </h4>
                        
                        <nav class="nav flex-column">
                            <a href="#" class="nav-link active" onclick="showPage('dashboard')">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                            <a href="#" class="nav-link" onclick="showPage('products')">
                                <i class="fas fa-box me-2"></i>
                                المنتجات
                            </a>
                            <a href="#" class="nav-link" onclick="showPage('customers')">
                                <i class="fas fa-users me-2"></i>
                                العملاء
                            </a>
                            <a href="#" class="nav-link" onclick="showPage('sales')">
                                <i class="fas fa-shopping-cart me-2"></i>
                                المبيعات
                            </a>
                            <a href="#" class="nav-link" onclick="showPage('reports')">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                            <a href="#" class="nav-link" onclick="showPage('expenses')">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                المصروفات
                            </a>
                            <a href="#" class="nav-link" onclick="showPage('settings')">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات
                            </a>
                        </nav>
                    </div>
                </div>

                <!-- المحتوى الرئيسي -->
                <div class="col-md-9 col-lg-10 content-area">
                    <!-- لوحة التحكم -->
                    <div id="dashboard" class="page active">
                        <h2 class="mb-4">لوحة التحكم</h2>
                        
                        <!-- الإحصائيات -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <h5>إجمالي المبيعات</h5>
                                    <h3 id="total-sales">0 ج.م</h3>
                                    <small>هذا الشهر</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <h5>عدد المنتجات</h5>
                                    <h3 id="total-products">0</h3>
                                    <small>في المخزون</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <h5>عدد العملاء</h5>
                                    <h3 id="total-customers">0</h3>
                                    <small>مسجل</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <h5>المعاملات اليوم</h5>
                                    <h3 id="today-transactions">0</h3>
                                    <small>معاملة</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- الإجراءات السريعة -->
                        <div class="row">
                            <div class="col-md-4">
                                <div class="quick-action" onclick="showPage('sales')">
                                    <i class="fas fa-plus-circle fa-3x text-primary mb-3"></i>
                                    <h5>بيع جديد</h5>
                                    <p>إنشاء فاتورة بيع جديدة</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="quick-action" onclick="showPage('products')">
                                    <i class="fas fa-box-open fa-3x text-success mb-3"></i>
                                    <h5>إضافة منتج</h5>
                                    <p>إضافة منتج جديد للمخزون</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="quick-action" onclick="showPage('reports')">
                                    <i class="fas fa-chart-line fa-3x text-info mb-3"></i>
                                    <h5>عرض التقارير</h5>
                                    <p>مراجعة تقارير المبيعات</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صفحة المنتجات -->
                    <div id="products" class="page">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2>إدارة المنتجات</h2>
                            <button class="btn btn-primary" onclick="showAddProductModal()">
                                <i class="fas fa-plus me-2"></i>
                                إضافة منتج جديد
                            </button>
                        </div>
                        
                        <div class="card">
                            <div class="card-body">
                                <div id="products-content">
                                    <p class="text-center text-muted">جاري تحميل المنتجات...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صفحة العملاء -->
                    <div id="customers" class="page">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2>إدارة العملاء</h2>
                            <button class="btn btn-primary" onclick="showAddCustomerModal()">
                                <i class="fas fa-plus me-2"></i>
                                إضافة عميل جديد
                            </button>
                        </div>
                        
                        <div class="card">
                            <div class="card-body">
                                <div id="customers-content">
                                    <p class="text-center text-muted">جاري تحميل العملاء...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صفحة المبيعات -->
                    <div id="sales" class="page">
                        <h2 class="mb-4">نقطة البيع</h2>
                        
                        <div class="card">
                            <div class="card-body">
                                <div id="sales-content">
                                    <p class="text-center text-muted">جاري تحميل نقطة البيع...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صفحة التقارير -->
                    <div id="reports" class="page">
                        <h2 class="mb-4">التقارير والإحصائيات</h2>
                        
                        <div class="card">
                            <div class="card-body">
                                <div id="reports-content">
                                    <p class="text-center text-muted">جاري تحميل التقارير...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صفحة المصروفات -->
                    <div id="expenses" class="page">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2>إدارة المصروفات</h2>
                            <button class="btn btn-primary" onclick="showAddExpenseModal()">
                                <i class="fas fa-plus me-2"></i>
                                إضافة مصروف جديد
                            </button>
                        </div>
                        
                        <div class="card">
                            <div class="card-body">
                                <div id="expenses-content">
                                    <p class="text-center text-muted">جاري تحميل المصروفات...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- صفحة الإعدادات -->
                    <div id="settings" class="page">
                        <h2 class="mb-4">إعدادات النظام</h2>
                        
                        <div class="card">
                            <div class="card-body">
                                <div id="settings-content">
                                    <p class="text-center text-muted">جاري تحميل الإعدادات...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- تحميل الوحدات الأساسية -->
    <script src="src/utils/helpers.js"></script>
    <script src="src/utils/validators.js"></script>
    <script src="src/database/database-optimizer.js"></script>
    <script src="src/database/unified-storage.js"></script>
    <script src="src/database/data-migration.js"></script>
    <script src="src/core/module-manager.js"></script>
    <script src="src/core/consolidated-functions.js"></script>
    <script src="src/core/performance-optimizer.js"></script>
    <script src="src/ui/ui-optimizer.js"></script>
    <script src="src/core/asset-optimizer.js"></script>

    <!-- تحميل الوحدات المحسنة -->
    <script src="src/modules/products/products-manager.js"></script>
    <script src="src/modules/customers/customers-manager.js"></script>
    <script src="src/modules/sales/sales-manager.js"></script>
    <script src="src/modules/reports/reports-manager.js"></script>
    <script src="src/modules/expenses/expenses-manager.js"></script>

    <!-- تحميل لوحة مراقبة الأداء -->
    <script src="src/ui/performance-dashboard.js"></script>

    <script src="src/core/app-initializer.js"></script>
    
    <!-- دوال التنقل الأساسية -->
    <script>
        // دالة التنقل بين الصفحات
        function showPage(pageId) {
            // إخفاء جميع الصفحات
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            
            // إزالة active من جميع روابط التنقل
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            
            // إظهار الصفحة المطلوبة
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
            }
            
            // تفعيل رابط التنقل
            const activeLink = document.querySelector(`[onclick="showPage('${pageId}')"]`);
            if (activeLink) {
                activeLink.classList.add('active');
            }
            
            // تحديث محتوى الصفحة إذا لزم الأمر
            loadPageContent(pageId);
        }
        
        // تحميل محتوى الصفحة
        function loadPageContent(pageId) {
            const contentDiv = document.getElementById(`${pageId}-content`);
            if (!contentDiv) return;
            
            // استخدام المدراء المناسبين لكل صفحة
            switch (pageId) {
                case 'dashboard':
                    loadDashboardData();
                    break;
                case 'products':
                    if (window.ProductsManager) {
                        window.ProductsManager.loadProductsPage();
                    }
                    break;
                case 'customers':
                    if (window.CustomersManager) {
                        window.CustomersManager.loadCustomersPage();
                    }
                    break;
                case 'sales':
                    if (window.SalesManager) {
                        window.SalesManager.loadSalesPage();
                    }
                    break;
                case 'reports':
                    if (window.ReportsManager) {
                        window.ReportsManager.loadReportsPage();
                    }
                    break;
            }
        }
        
        // تحميل بيانات لوحة التحكم
        async function loadDashboardData() {
            try {
                if (window.UnifiedStorage) {
                    const products = await window.UnifiedStorage.load('products') || [];
                    const customers = await window.UnifiedStorage.load('customers') || [];
                    const sales = await window.UnifiedStorage.load('sales') || [];
                    
                    // تحديث الإحصائيات
                    document.getElementById('total-products').textContent = products.length;
                    document.getElementById('total-customers').textContent = customers.length;
                    
                    // حساب إجمالي المبيعات
                    const totalSales = sales.reduce((sum, sale) => sum + (parseFloat(sale.total) || 0), 0);
                    document.getElementById('total-sales').textContent = Utils.formatCurrency(totalSales);
                    
                    // حساب معاملات اليوم
                    const today = new Date().toDateString();
                    const todayTransactions = sales.filter(sale => 
                        new Date(sale.date).toDateString() === today
                    ).length;
                    document.getElementById('today-transactions').textContent = todayTransactions;
                }
            } catch (error) {
                console.error('❌ Error loading dashboard data:', error);
            }
        }
        
        // دوال مؤقتة للنماذج
        function showAddProductModal() {
            if (window.Utils) {
                Utils.showMessage('سيتم تحميل نموذج إضافة المنتج قريباً', 'info');
            }
        }
        
        function showAddCustomerModal() {
            if (window.Utils) {
                Utils.showMessage('سيتم تحميل نموذج إضافة العميل قريباً', 'info');
            }
        }
        
        function showAddExpenseModal() {
            if (window.Utils) {
                Utils.showMessage('سيتم تحميل نموذج إضافة المصروف قريباً', 'info');
            }
        }
    </script>
</body>
</html>
