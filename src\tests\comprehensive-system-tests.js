/**
 * نظام الاختبار الشامل
 * Comprehensive System Testing for Black Horse POS
 * 
 * يقوم بإجراء اختبارات شاملة لجميع مكونات النظام والتحسينات
 */

class ComprehensiveSystemTests {
    constructor() {
        this.testResults = [];
        this.totalTests = 0;
        this.passedTests = 0;
        this.failedTests = 0;
        this.startTime = null;
        this.endTime = null;
        
        // مجموعات الاختبارات
        this.testSuites = {
            database: 'اختبارات قاعدة البيانات',
            ui: 'اختبارات واجهة المستخدم',
            performance: 'اختبارات الأداء',
            modules: 'اختبارات الوحدات',
            integration: 'اختبارات التكامل',
            security: 'اختبارات الأمان',
            optimization: 'اختبارات التحسينات'
        };
        
        console.log('🧪 تم تهيئة نظام الاختبار الشامل');
    }

    /**
     * تشغيل جميع الاختبارات
     */
    async runAllTests() {
        console.log('🚀 بدء الاختبار الشامل للنظام...');
        this.startTime = Date.now();
        this.testResults = [];
        this.totalTests = 0;
        this.passedTests = 0;
        this.failedTests = 0;

        try {
            // اختبارات قاعدة البيانات
            await this.runDatabaseTests();
            
            // اختبارات واجهة المستخدم
            await this.runUITests();
            
            // اختبارات الأداء
            await this.runPerformanceTests();
            
            // اختبارات الوحدات
            await this.runModuleTests();
            
            // اختبارات التكامل
            await this.runIntegrationTests();
            
            // اختبارات الأمان
            await this.runSecurityTests();
            
            // اختبارات التحسينات
            await this.runOptimizationTests();
            
            this.endTime = Date.now();
            
            // إنشاء التقرير النهائي
            const report = this.generateFinalReport();
            console.log('✅ اكتمل الاختبار الشامل');
            
            return report;
            
        } catch (error) {
            console.error('❌ خطأ في الاختبار الشامل:', error);
            this.logTestResult('System Error', 'خطأ عام في النظام', false, error.message);
            return this.generateFinalReport();
        }
    }

    /**
     * اختبارات قاعدة البيانات
     */
    async runDatabaseTests() {
        console.log('🗄️ تشغيل اختبارات قاعدة البيانات...');
        
        // اختبار الاتصال بقاعدة البيانات
        await this.testDatabaseConnection();
        
        // اختبار عمليات CRUD
        await this.testCRUDOperations();
        
        // اختبار الضغط
        await this.testDataCompression();
        
        // اختبار الفهرسة
        await this.testIndexing();
        
        // اختبار المعالجة المجمعة
        await this.testBatchProcessing();
        
        // اختبار النسخ الاحتياطي
        await this.testBackupRestore();
    }

    /**
     * اختبار الاتصال بقاعدة البيانات
     */
    async testDatabaseConnection() {
        try {
            if (window.UnifiedStorage) {
                const testData = { test: 'connection', timestamp: Date.now() };
                await window.UnifiedStorage.setItem('test_connection', testData);
                const retrieved = await window.UnifiedStorage.getItem('test_connection');
                
                if (retrieved && retrieved.test === 'connection') {
                    this.logTestResult('Database', 'اختبار الاتصال بقاعدة البيانات', true);
                    await window.UnifiedStorage.removeItem('test_connection');
                } else {
                    this.logTestResult('Database', 'اختبار الاتصال بقاعدة البيانات', false, 'فشل في استرجاع البيانات');
                }
            } else {
                this.logTestResult('Database', 'اختبار الاتصال بقاعدة البيانات', false, 'UnifiedStorage غير متاح');
            }
        } catch (error) {
            this.logTestResult('Database', 'اختبار الاتصال بقاعدة البيانات', false, error.message);
        }
    }

    /**
     * اختبار عمليات CRUD
     */
    async testCRUDOperations() {
        try {
            if (window.UnifiedStorage) {
                const testId = 'test_crud_' + Date.now();
                const testData = { id: testId, name: 'Test Product', price: 100 };
                
                // Create
                await window.UnifiedStorage.setItem(testId, testData);
                
                // Read
                const readData = await window.UnifiedStorage.getItem(testId);
                if (!readData || readData.name !== 'Test Product') {
                    throw new Error('فشل في قراءة البيانات');
                }
                
                // Update
                testData.price = 150;
                await window.UnifiedStorage.setItem(testId, testData);
                const updatedData = await window.UnifiedStorage.getItem(testId);
                if (!updatedData || updatedData.price !== 150) {
                    throw new Error('فشل في تحديث البيانات');
                }
                
                // Delete
                await window.UnifiedStorage.removeItem(testId);
                const deletedData = await window.UnifiedStorage.getItem(testId);
                if (deletedData) {
                    throw new Error('فشل في حذف البيانات');
                }
                
                this.logTestResult('Database', 'اختبار عمليات CRUD', true);
            } else {
                this.logTestResult('Database', 'اختبار عمليات CRUD', false, 'UnifiedStorage غير متاح');
            }
        } catch (error) {
            this.logTestResult('Database', 'اختبار عمليات CRUD', false, error.message);
        }
    }

    /**
     * اختبار ضغط البيانات
     */
    async testDataCompression() {
        try {
            if (window.LZString && window.UnifiedStorage) {
                const largeData = 'A'.repeat(1000); // بيانات كبيرة للاختبار
                const compressed = window.LZString.compress(largeData);
                const decompressed = window.LZString.decompress(compressed);
                
                if (decompressed === largeData && compressed.length < largeData.length) {
                    this.logTestResult('Database', 'اختبار ضغط البيانات', true);
                } else {
                    this.logTestResult('Database', 'اختبار ضغط البيانات', false, 'فشل في الضغط أو إلغاء الضغط');
                }
            } else {
                this.logTestResult('Database', 'اختبار ضغط البيانات', false, 'LZString غير متاح');
            }
        } catch (error) {
            this.logTestResult('Database', 'اختبار ضغط البيانات', false, error.message);
        }
    }

    /**
     * اختبار الفهرسة
     */
    async testIndexing() {
        try {
            if (window.DatabaseOptimizer) {
                // محاولة إنشاء فهرس تجريبي
                const testIndex = await window.DatabaseOptimizer.createIndex('test_products', 'name');
                if (testIndex) {
                    this.logTestResult('Database', 'اختبار الفهرسة', true);
                } else {
                    this.logTestResult('Database', 'اختبار الفهرسة', false, 'فشل في إنشاء الفهرس');
                }
            } else {
                this.logTestResult('Database', 'اختبار الفهرسة', false, 'DatabaseOptimizer غير متاح');
            }
        } catch (error) {
            this.logTestResult('Database', 'اختبار الفهرسة', false, error.message);
        }
    }

    /**
     * اختبار المعالجة المجمعة
     */
    async testBatchProcessing() {
        try {
            if (window.DatabaseOptimizer && window.DatabaseOptimizer.processBatch) {
                const batchOperations = [
                    { type: 'set', key: 'batch_test_1', value: { name: 'Test 1' } },
                    { type: 'set', key: 'batch_test_2', value: { name: 'Test 2' } },
                    { type: 'set', key: 'batch_test_3', value: { name: 'Test 3' } }
                ];
                
                await window.DatabaseOptimizer.processBatch(batchOperations);
                this.logTestResult('Database', 'اختبار المعالجة المجمعة', true);
                
                // تنظيف البيانات التجريبية
                for (let i = 1; i <= 3; i++) {
                    await window.UnifiedStorage.removeItem(`batch_test_${i}`);
                }
            } else {
                this.logTestResult('Database', 'اختبار المعالجة المجمعة', false, 'المعالجة المجمعة غير متاحة');
            }
        } catch (error) {
            this.logTestResult('Database', 'اختبار المعالجة المجمعة', false, error.message);
        }
    }

    /**
     * اختبار النسخ الاحتياطي
     */
    async testBackupRestore() {
        try {
            if (window.UnifiedStorage && window.UnifiedStorage.exportData) {
                const backup = await window.UnifiedStorage.exportData();
                if (backup && typeof backup === 'string') {
                    this.logTestResult('Database', 'اختبار النسخ الاحتياطي', true);
                } else {
                    this.logTestResult('Database', 'اختبار النسخ الاحتياطي', false, 'فشل في إنشاء النسخة الاحتياطية');
                }
            } else {
                this.logTestResult('Database', 'اختبار النسخ الاحتياطي', false, 'وظيفة النسخ الاحتياطي غير متاحة');
            }
        } catch (error) {
            this.logTestResult('Database', 'اختبار النسخ الاحتياطي', false, error.message);
        }
    }

    /**
     * اختبارات واجهة المستخدم
     */
    async runUITests() {
        console.log('🎨 تشغيل اختبارات واجهة المستخدم...');
        
        // اختبار تحميل العناصر الأساسية
        await this.testUIElementsLoading();
        
        // اختبار الاستجابة
        await this.testResponsiveness();
        
        // اختبار التمرير الافتراضي
        await this.testVirtualScrolling();
        
        // اختبار التحميل التدريجي
        await this.testLazyLoading();
        
        // اختبار الرسوم المتحركة
        await this.testAnimations();
    }

    /**
     * اختبار تحميل العناصر الأساسية
     */
    async testUIElementsLoading() {
        try {
            const requiredElements = [
                'body',
                '.navbar',
                '.main-content',
                '#app'
            ];
            
            let allElementsFound = true;
            const missingElements = [];
            
            requiredElements.forEach(selector => {
                const element = document.querySelector(selector);
                if (!element) {
                    allElementsFound = false;
                    missingElements.push(selector);
                }
            });
            
            if (allElementsFound) {
                this.logTestResult('UI', 'اختبار تحميل العناصر الأساسية', true);
            } else {
                this.logTestResult('UI', 'اختبار تحميل العناصر الأساسية', false, `عناصر مفقودة: ${missingElements.join(', ')}`);
            }
        } catch (error) {
            this.logTestResult('UI', 'اختبار تحميل العناصر الأساسية', false, error.message);
        }
    }

    /**
     * اختبار الاستجابة
     */
    async testResponsiveness() {
        try {
            const viewportWidths = [320, 768, 1024, 1920];
            let responsiveTest = true;
            
            // محاكاة تغيير حجم النافذة
            viewportWidths.forEach(width => {
                // في بيئة حقيقية، يمكن استخدام window.resizeTo
                // هنا نقوم بفحص CSS media queries
                const mediaQuery = window.matchMedia(`(min-width: ${width}px)`);
                if (!mediaQuery) {
                    responsiveTest = false;
                }
            });
            
            this.logTestResult('UI', 'اختبار الاستجابة', responsiveTest);
        } catch (error) {
            this.logTestResult('UI', 'اختبار الاستجابة', false, error.message);
        }
    }

    /**
     * اختبار التمرير الافتراضي
     */
    async testVirtualScrolling() {
        try {
            if (window.UIOptimizer && window.UIOptimizer.VirtualScrollManager) {
                const virtualScroll = new window.UIOptimizer.VirtualScrollManager({
                    container: document.body,
                    itemHeight: 50,
                    items: Array.from({ length: 1000 }, (_, i) => ({ id: i, name: `Item ${i}` }))
                });
                
                if (virtualScroll) {
                    this.logTestResult('UI', 'اختبار التمرير الافتراضي', true);
                } else {
                    this.logTestResult('UI', 'اختبار التمرير الافتراضي', false, 'فشل في إنشاء التمرير الافتراضي');
                }
            } else {
                this.logTestResult('UI', 'اختبار التمرير الافتراضي', false, 'VirtualScrollManager غير متاح');
            }
        } catch (error) {
            this.logTestResult('UI', 'اختبار التمرير الافتراضي', false, error.message);
        }
    }

    /**
     * اختبار التحميل التدريجي
     */
    async testLazyLoading() {
        try {
            if (window.UIOptimizer && window.UIOptimizer.LazyLoader) {
                const lazyLoader = new window.UIOptimizer.LazyLoader();
                if (lazyLoader && typeof lazyLoader.observe === 'function') {
                    this.logTestResult('UI', 'اختبار التحميل التدريجي', true);
                } else {
                    this.logTestResult('UI', 'اختبار التحميل التدريجي', false, 'LazyLoader غير صالح');
                }
            } else {
                this.logTestResult('UI', 'اختبار التحميل التدريجي', false, 'LazyLoader غير متاح');
            }
        } catch (error) {
            this.logTestResult('UI', 'اختبار التحميل التدريجي', false, error.message);
        }
    }

    /**
     * اختبار الرسوم المتحركة
     */
    async testAnimations() {
        try {
            if (window.UIOptimizer && window.UIOptimizer.AnimationManager) {
                const animationManager = new window.UIOptimizer.AnimationManager();
                if (animationManager && typeof animationManager.animate === 'function') {
                    this.logTestResult('UI', 'اختبار الرسوم المتحركة', true);
                } else {
                    this.logTestResult('UI', 'اختبار الرسوم المتحركة', false, 'AnimationManager غير صالح');
                }
            } else {
                this.logTestResult('UI', 'اختبار الرسوم المتحركة', false, 'AnimationManager غير متاح');
            }
        } catch (error) {
            this.logTestResult('UI', 'اختبار الرسوم المتحركة', false, error.message);
        }
    }

    /**
     * اختبارات الأداء
     */
    async runPerformanceTests() {
        console.log('⚡ تشغيل اختبارات الأداء...');
        
        // اختبار سرعة التحميل
        await this.testLoadingSpeed();
        
        // اختبار استخدام الذاكرة
        await this.testMemoryUsage();
        
        // اختبار أداء قاعدة البيانات
        await this.testDatabasePerformance();
        
        // اختبار أداء واجهة المستخدم
        await this.testUIPerformance();
    }

    /**
     * اختبار سرعة التحميل
     */
    async testLoadingSpeed() {
        try {
            const startTime = performance.now();
            
            // محاكاة تحميل البيانات
            await new Promise(resolve => setTimeout(resolve, 100));
            
            const endTime = performance.now();
            const loadTime = endTime - startTime;
            
            // يجب أن يكون التحميل أقل من ثانية واحدة
            if (loadTime < 1000) {
                this.logTestResult('Performance', 'اختبار سرعة التحميل', true, `وقت التحميل: ${loadTime.toFixed(2)}ms`);
            } else {
                this.logTestResult('Performance', 'اختبار سرعة التحميل', false, `وقت التحميل بطيء: ${loadTime.toFixed(2)}ms`);
            }
        } catch (error) {
            this.logTestResult('Performance', 'اختبار سرعة التحميل', false, error.message);
        }
    }

    /**
     * اختبار استخدام الذاكرة
     */
    async testMemoryUsage() {
        try {
            if (performance.memory) {
                const memoryInfo = performance.memory;
                const usedMemoryMB = memoryInfo.usedJSHeapSize / 1024 / 1024;
                
                // يجب أن يكون استخدام الذاكرة أقل من 100 ميجابايت
                if (usedMemoryMB < 100) {
                    this.logTestResult('Performance', 'اختبار استخدام الذاكرة', true, `الذاكرة المستخدمة: ${usedMemoryMB.toFixed(2)}MB`);
                } else {
                    this.logTestResult('Performance', 'اختبار استخدام الذاكرة', false, `استخدام ذاكرة عالي: ${usedMemoryMB.toFixed(2)}MB`);
                }
            } else {
                this.logTestResult('Performance', 'اختبار استخدام الذاكرة', false, 'معلومات الذاكرة غير متاحة');
            }
        } catch (error) {
            this.logTestResult('Performance', 'اختبار استخدام الذاكرة', false, error.message);
        }
    }

    /**
     * اختبار أداء قاعدة البيانات
     */
    async testDatabasePerformance() {
        try {
            if (window.UnifiedStorage) {
                const startTime = performance.now();
                
                // اختبار كتابة وقراءة 100 عنصر
                for (let i = 0; i < 100; i++) {
                    await window.UnifiedStorage.setItem(`perf_test_${i}`, { id: i, data: `test data ${i}` });
                }
                
                for (let i = 0; i < 100; i++) {
                    await window.UnifiedStorage.getItem(`perf_test_${i}`);
                }
                
                const endTime = performance.now();
                const operationTime = endTime - startTime;
                
                // تنظيف البيانات التجريبية
                for (let i = 0; i < 100; i++) {
                    await window.UnifiedStorage.removeItem(`perf_test_${i}`);
                }
                
                // يجب أن تكون العمليات أقل من 5 ثوانٍ
                if (operationTime < 5000) {
                    this.logTestResult('Performance', 'اختبار أداء قاعدة البيانات', true, `وقت العمليات: ${operationTime.toFixed(2)}ms`);
                } else {
                    this.logTestResult('Performance', 'اختبار أداء قاعدة البيانات', false, `أداء بطيء: ${operationTime.toFixed(2)}ms`);
                }
            } else {
                this.logTestResult('Performance', 'اختبار أداء قاعدة البيانات', false, 'UnifiedStorage غير متاح');
            }
        } catch (error) {
            this.logTestResult('Performance', 'اختبار أداء قاعدة البيانات', false, error.message);
        }
    }

    /**
     * اختبار أداء واجهة المستخدم
     */
    async testUIPerformance() {
        try {
            const startTime = performance.now();
            
            // محاكاة عمليات DOM
            const testDiv = document.createElement('div');
            testDiv.innerHTML = '<p>Test content</p>';
            document.body.appendChild(testDiv);
            
            // قياس وقت الرسم
            await new Promise(resolve => requestAnimationFrame(resolve));
            
            const endTime = performance.now();
            const renderTime = endTime - startTime;
            
            // تنظيف
            document.body.removeChild(testDiv);
            
            // يجب أن يكون الرسم أقل من 16ms (60 FPS)
            if (renderTime < 16) {
                this.logTestResult('Performance', 'اختبار أداء واجهة المستخدم', true, `وقت الرسم: ${renderTime.toFixed(2)}ms`);
            } else {
                this.logTestResult('Performance', 'اختبار أداء واجهة المستخدم', false, `رسم بطيء: ${renderTime.toFixed(2)}ms`);
            }
        } catch (error) {
            this.logTestResult('Performance', 'اختبار أداء واجهة المستخدم', false, error.message);
        }
    }

    /**
     * اختبارات الوحدات
     */
    async runModuleTests() {
        console.log('📦 تشغيل اختبارات الوحدات...');

        // اختبار وحدة المنتجات
        await this.testProductsModule();

        // اختبار وحدة العملاء
        await this.testCustomersModule();

        // اختبار وحدة المبيعات
        await this.testSalesModule();

        // اختبار وحدة التقارير
        await this.testReportsModule();

        // اختبار وحدة المصروفات
        await this.testExpensesModule();
    }

    /**
     * اختبار وحدة المنتجات
     */
    async testProductsModule() {
        try {
            if (window.ProductsManager) {
                const manager = window.ProductsManager;

                // اختبار إضافة منتج
                const testProduct = {
                    id: 'test_product_' + Date.now(),
                    name: 'منتج تجريبي',
                    price: 100,
                    quantity: 50
                };

                if (typeof manager.addProduct === 'function') {
                    await manager.addProduct(testProduct);
                    this.logTestResult('Modules', 'اختبار وحدة المنتجات - إضافة', true);
                } else {
                    this.logTestResult('Modules', 'اختبار وحدة المنتجات - إضافة', false, 'دالة addProduct غير متاحة');
                }

                // اختبار البحث
                if (typeof manager.searchProducts === 'function') {
                    const results = await manager.searchProducts('تجريبي');
                    this.logTestResult('Modules', 'اختبار وحدة المنتجات - البحث', true);
                } else {
                    this.logTestResult('Modules', 'اختبار وحدة المنتجات - البحث', false, 'دالة searchProducts غير متاحة');
                }

                // تنظيف
                if (typeof manager.deleteProduct === 'function') {
                    await manager.deleteProduct(testProduct.id);
                }
            } else {
                this.logTestResult('Modules', 'اختبار وحدة المنتجات', false, 'ProductsManager غير متاح');
            }
        } catch (error) {
            this.logTestResult('Modules', 'اختبار وحدة المنتجات', false, error.message);
        }
    }

    /**
     * اختبار وحدة العملاء
     */
    async testCustomersModule() {
        try {
            if (window.CustomersManager) {
                const manager = window.CustomersManager;

                // اختبار إضافة عميل
                const testCustomer = {
                    id: 'test_customer_' + Date.now(),
                    name: 'عميل تجريبي',
                    phone: '123456789',
                    email: '<EMAIL>'
                };

                if (typeof manager.addCustomer === 'function') {
                    await manager.addCustomer(testCustomer);
                    this.logTestResult('Modules', 'اختبار وحدة العملاء - إضافة', true);
                } else {
                    this.logTestResult('Modules', 'اختبار وحدة العملاء - إضافة', false, 'دالة addCustomer غير متاحة');
                }

                // تنظيف
                if (typeof manager.deleteCustomer === 'function') {
                    await manager.deleteCustomer(testCustomer.id);
                }
            } else {
                this.logTestResult('Modules', 'اختبار وحدة العملاء', false, 'CustomersManager غير متاح');
            }
        } catch (error) {
            this.logTestResult('Modules', 'اختبار وحدة العملاء', false, error.message);
        }
    }

    /**
     * اختبار وحدة المبيعات
     */
    async testSalesModule() {
        try {
            if (window.SalesManager) {
                const manager = window.SalesManager;

                // اختبار إضافة عملية بيع
                const testSale = {
                    id: 'test_sale_' + Date.now(),
                    customerId: 'test_customer',
                    items: [
                        { productId: 'test_product', quantity: 2, price: 100 }
                    ],
                    total: 200,
                    date: new Date().toISOString()
                };

                if (typeof manager.addSale === 'function') {
                    await manager.addSale(testSale);
                    this.logTestResult('Modules', 'اختبار وحدة المبيعات - إضافة', true);
                } else {
                    this.logTestResult('Modules', 'اختبار وحدة المبيعات - إضافة', false, 'دالة addSale غير متاحة');
                }

                // اختبار حساب الإجمالي
                if (typeof manager.calculateTotal === 'function') {
                    const total = manager.calculateTotal(testSale.items);
                    if (total === 200) {
                        this.logTestResult('Modules', 'اختبار وحدة المبيعات - حساب الإجمالي', true);
                    } else {
                        this.logTestResult('Modules', 'اختبار وحدة المبيعات - حساب الإجمالي', false, `إجمالي خاطئ: ${total}`);
                    }
                } else {
                    this.logTestResult('Modules', 'اختبار وحدة المبيعات - حساب الإجمالي', false, 'دالة calculateTotal غير متاحة');
                }

                // تنظيف
                if (typeof manager.deleteSale === 'function') {
                    await manager.deleteSale(testSale.id);
                }
            } else {
                this.logTestResult('Modules', 'اختبار وحدة المبيعات', false, 'SalesManager غير متاح');
            }
        } catch (error) {
            this.logTestResult('Modules', 'اختبار وحدة المبيعات', false, error.message);
        }
    }

    /**
     * اختبار وحدة التقارير
     */
    async testReportsModule() {
        try {
            if (window.ReportsManager) {
                const manager = window.ReportsManager;

                // اختبار إنشاء تقرير المبيعات
                if (typeof manager.generateSalesReport === 'function') {
                    const report = await manager.generateSalesReport({
                        startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                        endDate: new Date()
                    });

                    if (report && typeof report === 'object') {
                        this.logTestResult('Modules', 'اختبار وحدة التقارير - تقرير المبيعات', true);
                    } else {
                        this.logTestResult('Modules', 'اختبار وحدة التقارير - تقرير المبيعات', false, 'تقرير غير صالح');
                    }
                } else {
                    this.logTestResult('Modules', 'اختبار وحدة التقارير - تقرير المبيعات', false, 'دالة generateSalesReport غير متاحة');
                }

                // اختبار تصدير التقرير
                if (typeof manager.exportReport === 'function') {
                    const exported = await manager.exportReport({ data: 'test' }, 'json');
                    if (exported) {
                        this.logTestResult('Modules', 'اختبار وحدة التقارير - التصدير', true);
                    } else {
                        this.logTestResult('Modules', 'اختبار وحدة التقارير - التصدير', false, 'فشل في التصدير');
                    }
                } else {
                    this.logTestResult('Modules', 'اختبار وحدة التقارير - التصدير', false, 'دالة exportReport غير متاحة');
                }
            } else {
                this.logTestResult('Modules', 'اختبار وحدة التقارير', false, 'ReportsManager غير متاح');
            }
        } catch (error) {
            this.logTestResult('Modules', 'اختبار وحدة التقارير', false, error.message);
        }
    }

    /**
     * اختبار وحدة المصروفات
     */
    async testExpensesModule() {
        try {
            if (window.ExpensesManager) {
                const manager = window.ExpensesManager;

                // اختبار إضافة مصروف
                const testExpense = {
                    id: 'test_expense_' + Date.now(),
                    description: 'مصروف تجريبي',
                    amount: 50,
                    category: 'اختبار',
                    date: new Date().toISOString()
                };

                if (typeof manager.addExpense === 'function') {
                    await manager.addExpense(testExpense);
                    this.logTestResult('Modules', 'اختبار وحدة المصروفات - إضافة', true);
                } else {
                    this.logTestResult('Modules', 'اختبار وحدة المصروفات - إضافة', false, 'دالة addExpense غير متاحة');
                }

                // اختبار حساب الإجمالي
                if (typeof manager.calculateTotalExpenses === 'function') {
                    const total = await manager.calculateTotalExpenses();
                    if (typeof total === 'number') {
                        this.logTestResult('Modules', 'اختبار وحدة المصروفات - حساب الإجمالي', true);
                    } else {
                        this.logTestResult('Modules', 'اختبار وحدة المصروفات - حساب الإجمالي', false, 'إجمالي غير صالح');
                    }
                } else {
                    this.logTestResult('Modules', 'اختبار وحدة المصروفات - حساب الإجمالي', false, 'دالة calculateTotalExpenses غير متاحة');
                }

                // تنظيف
                if (typeof manager.deleteExpense === 'function') {
                    await manager.deleteExpense(testExpense.id);
                }
            } else {
                this.logTestResult('Modules', 'اختبار وحدة المصروفات', false, 'ExpensesManager غير متاح');
            }
        } catch (error) {
            this.logTestResult('Modules', 'اختبار وحدة المصروفات', false, error.message);
        }
    }

    /**
     * اختبارات التكامل
     */
    async runIntegrationTests() {
        console.log('🔗 تشغيل اختبارات التكامل...');

        // اختبار التكامل بين المنتجات والمبيعات
        await this.testProductsSalesIntegration();

        // اختبار التكامل بين المبيعات والتقارير
        await this.testSalesReportsIntegration();

        // اختبار التكامل بين قاعدة البيانات والواجهة
        await this.testDatabaseUIIntegration();
    }

    /**
     * اختبار التكامل بين المنتجات والمبيعات
     */
    async testProductsSalesIntegration() {
        try {
            if (window.ProductsManager && window.SalesManager) {
                // إضافة منتج تجريبي
                const testProduct = {
                    id: 'integration_product_' + Date.now(),
                    name: 'منتج تكامل',
                    price: 100,
                    quantity: 10
                };

                await window.ProductsManager.addProduct(testProduct);

                // إنشاء عملية بيع للمنتج
                const testSale = {
                    id: 'integration_sale_' + Date.now(),
                    items: [
                        { productId: testProduct.id, quantity: 2, price: testProduct.price }
                    ],
                    total: 200
                };

                await window.SalesManager.addSale(testSale);

                // التحقق من تحديث المخزون
                const updatedProduct = await window.ProductsManager.getProduct(testProduct.id);
                if (updatedProduct && updatedProduct.quantity === 8) {
                    this.logTestResult('Integration', 'اختبار التكامل منتجات-مبيعات', true);
                } else {
                    this.logTestResult('Integration', 'اختبار التكامل منتجات-مبيعات', false, 'لم يتم تحديث المخزون');
                }

                // تنظيف
                await window.ProductsManager.deleteProduct(testProduct.id);
                await window.SalesManager.deleteSale(testSale.id);
            } else {
                this.logTestResult('Integration', 'اختبار التكامل منتجات-مبيعات', false, 'الوحدات غير متاحة');
            }
        } catch (error) {
            this.logTestResult('Integration', 'اختبار التكامل منتجات-مبيعات', false, error.message);
        }
    }

    /**
     * اختبار التكامل بين المبيعات والتقارير
     */
    async testSalesReportsIntegration() {
        try {
            if (window.SalesManager && window.ReportsManager) {
                // إضافة عملية بيع تجريبية
                const testSale = {
                    id: 'integration_sale_report_' + Date.now(),
                    items: [{ productId: 'test', quantity: 1, price: 100 }],
                    total: 100,
                    date: new Date().toISOString()
                };

                await window.SalesManager.addSale(testSale);

                // إنشاء تقرير يتضمن هذه العملية
                const report = await window.ReportsManager.generateSalesReport({
                    startDate: new Date(Date.now() - 24 * 60 * 60 * 1000),
                    endDate: new Date()
                });

                if (report && report.sales && report.sales.some(sale => sale.id === testSale.id)) {
                    this.logTestResult('Integration', 'اختبار التكامل مبيعات-تقارير', true);
                } else {
                    this.logTestResult('Integration', 'اختبار التكامل مبيعات-تقارير', false, 'البيع لم يظهر في التقرير');
                }

                // تنظيف
                await window.SalesManager.deleteSale(testSale.id);
            } else {
                this.logTestResult('Integration', 'اختبار التكامل مبيعات-تقارير', false, 'الوحدات غير متاحة');
            }
        } catch (error) {
            this.logTestResult('Integration', 'اختبار التكامل مبيعات-تقارير', false, error.message);
        }
    }

    /**
     * اختبار التكامل بين قاعدة البيانات والواجهة
     */
    async testDatabaseUIIntegration() {
        try {
            if (window.UnifiedStorage) {
                // حفظ بيانات في قاعدة البيانات
                const testData = { id: 'ui_integration_test', name: 'اختبار التكامل' };
                await window.UnifiedStorage.setItem('ui_test', testData);

                // محاولة عرض البيانات في الواجهة
                const testElement = document.createElement('div');
                testElement.id = 'integration-test-element';
                testElement.textContent = testData.name;
                document.body.appendChild(testElement);

                // التحقق من وجود العنصر
                const displayedElement = document.getElementById('integration-test-element');
                if (displayedElement && displayedElement.textContent === testData.name) {
                    this.logTestResult('Integration', 'اختبار التكامل قاعدة البيانات-واجهة', true);
                } else {
                    this.logTestResult('Integration', 'اختبار التكامل قاعدة البيانات-واجهة', false, 'فشل في عرض البيانات');
                }

                // تنظيف
                document.body.removeChild(testElement);
                await window.UnifiedStorage.removeItem('ui_test');
            } else {
                this.logTestResult('Integration', 'اختبار التكامل قاعدة البيانات-واجهة', false, 'UnifiedStorage غير متاح');
            }
        } catch (error) {
            this.logTestResult('Integration', 'اختبار التكامل قاعدة البيانات-واجهة', false, error.message);
        }
    }

    /**
     * اختبارات الأمان
     */
    async runSecurityTests() {
        console.log('🔒 تشغيل اختبارات الأمان...');

        // اختبار حماية XSS
        await this.testXSSProtection();

        // اختبار أمان البيانات
        await this.testDataSecurity();

        // اختبار التشفير
        await this.testEncryption();

        // اختبار التحقق من الصحة
        await this.testInputValidation();
    }

    /**
     * اختبار حماية XSS
     */
    async testXSSProtection() {
        try {
            // محاولة حقن كود JavaScript ضار
            const maliciousInput = '<script>alert("XSS")</script>';
            const sanitizedInput = this.sanitizeInput(maliciousInput);

            if (sanitizedInput !== maliciousInput && !sanitizedInput.includes('<script>')) {
                this.logTestResult('Security', 'اختبار حماية XSS', true);
            } else {
                this.logTestResult('Security', 'اختبار حماية XSS', false, 'لم يتم تنظيف المدخلات الضارة');
            }
        } catch (error) {
            this.logTestResult('Security', 'اختبار حماية XSS', false, error.message);
        }
    }

    /**
     * اختبار أمان البيانات
     */
    async testDataSecurity() {
        try {
            if (window.UnifiedStorage) {
                // اختبار تشفير البيانات الحساسة
                const sensitiveData = { password: 'secret123', creditCard: '1234-5678-9012-3456' };
                const encrypted = await this.encryptSensitiveData(sensitiveData);

                if (encrypted && encrypted !== JSON.stringify(sensitiveData)) {
                    this.logTestResult('Security', 'اختبار أمان البيانات', true);
                } else {
                    this.logTestResult('Security', 'اختبار أمان البيانات', false, 'البيانات الحساسة غير مشفرة');
                }
            } else {
                this.logTestResult('Security', 'اختبار أمان البيانات', false, 'UnifiedStorage غير متاح');
            }
        } catch (error) {
            this.logTestResult('Security', 'اختبار أمان البيانات', false, error.message);
        }
    }

    /**
     * اختبار التشفير
     */
    async testEncryption() {
        try {
            const testData = 'بيانات سرية للاختبار';
            const encrypted = this.simpleEncrypt(testData);
            const decrypted = this.simpleDecrypt(encrypted);

            if (encrypted !== testData && decrypted === testData) {
                this.logTestResult('Security', 'اختبار التشفير', true);
            } else {
                this.logTestResult('Security', 'اختبار التشفير', false, 'فشل في التشفير أو فك التشفير');
            }
        } catch (error) {
            this.logTestResult('Security', 'اختبار التشفير', false, error.message);
        }
    }

    /**
     * اختبار التحقق من الصحة
     */
    async testInputValidation() {
        try {
            const validInputs = [
                { type: 'email', value: '<EMAIL>', expected: true },
                { type: 'email', value: 'invalid-email', expected: false },
                { type: 'phone', value: '123456789', expected: true },
                { type: 'phone', value: 'abc', expected: false },
                { type: 'number', value: '123', expected: true },
                { type: 'number', value: 'abc', expected: false }
            ];

            let allTestsPassed = true;

            validInputs.forEach(test => {
                const isValid = this.validateInput(test.type, test.value);
                if (isValid !== test.expected) {
                    allTestsPassed = false;
                }
            });

            if (allTestsPassed) {
                this.logTestResult('Security', 'اختبار التحقق من الصحة', true);
            } else {
                this.logTestResult('Security', 'اختبار التحقق من الصحة', false, 'فشل في بعض اختبارات التحقق');
            }
        } catch (error) {
            this.logTestResult('Security', 'اختبار التحقق من الصحة', false, error.message);
        }
    }

    /**
     * اختبارات التحسينات
     */
    async runOptimizationTests() {
        console.log('🚀 تشغيل اختبارات التحسينات...');

        // اختبار تحسينات الأصول
        await this.testAssetOptimization();

        // اختبار التخزين المؤقت
        await this.testCaching();

        // اختبار ضغط البيانات
        await this.testDataCompression();

        // اختبار تحسينات الأداء
        await this.testPerformanceOptimizations();
    }

    /**
     * اختبار تحسينات الأصول
     */
    async testAssetOptimization() {
        try {
            if (window.AssetOptimizer) {
                const optimizer = window.AssetOptimizer;

                // اختبار ضغط CSS
                if (typeof optimizer.minifyCSS === 'function') {
                    const css = 'body { margin: 0; padding: 0; }';
                    const minified = optimizer.minifyCSS(css);
                    if (minified.length < css.length) {
                        this.logTestResult('Optimization', 'اختبار ضغط CSS', true);
                    } else {
                        this.logTestResult('Optimization', 'اختبار ضغط CSS', false, 'لم يتم ضغط CSS');
                    }
                } else {
                    this.logTestResult('Optimization', 'اختبار ضغط CSS', false, 'دالة minifyCSS غير متاحة');
                }

                // اختبار ضغط JavaScript
                if (typeof optimizer.minifyJS === 'function') {
                    const js = 'function test() { console.log("test"); }';
                    const minified = optimizer.minifyJS(js);
                    if (minified.length < js.length) {
                        this.logTestResult('Optimization', 'اختبار ضغط JavaScript', true);
                    } else {
                        this.logTestResult('Optimization', 'اختبار ضغط JavaScript', false, 'لم يتم ضغط JavaScript');
                    }
                } else {
                    this.logTestResult('Optimization', 'اختبار ضغط JavaScript', false, 'دالة minifyJS غير متاحة');
                }
            } else {
                this.logTestResult('Optimization', 'اختبار تحسينات الأصول', false, 'AssetOptimizer غير متاح');
            }
        } catch (error) {
            this.logTestResult('Optimization', 'اختبار تحسينات الأصول', false, error.message);
        }
    }

    /**
     * اختبار التخزين المؤقت
     */
    async testCaching() {
        try {
            if (window.CacheManager) {
                const cache = window.CacheManager;

                // اختبار حفظ واسترجاع من التخزين المؤقت
                const testKey = 'cache_test_' + Date.now();
                const testValue = { data: 'test cache data' };

                await cache.set(testKey, testValue, 60000); // 60 ثانية
                const cachedValue = await cache.get(testKey);

                if (cachedValue && cachedValue.data === testValue.data) {
                    this.logTestResult('Optimization', 'اختبار التخزين المؤقت', true);
                } else {
                    this.logTestResult('Optimization', 'اختبار التخزين المؤقت', false, 'فشل في التخزين المؤقت');
                }

                // تنظيف
                await cache.delete(testKey);
            } else {
                this.logTestResult('Optimization', 'اختبار التخزين المؤقت', false, 'CacheManager غير متاح');
            }
        } catch (error) {
            this.logTestResult('Optimization', 'اختبار التخزين المؤقت', false, error.message);
        }
    }

    /**
     * اختبار تحسينات الأداء
     */
    async testPerformanceOptimizations() {
        try {
            if (window.PerformanceOptimizer) {
                const optimizer = window.PerformanceOptimizer;

                // اختبار تحسين الذاكرة
                if (typeof optimizer.optimizeMemory === 'function') {
                    const memoryBefore = performance.memory ? performance.memory.usedJSHeapSize : 0;
                    await optimizer.optimizeMemory();
                    const memoryAfter = performance.memory ? performance.memory.usedJSHeapSize : 0;

                    // يجب أن تقل الذاكرة المستخدمة أو تبقى كما هي
                    if (memoryAfter <= memoryBefore) {
                        this.logTestResult('Optimization', 'اختبار تحسين الذاكرة', true);
                    } else {
                        this.logTestResult('Optimization', 'اختبار تحسين الذاكرة', false, 'زادت الذاكرة المستخدمة');
                    }
                } else {
                    this.logTestResult('Optimization', 'اختبار تحسين الذاكرة', false, 'دالة optimizeMemory غير متاحة');
                }

                // اختبار تحسين DOM
                if (typeof optimizer.optimizeDOM === 'function') {
                    const elementsBefore = document.querySelectorAll('*').length;
                    await optimizer.optimizeDOM();
                    const elementsAfter = document.querySelectorAll('*').length;

                    // يجب أن يقل عدد العناصر أو يبقى كما هو
                    if (elementsAfter <= elementsBefore) {
                        this.logTestResult('Optimization', 'اختبار تحسين DOM', true);
                    } else {
                        this.logTestResult('Optimization', 'اختبار تحسين DOM', false, 'زاد عدد عناصر DOM');
                    }
                } else {
                    this.logTestResult('Optimization', 'اختبار تحسين DOM', false, 'دالة optimizeDOM غير متاحة');
                }
            } else {
                this.logTestResult('Optimization', 'اختبار تحسينات الأداء', false, 'PerformanceOptimizer غير متاح');
            }
        } catch (error) {
            this.logTestResult('Optimization', 'اختبار تحسينات الأداء', false, error.message);
        }
    }

    /**
     * تسجيل نتيجة اختبار
     */
    logTestResult(suite, testName, passed, details = '') {
        const result = {
            suite,
            testName,
            passed,
            details,
            timestamp: new Date().toISOString()
        };

        this.testResults.push(result);
        this.totalTests++;

        if (passed) {
            this.passedTests++;
            console.log(`✅ ${suite}: ${testName}`);
        } else {
            this.failedTests++;
            console.log(`❌ ${suite}: ${testName} - ${details}`);
        }

        if (details) {
            console.log(`   📝 ${details}`);
        }
    }

    /**
     * إنشاء التقرير النهائي
     */
    generateFinalReport() {
        const duration = this.endTime - this.startTime;
        const successRate = ((this.passedTests / this.totalTests) * 100).toFixed(2);

        const report = {
            summary: {
                totalTests: this.totalTests,
                passedTests: this.passedTests,
                failedTests: this.failedTests,
                successRate: `${successRate}%`,
                duration: `${duration}ms`,
                timestamp: new Date().toISOString()
            },
            testSuites: {},
            results: this.testResults,
            recommendations: this.generateRecommendations()
        };

        // تجميع النتائج حسب مجموعة الاختبارات
        Object.keys(this.testSuites).forEach(suite => {
            const suiteResults = this.testResults.filter(result =>
                result.suite.toLowerCase() === suite.toLowerCase()
            );

            report.testSuites[suite] = {
                name: this.testSuites[suite],
                total: suiteResults.length,
                passed: suiteResults.filter(r => r.passed).length,
                failed: suiteResults.filter(r => !r.passed).length,
                results: suiteResults
            };
        });

        console.log('\n📊 تقرير الاختبار الشامل:');
        console.log(`📈 إجمالي الاختبارات: ${this.totalTests}`);
        console.log(`✅ نجح: ${this.passedTests}`);
        console.log(`❌ فشل: ${this.failedTests}`);
        console.log(`📊 معدل النجاح: ${successRate}%`);
        console.log(`⏱️ المدة: ${duration}ms`);

        return report;
    }

    /**
     * إنشاء التوصيات
     */
    generateRecommendations() {
        const recommendations = [];
        const failedTests = this.testResults.filter(result => !result.passed);

        if (failedTests.length === 0) {
            recommendations.push({
                type: 'success',
                message: 'جميع الاختبارات نجحت! النظام يعمل بشكل مثالي.',
                priority: 'low'
            });
        } else {
            // تحليل الأخطاء وإنشاء توصيات
            const errorsByCategory = {};
            failedTests.forEach(test => {
                if (!errorsByCategory[test.suite]) {
                    errorsByCategory[test.suite] = [];
                }
                errorsByCategory[test.suite].push(test);
            });

            Object.keys(errorsByCategory).forEach(category => {
                const errors = errorsByCategory[category];
                recommendations.push({
                    type: 'error',
                    category,
                    message: `يحتاج ${this.testSuites[category.toLowerCase()]} إلى مراجعة (${errors.length} أخطاء)`,
                    details: errors.map(e => e.testName),
                    priority: errors.length > 3 ? 'high' : 'medium'
                });
            });
        }

        // توصيات عامة للتحسين
        if (this.passedTests / this.totalTests > 0.9) {
            recommendations.push({
                type: 'improvement',
                message: 'النظام في حالة ممتازة. يمكن التركيز على التحسينات الإضافية.',
                priority: 'low'
            });
        } else if (this.passedTests / this.totalTests > 0.7) {
            recommendations.push({
                type: 'warning',
                message: 'النظام في حالة جيدة ولكن يحتاج بعض التحسينات.',
                priority: 'medium'
            });
        } else {
            recommendations.push({
                type: 'critical',
                message: 'النظام يحتاج مراجعة شاملة وإصلاحات عاجلة.',
                priority: 'high'
            });
        }

        return recommendations;
    }

    /**
     * وظائف مساعدة للأمان
     */

    /**
     * تنظيف المدخلات من الأكواد الضارة
     */
    sanitizeInput(input) {
        if (typeof input !== 'string') return input;

        return input
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
            .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
            .replace(/javascript:/gi, '')
            .replace(/on\w+\s*=/gi, '')
            .replace(/<[^>]*>/g, '');
    }

    /**
     * تشفير البيانات الحساسة
     */
    async encryptSensitiveData(data) {
        try {
            // تشفير بسيط للاختبار (في الإنتاج يجب استخدام تشفير قوي)
            const jsonString = JSON.stringify(data);
            const encrypted = btoa(jsonString); // Base64 encoding
            return encrypted;
        } catch (error) {
            console.error('خطأ في تشفير البيانات:', error);
            return null;
        }
    }

    /**
     * تشفير بسيط
     */
    simpleEncrypt(text) {
        const shift = 3;
        return text.split('').map(char => {
            const code = char.charCodeAt(0);
            return String.fromCharCode(code + shift);
        }).join('');
    }

    /**
     * فك التشفير البسيط
     */
    simpleDecrypt(encryptedText) {
        const shift = 3;
        return encryptedText.split('').map(char => {
            const code = char.charCodeAt(0);
            return String.fromCharCode(code - shift);
        }).join('');
    }

    /**
     * التحقق من صحة المدخلات
     */
    validateInput(type, value) {
        switch (type) {
            case 'email':
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(value);

            case 'phone':
                const phoneRegex = /^\d{8,15}$/;
                return phoneRegex.test(value);

            case 'number':
                return !isNaN(value) && !isNaN(parseFloat(value));

            case 'text':
                return typeof value === 'string' && value.trim().length > 0;

            default:
                return true;
        }
    }

    /**
     * تصدير التقرير
     */
    exportReport(report, format = 'json') {
        try {
            let content;
            let filename;
            let mimeType;

            switch (format.toLowerCase()) {
                case 'json':
                    content = JSON.stringify(report, null, 2);
                    filename = `system-test-report-${Date.now()}.json`;
                    mimeType = 'application/json';
                    break;

                case 'csv':
                    content = this.convertToCSV(report.results);
                    filename = `system-test-report-${Date.now()}.csv`;
                    mimeType = 'text/csv';
                    break;

                case 'txt':
                    content = this.convertToText(report);
                    filename = `system-test-report-${Date.now()}.txt`;
                    mimeType = 'text/plain';
                    break;

                default:
                    throw new Error('تنسيق غير مدعوم');
            }

            // إنشاء رابط التحميل
            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            link.click();

            // تنظيف
            URL.revokeObjectURL(url);

            return true;
        } catch (error) {
            console.error('خطأ في تصدير التقرير:', error);
            return false;
        }
    }

    /**
     * تحويل النتائج إلى CSV
     */
    convertToCSV(results) {
        const headers = ['مجموعة الاختبار', 'اسم الاختبار', 'النتيجة', 'التفاصيل', 'الوقت'];
        const csvContent = [headers.join(',')];

        results.forEach(result => {
            const row = [
                result.suite,
                result.testName,
                result.passed ? 'نجح' : 'فشل',
                result.details || '',
                result.timestamp
            ];
            csvContent.push(row.map(field => `"${field}"`).join(','));
        });

        return csvContent.join('\n');
    }

    /**
     * تحويل التقرير إلى نص
     */
    convertToText(report) {
        let text = 'تقرير الاختبار الشامل لنظام Black Horse POS\n';
        text += '='.repeat(50) + '\n\n';

        text += 'ملخص النتائج:\n';
        text += `إجمالي الاختبارات: ${report.summary.totalTests}\n`;
        text += `نجح: ${report.summary.passedTests}\n`;
        text += `فشل: ${report.summary.failedTests}\n`;
        text += `معدل النجاح: ${report.summary.successRate}\n`;
        text += `المدة: ${report.summary.duration}\n`;
        text += `التاريخ: ${report.summary.timestamp}\n\n`;

        text += 'النتائج التفصيلية:\n';
        text += '-'.repeat(30) + '\n';

        Object.keys(report.testSuites).forEach(suite => {
            const suiteData = report.testSuites[suite];
            text += `\n${suiteData.name}:\n`;
            text += `  إجمالي: ${suiteData.total}\n`;
            text += `  نجح: ${suiteData.passed}\n`;
            text += `  فشل: ${suiteData.failed}\n`;

            suiteData.results.forEach(result => {
                const status = result.passed ? '✅' : '❌';
                text += `  ${status} ${result.testName}`;
                if (result.details) {
                    text += ` - ${result.details}`;
                }
                text += '\n';
            });
        });

        text += '\nالتوصيات:\n';
        text += '-'.repeat(20) + '\n';
        report.recommendations.forEach((rec, index) => {
            text += `${index + 1}. [${rec.priority.toUpperCase()}] ${rec.message}\n`;
            if (rec.details) {
                text += `   التفاصيل: ${rec.details.join(', ')}\n`;
            }
        });

        return text;
    }

    /**
     * حفظ التقرير في قاعدة البيانات
     */
    async saveReportToDatabase(report) {
        try {
            if (window.UnifiedStorage) {
                const reportId = `test_report_${Date.now()}`;
                await window.UnifiedStorage.setItem(reportId, report);
                console.log(`تم حفظ التقرير برقم: ${reportId}`);
                return reportId;
            } else {
                console.warn('لا يمكن حفظ التقرير - قاعدة البيانات غير متاحة');
                return null;
            }
        } catch (error) {
            console.error('خطأ في حفظ التقرير:', error);
            return null;
        }
    }

    /**
     * مقارنة التقارير
     */
    async compareReports(reportId1, reportId2) {
        try {
            if (!window.UnifiedStorage) {
                throw new Error('قاعدة البيانات غير متاحة');
            }

            const report1 = await window.UnifiedStorage.getItem(reportId1);
            const report2 = await window.UnifiedStorage.getItem(reportId2);

            if (!report1 || !report2) {
                throw new Error('لم يتم العثور على أحد التقارير');
            }

            const comparison = {
                report1: {
                    id: reportId1,
                    timestamp: report1.summary.timestamp,
                    successRate: parseFloat(report1.summary.successRate),
                    totalTests: report1.summary.totalTests
                },
                report2: {
                    id: reportId2,
                    timestamp: report2.summary.timestamp,
                    successRate: parseFloat(report2.summary.successRate),
                    totalTests: report2.summary.totalTests
                },
                improvements: {
                    successRateChange: parseFloat(report2.summary.successRate) - parseFloat(report1.summary.successRate),
                    testCountChange: report2.summary.totalTests - report1.summary.totalTests
                }
            };

            return comparison;
        } catch (error) {
            console.error('خطأ في مقارنة التقارير:', error);
            return null;
        }
    }
}

// تصدير الفئة للاستخدام العام
if (typeof window !== 'undefined') {
    window.ComprehensiveSystemTests = ComprehensiveSystemTests;
}

// تصدير للاستخدام في Node.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ComprehensiveSystemTests;
}
