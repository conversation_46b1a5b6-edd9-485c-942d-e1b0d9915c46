// Test Critical Fixes - اختبار الإصلاحات الحرجة
console.log('🧪 Loading Critical Fixes Test System...');

// ===== TEST SYSTEM FOR CRITICAL FIXES =====
// نظام اختبار الإصلاحات الحرجة

const CriticalFixesTest = {
    results: {
        total: 0,
        passed: 0,
        failed: 0,
        tests: []
    },

    // Test 1: Product Addition Function
    testProductAddition: function() {
        console.log('🧪 Testing product addition...');
        
        try {
            // Check if addProduct function exists
            if (typeof window.addProduct !== 'function') {
                throw new Error('addProduct function not found');
            }

            // Check if form elements exist
            const requiredElements = [
                'productName',
                'productPrice', 
                'productStock',
                'productBarcode'
            ];

            for (let elementId of requiredElements) {
                const element = document.getElementById(elementId);
                if (!element) {
                    throw new Error(`Required element ${elementId} not found`);
                }
            }

            this.results.tests.push({
                name: 'Product Addition Function',
                status: 'PASSED',
                details: 'addProduct function and required elements found'
            });
            this.results.passed++;

        } catch (error) {
            this.results.tests.push({
                name: 'Product Addition Function',
                status: 'FAILED',
                error: error.message
            });
            this.results.failed++;
        }
        
        this.results.total++;
    },

    // Test 2: Barcode Generation Function
    testBarcodeGeneration: function() {
        console.log('🧪 Testing barcode generation...');
        
        try {
            // Check if generateBarcode function exists
            if (typeof window.generateBarcode !== 'function') {
                throw new Error('generateBarcode function not found');
            }

            // Check if barcode field exists
            const barcodeField = document.getElementById('productBarcode');
            if (!barcodeField) {
                throw new Error('productBarcode field not found');
            }

            // Test barcode generation
            const originalValue = barcodeField.value;
            window.generateBarcode();
            
            // Check if barcode was generated
            if (barcodeField.value === originalValue && !originalValue) {
                throw new Error('Barcode was not generated');
            }

            this.results.tests.push({
                name: 'Barcode Generation Function',
                status: 'PASSED',
                details: 'generateBarcode function works correctly'
            });
            this.results.passed++;

        } catch (error) {
            this.results.tests.push({
                name: 'Barcode Generation Function',
                status: 'FAILED',
                error: error.message
            });
            this.results.failed++;
        }
        
        this.results.total++;
    },

    // Test 3: Button Event Handlers
    testButtonHandlers: function() {
        console.log('🧪 Testing button handlers...');
        
        try {
            let buttonsFound = 0;
            let buttonsWorking = 0;

            // Test Add Product Button
            const addProductBtn = document.querySelector('[onclick*="showAddProductModal"], [data-action="add-product"], .btn-add-product');
            if (addProductBtn) {
                buttonsFound++;
                if (addProductBtn.onclick || addProductBtn.hasAttribute('data-fixed')) {
                    buttonsWorking++;
                }
            }

            // Test Generate Barcode Button
            const generateBarcodeBtn = document.querySelector('[onclick*="generateBarcode"], [data-action="generate-barcode"], .btn-generate-barcode');
            if (generateBarcodeBtn) {
                buttonsFound++;
                if (generateBarcodeBtn.onclick || generateBarcodeBtn.hasAttribute('data-fixed')) {
                    buttonsWorking++;
                }
            }

            // Test Save Product Button
            const saveProductBtn = document.querySelector('[onclick*="addProduct"], .btn-save-product');
            if (saveProductBtn) {
                buttonsFound++;
                if (saveProductBtn.onclick || saveProductBtn.hasAttribute('data-fixed')) {
                    buttonsWorking++;
                }
            }

            if (buttonsFound === 0) {
                throw new Error('No critical buttons found');
            }

            if (buttonsWorking < buttonsFound) {
                throw new Error(`Only ${buttonsWorking}/${buttonsFound} buttons have working handlers`);
            }

            this.results.tests.push({
                name: 'Button Event Handlers',
                status: 'PASSED',
                details: `${buttonsWorking}/${buttonsFound} buttons have working handlers`
            });
            this.results.passed++;

        } catch (error) {
            this.results.tests.push({
                name: 'Button Event Handlers',
                status: 'FAILED',
                error: error.message
            });
            this.results.failed++;
        }
        
        this.results.total++;
    },

    // Test 4: Modal Functions
    testModalFunctions: function() {
        console.log('🧪 Testing modal functions...');
        
        try {
            // Check if modal functions exist
            if (typeof window.showModal !== 'function') {
                throw new Error('showModal function not found');
            }

            if (typeof window.closeModal !== 'function') {
                throw new Error('closeModal function not found');
            }

            // Check if product modal exists
            const productModal = document.getElementById('addProductModal');
            if (!productModal) {
                throw new Error('addProductModal not found');
            }

            this.results.tests.push({
                name: 'Modal Functions',
                status: 'PASSED',
                details: 'Modal functions and elements found'
            });
            this.results.passed++;

        } catch (error) {
            this.results.tests.push({
                name: 'Modal Functions',
                status: 'FAILED',
                error: error.message
            });
            this.results.failed++;
        }
        
        this.results.total++;
    },

    // Test 5: Data Validation
    testDataValidation: function() {
        console.log('🧪 Testing data validation...');
        
        try {
            // Check if DataValidator exists
            if (typeof DataValidator === 'undefined') {
                throw new Error('DataValidator not found');
            }

            // Check if validation functions exist
            if (typeof DataValidator.validateProduct !== 'function') {
                throw new Error('DataValidator.validateProduct not found');
            }

            if (typeof validateProductData !== 'function') {
                throw new Error('validateProductData function not found');
            }

            this.results.tests.push({
                name: 'Data Validation',
                status: 'PASSED',
                details: 'Data validation functions found'
            });
            this.results.passed++;

        } catch (error) {
            this.results.tests.push({
                name: 'Data Validation',
                status: 'FAILED',
                error: error.message
            });
            this.results.failed++;
        }
        
        this.results.total++;
    },

    // Run All Tests
    runAllTests: function() {
        console.log('🚀 Starting Critical Fixes Test Suite...');
        
        // Reset results
        this.results = {
            total: 0,
            passed: 0,
            failed: 0,
            tests: []
        };

        // Run all tests
        this.testProductAddition();
        this.testBarcodeGeneration();
        this.testButtonHandlers();
        this.testModalFunctions();
        this.testDataValidation();

        // Display results
        this.displayResults();
        
        return this.results;
    },

    // Display Test Results
    displayResults: function() {
        console.log('\n🧪 ===== CRITICAL FIXES TEST RESULTS =====');
        console.log(`📊 Total Tests: ${this.results.total}`);
        console.log(`✅ Passed: ${this.results.passed}`);
        console.log(`❌ Failed: ${this.results.failed}`);
        console.log(`📈 Success Rate: ${((this.results.passed / this.results.total) * 100).toFixed(1)}%`);
        
        console.log('\n📋 Detailed Results:');
        this.results.tests.forEach((test, index) => {
            const status = test.status === 'PASSED' ? '✅' : '❌';
            console.log(`${index + 1}. ${status} ${test.name}`);
            if (test.details) {
                console.log(`   📝 ${test.details}`);
            }
            if (test.error) {
                console.log(`   🚨 Error: ${test.error}`);
            }
        });

        // Show notification if available
        if (typeof showNotification === 'function') {
            const message = `اختبار الإصلاحات: ${this.results.passed}/${this.results.total} نجح`;
            const type = this.results.failed === 0 ? 'success' : 'warning';
            showNotification(message, type);
        }

        console.log('\n🏁 Critical Fixes Test Suite Completed\n');
    }
};

// Auto-run tests when page loads
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        CriticalFixesTest.runAllTests();
    }, 3000); // Wait 3 seconds for everything to load
});

// Make test system globally available
window.CriticalFixesTest = CriticalFixesTest;

console.log('✅ Critical Fixes Test System loaded successfully');
