/**
 * HR Management Module
 * إدارة الموارد البشرية - نظام شامل لإدارة الموظفين والحضور والرواتب
 */

class HRModule {
    constructor() {
        this.currentView = 'employees';
        this.selectedEmployees = new Set();
        this.filters = {
            status: 'all',
            department: 'all',
            position: 'all',
            date_from: '',
            date_to: '',
            search: ''
        };
        this.pagination = {
            page: 1,
            limit: 20,
            total: 0
        };
        
        // Data stores
        this.employees = new Map();
        this.departments = new Map();
        this.positions = new Map();
        this.attendance = new Map();
        this.payroll = new Map();
        this.evaluations = new Map();
        
        // Current data
        this.currentEmployee = null;
        this.currentPayrollPeriod = null;
    }

    /**
     * Initialize HR module
     */
    async initialize() {
        console.log('Initializing HR Module...');
        await this.loadEmployees();
        await this.loadDepartments();
        await this.loadPositions();
        await this.loadAttendance();
        await this.loadPayroll();
        await this.createSampleData();
    }

    /**
     * Show HR module
     */
    async show(params = {}) {
        this.currentView = params.view || 'employees';
        await this.render();
        await this.loadData();
        this.setupEventListeners();
    }

    /**
     * Render HR module
     */
    async render() {
        const container = document.getElementById('moduleContainer');
        if (!container) return;

        container.innerHTML = `
            <div class="hr-module">
                <!-- Module Header -->
                <div class="module-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2><i class="fas fa-users me-2"></i>إدارة الموارد البشرية</h2>
                            <p class="mb-0">نظام شامل لإدارة الموظفين والحضور والرواتب</p>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-light" onclick="HR.showAddEmployeeModal()">
                                <i class="fas fa-user-plus me-1"></i>موظف جديد
                            </button>
                            <button class="btn btn-light" onclick="HR.showAttendanceModal()">
                                <i class="fas fa-clock me-1"></i>تسجيل حضور
                            </button>
                            <button class="btn btn-light" onclick="HR.generatePayroll()">
                                <i class="fas fa-money-bill-wave me-1"></i>إنشاء كشف راتب
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Navigation Tabs -->
                <ul class="nav nav-tabs mb-4">
                    <li class="nav-item">
                        <a class="nav-link ${this.currentView === 'employees' ? 'active' : ''}" 
                           href="#" onclick="HR.switchView('employees')">
                            <i class="fas fa-users me-1"></i>الموظفين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link ${this.currentView === 'attendance' ? 'active' : ''}" 
                           href="#" onclick="HR.switchView('attendance')">
                            <i class="fas fa-clock me-1"></i>الحضور والانصراف
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link ${this.currentView === 'payroll' ? 'active' : ''}" 
                           href="#" onclick="HR.switchView('payroll')">
                            <i class="fas fa-money-bill-wave me-1"></i>الرواتب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link ${this.currentView === 'evaluations' ? 'active' : ''}" 
                           href="#" onclick="HR.switchView('evaluations')">
                            <i class="fas fa-star me-1"></i>تقييم الأداء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link ${this.currentView === 'reports' ? 'active' : ''}" 
                           href="#" onclick="HR.switchView('reports')">
                            <i class="fas fa-chart-bar me-1"></i>التقارير
                        </a>
                    </li>
                </ul>

                <!-- Content Area -->
                <div id="hrContent">
                    ${await this.renderCurrentView()}
                </div>

                <!-- Modals -->
                ${this.renderModals()}
            </div>
        `;
    }

    /**
     * Render current view content
     */
    async renderCurrentView() {
        switch (this.currentView) {
            case 'employees':
                return await this.renderEmployeesView();
            case 'attendance':
                return await this.renderAttendanceView();
            case 'payroll':
                return await this.renderPayrollView();
            case 'evaluations':
                return await this.renderEvaluationsView();
            case 'reports':
                return await this.renderReportsView();
            default:
                return await this.renderEmployeesView();
        }
    }

    /**
     * Render employees view
     */
    async renderEmployeesView() {
        return `
            <div class="employees-view">
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="totalEmployeesCount">0</h4>
                                        <p class="mb-0">إجمالي الموظفين</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="activeEmployeesCount">0</h4>
                                        <p class="mb-0">الموظفين النشطين</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-user-check fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="presentTodayCount">0</h4>
                                        <p class="mb-0">حاضرين اليوم</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-clock fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="totalSalariesAmount">0</h4>
                                        <p class="mb-0">إجمالي الرواتب</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-money-bill-wave fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">البحث</label>
                                <input type="text" class="form-control" id="searchInput" placeholder="البحث في الموظفين...">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">الحالة</label>
                                <select class="form-select" id="statusFilter">
                                    <option value="all">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                    <option value="suspended">موقوف</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">القسم</label>
                                <select class="form-select" id="departmentFilter">
                                    <option value="all">جميع الأقسام</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">المنصب</label>
                                <select class="form-select" id="positionFilter">
                                    <option value="all">جميع المناصب</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-primary" onclick="HR.applyFilters()">
                                        <i class="fas fa-search me-1"></i>بحث
                                    </button>
                                    <button class="btn btn-secondary" onclick="HR.clearFilters()">
                                        <i class="fas fa-times me-1"></i>مسح
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Employees Grid -->
                <div class="row" id="employeesGrid">
                    <!-- Employees will be loaded here -->
                </div>
            </div>
        `;
    }

    /**
     * Render attendance view
     */
    async renderAttendanceView() {
        return `
            <div class="attendance-view">
                <!-- Today's Attendance Summary -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h3 id="todayPresentCount">0</h3>
                                <p class="mb-0">حاضرين اليوم</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h3 id="todayAbsentCount">0</h3>
                                <p class="mb-0">غائبين اليوم</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h3 id="todayLateCount">0</h3>
                                <p class="mb-0">متأخرين اليوم</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h3 id="todayOvertimeCount">0</h3>
                                <p class="mb-0">ساعات إضافية</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">إجراءات سريعة</h5>
                        <div class="btn-group">
                            <button class="btn btn-success" onclick="HR.markAttendance('check_in')">
                                <i class="fas fa-sign-in-alt me-1"></i>تسجيل حضور
                            </button>
                            <button class="btn btn-warning" onclick="HR.markAttendance('check_out')">
                                <i class="fas fa-sign-out-alt me-1"></i>تسجيل انصراف
                            </button>
                            <button class="btn btn-info" onclick="HR.markAttendance('break_start')">
                                <i class="fas fa-coffee me-1"></i>بداية استراحة
                            </button>
                            <button class="btn btn-secondary" onclick="HR.markAttendance('break_end')">
                                <i class="fas fa-play me-1"></i>نهاية استراحة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Attendance Table -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-clock me-2"></i>سجل الحضور اليومي</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="attendanceTable">
                                <thead>
                                    <tr>
                                        <th>الموظف</th>
                                        <th>وقت الحضور</th>
                                        <th>وقت الانصراف</th>
                                        <th>ساعات العمل</th>
                                        <th>الحالة</th>
                                        <th>ملاحظات</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Attendance records will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render payroll view
     */
    async renderPayrollView() {
        return `
            <div class="payroll-view">
                <!-- Payroll Summary -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h3 id="monthlyPayrollTotal">0</h3>
                                <p class="mb-0">إجمالي الرواتب الشهرية</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h3 id="paidPayrollCount">0</h3>
                                <p class="mb-0">رواتب مدفوعة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h3 id="pendingPayrollCount">0</h3>
                                <p class="mb-0">رواتب معلقة</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payroll Actions -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>إدارة كشوف الرواتب</h5>
                                <div class="btn-group">
                                    <button class="btn btn-primary" onclick="HR.generateMonthlyPayroll()">
                                        <i class="fas fa-calculator me-1"></i>إنشاء كشف راتب شهري
                                    </button>
                                    <button class="btn btn-success" onclick="HR.processPayroll()">
                                        <i class="fas fa-money-bill-wave me-1"></i>معالجة الرواتب
                                    </button>
                                    <button class="btn btn-info" onclick="HR.exportPayroll()">
                                        <i class="fas fa-download me-1"></i>تصدير كشف الراتب
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">فترة الراتب</label>
                                <div class="row">
                                    <div class="col-6">
                                        <select class="form-select" id="payrollMonth">
                                            <option value="1">يناير</option>
                                            <option value="2">فبراير</option>
                                            <option value="3">مارس</option>
                                            <option value="4">أبريل</option>
                                            <option value="5">مايو</option>
                                            <option value="6">يونيو</option>
                                            <option value="7">يوليو</option>
                                            <option value="8">أغسطس</option>
                                            <option value="9">سبتمبر</option>
                                            <option value="10">أكتوبر</option>
                                            <option value="11">نوفمبر</option>
                                            <option value="12">ديسمبر</option>
                                        </select>
                                    </div>
                                    <div class="col-6">
                                        <select class="form-select" id="payrollYear">
                                            <option value="2024">2024</option>
                                            <option value="2023">2023</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payroll Table -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-money-bill-wave me-2"></i>كشوف الرواتب</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="payrollTable">
                                <thead>
                                    <tr>
                                        <th>الموظف</th>
                                        <th>الراتب الأساسي</th>
                                        <th>البدلات</th>
                                        <th>الساعات الإضافية</th>
                                        <th>الخصومات</th>
                                        <th>صافي الراتب</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Payroll records will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render evaluations view
     */
    async renderEvaluationsView() {
        return `
            <div class="evaluations-view">
                <!-- Evaluation Summary -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h3 id="excellentRatingCount">0</h3>
                                <p class="mb-0">ممتاز</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h3 id="goodRatingCount">0</h3>
                                <p class="mb-0">جيد</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h3 id="averageRatingCount">0</h3>
                                <p class="mb-0">متوسط</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h3 id="poorRatingCount">0</h3>
                                <p class="mb-0">ضعيف</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Evaluation Actions -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5>تقييمات الأداء</h5>
                            <button class="btn btn-primary" onclick="HR.showEvaluationModal()">
                                <i class="fas fa-star me-1"></i>تقييم جديد
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Evaluations Table -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-star me-2"></i>سجل التقييمات</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="evaluationsTable">
                                <thead>
                                    <tr>
                                        <th>الموظف</th>
                                        <th>فترة التقييم</th>
                                        <th>التقييم العام</th>
                                        <th>الأداء</th>
                                        <th>الالتزام</th>
                                        <th>التعاون</th>
                                        <th>المقيم</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Evaluation records will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render reports view
     */
    async renderReportsView() {
        return `
            <div class="reports-view">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-bar me-2"></i>تقرير الموظفين</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="employeesChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-line me-2"></i>تقرير الحضور</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="attendanceChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-pie me-2"></i>تقرير الرواتب</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="payrollChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-area me-2"></i>تقرير الأداء</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="performanceChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render modals
     */
    renderModals() {
        return `
            <!-- Add Employee Modal -->
            <div class="modal fade" id="addEmployeeModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إضافة موظف جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="addEmployeeForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الاسم الأول *</label>
                                        <input type="text" class="form-control" name="first_name" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الاسم الأخير *</label>
                                        <input type="text" class="form-control" name="last_name" required>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">رقم الموظف *</label>
                                        <input type="text" class="form-control" name="employee_id" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الرقم القومي</label>
                                        <input type="text" class="form-control" name="national_id">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">القسم *</label>
                                        <select class="form-select" name="department_id" required>
                                            <option value="">اختر القسم</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">المنصب *</label>
                                        <select class="form-select" name="position_id" required>
                                            <option value="">اختر المنصب</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ التوظيف *</label>
                                        <input type="date" class="form-control" name="hire_date" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الراتب الأساسي *</label>
                                        <input type="number" class="form-control" name="base_salary" step="0.01" required>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" name="phone">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" name="email">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الحالة الاجتماعية</label>
                                        <select class="form-select" name="marital_status">
                                            <option value="">اختر الحالة</option>
                                            <option value="single">أعزب</option>
                                            <option value="married">متزوج</option>
                                            <option value="divorced">مطلق</option>
                                            <option value="widowed">أرمل</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الحالة</label>
                                        <select class="form-select" name="status">
                                            <option value="active">نشط</option>
                                            <option value="inactive">غير نشط</option>
                                            <option value="suspended">موقوف</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="address" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="HR.saveEmployee()">حفظ</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Attendance Modal -->
            <div class="modal fade" id="attendanceModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تسجيل حضور</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="attendanceForm">
                                <div class="mb-3">
                                    <label class="form-label">الموظف *</label>
                                    <select class="form-select" name="employee_id" required>
                                        <option value="">اختر الموظف</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">نوع التسجيل *</label>
                                    <select class="form-select" name="type" required>
                                        <option value="check_in">حضور</option>
                                        <option value="check_out">انصراف</option>
                                        <option value="break_start">بداية استراحة</option>
                                        <option value="break_end">نهاية استراحة</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">التاريخ والوقت</label>
                                    <input type="datetime-local" class="form-control" name="timestamp" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="HR.saveAttendance()">حفظ</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Evaluation Modal -->
            <div class="modal fade" id="evaluationModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تقييم أداء موظف</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="evaluationForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الموظف *</label>
                                        <select class="form-select" name="employee_id" required>
                                            <option value="">اختر الموظف</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">فترة التقييم *</label>
                                        <select class="form-select" name="period" required>
                                            <option value="monthly">شهري</option>
                                            <option value="quarterly">ربع سنوي</option>
                                            <option value="semi_annual">نصف سنوي</option>
                                            <option value="annual">سنوي</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">الأداء العام (1-5)</label>
                                        <input type="number" class="form-control" name="performance_rating" min="1" max="5" required>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">الالتزام (1-5)</label>
                                        <input type="number" class="form-control" name="commitment_rating" min="1" max="5" required>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">التعاون (1-5)</label>
                                        <input type="number" class="form-control" name="teamwork_rating" min="1" max="5" required>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">الإبداع (1-5)</label>
                                        <input type="number" class="form-control" name="creativity_rating" min="1" max="5" required>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">نقاط القوة</label>
                                    <textarea class="form-control" name="strengths" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">نقاط التحسين</label>
                                    <textarea class="form-control" name="improvements" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">التوصيات</label>
                                    <textarea class="form-control" name="recommendations" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="HR.saveEvaluation()">حفظ التقييم</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Switch view
     */
    async switchView(view) {
        this.currentView = view;
        await this.render();
        await this.loadData();
        this.setupEventListeners();
    }

    /**
     * Load data based on current view
     */
    async loadData() {
        switch (this.currentView) {
            case 'employees':
                await this.loadEmployeesData();
                break;
            case 'attendance':
                await this.loadAttendanceData();
                break;
            case 'payroll':
                await this.loadPayrollData();
                break;
            case 'evaluations':
                await this.loadEvaluationsData();
                break;
            case 'reports':
                await this.loadReportsData();
                break;
        }
    }

    /**
     * Load employees data
     */
    async loadEmployeesData() {
        try {
            // Update statistics
            const totalEmployees = this.employees.size;
            const activeEmployees = Array.from(this.employees.values()).filter(emp => emp.status === 'active').length;
            const presentToday = Array.from(this.attendance.values()).filter(att =>
                att.date === new Date().toISOString().split('T')[0] && att.type === 'check_in'
            ).length;
            const totalSalaries = Array.from(this.employees.values()).reduce((sum, emp) => sum + (emp.base_salary || 0), 0);

            document.getElementById('totalEmployeesCount').textContent = totalEmployees;
            document.getElementById('activeEmployeesCount').textContent = activeEmployees;
            document.getElementById('presentTodayCount').textContent = presentToday;
            document.getElementById('totalSalariesAmount').textContent = Utils.formatCurrency(totalSalaries);

            // Load employees grid
            await this.renderEmployeesGrid();

            // Load filter options
            await this.loadFilterOptions();
        } catch (error) {
            console.error('Error loading employees data:', error);
        }
    }

    /**
     * Render employees grid
     */
    async renderEmployeesGrid() {
        const grid = document.getElementById('employeesGrid');
        if (!grid) return;

        const employees = this.getFilteredEmployees();

        if (employees.length === 0) {
            grid.innerHTML = `
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5>لا توجد موظفين</h5>
                        <p class="text-muted">ابدأ بإضافة موظف جديد</p>
                        <button class="btn btn-primary" onclick="HR.showAddEmployeeModal()">
                            <i class="fas fa-plus me-1"></i>إضافة موظف
                        </button>
                    </div>
                </div>
            `;
            return;
        }

        grid.innerHTML = employees.map(employee => `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="avatar-circle me-3">
                                <i class="fas fa-user"></i>
                            </div>
                            <div>
                                <h6 class="mb-0">${employee.first_name} ${employee.last_name}</h6>
                                <small class="text-muted">${employee.employee_id}</small>
                            </div>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">القسم:</small>
                            <span class="fw-bold">${this.getDepartmentName(employee.department_id)}</span>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">المنصب:</small>
                            <span class="fw-bold">${this.getPositionName(employee.position_id)}</span>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">الراتب:</small>
                            <span class="fw-bold">${Utils.formatCurrency(employee.base_salary)}</span>
                        </div>
                        <div class="mb-3">
                            <span class="badge ${this.getStatusBadgeClass(employee.status)}">${this.getStatusText(employee.status)}</span>
                        </div>
                        <div class="btn-group w-100">
                            <button class="btn btn-sm btn-outline-primary" onclick="HR.viewEmployee('${employee.id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-warning" onclick="HR.editEmployee('${employee.id}')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="HR.deleteEmployee('${employee.id}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * Get filtered employees
     */
    getFilteredEmployees() {
        let employees = Array.from(this.employees.values());

        // Apply filters
        if (this.filters.status !== 'all') {
            employees = employees.filter(emp => emp.status === this.filters.status);
        }
        if (this.filters.department !== 'all') {
            employees = employees.filter(emp => emp.department_id === this.filters.department);
        }
        if (this.filters.position !== 'all') {
            employees = employees.filter(emp => emp.position_id === this.filters.position);
        }
        if (this.filters.search) {
            const search = this.filters.search.toLowerCase();
            employees = employees.filter(emp =>
                emp.first_name.toLowerCase().includes(search) ||
                emp.last_name.toLowerCase().includes(search) ||
                emp.employee_id.toLowerCase().includes(search)
            );
        }

        return employees;
    }

    /**
     * Load filter options
     */
    async loadFilterOptions() {
        // Load departments
        const departmentFilter = document.getElementById('departmentFilter');
        if (departmentFilter) {
            departmentFilter.innerHTML = '<option value="all">جميع الأقسام</option>' +
                Array.from(this.departments.values()).map(dept =>
                    `<option value="${dept.id}">${dept.name}</option>`
                ).join('');
        }

        // Load positions
        const positionFilter = document.getElementById('positionFilter');
        if (positionFilter) {
            positionFilter.innerHTML = '<option value="all">جميع المناصب</option>' +
                Array.from(this.positions.values()).map(pos =>
                    `<option value="${pos.id}">${pos.name}</option>`
                ).join('');
        }
    }

    /**
     * Get department name
     */
    getDepartmentName(departmentId) {
        const department = this.departments.get(departmentId);
        return department ? department.name : 'غير محدد';
    }

    /**
     * Get position name
     */
    getPositionName(positionId) {
        const position = this.positions.get(positionId);
        return position ? position.name : 'غير محدد';
    }

    /**
     * Get status badge class
     */
    getStatusBadgeClass(status) {
        const classes = {
            'active': 'bg-success',
            'inactive': 'bg-secondary',
            'suspended': 'bg-danger'
        };
        return classes[status] || 'bg-secondary';
    }

    /**
     * Get status text
     */
    getStatusText(status) {
        const texts = {
            'active': 'نشط',
            'inactive': 'غير نشط',
            'suspended': 'موقوف'
        };
        return texts[status] || 'غير محدد';
    }

    /**
     * Show add employee modal
     */
    showAddEmployeeModal() {
        const modal = new bootstrap.Modal(document.getElementById('addEmployeeModal'));

        // Reset form
        document.getElementById('addEmployeeForm').reset();

        // Load departments and positions
        this.loadModalOptions();

        // Set default values
        document.querySelector('[name="hire_date"]').value = new Date().toISOString().split('T')[0];
        document.querySelector('[name="status"]').value = 'active';

        modal.show();
    }

    /**
     * Load modal options
     */
    loadModalOptions() {
        // Load departments in modal
        const departmentSelect = document.querySelector('#addEmployeeModal [name="department_id"]');
        if (departmentSelect) {
            departmentSelect.innerHTML = '<option value="">اختر القسم</option>' +
                Array.from(this.departments.values()).map(dept =>
                    `<option value="${dept.id}">${dept.name}</option>`
                ).join('');
        }

        // Load positions in modal
        const positionSelect = document.querySelector('#addEmployeeModal [name="position_id"]');
        if (positionSelect) {
            positionSelect.innerHTML = '<option value="">اختر المنصب</option>' +
                Array.from(this.positions.values()).map(pos =>
                    `<option value="${pos.id}">${pos.name}</option>`
                ).join('');
        }

        // Load employees in attendance modal
        const employeeSelect = document.querySelector('#attendanceModal [name="employee_id"]');
        if (employeeSelect) {
            employeeSelect.innerHTML = '<option value="">اختر الموظف</option>' +
                Array.from(this.employees.values()).map(emp =>
                    `<option value="${emp.id}">${emp.first_name} ${emp.last_name} (${emp.employee_id})</option>`
                ).join('');
        }

        // Load employees in evaluation modal
        const evalEmployeeSelect = document.querySelector('#evaluationModal [name="employee_id"]');
        if (evalEmployeeSelect) {
            evalEmployeeSelect.innerHTML = '<option value="">اختر الموظف</option>' +
                Array.from(this.employees.values()).map(emp =>
                    `<option value="${emp.id}">${emp.first_name} ${emp.last_name} (${emp.employee_id})</option>`
                ).join('');
        }
    }

    /**
     * Save employee
     */
    async saveEmployee() {
        try {
            const form = document.getElementById('addEmployeeForm');
            const formData = new FormData(form);

            // Validate required fields
            if (!formData.get('first_name') || !formData.get('last_name') || !formData.get('employee_id')) {
                Utils.showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            // Check if employee ID already exists
            const existingEmployee = Array.from(this.employees.values()).find(emp =>
                emp.employee_id === formData.get('employee_id')
            );
            if (existingEmployee) {
                Utils.showAlert('رقم الموظف موجود بالفعل', 'error');
                return;
            }

            const employee = {
                id: Utils.generateId(),
                first_name: formData.get('first_name'),
                last_name: formData.get('last_name'),
                employee_id: formData.get('employee_id'),
                national_id: formData.get('national_id'),
                department_id: formData.get('department_id'),
                position_id: formData.get('position_id'),
                hire_date: formData.get('hire_date'),
                base_salary: parseFloat(formData.get('base_salary')) || 0,
                phone: formData.get('phone'),
                email: formData.get('email'),
                address: formData.get('address'),
                marital_status: formData.get('marital_status'),
                status: formData.get('status') || 'active',
                notes: formData.get('notes'),
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };

            // Save to database
            await Database.employees.add(employee);
            this.employees.set(employee.id, employee);

            // Close modal and refresh
            bootstrap.Modal.getInstance(document.getElementById('addEmployeeModal')).hide();
            await this.loadEmployeesData();

            Utils.showAlert('تم إضافة الموظف بنجاح', 'success');
        } catch (error) {
            console.error('Error saving employee:', error);
            Utils.showAlert('حدث خطأ أثناء حفظ الموظف', 'error');
        }
    }

    /**
     * Show attendance modal
     */
    showAttendanceModal() {
        const modal = new bootstrap.Modal(document.getElementById('attendanceModal'));

        // Reset form
        document.getElementById('attendanceForm').reset();

        // Load employees
        this.loadModalOptions();

        // Set current time
        const now = new Date();
        const timestamp = now.toISOString().slice(0, 16);
        document.querySelector('[name="timestamp"]').value = timestamp;

        modal.show();
    }

    /**
     * Mark attendance
     */
    async markAttendance(type) {
        try {
            // Show quick attendance modal or process directly
            const employeeId = prompt('أدخل رقم الموظف:');
            if (!employeeId) return;

            const employee = Array.from(this.employees.values()).find(emp =>
                emp.employee_id === employeeId
            );

            if (!employee) {
                Utils.showAlert('الموظف غير موجود', 'error');
                return;
            }

            const attendance = {
                id: Utils.generateId(),
                employee_id: employee.id,
                date: new Date().toISOString().split('T')[0],
                type: type,
                timestamp: new Date().toISOString(),
                notes: '',
                created_at: new Date().toISOString()
            };

            // Save to database
            await Database.attendance.add(attendance);
            this.attendance.set(attendance.id, attendance);

            // Refresh attendance data
            if (this.currentView === 'attendance') {
                await this.loadAttendanceData();
            }

            Utils.showAlert(`تم تسجيل ${this.getAttendanceTypeText(type)} للموظف ${employee.first_name} ${employee.last_name}`, 'success');
        } catch (error) {
            console.error('Error marking attendance:', error);
            Utils.showAlert('حدث خطأ أثناء تسجيل الحضور', 'error');
        }
    }

    /**
     * Get attendance type text
     */
    getAttendanceTypeText(type) {
        const types = {
            'check_in': 'الحضور',
            'check_out': 'الانصراف',
            'break_start': 'بداية الاستراحة',
            'break_end': 'نهاية الاستراحة'
        };
        return types[type] || type;
    }

    /**
     * Save attendance
     */
    async saveAttendance() {
        try {
            const form = document.getElementById('attendanceForm');
            const formData = new FormData(form);

            if (!formData.get('employee_id') || !formData.get('type') || !formData.get('timestamp')) {
                Utils.showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            const attendance = {
                id: Utils.generateId(),
                employee_id: formData.get('employee_id'),
                date: formData.get('timestamp').split('T')[0],
                type: formData.get('type'),
                timestamp: formData.get('timestamp'),
                notes: formData.get('notes'),
                created_at: new Date().toISOString()
            };

            // Save to database
            await Database.attendance.add(attendance);
            this.attendance.set(attendance.id, attendance);

            // Close modal and refresh
            bootstrap.Modal.getInstance(document.getElementById('attendanceModal')).hide();
            await this.loadAttendanceData();

            Utils.showAlert('تم تسجيل الحضور بنجاح', 'success');
        } catch (error) {
            console.error('Error saving attendance:', error);
            Utils.showAlert('حدث خطأ أثناء حفظ الحضور', 'error');
        }
    }

    /**
     * Load employees from database
     */
    async loadEmployees() {
        try {
            const employees = await Database.employees.getAll();
            this.employees.clear();
            employees.forEach(employee => {
                this.employees.set(employee.id, employee);
            });
        } catch (error) {
            console.error('Error loading employees:', error);
        }
    }

    /**
     * Load departments from database
     */
    async loadDepartments() {
        try {
            const departments = await Database.departments.getAll();
            this.departments.clear();
            departments.forEach(department => {
                this.departments.set(department.id, department);
            });
        } catch (error) {
            console.error('Error loading departments:', error);
        }
    }

    /**
     * Load positions from database
     */
    async loadPositions() {
        try {
            const positions = await Database.positions.getAll();
            this.positions.clear();
            positions.forEach(position => {
                this.positions.set(position.id, position);
            });
        } catch (error) {
            console.error('Error loading positions:', error);
        }
    }

    /**
     * Load attendance from database
     */
    async loadAttendance() {
        try {
            const attendance = await Database.attendance.getAll();
            this.attendance.clear();
            attendance.forEach(record => {
                this.attendance.set(record.id, record);
            });
        } catch (error) {
            console.error('Error loading attendance:', error);
        }
    }

    /**
     * Load payroll from database
     */
    async loadPayroll() {
        try {
            const payroll = await Database.payroll.getAll();
            this.payroll.clear();
            payroll.forEach(record => {
                this.payroll.set(record.id, record);
            });
        } catch (error) {
            console.error('Error loading payroll:', error);
        }
    }

    /**
     * Create sample data
     */
    async createSampleData() {
        try {
            // Create sample departments if none exist
            if (this.departments.size === 0) {
                const sampleDepartments = [
                    { id: 'dept-1', name: 'المبيعات', description: 'قسم المبيعات والتسويق' },
                    { id: 'dept-2', name: 'المحاسبة', description: 'قسم المحاسبة والمالية' },
                    { id: 'dept-3', name: 'المخازن', description: 'قسم إدارة المخازن' },
                    { id: 'dept-4', name: 'الإدارة', description: 'الإدارة العليا' },
                    { id: 'dept-5', name: 'تقنية المعلومات', description: 'قسم تقنية المعلومات' }
                ];

                for (const dept of sampleDepartments) {
                    await Database.departments.add(dept);
                    this.departments.set(dept.id, dept);
                }
            }

            // Create sample positions if none exist
            if (this.positions.size === 0) {
                const samplePositions = [
                    { id: 'pos-1', name: 'مدير', department_id: 'dept-4', description: 'مدير عام' },
                    { id: 'pos-2', name: 'مدير مبيعات', department_id: 'dept-1', description: 'مدير قسم المبيعات' },
                    { id: 'pos-3', name: 'مندوب مبيعات', department_id: 'dept-1', description: 'مندوب مبيعات' },
                    { id: 'pos-4', name: 'محاسب', department_id: 'dept-2', description: 'محاسب' },
                    { id: 'pos-5', name: 'أمين مخزن', department_id: 'dept-3', description: 'أمين مخزن' },
                    { id: 'pos-6', name: 'كاشير', department_id: 'dept-1', description: 'كاشير' },
                    { id: 'pos-7', name: 'مطور', department_id: 'dept-5', description: 'مطور برمجيات' }
                ];

                for (const pos of samplePositions) {
                    await Database.positions.add(pos);
                    this.positions.set(pos.id, pos);
                }
            }

            // Create sample employees if none exist
            if (this.employees.size === 0) {
                const sampleEmployees = [
                    {
                        id: 'emp-1',
                        first_name: 'أحمد',
                        last_name: 'محمد',
                        employee_id: 'EMP001',
                        national_id: '12345678901234',
                        department_id: 'dept-4',
                        position_id: 'pos-1',
                        hire_date: '2023-01-15',
                        base_salary: 15000,
                        phone: '01234567890',
                        email: '<EMAIL>',
                        status: 'active',
                        marital_status: 'married',
                        address: 'القاهرة، مصر',
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    },
                    {
                        id: 'emp-2',
                        first_name: 'فاطمة',
                        last_name: 'علي',
                        employee_id: 'EMP002',
                        national_id: '12345678901235',
                        department_id: 'dept-1',
                        position_id: 'pos-2',
                        hire_date: '2023-02-01',
                        base_salary: 12000,
                        phone: '01234567891',
                        email: '<EMAIL>',
                        status: 'active',
                        marital_status: 'single',
                        address: 'الجيزة، مصر',
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    },
                    {
                        id: 'emp-3',
                        first_name: 'محمد',
                        last_name: 'حسن',
                        employee_id: 'EMP003',
                        national_id: '12345678901236',
                        department_id: 'dept-1',
                        position_id: 'pos-3',
                        hire_date: '2023-03-10',
                        base_salary: 8000,
                        phone: '01234567892',
                        email: '<EMAIL>',
                        status: 'active',
                        marital_status: 'married',
                        address: 'الإسكندرية، مصر',
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    },
                    {
                        id: 'emp-4',
                        first_name: 'سارة',
                        last_name: 'أحمد',
                        employee_id: 'EMP004',
                        national_id: '12345678901237',
                        department_id: 'dept-2',
                        position_id: 'pos-4',
                        hire_date: '2023-04-05',
                        base_salary: 10000,
                        phone: '01234567893',
                        email: '<EMAIL>',
                        status: 'active',
                        marital_status: 'single',
                        address: 'القاهرة، مصر',
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    },
                    {
                        id: 'emp-5',
                        first_name: 'خالد',
                        last_name: 'عبدالله',
                        employee_id: 'EMP005',
                        national_id: '12345678901238',
                        department_id: 'dept-3',
                        position_id: 'pos-5',
                        hire_date: '2023-05-20',
                        base_salary: 7000,
                        phone: '01234567894',
                        email: '<EMAIL>',
                        status: 'active',
                        marital_status: 'married',
                        address: 'الجيزة، مصر',
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    }
                ];

                for (const emp of sampleEmployees) {
                    await Database.employees.add(emp);
                    this.employees.set(emp.id, emp);
                }
            }

            // Create sample attendance records
            if (this.attendance.size === 0) {
                const today = new Date().toISOString().split('T')[0];
                const sampleAttendance = [];

                // Create attendance for today
                Array.from(this.employees.values()).forEach(employee => {
                    const checkInTime = new Date();
                    checkInTime.setHours(8 + Math.floor(Math.random() * 2), Math.floor(Math.random() * 60));

                    sampleAttendance.push({
                        id: Utils.generateId(),
                        employee_id: employee.id,
                        date: today,
                        type: 'check_in',
                        timestamp: checkInTime.toISOString(),
                        notes: '',
                        created_at: new Date().toISOString()
                    });
                });

                for (const att of sampleAttendance) {
                    await Database.attendance.add(att);
                    this.attendance.set(att.id, att);
                }
            }

        } catch (error) {
            console.error('Error creating sample data:', error);
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Search input with debounce
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce((e) => {
                this.filters.search = e.target.value;
                this.applyFilters();
            }, 300));
        }

        // Filter selects
        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filters.status = e.target.value;
                this.applyFilters();
            });
        }

        const departmentFilter = document.getElementById('departmentFilter');
        if (departmentFilter) {
            departmentFilter.addEventListener('change', (e) => {
                this.filters.department = e.target.value;
                this.applyFilters();
            });
        }

        const positionFilter = document.getElementById('positionFilter');
        if (positionFilter) {
            positionFilter.addEventListener('change', (e) => {
                this.filters.position = e.target.value;
                this.applyFilters();
            });
        }
    }

    /**
     * Apply filters
     */
    async applyFilters() {
        if (this.currentView === 'employees') {
            await this.renderEmployeesGrid();
        }
    }

    /**
     * Clear filters
     */
    async clearFilters() {
        this.filters = {
            status: 'all',
            department: 'all',
            position: 'all',
            date_from: '',
            date_to: '',
            search: ''
        };

        // Reset form inputs
        const searchInput = document.getElementById('searchInput');
        if (searchInput) searchInput.value = '';

        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) statusFilter.value = 'all';

        const departmentFilter = document.getElementById('departmentFilter');
        if (departmentFilter) departmentFilter.value = 'all';

        const positionFilter = document.getElementById('positionFilter');
        if (positionFilter) positionFilter.value = 'all';

        await this.applyFilters();
    }

    /**
     * Load attendance data
     */
    async loadAttendanceData() {
        try {
            const today = new Date().toISOString().split('T')[0];
            const todayAttendance = Array.from(this.attendance.values()).filter(att => att.date === today);

            // Update statistics
            const presentToday = todayAttendance.filter(att => att.type === 'check_in').length;
            const totalEmployees = this.employees.size;
            const absentToday = totalEmployees - presentToday;
            const lateToday = todayAttendance.filter(att => {
                if (att.type === 'check_in') {
                    const checkInTime = new Date(att.timestamp);
                    return checkInTime.getHours() > 9; // Assuming work starts at 9 AM
                }
                return false;
            }).length;

            document.getElementById('todayPresentCount').textContent = presentToday;
            document.getElementById('todayAbsentCount').textContent = absentToday;
            document.getElementById('todayLateCount').textContent = lateToday;
            document.getElementById('todayOvertimeCount').textContent = '0'; // Placeholder

            // Load attendance table
            await this.renderAttendanceTable();
        } catch (error) {
            console.error('Error loading attendance data:', error);
        }
    }

    /**
     * Render attendance table
     */
    async renderAttendanceTable() {
        const tableBody = document.querySelector('#attendanceTable tbody');
        if (!tableBody) return;

        const today = new Date().toISOString().split('T')[0];
        const todayAttendance = Array.from(this.attendance.values()).filter(att => att.date === today);

        // Group by employee
        const employeeAttendance = new Map();
        todayAttendance.forEach(att => {
            if (!employeeAttendance.has(att.employee_id)) {
                employeeAttendance.set(att.employee_id, []);
            }
            employeeAttendance.get(att.employee_id).push(att);
        });

        if (employeeAttendance.size === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <i class="fas fa-clock fa-2x text-muted mb-2"></i>
                        <p class="mb-0">لا توجد سجلات حضور لهذا اليوم</p>
                    </td>
                </tr>
            `;
            return;
        }

        tableBody.innerHTML = Array.from(employeeAttendance.entries()).map(([employeeId, records]) => {
            const employee = this.employees.get(employeeId);
            if (!employee) return '';

            const checkIn = records.find(r => r.type === 'check_in');
            const checkOut = records.find(r => r.type === 'check_out');

            const checkInTime = checkIn ? new Date(checkIn.timestamp).toLocaleTimeString('ar-EG') : '-';
            const checkOutTime = checkOut ? new Date(checkOut.timestamp).toLocaleTimeString('ar-EG') : '-';

            let workingHours = '-';
            let status = 'غائب';
            let statusClass = 'bg-danger';

            if (checkIn) {
                status = 'حاضر';
                statusClass = 'bg-success';

                if (checkOut) {
                    const hours = (new Date(checkOut.timestamp) - new Date(checkIn.timestamp)) / (1000 * 60 * 60);
                    workingHours = hours.toFixed(1) + ' ساعة';
                }
            }

            return `
                <tr>
                    <td>${employee.first_name} ${employee.last_name}</td>
                    <td>${checkInTime}</td>
                    <td>${checkOutTime}</td>
                    <td>${workingHours}</td>
                    <td><span class="badge ${statusClass}">${status}</span></td>
                    <td>${checkIn?.notes || '-'}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="HR.viewEmployeeAttendance('${employeeId}')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }

    /**
     * Load payroll data
     */
    async loadPayrollData() {
        try {
            // Update statistics
            const currentMonth = new Date().getMonth() + 1;
            const currentYear = new Date().getFullYear();

            const monthlyPayroll = Array.from(this.payroll.values()).filter(p =>
                p.month === currentMonth && p.year === currentYear
            );

            const totalPayroll = monthlyPayroll.reduce((sum, p) => sum + (p.net_salary || 0), 0);
            const paidPayroll = monthlyPayroll.filter(p => p.status === 'paid').length;
            const pendingPayroll = monthlyPayroll.filter(p => p.status === 'pending').length;

            document.getElementById('monthlyPayrollTotal').textContent = Utils.formatCurrency(totalPayroll);
            document.getElementById('paidPayrollCount').textContent = paidPayroll;
            document.getElementById('pendingPayrollCount').textContent = pendingPayroll;

            // Set current month/year in selects
            document.getElementById('payrollMonth').value = currentMonth;
            document.getElementById('payrollYear').value = currentYear;

        } catch (error) {
            console.error('Error loading payroll data:', error);
        }
    }

    /**
     * Load evaluations data
     */
    async loadEvaluationsData() {
        try {
            // Update statistics
            const evaluations = Array.from(this.evaluations.values());

            const excellentCount = evaluations.filter(e => e.overall_rating >= 4.5).length;
            const goodCount = evaluations.filter(e => e.overall_rating >= 3.5 && e.overall_rating < 4.5).length;
            const averageCount = evaluations.filter(e => e.overall_rating >= 2.5 && e.overall_rating < 3.5).length;
            const poorCount = evaluations.filter(e => e.overall_rating < 2.5).length;

            document.getElementById('excellentRatingCount').textContent = excellentCount;
            document.getElementById('goodRatingCount').textContent = goodCount;
            document.getElementById('averageRatingCount').textContent = averageCount;
            document.getElementById('poorRatingCount').textContent = poorCount;

        } catch (error) {
            console.error('Error loading evaluations data:', error);
        }
    }

    /**
     * Load reports data
     */
    async loadReportsData() {
        try {
            // This would typically load chart data
            console.log('Loading reports data...');
        } catch (error) {
            console.error('Error loading reports data:', error);
        }
    }

    /**
     * Show evaluation modal
     */
    showEvaluationModal() {
        const modal = new bootstrap.Modal(document.getElementById('evaluationModal'));

        // Reset form
        document.getElementById('evaluationForm').reset();

        // Load employees
        this.loadModalOptions();

        modal.show();
    }

    /**
     * Save evaluation
     */
    async saveEvaluation() {
        try {
            const form = document.getElementById('evaluationForm');
            const formData = new FormData(form);

            if (!formData.get('employee_id')) {
                Utils.showAlert('يرجى اختيار الموظف', 'error');
                return;
            }

            const evaluation = {
                id: Utils.generateId(),
                employee_id: formData.get('employee_id'),
                period: formData.get('period'),
                performance_rating: parseInt(formData.get('performance_rating')),
                commitment_rating: parseInt(formData.get('commitment_rating')),
                teamwork_rating: parseInt(formData.get('teamwork_rating')),
                creativity_rating: parseInt(formData.get('creativity_rating')),
                overall_rating: (
                    parseInt(formData.get('performance_rating')) +
                    parseInt(formData.get('commitment_rating')) +
                    parseInt(formData.get('teamwork_rating')) +
                    parseInt(formData.get('creativity_rating'))
                ) / 4,
                strengths: formData.get('strengths'),
                improvements: formData.get('improvements'),
                recommendations: formData.get('recommendations'),
                evaluator_id: 'current_user', // Would be actual user ID
                evaluation_date: new Date().toISOString().split('T')[0],
                created_at: new Date().toISOString()
            };

            // Save to database
            await Database.evaluations.add(evaluation);
            this.evaluations.set(evaluation.id, evaluation);

            // Close modal and refresh
            bootstrap.Modal.getInstance(document.getElementById('evaluationModal')).hide();
            await this.loadEvaluationsData();

            Utils.showAlert('تم حفظ التقييم بنجاح', 'success');
        } catch (error) {
            console.error('Error saving evaluation:', error);
            Utils.showAlert('حدث خطأ أثناء حفظ التقييم', 'error');
        }
    }
}

// Create global instance
window.HR = new HRModule();
