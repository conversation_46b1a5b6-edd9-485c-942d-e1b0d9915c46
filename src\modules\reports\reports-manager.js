/**
 * Black Horse POS - Reports Manager (Optimized)
 * مدير التقارير المحسن
 * Developer: Augment Agent
 */

class ReportsManager {
    constructor() {
        this.reports = [];
        this.isInitialized = false;
        this.backgroundProcessing = true;
        this.reportCache = new Map();
        this.cacheExpiry = 5 * 60 * 1000; // 5 دقائق
        
        console.log('📊 Reports Manager initialized');
    }

    // تهيئة مدير التقارير
    async init() {
        try {
            console.log('🚀 Initializing Reports Manager...');
            
            // تحميل التقارير المحفوظة من التخزين المؤقت
            if (window.PerformanceOptimizer) {
                const cachedReports = window.PerformanceOptimizer.getCachedData('reports');
                if (cachedReports) {
                    this.reports = cachedReports;
                    console.log('📂 Reports loaded from cache');
                }
            }
            
            // إعداد معالجة التقارير في الخلفية
            this.setupBackgroundProcessing();
            
            this.isInitialized = true;
            console.log('✅ Reports Manager ready');
            
        } catch (error) {
            console.error('❌ Error initializing Reports Manager:', error);
            this.isInitialized = true;
        }
    }

    // إعداد معالجة التقارير في الخلفية
    setupBackgroundProcessing() {
        try {
            if (!this.backgroundProcessing) return;
            
            // معالجة دورية للتقارير
            setInterval(() => {
                this.processBackgroundReports();
            }, 60000); // كل دقيقة
            
            console.log('⚙️ Background processing enabled');
            
        } catch (error) {
            console.error('❌ Error setting up background processing:', error);
        }
    }

    // معالجة التقارير في الخلفية
    async processBackgroundReports() {
        try {
            // تنظيف التخزين المؤقت المنتهي الصلاحية
            this.cleanupExpiredCache();
            
            // إعداد التقارير الأساسية مسبقاً
            await this.preloadBasicReports();
            
        } catch (error) {
            console.error('❌ Error in background processing:', error);
        }
    }

    // تنظيف التخزين المؤقت المنتهي الصلاحية
    cleanupExpiredCache() {
        try {
            const now = Date.now();
            
            for (const [key, value] of this.reportCache.entries()) {
                if (now - value.timestamp > this.cacheExpiry) {
                    this.reportCache.delete(key);
                }
            }
            
        } catch (error) {
            console.error('❌ Error cleaning up cache:', error);
        }
    }

    // تحميل التقارير الأساسية مسبقاً
    async preloadBasicReports() {
        try {
            // تحميل تقرير المبيعات اليومية
            await this.generateSalesReport('today', true);
            
            // تحميل تقرير المخزون المنخفض
            await this.generateInventoryReport('low_stock', true);
            
        } catch (error) {
            console.error('❌ Error preloading reports:', error);
        }
    }

    // توليد تقرير المبيعات
    async generateSalesReport(period = 'today', isBackground = false) {
        try {
            const cacheKey = `sales_${period}`;
            
            // التحقق من التخزين المؤقت أولاً
            if (!isBackground && this.reportCache.has(cacheKey)) {
                const cached = this.reportCache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.cacheExpiry) {
                    console.log('📂 Sales report loaded from cache');
                    return cached.data;
                }
            }
            
            if (!isBackground) {
                console.log(`📊 Generating sales report for ${period}...`);
            }
            
            // الحصول على بيانات المبيعات
            const salesData = await this.getSalesData(period);
            
            // إنشاء التقرير
            const report = {
                id: this.generateReportId(),
                type: 'sales',
                period: period,
                generatedAt: new Date().toISOString(),
                data: {
                    summary: this.calculateSalesSummary(salesData),
                    details: salesData,
                    charts: this.prepareSalesCharts(salesData),
                    trends: this.calculateSalesTrends(salesData)
                }
            };
            
            // حفظ في التخزين المؤقت
            this.reportCache.set(cacheKey, {
                data: report,
                timestamp: Date.now()
            });
            
            if (!isBackground) {
                console.log('✅ Sales report generated');
            }
            
            return report;
            
        } catch (error) {
            console.error('❌ Error generating sales report:', error);
            return null;
        }
    }

    // الحصول على بيانات المبيعات
    async getSalesData(period) {
        try {
            if (!window.SalesManager) {
                throw new Error('Sales Manager not available');
            }
            
            const stats = window.SalesManager.getSalesStats(period);
            const allSales = window.SalesManager.getAllSales();
            
            // تصفية المبيعات حسب الفترة
            const now = new Date();
            let startDate;
            
            switch (period) {
                case 'today':
                    startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    break;
                case 'week':
                    startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    break;
                case 'month':
                    startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                    break;
                case 'year':
                    startDate = new Date(now.getFullYear(), 0, 1);
                    break;
                default:
                    startDate = new Date(0);
            }
            
            const filteredSales = allSales.filter(sale => 
                sale.status === 'completed' && new Date(sale.date) >= startDate
            );
            
            return {
                stats,
                sales: filteredSales
            };
            
        } catch (error) {
            console.error('❌ Error getting sales data:', error);
            return { stats: null, sales: [] };
        }
    }

    // حساب ملخص المبيعات
    calculateSalesSummary(salesData) {
        try {
            const { stats, sales } = salesData;
            
            if (!stats) {
                return {
                    totalSales: 0,
                    totalRevenue: 0,
                    averageSale: 0,
                    totalItems: 0
                };
            }
            
            return {
                totalSales: stats.totalSales,
                totalRevenue: stats.totalRevenue,
                averageSale: stats.averageSale,
                totalItems: stats.totalItems,
                paymentMethods: stats.paymentMethods,
                topProducts: stats.topProducts
            };
            
        } catch (error) {
            console.error('❌ Error calculating sales summary:', error);
            return {};
        }
    }

    // إعداد مخططات المبيعات
    prepareSalesCharts(salesData) {
        try {
            const { sales } = salesData;
            
            // مخطط المبيعات حسب الوقت
            const timeChart = this.prepareSalesTimeChart(sales);
            
            // مخطط المبيعات حسب طريقة الدفع
            const paymentChart = this.preparePaymentMethodChart(sales);
            
            // مخطط أفضل المنتجات
            const productsChart = this.prepareTopProductsChart(sales);
            
            return {
                time: timeChart,
                payment: paymentChart,
                products: productsChart
            };
            
        } catch (error) {
            console.error('❌ Error preparing sales charts:', error);
            return {};
        }
    }

    // مخطط المبيعات حسب الوقت
    prepareSalesTimeChart(sales) {
        try {
            const hourlyData = {};
            
            sales.forEach(sale => {
                const hour = new Date(sale.date).getHours();
                if (!hourlyData[hour]) {
                    hourlyData[hour] = { count: 0, total: 0 };
                }
                hourlyData[hour].count++;
                hourlyData[hour].total += sale.total;
            });
            
            const labels = [];
            const counts = [];
            const totals = [];
            
            for (let hour = 0; hour < 24; hour++) {
                labels.push(`${hour}:00`);
                counts.push(hourlyData[hour] ? hourlyData[hour].count : 0);
                totals.push(hourlyData[hour] ? hourlyData[hour].total : 0);
            }
            
            return {
                labels,
                datasets: [
                    {
                        label: 'عدد المبيعات',
                        data: counts,
                        type: 'line'
                    },
                    {
                        label: 'إجمالي المبيعات',
                        data: totals,
                        type: 'bar'
                    }
                ]
            };
            
        } catch (error) {
            console.error('❌ Error preparing time chart:', error);
            return {};
        }
    }

    // مخطط طرق الدفع
    preparePaymentMethodChart(sales) {
        try {
            const paymentData = {};
            
            sales.forEach(sale => {
                if (!paymentData[sale.paymentMethod]) {
                    paymentData[sale.paymentMethod] = { count: 0, total: 0 };
                }
                paymentData[sale.paymentMethod].count++;
                paymentData[sale.paymentMethod].total += sale.total;
            });
            
            const labels = Object.keys(paymentData);
            const counts = labels.map(method => paymentData[method].count);
            const totals = labels.map(method => paymentData[method].total);
            
            return {
                labels,
                datasets: [
                    {
                        label: 'عدد المعاملات',
                        data: counts,
                        type: 'pie'
                    },
                    {
                        label: 'إجمالي المبلغ',
                        data: totals,
                        type: 'doughnut'
                    }
                ]
            };
            
        } catch (error) {
            console.error('❌ Error preparing payment chart:', error);
            return {};
        }
    }

    // مخطط أفضل المنتجات
    prepareTopProductsChart(sales) {
        try {
            const products = {};
            
            sales.forEach(sale => {
                sale.items.forEach(item => {
                    if (!products[item.productId]) {
                        products[item.productId] = {
                            name: item.productName,
                            quantity: 0,
                            revenue: 0
                        };
                    }
                    products[item.productId].quantity += item.quantity;
                    products[item.productId].revenue += item.total;
                });
            });
            
            const topProducts = Object.values(products)
                .sort((a, b) => b.quantity - a.quantity)
                .slice(0, 10);
            
            const labels = topProducts.map(p => p.name);
            const quantities = topProducts.map(p => p.quantity);
            const revenues = topProducts.map(p => p.revenue);
            
            return {
                labels,
                datasets: [
                    {
                        label: 'الكمية المباعة',
                        data: quantities,
                        type: 'bar'
                    },
                    {
                        label: 'الإيرادات',
                        data: revenues,
                        type: 'line'
                    }
                ]
            };
            
        } catch (error) {
            console.error('❌ Error preparing products chart:', error);
            return {};
        }
    }

    // حساب اتجاهات المبيعات
    calculateSalesTrends(salesData) {
        try {
            const { sales } = salesData;
            
            // تجميع المبيعات حسب اليوم
            const dailyData = {};
            
            sales.forEach(sale => {
                const date = new Date(sale.date).toDateString();
                if (!dailyData[date]) {
                    dailyData[date] = { count: 0, total: 0 };
                }
                dailyData[date].count++;
                dailyData[date].total += sale.total;
            });
            
            const dates = Object.keys(dailyData).sort();
            const trends = {
                growth: 0,
                direction: 'stable',
                prediction: 0
            };
            
            if (dates.length >= 2) {
                const firstDay = dailyData[dates[0]];
                const lastDay = dailyData[dates[dates.length - 1]];
                
                trends.growth = ((lastDay.total - firstDay.total) / firstDay.total) * 100;
                trends.direction = trends.growth > 5 ? 'up' : trends.growth < -5 ? 'down' : 'stable';
                
                // توقع بسيط للمبيعات
                const avgDaily = Object.values(dailyData).reduce((sum, day) => sum + day.total, 0) / dates.length;
                trends.prediction = avgDaily * 30; // توقع شهري
            }
            
            return trends;
            
        } catch (error) {
            console.error('❌ Error calculating trends:', error);
            return {};
        }
    }

    // توليد تقرير المخزون
    async generateInventoryReport(type = 'all', isBackground = false) {
        try {
            const cacheKey = `inventory_${type}`;
            
            // التحقق من التخزين المؤقت
            if (!isBackground && this.reportCache.has(cacheKey)) {
                const cached = this.reportCache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.cacheExpiry) {
                    console.log('📂 Inventory report loaded from cache');
                    return cached.data;
                }
            }
            
            if (!isBackground) {
                console.log(`📦 Generating inventory report: ${type}...`);
            }
            
            // الحصول على بيانات المخزون
            const inventoryData = await this.getInventoryData(type);
            
            // إنشاء التقرير
            const report = {
                id: this.generateReportId(),
                type: 'inventory',
                subtype: type,
                generatedAt: new Date().toISOString(),
                data: {
                    summary: this.calculateInventorySummary(inventoryData),
                    details: inventoryData,
                    alerts: this.generateInventoryAlerts(inventoryData)
                }
            };
            
            // حفظ في التخزين المؤقت
            this.reportCache.set(cacheKey, {
                data: report,
                timestamp: Date.now()
            });
            
            if (!isBackground) {
                console.log('✅ Inventory report generated');
            }
            
            return report;
            
        } catch (error) {
            console.error('❌ Error generating inventory report:', error);
            return null;
        }
    }

    // الحصول على بيانات المخزون
    async getInventoryData(type) {
        try {
            if (!window.ProductsManager) {
                throw new Error('Products Manager not available');
            }
            
            const allProducts = window.ProductsManager.getAllProducts();
            
            switch (type) {
                case 'low_stock':
                    return allProducts.filter(product => product.stock <= (product.minStock || 10));
                case 'out_of_stock':
                    return allProducts.filter(product => product.stock <= 0);
                case 'overstocked':
                    return allProducts.filter(product => product.stock > (product.maxStock || 1000));
                default:
                    return allProducts;
            }
            
        } catch (error) {
            console.error('❌ Error getting inventory data:', error);
            return [];
        }
    }

    // حساب ملخص المخزون
    calculateInventorySummary(inventoryData) {
        try {
            const summary = {
                totalProducts: inventoryData.length,
                totalValue: inventoryData.reduce((sum, product) => sum + (product.stock * product.price), 0),
                lowStockCount: inventoryData.filter(p => p.stock <= (p.minStock || 10)).length,
                outOfStockCount: inventoryData.filter(p => p.stock <= 0).length,
                categories: {}
            };
            
            // تجميع حسب الفئات
            inventoryData.forEach(product => {
                const category = product.category || 'غير محدد';
                if (!summary.categories[category]) {
                    summary.categories[category] = { count: 0, value: 0 };
                }
                summary.categories[category].count++;
                summary.categories[category].value += product.stock * product.price;
            });
            
            return summary;
            
        } catch (error) {
            console.error('❌ Error calculating inventory summary:', error);
            return {};
        }
    }

    // توليد تنبيهات المخزون
    generateInventoryAlerts(inventoryData) {
        try {
            const alerts = [];
            
            inventoryData.forEach(product => {
                if (product.stock <= 0) {
                    alerts.push({
                        type: 'critical',
                        message: `المنتج "${product.name}" نفد من المخزون`,
                        productId: product.id,
                        action: 'restock'
                    });
                } else if (product.stock <= (product.minStock || 10)) {
                    alerts.push({
                        type: 'warning',
                        message: `المنتج "${product.name}" مخزونه منخفض (${product.stock})`,
                        productId: product.id,
                        action: 'reorder'
                    });
                }
            });
            
            return alerts;
            
        } catch (error) {
            console.error('❌ Error generating inventory alerts:', error);
            return [];
        }
    }

    // توليد معرف التقرير
    generateReportId() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substr(2, 5);
        return `RPT-${timestamp}-${random}`;
    }

    // تصدير التقرير
    async exportReport(report, format = 'json') {
        try {
            console.log(`📤 Exporting report: ${report.id} as ${format}`);
            
            switch (format) {
                case 'json':
                    return this.exportAsJSON(report);
                case 'csv':
                    return this.exportAsCSV(report);
                case 'pdf':
                    return this.exportAsPDF(report);
                default:
                    throw new Error(`Unsupported format: ${format}`);
            }
            
        } catch (error) {
            console.error('❌ Error exporting report:', error);
            throw error;
        }
    }

    // تصدير كـ JSON
    exportAsJSON(report) {
        try {
            const jsonData = JSON.stringify(report, null, 2);
            const blob = new Blob([jsonData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `${report.type}_report_${report.id}.json`;
            a.click();
            
            URL.revokeObjectURL(url);
            console.log('✅ Report exported as JSON');
            
        } catch (error) {
            console.error('❌ Error exporting as JSON:', error);
            throw error;
        }
    }

    // تصدير كـ CSV
    exportAsCSV(report) {
        try {
            let csvContent = '';
            
            if (report.type === 'sales' && report.data.details.sales) {
                // تصدير بيانات المبيعات
                csvContent = 'ID,Date,Customer,Total,Payment Method,Status\n';
                report.data.details.sales.forEach(sale => {
                    csvContent += `${sale.id},${sale.date},${sale.customerName},${sale.total},${sale.paymentMethod},${sale.status}\n`;
                });
            } else if (report.type === 'inventory') {
                // تصدير بيانات المخزون
                csvContent = 'ID,Name,Category,Stock,Price,Total Value\n';
                report.data.details.forEach(product => {
                    csvContent += `${product.id},${product.name},${product.category},${product.stock},${product.price},${product.stock * product.price}\n`;
                });
            }
            
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `${report.type}_report_${report.id}.csv`;
            a.click();
            
            URL.revokeObjectURL(url);
            console.log('✅ Report exported as CSV');
            
        } catch (error) {
            console.error('❌ Error exporting as CSV:', error);
            throw error;
        }
    }

    // تصدير كـ PDF (مبسط)
    exportAsPDF(report) {
        try {
            // سيتم تطوير تصدير PDF لاحقاً
            console.log('ℹ️ PDF export (to be implemented)');
            throw new Error('PDF export not yet implemented');
            
        } catch (error) {
            console.error('❌ Error exporting as PDF:', error);
            throw error;
        }
    }

    // الحصول على التقارير المحفوظة
    getSavedReports() {
        return this.reports;
    }

    // حفظ تقرير
    saveReport(report) {
        try {
            this.reports.unshift(report);
            
            // حفظ في التخزين المؤقت
            if (window.PerformanceOptimizer) {
                window.PerformanceOptimizer.cacheData('reports', this.reports);
            }
            
            console.log(`💾 Report saved: ${report.id}`);
            
        } catch (error) {
            console.error('❌ Error saving report:', error);
        }
    }

    // حذف تقرير
    deleteReport(reportId) {
        try {
            const index = this.reports.findIndex(r => r.id === reportId);
            if (index >= 0) {
                this.reports.splice(index, 1);
                
                // تحديث التخزين المؤقت
                if (window.PerformanceOptimizer) {
                    window.PerformanceOptimizer.cacheData('reports', this.reports);
                }
                
                console.log(`🗑️ Report deleted: ${reportId}`);
                return true;
            }
            return false;
            
        } catch (error) {
            console.error('❌ Error deleting report:', error);
            return false;
        }
    }

    // تنظيف الموارد
    cleanup() {
        try {
            this.reportCache.clear();
            console.log('✅ Reports Manager cleaned up');
            
        } catch (error) {
            console.error('❌ Error cleaning up Reports Manager:', error);
        }
    }
}

// إنشاء instance عام
window.ReportsManager = new ReportsManager();

// تصدير للاستخدام في الوحدات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ReportsManager;
}

console.log('✅ Reports Manager loaded successfully');
