/**
 * Black Horse POS - Function Conflicts Fix
 * إصلاح تضارب الوظائف المكررة
 * Developer: Augment Agent
 */

// ===== FUNCTION CONFLICTS RESOLVER =====
const FunctionConflictsResolver = {
    // قائمة الوظائف المكررة المكتشفة
    duplicateFunctions: {
        navigation: ['showPage'],
        dataManagement: [
            'loadFromLocalStorage', 'saveToLocalStorage', 'saveAllData', 'loadAllData',
            'saveProductsData', 'saveCustomersData', 'saveSuppliersData', 'saveSalesData',
            'loadSystemSettings', 'saveSystemSettings', 'loadPageData'
        ],
        products: ['addProduct', 'saveProduct'],
        customers: ['addCustomer', 'saveCustomer'],
        settings: ['loadSettings', 'saveSettings']
    },
    
    // الوظائف المحسنة الموحدة
    unifiedFunctions: {},
    
    // تهيئة النظام
    init: function() {
        try {
            console.log('🔧 Initializing Function Conflicts Resolver...');
            
            // إنشاء الوظائف الموحدة
            this.createUnifiedFunctions();
            
            // إزالة الوظائف المكررة
            this.removeConflictingFunctions();
            
            // تطبيق الوظائف الموحدة
            this.applyUnifiedFunctions();
            
            console.log('✅ Function conflicts resolved successfully');
        } catch (error) {
            console.error('❌ Error resolving function conflicts:', error);
        }
    },
    
    // إنشاء الوظائف الموحدة
    createUnifiedFunctions: function() {
        try {
            // وظيفة التنقل الموحدة
            this.unifiedFunctions.showPage = function(pageId) {
                try {
                    console.log('📄 [UNIFIED] Switching to page:', pageId);
                    
                    // التحقق من صحة pageId
                    if (!pageId || typeof pageId !== 'string') {
                        console.error('❌ Invalid pageId:', pageId);
                        return false;
                    }
                    
                    // إخفاء جميع الصفحات
                    document.querySelectorAll('.page').forEach(page => {
                        page.style.display = 'none';
                        page.classList.remove('active');
                    });
                    
                    // إظهار الصفحة المطلوبة
                    const targetPage = document.getElementById(pageId);
                    if (targetPage) {
                        targetPage.style.display = 'block';
                        targetPage.classList.add('active');
                        
                        // تحديث currentPage
                        if (typeof window !== 'undefined') {
                            window.currentPage = pageId;
                        }
                        
                        // تحميل بيانات الصفحة
                        FunctionConflictsResolver.unifiedFunctions.loadPageData(pageId);
                        
                        // تحديث التنقل
                        FunctionConflictsResolver.updateNavigation(pageId);
                        
                        console.log('✅ Page switched successfully:', pageId);
                        return true;
                    } else {
                        console.error('❌ Page not found:', pageId);
                        return false;
                    }
                } catch (error) {
                    console.error('❌ Error in unified showPage:', error);
                    return false;
                }
            };
            
            // وظيفة تحميل البيانات الموحدة
            this.unifiedFunctions.loadFromLocalStorage = function() {
                try {
                    console.log('📂 [UNIFIED] Loading data from localStorage...');
                    
                    // تحميل البيانات الأساسية
                    const dataTypes = ['products', 'customers', 'suppliers', 'sales', 'expenses', 'users', 'roles'];
                    
                    dataTypes.forEach(type => {
                        try {
                            const savedData = localStorage.getItem(type);
                            if (savedData) {
                                window[type] = JSON.parse(savedData);
                                console.log(`✅ Loaded ${type}: ${window[type].length} items`);
                            } else {
                                window[type] = [];
                                console.log(`ℹ️ No saved data for ${type}, initialized empty array`);
                            }
                        } catch (error) {
                            console.error(`❌ Error loading ${type}:`, error);
                            window[type] = [];
                        }
                    });
                    
                    // تحميل الإعدادات
                    try {
                        const settings = localStorage.getItem('systemSettings');
                        if (settings) {
                            window.systemSettings = JSON.parse(settings);
                        }
                    } catch (error) {
                        console.error('❌ Error loading settings:', error);
                    }
                    
                    console.log('✅ All data loaded successfully');
                    return true;
                } catch (error) {
                    console.error('❌ Error in unified loadFromLocalStorage:', error);
                    return false;
                }
            };
            
            // وظيفة حفظ البيانات الموحدة
            this.unifiedFunctions.saveToLocalStorage = function() {
                try {
                    console.log('💾 [UNIFIED] Saving data to localStorage...');
                    
                    const dataTypes = ['products', 'customers', 'suppliers', 'sales', 'expenses', 'users', 'roles'];
                    let savedCount = 0;
                    
                    dataTypes.forEach(type => {
                        try {
                            if (window[type]) {
                                localStorage.setItem(type, JSON.stringify(window[type]));
                                savedCount++;
                                console.log(`✅ Saved ${type}: ${window[type].length} items`);
                            }
                        } catch (error) {
                            console.error(`❌ Error saving ${type}:`, error);
                        }
                    });
                    
                    // حفظ الإعدادات
                    try {
                        if (window.systemSettings) {
                            localStorage.setItem('systemSettings', JSON.stringify(window.systemSettings));
                        }
                    } catch (error) {
                        console.error('❌ Error saving settings:', error);
                    }
                    
                    // حفظ وقت آخر حفظ
                    localStorage.setItem('lastSaveTime', Date.now().toString());
                    
                    console.log(`✅ Saved ${savedCount} data types successfully`);
                    return true;
                } catch (error) {
                    console.error('❌ Error in unified saveToLocalStorage:', error);
                    return false;
                }
            };
            
            // وظيفة تحميل بيانات الصفحة الموحدة
            this.unifiedFunctions.loadPageData = function(pageId) {
                try {
                    console.log('📊 [UNIFIED] Loading data for page:', pageId);
                    
                    switch (pageId) {
                        case 'dashboard':
                            // تحديث إحصائيات لوحة التحكم
                            if (typeof updateDashboardStats === 'function') {
                                updateDashboardStats();
                            }
                            break;
                            
                        case 'products':
                            // تحديث جدول المنتجات
                            if (typeof displayProducts === 'function') {
                                displayProducts();
                            }
                            break;
                            
                        case 'customers':
                            // تحديث جدول العملاء
                            if (typeof displayCustomers === 'function') {
                                displayCustomers();
                            }
                            break;
                            
                        case 'sales':
                            // تحديث جدول المبيعات
                            if (typeof displaySales === 'function') {
                                displaySales();
                            }
                            break;
                            
                        case 'pos':
                            // تحديث نقطة البيع
                            if (typeof displayProductsInPOS === 'function') {
                                displayProductsInPOS();
                            }
                            if (typeof loadCustomersInPOS === 'function') {
                                loadCustomersInPOS();
                            }
                            break;
                            
                        default:
                            console.log(`ℹ️ No specific data loading for page: ${pageId}`);
                    }
                    
                    return true;
                } catch (error) {
                    console.error('❌ Error loading page data:', error);
                    return false;
                }
            };
            
            console.log('✅ Unified functions created');
        } catch (error) {
            console.error('❌ Error creating unified functions:', error);
        }
    },
    
    // إزالة الوظائف المتضاربة
    removeConflictingFunctions: function() {
        try {
            console.log('🧹 Removing conflicting functions...');
            
            // قائمة الوظائف المكررة للإزالة
            const functionsToRemove = [
                'showPage_old', 'showPage_backup', 'showPage_legacy',
                'loadFromLocalStorage_old', 'saveToLocalStorage_old',
                'loadPageData_old', 'saveAllData_old'
            ];
            
            functionsToRemove.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    delete window[funcName];
                    console.log(`🗑️ Removed function: ${funcName}`);
                }
            });
            
            console.log('✅ Conflicting functions removed');
        } catch (error) {
            console.error('❌ Error removing conflicting functions:', error);
        }
    },
    
    // تطبيق الوظائف الموحدة
    applyUnifiedFunctions: function() {
        try {
            console.log('🔄 Applying unified functions...');
            
            // تطبيق الوظائف الموحدة على النافذة العامة
            Object.keys(this.unifiedFunctions).forEach(funcName => {
                window[funcName] = this.unifiedFunctions[funcName];
                console.log(`✅ Applied unified function: ${funcName}`);
            });
            
            console.log('✅ Unified functions applied successfully');
        } catch (error) {
            console.error('❌ Error applying unified functions:', error);
        }
    },
    
    // تحديث التنقل
    updateNavigation: function(pageId) {
        try {
            // تحديث الأزرار النشطة في التنقل
            document.querySelectorAll('.nav-item, .sidebar-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // تفعيل الزر المناسب
            const activeNavItem = document.querySelector(`[onclick*="${pageId}"], [data-page="${pageId}"]`);
            if (activeNavItem) {
                activeNavItem.classList.add('active');
            }
            
            // تحديث عنوان الصفحة
            const pageTitle = this.getPageTitle(pageId);
            document.title = `Black Horse POS - ${pageTitle}`;
            
        } catch (error) {
            console.error('❌ Error updating navigation:', error);
        }
    },
    
    // الحصول على عنوان الصفحة
    getPageTitle: function(pageId) {
        const titles = {
            'dashboard': 'لوحة التحكم',
            'products': 'المنتجات',
            'customers': 'العملاء',
            'suppliers': 'الموردين',
            'sales': 'المبيعات',
            'pos': 'نقطة البيع',
            'reports': 'التقارير',
            'settings': 'الإعدادات',
            'users': 'المستخدمين'
        };
        
        return titles[pageId] || 'صفحة غير معروفة';
    }
};

// تهيئة تلقائية عند تحميل الملف
if (typeof window !== 'undefined') {
    window.FunctionConflictsResolver = FunctionConflictsResolver;
    
    // تهيئة عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            FunctionConflictsResolver.init();
        }, 1000); // انتظار ثانية واحدة للتأكد من تحميل جميع الملفات
    });
}

console.log('📦 Function Conflicts Resolver loaded');
