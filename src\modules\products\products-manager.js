/**
 * Black Horse POS - Products Manager
 * مدير المنتجات المحسن
 * Developer: Augment Agent
 */

class ProductsManager {
    constructor() {
        this.products = [];
        this.filteredProducts = [];
        this.currentPage = 1;
        this.itemsPerPage = 20;
        this.searchQuery = '';
        this.sortBy = 'name';
        this.sortOrder = 'asc';
        
        console.log('📦 Products Manager initialized');
        this.init();
    }

    // تهيئة مدير المنتجات
    async init() {
        try {
            // تحميل المنتجات من التخزين المؤقت أولاً
            if (window.PerformanceOptimizer) {
                const cachedProducts = window.PerformanceOptimizer.getCachedData('products');
                if (cachedProducts) {
                    this.products = cachedProducts;
                    console.log('📂 Products loaded from cache');
                    return;
                }
            }
            
            // تحميل من قاعدة البيانات
            await this.loadProducts();
            
            console.log('✅ Products Manager ready');
            
        } catch (error) {
            console.error('❌ Error initializing Products Manager:', error);
        }
    }

    // تحميل المنتجات
    async loadProducts() {
        try {
            console.log('📂 Loading products...');
            
            if (window.UnifiedStorage) {
                this.products = await window.UnifiedStorage.load('products', []);
            } else {
                // fallback للطريقة التقليدية
                const data = localStorage.getItem('products');
                this.products = data ? JSON.parse(data) : [];
            }
            
            // حفظ في التخزين المؤقت
            if (window.PerformanceOptimizer) {
                window.PerformanceOptimizer.cacheData('products', this.products);
            }
            
            this.filteredProducts = [...this.products];
            
            console.log(`✅ Loaded ${this.products.length} products`);
            
        } catch (error) {
            console.error('❌ Error loading products:', error);
            this.products = [];
            this.filteredProducts = [];
        }
    }

    // حفظ المنتجات
    async saveProducts() {
        try {
            console.log('💾 Saving products...');
            
            if (window.UnifiedStorage) {
                await window.UnifiedStorage.save('products', this.products);
            } else {
                // fallback للطريقة التقليدية
                localStorage.setItem('products', JSON.stringify(this.products));
            }
            
            // تحديث التخزين المؤقت
            if (window.PerformanceOptimizer) {
                window.PerformanceOptimizer.cacheData('products', this.products);
            }
            
            console.log('✅ Products saved successfully');
            
        } catch (error) {
            console.error('❌ Error saving products:', error);
            throw error;
        }
    }

    // إضافة منتج جديد
    async addProduct(productData) {
        try {
            console.log('➕ Adding new product...');
            
            // التحقق من صحة البيانات
            if (window.Validators && !window.Validators.validateProduct(productData)) {
                throw new Error('بيانات المنتج غير صحيحة');
            }
            
            // إنشاء منتج جديد
            const newProduct = {
                id: Date.now().toString(),
                ...productData,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                stock: parseInt(productData.stock) || 0,
                price: parseFloat(productData.price) || 0,
                cost: parseFloat(productData.cost) || 0
            };
            
            // إضافة للقائمة
            this.products.push(newProduct);
            this.filteredProducts = [...this.products];
            
            // حفظ التغييرات
            await this.saveProducts();
            
            // تحديث الواجهة
            this.refreshProductsDisplay();
            
            // إظهار رسالة نجاح
            if (window.Utils) {
                window.Utils.showMessage('تم إضافة المنتج بنجاح', 'success');
            }
            
            console.log('✅ Product added successfully:', newProduct.id);
            return newProduct;
            
        } catch (error) {
            console.error('❌ Error adding product:', error);
            if (window.Utils) {
                window.Utils.showMessage('خطأ في إضافة المنتج: ' + error.message, 'error');
            }
            throw error;
        }
    }

    // تعديل منتج
    async editProduct(productId, updatedData) {
        try {
            console.log(`✏️ Editing product: ${productId}`);
            
            const productIndex = this.products.findIndex(p => p.id === productId);
            if (productIndex === -1) {
                throw new Error('المنتج غير موجود');
            }
            
            // التحقق من صحة البيانات
            if (window.Validators && !window.Validators.validateProduct(updatedData)) {
                throw new Error('بيانات المنتج غير صحيحة');
            }
            
            // تحديث المنتج
            this.products[productIndex] = {
                ...this.products[productIndex],
                ...updatedData,
                updatedAt: new Date().toISOString(),
                stock: parseInt(updatedData.stock) || this.products[productIndex].stock,
                price: parseFloat(updatedData.price) || this.products[productIndex].price,
                cost: parseFloat(updatedData.cost) || this.products[productIndex].cost
            };
            
            this.filteredProducts = [...this.products];
            
            // حفظ التغييرات
            await this.saveProducts();
            
            // تحديث الواجهة
            this.refreshProductsDisplay();
            
            // إظهار رسالة نجاح
            if (window.Utils) {
                window.Utils.showMessage('تم تحديث المنتج بنجاح', 'success');
            }
            
            console.log('✅ Product updated successfully:', productId);
            return this.products[productIndex];
            
        } catch (error) {
            console.error('❌ Error editing product:', error);
            if (window.Utils) {
                window.Utils.showMessage('خطأ في تعديل المنتج: ' + error.message, 'error');
            }
            throw error;
        }
    }

    // حذف منتج
    async deleteProduct(productId) {
        try {
            console.log(`🗑️ Deleting product: ${productId}`);
            
            const productIndex = this.products.findIndex(p => p.id === productId);
            if (productIndex === -1) {
                throw new Error('المنتج غير موجود');
            }
            
            // تأكيد الحذف
            if (window.Utils && !window.Utils.confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
                return null;
            }
            
            // حذف المنتج
            const deletedProduct = this.products.splice(productIndex, 1)[0];
            this.filteredProducts = [...this.products];
            
            // حفظ التغييرات
            await this.saveProducts();
            
            // تحديث الواجهة
            this.refreshProductsDisplay();
            
            // إظهار رسالة نجاح
            if (window.Utils) {
                window.Utils.showMessage('تم حذف المنتج بنجاح', 'success');
            }
            
            console.log('✅ Product deleted successfully:', productId);
            return deletedProduct;
            
        } catch (error) {
            console.error('❌ Error deleting product:', error);
            if (window.Utils) {
                window.Utils.showMessage('خطأ في حذف المنتج: ' + error.message, 'error');
            }
            throw error;
        }
    }

    // البحث في المنتجات
    searchProducts(query) {
        try {
            this.searchQuery = query.toLowerCase();
            
            if (!this.searchQuery) {
                this.filteredProducts = [...this.products];
            } else {
                this.filteredProducts = this.products.filter(product => 
                    product.name.toLowerCase().includes(this.searchQuery) ||
                    product.barcode?.toLowerCase().includes(this.searchQuery) ||
                    product.category?.toLowerCase().includes(this.searchQuery)
                );
            }
            
            this.currentPage = 1;
            this.refreshProductsDisplay();
            
            console.log(`🔍 Search results: ${this.filteredProducts.length} products found`);
            
        } catch (error) {
            console.error('❌ Error searching products:', error);
        }
    }

    // ترتيب المنتجات
    sortProducts(sortBy, sortOrder = 'asc') {
        try {
            this.sortBy = sortBy;
            this.sortOrder = sortOrder;
            
            this.filteredProducts.sort((a, b) => {
                let valueA = a[sortBy];
                let valueB = b[sortBy];
                
                // معالجة القيم الرقمية
                if (sortBy === 'price' || sortBy === 'cost' || sortBy === 'stock') {
                    valueA = parseFloat(valueA) || 0;
                    valueB = parseFloat(valueB) || 0;
                } else {
                    valueA = valueA?.toString().toLowerCase() || '';
                    valueB = valueB?.toString().toLowerCase() || '';
                }
                
                if (sortOrder === 'asc') {
                    return valueA > valueB ? 1 : -1;
                } else {
                    return valueA < valueB ? 1 : -1;
                }
            });
            
            this.refreshProductsDisplay();
            
            console.log(`📊 Products sorted by ${sortBy} (${sortOrder})`);
            
        } catch (error) {
            console.error('❌ Error sorting products:', error);
        }
    }

    // تحديث عرض المنتجات
    refreshProductsDisplay() {
        try {
            const contentDiv = document.getElementById('products-content');
            if (!contentDiv) return;
            
            // حساب الصفحات
            const totalPages = Math.ceil(this.filteredProducts.length / this.itemsPerPage);
            const startIndex = (this.currentPage - 1) * this.itemsPerPage;
            const endIndex = startIndex + this.itemsPerPage;
            const currentProducts = this.filteredProducts.slice(startIndex, endIndex);
            
            // إنشاء HTML للمنتجات
            let html = `
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <input type="text" class="form-control" placeholder="البحث في المنتجات..." 
                               onkeyup="window.ProductsManager.searchProducts(this.value)" style="width: 300px;">
                    </div>
                    <div>
                        <select class="form-select" onchange="window.ProductsManager.sortProducts(this.value)" style="width: 200px;">
                            <option value="name">ترتيب حسب الاسم</option>
                            <option value="price">ترتيب حسب السعر</option>
                            <option value="stock">ترتيب حسب المخزون</option>
                            <option value="createdAt">ترتيب حسب التاريخ</option>
                        </select>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>الباركود</th>
                                <th>الفئة</th>
                                <th>السعر</th>
                                <th>المخزون</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            if (currentProducts.length === 0) {
                html += `
                    <tr>
                        <td colspan="6" class="text-center text-muted">
                            ${this.searchQuery ? 'لا توجد منتجات تطابق البحث' : 'لا توجد منتجات'}
                        </td>
                    </tr>
                `;
            } else {
                currentProducts.forEach(product => {
                    html += `
                        <tr>
                            <td>${product.name}</td>
                            <td>${product.barcode || '-'}</td>
                            <td>${product.category || '-'}</td>
                            <td>${window.Utils ? window.Utils.formatCurrency(product.price) : product.price + ' ج.م'}</td>
                            <td>
                                <span class="badge ${product.stock > 10 ? 'bg-success' : product.stock > 0 ? 'bg-warning' : 'bg-danger'}">
                                    ${product.stock}
                                </span>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-primary me-1" onclick="window.ProductsManager.showEditModal('${product.id}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="window.ProductsManager.deleteProduct('${product.id}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                });
            }
            
            html += `
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <nav aria-label="Products pagination">
                    <ul class="pagination justify-content-center">
            `;
            
            // أزرار الصفحات
            for (let i = 1; i <= totalPages; i++) {
                html += `
                    <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="window.ProductsManager.goToPage(${i})">${i}</a>
                    </li>
                `;
            }
            
            html += `
                    </ul>
                </nav>
                
                <div class="text-center text-muted">
                    عرض ${startIndex + 1} - ${Math.min(endIndex, this.filteredProducts.length)} من ${this.filteredProducts.length} منتج
                </div>
            `;
            
            contentDiv.innerHTML = html;
            
        } catch (error) {
            console.error('❌ Error refreshing products display:', error);
        }
    }

    // الانتقال لصفحة معينة
    goToPage(page) {
        this.currentPage = page;
        this.refreshProductsDisplay();
    }

    // تحميل صفحة المنتجات
    async loadProductsPage() {
        try {
            console.log('📦 Loading products page...');
            
            // تحميل المنتجات إذا لم تكن محملة
            if (this.products.length === 0) {
                await this.loadProducts();
            }
            
            // عرض المنتجات
            this.refreshProductsDisplay();
            
            console.log('✅ Products page loaded');
            
        } catch (error) {
            console.error('❌ Error loading products page:', error);
            
            const contentDiv = document.getElementById('products-content');
            if (contentDiv) {
                contentDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        خطأ في تحميل المنتجات. يرجى المحاولة مرة أخرى.
                    </div>
                `;
            }
        }
    }

    // إظهار نموذج التعديل
    showEditModal(productId) {
        // سيتم تطوير هذه الوظيفة لاحقاً
        if (window.Utils) {
            window.Utils.showMessage('سيتم تطوير نموذج التعديل قريباً', 'info');
        }
    }

    // الحصول على منتج بالمعرف
    getProductById(productId) {
        return this.products.find(p => p.id === productId);
    }

    // الحصول على إحصائيات المنتجات
    getProductsStats() {
        return {
            total: this.products.length,
            lowStock: this.products.filter(p => p.stock <= 5).length,
            outOfStock: this.products.filter(p => p.stock === 0).length,
            totalValue: this.products.reduce((sum, p) => sum + (p.price * p.stock), 0)
        };
    }
}

// إنشاء instance عام
window.ProductsManager = new ProductsManager();

// تصدير للاستخدام في الوحدات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProductsManager;
}

console.log('✅ Products Manager loaded successfully');
