/**
 * Black Horse POS - Simple Dropdown Fix
 * إصلاح بسيط وفعال للقوائم المنسدلة
 * Developer: Augment Agent
 */

console.log('🔧 Loading Simple Dropdown Fix...');

// ===== SIMPLE DROPDOWN FIX =====
const SimpleDropdownFix = {
    isInitialized: false,
    
    // تهيئة النظام
    init: function() {
        try {
            console.log('📂 Initializing Simple Dropdown Fix...');
            
            // إزالة الوظائف المتعارضة
            this.removeConflictingFunctions();
            
            // إنشاء وظيفة بسيطة وموثوقة
            this.createSimpleToggleFunction();
            
            // إعداد معالجات الأحداث
            this.setupEventHandlers();
            
            // إضافة ستايل بسيط
            this.addSimpleStyles();
            
            this.isInitialized = true;
            console.log('✅ Simple Dropdown Fix initialized successfully');
        } catch (error) {
            console.error('❌ Error initializing Simple Dropdown Fix:', error);
        }
    },
    
    // إزالة الوظائف المتعارضة
    removeConflictingFunctions: function() {
        try {
            console.log('🗑️ Removing conflicting functions...');
            
            // إزالة جميع معالجات onclick القديمة
            const dropdownToggles = document.querySelectorAll('[onclick*="toggleDropdown"]');
            dropdownToggles.forEach(toggle => {
                toggle.removeAttribute('onclick');
            });
            
            console.log(`✅ Removed ${dropdownToggles.length} conflicting onclick handlers`);
        } catch (error) {
            console.error('❌ Error removing conflicting functions:', error);
        }
    },
    
    // إنشاء وظيفة تبديل بسيطة
    createSimpleToggleFunction: function() {
        try {
            console.log('⚙️ Creating simple toggle function...');
            
            // وظيفة بسيطة وموثوقة
            window.toggleDropdown = function(element) {
                try {
                    // العثور على القائمة المنسدلة
                    const dropdown = element.closest('.nav-dropdown') || element.parentElement;
                    if (!dropdown) {
                        console.warn('⚠️ Dropdown container not found');
                        return;
                    }
                    
                    const menu = dropdown.querySelector('.dropdown-menu');
                    if (!menu) {
                        console.warn('⚠️ Dropdown menu not found');
                        return;
                    }
                    
                    // التحقق من الحالة الحالية
                    const isOpen = menu.style.display === 'block';
                    
                    // إغلاق جميع القوائم الأخرى أولاً
                    document.querySelectorAll('.dropdown-menu').forEach(m => {
                        if (m !== menu) {
                            m.style.display = 'none';
                            const parentDropdown = m.closest('.nav-dropdown');
                            if (parentDropdown) {
                                parentDropdown.classList.remove('active');
                            }
                        }
                    });
                    
                    // تبديل القائمة الحالية
                    if (isOpen) {
                        // إغلاق القائمة
                        menu.style.display = 'none';
                        dropdown.classList.remove('active');
                        console.log('📂 Dropdown closed');
                    } else {
                        // فتح القائمة
                        menu.style.display = 'block';
                        dropdown.classList.add('active');
                        console.log('📂 Dropdown opened');
                    }
                    
                } catch (error) {
                    console.error('❌ Error in toggleDropdown:', error);
                }
            };
            
            console.log('✅ Simple toggle function created');
        } catch (error) {
            console.error('❌ Error creating toggle function:', error);
        }
    },
    
    // إعداد معالجات الأحداث
    setupEventHandlers: function() {
        try {
            console.log('🎯 Setting up event handlers...');
            
            // إعداد النقر على القوائم المنسدلة
            const dropdownToggles = document.querySelectorAll('.dropdown-toggle, .nav-link.dropdown-toggle, .user-menu-toggle');
            dropdownToggles.forEach(toggle => {
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    window.toggleDropdown(this);
                });
            });
            
            // إغلاق القوائم عند النقر خارجها
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.nav-dropdown') && !e.target.closest('.user-menu')) {
                    document.querySelectorAll('.dropdown-menu').forEach(menu => {
                        menu.style.display = 'none';
                        const dropdown = menu.closest('.nav-dropdown');
                        if (dropdown) {
                            dropdown.classList.remove('active');
                        }
                    });
                }
            });
            
            // إغلاق القوائم بمفتاح Escape
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    document.querySelectorAll('.dropdown-menu').forEach(menu => {
                        menu.style.display = 'none';
                        const dropdown = menu.closest('.nav-dropdown');
                        if (dropdown) {
                            dropdown.classList.remove('active');
                        }
                    });
                }
            });
            
            console.log(`✅ Set up event handlers for ${dropdownToggles.length} dropdown toggles`);
        } catch (error) {
            console.error('❌ Error setting up event handlers:', error);
        }
    },
    
    // إضافة ستايل بسيط
    addSimpleStyles: function() {
        try {
            console.log('🎨 Adding simple styles...');
            
            const style = document.createElement('style');
            style.id = 'simple-dropdown-styles';
            style.textContent = `
                /* ===== SIMPLE DROPDOWN STYLES ===== */
                
                /* إخفاء القوائم المنسدلة افتراضياً */
                .dropdown-menu {
                    display: none !important;
                    position: absolute !important;
                    top: 100% !important;
                    right: 0 !important;
                    background: white !important;
                    border: 1px solid #ddd !important;
                    border-radius: 8px !important;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
                    z-index: 1000 !important;
                    min-width: 200px !important;
                    padding: 8px 0 !important;
                    margin-top: 4px !important;
                }
                
                /* عرض القوائم النشطة */
                .nav-dropdown.active .dropdown-menu {
                    display: block !important;
                }
                
                /* تحسين عناصر القائمة */
                .dropdown-item {
                    display: block !important;
                    padding: 10px 16px !important;
                    color: #333 !important;
                    text-decoration: none !important;
                    transition: background-color 0.2s ease !important;
                    border-radius: 4px !important;
                    margin: 2px 8px !important;
                }
                
                .dropdown-item:hover {
                    background-color: #f8f9fa !important;
                    color: #007bff !important;
                }
                
                /* تحسين أيقونات القائمة */
                .dropdown-item i {
                    width: 20px !important;
                    text-align: center !important;
                    margin-left: 8px !important;
                }
                
                /* تحسين السهم */
                .dropdown-arrow {
                    transition: transform 0.3s ease !important;
                    font-size: 12px !important;
                    margin-right: 8px !important;
                }
                
                .nav-dropdown.active .dropdown-arrow {
                    transform: rotate(180deg) !important;
                }
                
                /* تحسين رابط القائمة */
                .dropdown-toggle {
                    cursor: pointer !important;
                    user-select: none !important;
                }
                
                .dropdown-toggle:hover {
                    background-color: rgba(255,255,255,0.1) !important;
                    border-radius: 8px !important;
                }
                
                /* قائمة المستخدم */
                .user-dropdown {
                    right: 0 !important;
                    left: auto !important;
                    min-width: 250px !important;
                }
                
                .user-dropdown-header {
                    padding: 16px !important;
                    border-bottom: 1px solid #eee !important;
                    background-color: #f8f9fa !important;
                }
                
                .dropdown-divider {
                    height: 1px !important;
                    background-color: #eee !important;
                    margin: 8px 0 !important;
                }
                
                /* تحسين للجوال */
                @media (max-width: 768px) {
                    .dropdown-menu {
                        position: fixed !important;
                        top: auto !important;
                        right: 10px !important;
                        left: 10px !important;
                        width: auto !important;
                        min-width: auto !important;
                    }
                }
            `;
            
            // إزالة الستايل القديم إن وجد
            const oldStyle = document.getElementById('simple-dropdown-styles');
            if (oldStyle) oldStyle.remove();
            
            document.head.appendChild(style);
            console.log('✅ Simple styles added');
        } catch (error) {
            console.error('❌ Error adding styles:', error);
        }
    },
    
    // اختبار القوائم المنسدلة
    testDropdowns: function() {
        try {
            console.log('🧪 Testing dropdowns...');
            
            const dropdowns = document.querySelectorAll('.nav-dropdown');
            let workingCount = 0;
            
            dropdowns.forEach((dropdown, index) => {
                const toggle = dropdown.querySelector('.dropdown-toggle, .nav-link');
                const menu = dropdown.querySelector('.dropdown-menu');
                
                if (toggle && menu) {
                    workingCount++;
                    console.log(`✅ Dropdown ${index + 1}: Working`);
                } else {
                    console.warn(`⚠️ Dropdown ${index + 1}: Missing elements`);
                }
            });
            
            console.log(`📊 Test Results: ${workingCount}/${dropdowns.length} dropdowns working`);
            return workingCount === dropdowns.length;
        } catch (error) {
            console.error('❌ Error testing dropdowns:', error);
            return false;
        }
    }
};

// تهيئة تلقائية عند تحميل الملف
if (typeof window !== 'undefined') {
    window.SimpleDropdownFix = SimpleDropdownFix;
    
    // تهيئة عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            SimpleDropdownFix.init();
            
            // اختبار القوائم بعد التهيئة
            setTimeout(() => {
                SimpleDropdownFix.testDropdowns();
            }, 1000);
        }, 500);
    });
}

console.log('📦 Simple Dropdown Fix loaded');
