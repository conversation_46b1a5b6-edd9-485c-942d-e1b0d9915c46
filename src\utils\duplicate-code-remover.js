/**
 * Black Horse POS - Duplicate Code Remover
 * أداة إزالة الكود المكرر
 * Developer: Augment Agent
 */

class DuplicateCodeRemover {
    constructor() {
        this.duplicateFunctions = new Map();
        this.cleanedFunctions = new Map();
        this.removalReport = {
            totalDuplicates: 0,
            removedFunctions: [],
            consolidatedFunctions: [],
            errors: []
        };
        
        console.log('🔍 Duplicate Code Remover initialized');
    }

    // تحليل الكود المكرر في app-clean.js
    async analyzeDuplicates() {
        try {
            console.log('🔍 Analyzing duplicate code...');
            
            // قائمة الدوال المكررة المعروفة
            const knownDuplicates = {
                'showPage': {
                    occurrences: 3,
                    lines: [1615, 29259, 32902],
                    description: 'دالة التنقل بين الصفحات'
                },
                'saveToLocalStorage': {
                    occurrences: 3,
                    lines: [3019, 35320, 35432],
                    description: 'دالة حفظ البيانات'
                },
                'loadFromLocalStorage': {
                    occurrences: 3,
                    lines: [734, 35357, 35432],
                    description: 'دالة تحميل البيانات'
                },
                'addProduct': {
                    occurrences: 2,
                    lines: [5641, 'modules/products-manager.js'],
                    description: 'دالة إضافة منتج'
                },
                'editProduct': {
                    occurrences: 2,
                    lines: [5973, 'modules/products-manager.js'],
                    description: 'دالة تعديل منتج'
                },
                'deleteProduct': {
                    occurrences: 2,
                    lines: [6020, 'modules/products-manager.js'],
                    description: 'دالة حذف منتج'
                }
            };
            
            this.duplicateFunctions = new Map(Object.entries(knownDuplicates));
            this.removalReport.totalDuplicates = Object.keys(knownDuplicates).length;
            
            console.log(`📊 Found ${this.removalReport.totalDuplicates} duplicate function groups`);
            return this.duplicateFunctions;
            
        } catch (error) {
            console.error('❌ Error analyzing duplicates:', error);
            this.removalReport.errors.push({
                step: 'analysis',
                error: error.message
            });
            return new Map();
        }
    }

    // إنشاء الدوال الموحدة
    createConsolidatedFunctions() {
        try {
            console.log('🔧 Creating consolidated functions...');
            
            // دالة التنقل الموحدة
            this.cleanedFunctions.set('showPage', {
                name: 'showPage',
                code: `
                function showPage(pageId) {
                    try {
                        console.log('📄 Navigating to page:', pageId);
                        
                        // استخدام NavigationManager إذا كان متاحاً
                        if (window.NavigationManager && typeof window.NavigationManager.showPage === 'function') {
                            return window.NavigationManager.showPage(pageId);
                        }
                        
                        // التنقل التقليدي كـ fallback
                        document.querySelectorAll('.page').forEach(page => {
                            page.style.display = 'none';
                            page.classList.remove('active');
                        });
                        
                        document.querySelectorAll('.nav-link').forEach(link => {
                            link.classList.remove('active');
                        });
                        
                        const targetPage = document.getElementById(pageId);
                        if (targetPage) {
                            targetPage.style.display = 'block';
                            targetPage.classList.add('active');
                        }
                        
                        const activeLink = document.querySelector(\`[onclick="showPage('\${pageId}')"]\`);
                        if (activeLink) {
                            activeLink.classList.add('active');
                        }
                        
                        // تحديث URL إذا أمكن
                        if (window.history && window.history.pushState) {
                            window.history.pushState({page: pageId}, '', \`#\${pageId}\`);
                        }
                        
                        // تحميل محتوى الصفحة
                        this.loadPageContent(pageId);
                        
                        console.log(\`✅ Successfully navigated to: \${pageId}\`);
                        
                    } catch (error) {
                        console.error('❌ Error in showPage:', error);
                        if (window.Utils) {
                            Utils.showMessage('خطأ في التنقل إلى الصفحة', 'error');
                        }
                    }
                }`,
                description: 'دالة التنقل الموحدة مع دعم NavigationManager'
            });
            
            // دالة الحفظ الموحدة
            this.cleanedFunctions.set('saveToLocalStorage', {
                name: 'saveToLocalStorage',
                code: `
                async function saveToLocalStorage(key, data) {
                    try {
                        console.log(\`💾 Saving data to: \${key}\`);
                        
                        // استخدام UnifiedStorage إذا كان متاحاً
                        if (window.UnifiedStorage && typeof window.UnifiedStorage.save === 'function') {
                            return await window.UnifiedStorage.save(key, data);
                        }
                        
                        // الحفظ التقليدي كـ fallback
                        if (typeof data === 'object') {
                            localStorage.setItem(key, JSON.stringify(data));
                        } else {
                            localStorage.setItem(key, data);
                        }
                        
                        console.log(\`✅ Data saved successfully: \${key}\`);
                        return true;
                        
                    } catch (error) {
                        console.error(\`❌ Error saving data to \${key}:\`, error);
                        if (window.Utils) {
                            Utils.showMessage('خطأ في حفظ البيانات', 'error');
                        }
                        return false;
                    }
                }`,
                description: 'دالة الحفظ الموحدة مع دعم UnifiedStorage'
            });
            
            // دالة التحميل الموحدة
            this.cleanedFunctions.set('loadFromLocalStorage', {
                name: 'loadFromLocalStorage',
                code: `
                async function loadFromLocalStorage(key, defaultValue = null) {
                    try {
                        console.log(\`📂 Loading data from: \${key}\`);
                        
                        // استخدام UnifiedStorage إذا كان متاحاً
                        if (window.UnifiedStorage && typeof window.UnifiedStorage.load === 'function') {
                            return await window.UnifiedStorage.load(key, defaultValue);
                        }
                        
                        // التحميل التقليدي كـ fallback
                        const data = localStorage.getItem(key);
                        if (data === null) {
                            return defaultValue;
                        }
                        
                        try {
                            return JSON.parse(data);
                        } catch (parseError) {
                            console.warn(\`⚠️ Could not parse JSON for \${key}, returning raw data\`);
                            return data;
                        }
                        
                    } catch (error) {
                        console.error(\`❌ Error loading data from \${key}:\`, error);
                        return defaultValue;
                    }
                }`,
                description: 'دالة التحميل الموحدة مع دعم UnifiedStorage'
            });
            
            // دالة تحميل محتوى الصفحة
            this.cleanedFunctions.set('loadPageContent', {
                name: 'loadPageContent',
                code: `
                function loadPageContent(pageId) {
                    try {
                        console.log(\`🔄 Loading content for page: \${pageId}\`);
                        
                        // استخدام المدراء المتخصصين
                        switch (pageId) {
                            case 'dashboard':
                                if (typeof loadDashboardData === 'function') {
                                    loadDashboardData();
                                }
                                break;
                            case 'products':
                                if (window.ProductsManager && typeof window.ProductsManager.loadProductsPage === 'function') {
                                    window.ProductsManager.loadProductsPage();
                                }
                                break;
                            case 'customers':
                                if (window.CustomersManager && typeof window.CustomersManager.loadCustomersPage === 'function') {
                                    window.CustomersManager.loadCustomersPage();
                                }
                                break;
                            case 'sales':
                                if (window.SalesManager && typeof window.SalesManager.loadSalesPage === 'function') {
                                    window.SalesManager.loadSalesPage();
                                }
                                break;
                            case 'reports':
                                if (window.ReportsManager && typeof window.ReportsManager.loadReportsPage === 'function') {
                                    window.ReportsManager.loadReportsPage();
                                }
                                break;
                            default:
                                console.log(\`ℹ️ No specific loader for page: \${pageId}\`);
                        }
                        
                    } catch (error) {
                        console.error(\`❌ Error loading content for \${pageId}:\`, error);
                    }
                }`,
                description: 'دالة تحميل محتوى الصفحات'
            });
            
            this.removalReport.consolidatedFunctions = Array.from(this.cleanedFunctions.keys());
            console.log(`✅ Created ${this.cleanedFunctions.size} consolidated functions`);
            
        } catch (error) {
            console.error('❌ Error creating consolidated functions:', error);
            this.removalReport.errors.push({
                step: 'consolidation',
                error: error.message
            });
        }
    }

    // إنشاء ملف الدوال الموحدة
    generateConsolidatedFile() {
        try {
            console.log('📝 Generating consolidated functions file...');
            
            let fileContent = `/**
 * Black Horse POS - Consolidated Functions
 * الدوال الموحدة بعد إزالة التكرار
 * Generated by: Duplicate Code Remover
 * Date: ${new Date().toISOString()}
 */

// ===== CONSOLIDATED FUNCTIONS =====
// تم دمج وتوحيد الدوال المكررة من app-clean.js

`;

            // إضافة كل دالة موحدة
            for (const [name, func] of this.cleanedFunctions) {
                fileContent += `
// ${func.description}
${func.code}

`;
            }

            fileContent += `
// ===== INITIALIZATION =====
// تهيئة الدوال الموحدة

document.addEventListener('DOMContentLoaded', function() {
    console.log('✅ Consolidated functions loaded and ready');
    
    // التحقق من توفر النظام الجديد
    if (window.ModuleManager && window.ModuleManager.isInitialized) {
        console.log('🔧 Using new module system');
    } else {
        console.log('🔄 Using fallback functions');
    }
});

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.ConsolidatedFunctions = {
        showPage,
        saveToLocalStorage,
        loadFromLocalStorage,
        loadPageContent
    };
}

console.log('✅ Consolidated Functions loaded successfully');
`;

            return fileContent;
            
        } catch (error) {
            console.error('❌ Error generating consolidated file:', error);
            this.removalReport.errors.push({
                step: 'file_generation',
                error: error.message
            });
            return null;
        }
    }

    // إنشاء تقرير الإزالة
    generateRemovalReport() {
        try {
            const report = `# تقرير إزالة الكود المكرر
## تاريخ الإنشاء: ${new Date().toLocaleString('ar-EG')}

---

## 📊 الإحصائيات

- **إجمالي مجموعات الدوال المكررة**: ${this.removalReport.totalDuplicates}
- **الدوال الموحدة المنشأة**: ${this.removalReport.consolidatedFunctions.length}
- **الأخطاء**: ${this.removalReport.errors.length}

---

## 🔍 الدوال المكررة المكتشفة

${Array.from(this.duplicateFunctions.entries()).map(([name, info]) => `
### ${name}
- **الوصف**: ${info.description}
- **عدد التكرارات**: ${info.occurrences}
- **المواقع**: ${Array.isArray(info.lines) ? info.lines.join(', ') : info.lines}
`).join('')}

---

## ✅ الدوال الموحدة المنشأة

${this.removalReport.consolidatedFunctions.map(name => `- ${name}`).join('\n')}

---

## 🚀 التوصيات

1. **استبدال الدوال المكررة** في app-clean.js بالدوال الموحدة
2. **حذف الإصدارات القديمة** من الدوال المكررة
3. **تحديث المراجع** لاستخدام النظام الجديد
4. **اختبار شامل** للتأكد من عمل جميع الوظائف

---

## ⚠️ الأخطاء

${this.removalReport.errors.length > 0 ? 
    this.removalReport.errors.map(error => `- **${error.step}**: ${error.error}`).join('\n') :
    'لا توجد أخطاء'
}
`;

            return report;
            
        } catch (error) {
            console.error('❌ Error generating removal report:', error);
            return 'خطأ في إنشاء التقرير';
        }
    }

    // تنفيذ عملية الإزالة الكاملة
    async execute() {
        try {
            console.log('🚀 Starting duplicate code removal process...');
            
            // 1. تحليل الكود المكرر
            await this.analyzeDuplicates();
            
            // 2. إنشاء الدوال الموحدة
            this.createConsolidatedFunctions();
            
            // 3. إنشاء ملف الدوال الموحدة
            const consolidatedFile = this.generateConsolidatedFile();
            
            // 4. إنشاء تقرير الإزالة
            const report = this.generateRemovalReport();
            
            console.log('✅ Duplicate code removal process completed');
            
            return {
                success: true,
                consolidatedFile,
                report,
                stats: this.removalReport
            };
            
        } catch (error) {
            console.error('❌ Error in duplicate code removal process:', error);
            return {
                success: false,
                error: error.message,
                stats: this.removalReport
            };
        }
    }
}

// إنشاء instance عام
window.DuplicateCodeRemover = new DuplicateCodeRemover();

// تصدير للاستخدام في الوحدات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DuplicateCodeRemover;
}

console.log('✅ Duplicate Code Remover loaded successfully');
