/**
 * نظام معالجة الأخطاء المتقدم
 * Advanced Error Handler for Black Horse POS
 * 
 * يوفر معالجة شاملة للأخطاء مع التسجيل والتقارير والإشعارات
 */

class ErrorHandler {
    constructor() {
        this.errors = [];
        this.errorCounts = {};
        this.maxErrors = 1000; // الحد الأقصى لعدد الأخطاء المحفوظة
        this.reportingEnabled = true;
        this.debugMode = false;
        
        // إعداد معالجة الأخطاء العامة
        this.setupGlobalErrorHandling();
        
        // إعداد مستويات الأخطاء
        this.errorLevels = {
            LOW: 'منخفض',
            MEDIUM: 'متوسط', 
            HIGH: 'عالي',
            CRITICAL: 'حرج'
        };
        
        console.log('🛡️ تم تهيئة نظام معالجة الأخطاء المتقدم');
    }

    /**
     * إعداد معالجة الأخطاء العامة
     */
    setupGlobalErrorHandling() {
        // معالجة أخطاء JavaScript العامة
        window.addEventListener('error', (event) => {
            this.logError('JavaScript Error', event.error, {
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                message: event.message,
                level: this.determineSeverityLevel(event.error)
            });
        });

        // معالجة أخطاء Promise غير المعالجة
        window.addEventListener('unhandledrejection', (event) => {
            this.logError('Unhandled Promise Rejection', event.reason, {
                level: 'HIGH',
                promise: event.promise
            });
        });

        // معالجة أخطاء الموارد (الصور، CSS، JS)
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.logError('Resource Loading Error', new Error(`Failed to load: ${event.target.src || event.target.href}`), {
                    element: event.target.tagName,
                    source: event.target.src || event.target.href,
                    level: 'MEDIUM'
                });
            }
        }, true);
    }

    /**
     * تسجيل خطأ جديد
     */
    logError(type, error, details = {}) {
        const errorInfo = {
            id: this.generateErrorId(),
            type: type,
            message: error?.message || error?.toString() || 'خطأ غير محدد',
            stack: error?.stack,
            timestamp: new Date().toISOString(),
            level: details.level || this.determineSeverityLevel(error),
            details: details,
            userAgent: navigator.userAgent,
            url: window.location.href,
            userId: this.getCurrentUserId(),
            sessionId: this.getSessionId()
        };

        // إضافة الخطأ إلى القائمة
        this.errors.unshift(errorInfo);
        
        // تحديث عداد الأخطاء
        this.errorCounts[type] = (this.errorCounts[type] || 0) + 1;
        
        // الحفاظ على الحد الأقصى للأخطاء
        if (this.errors.length > this.maxErrors) {
            this.errors = this.errors.slice(0, this.maxErrors);
        }
        
        // طباعة الخطأ في وحدة التحكم
        this.consoleLog(errorInfo);
        
        // إرسال إلى أنظمة المراقبة
        this.reportError(errorInfo);
        
        // إشعار المستخدم إذا كان الخطأ حرجاً
        if (errorInfo.level === 'CRITICAL') {
            this.notifyUser(errorInfo);
        }
        
        // حفظ في التخزين المحلي
        this.saveToLocalStorage();
        
        return errorInfo.id;
    }

    /**
     * تحديد مستوى خطورة الخطأ
     */
    determineSeverityLevel(error) {
        if (!error) return 'LOW';
        
        const message = error.message || error.toString();
        const stack = error.stack || '';
        
        // أخطاء حرجة
        if (message.includes('Database') || 
            message.includes('Storage') ||
            message.includes('Network') ||
            stack.includes('UnifiedStorage')) {
            return 'CRITICAL';
        }
        
        // أخطاء عالية
        if (message.includes('TypeError') ||
            message.includes('ReferenceError') ||
            message.includes('SyntaxError')) {
            return 'HIGH';
        }
        
        // أخطاء متوسطة
        if (message.includes('Warning') ||
            message.includes('Deprecated')) {
            return 'MEDIUM';
        }
        
        return 'LOW';
    }

    /**
     * طباعة الخطأ في وحدة التحكم
     */
    consoleLog(errorInfo) {
        const emoji = this.getErrorEmoji(errorInfo.level);
        const timestamp = new Date(errorInfo.timestamp).toLocaleTimeString('ar-SA');
        
        console.group(`${emoji} ${errorInfo.type} - ${timestamp}`);
        console.error(`الرسالة: ${errorInfo.message}`);
        console.error(`المستوى: ${this.errorLevels[errorInfo.level]}`);
        
        if (errorInfo.stack) {
            console.error('Stack Trace:', errorInfo.stack);
        }
        
        if (Object.keys(errorInfo.details).length > 0) {
            console.error('التفاصيل:', errorInfo.details);
        }
        
        console.groupEnd();
    }

    /**
     * الحصول على رمز تعبيري للخطأ
     */
    getErrorEmoji(level) {
        switch (level) {
            case 'CRITICAL': return '🚨';
            case 'HIGH': return '❌';
            case 'MEDIUM': return '⚠️';
            case 'LOW': return 'ℹ️';
            default: return '❓';
        }
    }

    /**
     * إرسال الخطأ إلى أنظمة المراقبة
     */
    reportError(errorInfo) {
        if (!this.reportingEnabled) return;
        
        try {
            // إرسال إلى لوحة مراقبة الأداء
            if (window.PerformanceDashboard && typeof window.PerformanceDashboard.reportError === 'function') {
                window.PerformanceDashboard.reportError(errorInfo);
            }
            
            // إرسال إلى خدمة التحليلات (إذا كانت متاحة)
            if (window.analytics && typeof window.analytics.track === 'function') {
                window.analytics.track('Error Occurred', {
                    errorType: errorInfo.type,
                    errorLevel: errorInfo.level,
                    errorMessage: errorInfo.message
                });
            }
            
        } catch (reportingError) {
            console.warn('فشل في إرسال تقرير الخطأ:', reportingError);
        }
    }

    /**
     * إشعار المستخدم بالخطأ
     */
    notifyUser(errorInfo) {
        try {
            if (window.Utils && typeof window.Utils.showMessage === 'function') {
                const message = this.getUserFriendlyMessage(errorInfo);
                window.Utils.showMessage(message, 'error');
            } else {
                // استخدام alert كبديل
                const message = this.getUserFriendlyMessage(errorInfo);
                alert(message);
            }
        } catch (notificationError) {
            console.error('فشل في إشعار المستخدم:', notificationError);
        }
    }

    /**
     * تحويل الخطأ إلى رسالة مفهومة للمستخدم
     */
    getUserFriendlyMessage(errorInfo) {
        switch (errorInfo.type) {
            case 'Database Error':
                return 'حدث خطأ في قاعدة البيانات. يرجى إعادة تحميل الصفحة.';
            case 'Network Error':
                return 'مشكلة في الاتصال بالإنترنت. يرجى التحقق من الاتصال.';
            case 'JavaScript Error':
                return 'حدث خطأ في التطبيق. سيتم إعادة تشغيل النظام تلقائياً.';
            case 'Resource Loading Error':
                return 'فشل في تحميل بعض الموارد. قد تتأثر بعض الوظائف.';
            default:
                return 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
        }
    }

    /**
     * إنشاء معرف فريد للخطأ
     */
    generateErrorId() {
        return 'err_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * الحصول على معرف المستخدم الحالي
     */
    getCurrentUserId() {
        try {
            // محاولة الحصول على معرف المستخدم من النظام
            if (window.currentUser && window.currentUser.id) {
                return window.currentUser.id;
            }
            
            // محاولة الحصول من localStorage
            const userData = localStorage.getItem('currentUser');
            if (userData) {
                const user = JSON.parse(userData);
                return user.id || 'unknown';
            }
            
            return 'guest';
        } catch (error) {
            return 'unknown';
        }
    }

    /**
     * الحصول على معرف الجلسة
     */
    getSessionId() {
        try {
            let sessionId = sessionStorage.getItem('sessionId');
            if (!sessionId) {
                sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                sessionStorage.setItem('sessionId', sessionId);
            }
            return sessionId;
        } catch (error) {
            return 'unknown_session';
        }
    }

    /**
     * حفظ الأخطاء في التخزين المحلي
     */
    saveToLocalStorage() {
        try {
            const errorData = {
                errors: this.errors.slice(0, 100), // حفظ آخر 100 خطأ فقط
                errorCounts: this.errorCounts,
                lastUpdate: new Date().toISOString()
            };
            
            localStorage.setItem('errorLog', JSON.stringify(errorData));
        } catch (error) {
            console.warn('فشل في حفظ سجل الأخطاء:', error);
        }
    }

    /**
     * تحميل الأخطاء من التخزين المحلي
     */
    loadFromLocalStorage() {
        try {
            const errorData = localStorage.getItem('errorLog');
            if (errorData) {
                const parsed = JSON.parse(errorData);
                this.errors = parsed.errors || [];
                this.errorCounts = parsed.errorCounts || {};
                console.log(`📂 تم تحميل ${this.errors.length} خطأ من التخزين المحلي`);
            }
        } catch (error) {
            console.warn('فشل في تحميل سجل الأخطاء:', error);
        }
    }

    /**
     * الحصول على تقرير الأخطاء
     */
    getErrorReport() {
        const now = new Date();
        const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        
        const recentErrors = this.errors.filter(error => 
            new Date(error.timestamp) > last24Hours
        );
        
        return {
            summary: {
                totalErrors: this.errors.length,
                recentErrors: recentErrors.length,
                errorsByLevel: this.groupErrorsByLevel(),
                errorsByType: this.errorCounts,
                mostCommonError: this.getMostCommonError()
            },
            recentErrors: recentErrors.slice(0, 20),
            criticalErrors: this.errors.filter(error => error.level === 'CRITICAL').slice(0, 10),
            timestamp: now.toISOString()
        };
    }

    /**
     * تجميع الأخطاء حسب المستوى
     */
    groupErrorsByLevel() {
        const grouped = {};
        this.errors.forEach(error => {
            grouped[error.level] = (grouped[error.level] || 0) + 1;
        });
        return grouped;
    }

    /**
     * الحصول على أكثر الأخطاء شيوعاً
     */
    getMostCommonError() {
        if (Object.keys(this.errorCounts).length === 0) return null;
        
        return Object.entries(this.errorCounts)
            .sort(([,a], [,b]) => b - a)[0];
    }

    /**
     * مسح سجل الأخطاء
     */
    clearErrors() {
        this.errors = [];
        this.errorCounts = {};
        localStorage.removeItem('errorLog');
        console.log('🧹 تم مسح سجل الأخطاء');
    }

    /**
     * تصدير سجل الأخطاء
     */
    exportErrors(format = 'json') {
        const report = this.getErrorReport();
        
        switch (format.toLowerCase()) {
            case 'json':
                return JSON.stringify(report, null, 2);
                
            case 'csv':
                return this.convertToCSV(report.recentErrors);
                
            case 'txt':
                return this.convertToText(report);
                
            default:
                return JSON.stringify(report, null, 2);
        }
    }

    /**
     * تحويل إلى CSV
     */
    convertToCSV(errors) {
        if (errors.length === 0) return 'لا توجد أخطاء للتصدير';
        
        const headers = ['التوقيت', 'النوع', 'المستوى', 'الرسالة', 'المصدر'];
        const rows = errors.map(error => [
            error.timestamp,
            error.type,
            error.level,
            error.message.replace(/"/g, '""'),
            error.url
        ]);
        
        return [headers, ...rows]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');
    }

    /**
     * تحويل إلى نص
     */
    convertToText(report) {
        let text = 'تقرير الأخطاء - Black Horse POS\n';
        text += '='.repeat(50) + '\n\n';
        
        text += `إجمالي الأخطاء: ${report.summary.totalErrors}\n`;
        text += `الأخطاء الحديثة (24 ساعة): ${report.summary.recentErrors}\n\n`;
        
        text += 'الأخطاء حسب المستوى:\n';
        Object.entries(report.summary.errorsByLevel).forEach(([level, count]) => {
            text += `  ${this.errorLevels[level]}: ${count}\n`;
        });
        
        text += '\nالأخطاء الحديثة:\n';
        text += '-'.repeat(30) + '\n';
        
        report.recentErrors.forEach(error => {
            text += `[${error.timestamp}] ${error.type}: ${error.message}\n`;
        });
        
        return text;
    }

    /**
     * تفعيل/إلغاء وضع التصحيح
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
        console.log(`🔧 وضع التصحيح: ${enabled ? 'مفعل' : 'معطل'}`);
    }

    /**
     * تفعيل/إلغاء التقارير
     */
    setReporting(enabled) {
        this.reportingEnabled = enabled;
        console.log(`📊 التقارير: ${enabled ? 'مفعلة' : 'معطلة'}`);
    }

    /**
     * إضافة معالج خطأ مخصص
     */
    addCustomHandler(errorType, handler) {
        if (typeof handler !== 'function') {
            throw new Error('المعالج يجب أن يكون دالة');
        }
        
        this.customHandlers = this.customHandlers || {};
        this.customHandlers[errorType] = handler;
        
        console.log(`✅ تم إضافة معالج مخصص لنوع الخطأ: ${errorType}`);
    }

    /**
     * إزالة معالج خطأ مخصص
     */
    removeCustomHandler(errorType) {
        if (this.customHandlers && this.customHandlers[errorType]) {
            delete this.customHandlers[errorType];
            console.log(`🗑️ تم إزالة المعالج المخصص لنوع الخطأ: ${errorType}`);
        }
    }

    /**
     * الحصول على إحصائيات الأداء
     */
    getPerformanceStats() {
        const now = Date.now();
        const oneHourAgo = now - (60 * 60 * 1000);
        
        const recentErrors = this.errors.filter(error => 
            new Date(error.timestamp).getTime() > oneHourAgo
        );
        
        return {
            errorsPerHour: recentErrors.length,
            averageErrorsPerDay: Math.round(this.errors.length / Math.max(1, this.getDaysSinceFirstError())),
            errorRate: this.calculateErrorRate(),
            systemStability: this.calculateSystemStability()
        };
    }

    /**
     * حساب عدد الأيام منذ أول خطأ
     */
    getDaysSinceFirstError() {
        if (this.errors.length === 0) return 1;
        
        const firstError = this.errors[this.errors.length - 1];
        const firstErrorTime = new Date(firstError.timestamp).getTime();
        const now = Date.now();
        
        return Math.max(1, Math.ceil((now - firstErrorTime) / (24 * 60 * 60 * 1000)));
    }

    /**
     * حساب معدل الأخطاء
     */
    calculateErrorRate() {
        const stats = this.getPerformanceStats();
        if (stats.errorsPerHour === 0) return 'ممتاز';
        if (stats.errorsPerHour < 5) return 'جيد';
        if (stats.errorsPerHour < 20) return 'متوسط';
        return 'يحتاج تحسين';
    }

    /**
     * حساب استقرار النظام
     */
    calculateSystemStability() {
        const criticalErrors = this.errors.filter(error => error.level === 'CRITICAL').length;
        const totalErrors = this.errors.length;
        
        if (totalErrors === 0) return 100;
        
        const stabilityScore = Math.max(0, 100 - (criticalErrors / totalErrors * 100) - (totalErrors / 100));
        return Math.round(stabilityScore);
    }
}

// تصدير الكلاس للاستخدام العام
if (typeof window !== 'undefined') {
    window.ErrorHandler = ErrorHandler;
}

// تصدير للاستخدام في Node.js إذا لزم الأمر
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ErrorHandler;
}
