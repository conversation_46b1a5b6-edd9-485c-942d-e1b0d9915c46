<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإدارة المالية المتقدمة - Black Horse POS</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .page-header h1 {
            color: #2c3e50;
            font-weight: 700;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }

        .page-header p {
            color: #6c757d;
            font-size: 1.1rem;
            margin: 0;
        }

        .financial-dashboard {
            margin-bottom: 30px;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .dashboard-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
            pointer-events: none;
        }

        .dashboard-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.9;
        }

        .dashboard-value {
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .dashboard-label {
            font-size: 1rem;
            opacity: 0.9;
        }

        .dashboard-change {
            font-size: 0.9rem;
            margin-top: 10px;
            padding: 5px 10px;
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.2);
        }

        .change-positive {
            color: #28a745;
        }

        .change-negative {
            color: #dc3545;
        }

        .view-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .view-tab {
            padding: 12px 24px;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            text-decoration: none;
            color: #495057;
        }

        .view-tab:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            color: #495057;
            text-decoration: none;
        }

        .view-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }

        .filters-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
        }

        .quick-actions {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .quick-action-btn {
            padding: 12px 24px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
            color: white;
            text-decoration: none;
        }

        .content-area {
            min-height: 400px;
        }

        .financial-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .financial-table .table {
            margin: 0;
        }

        .financial-table .table thead {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .financial-table .table thead th {
            border: none;
            padding: 15px;
            font-weight: 600;
        }

        .financial-table .table tbody td {
            padding: 12px 15px;
            border-color: #e9ecef;
        }

        .financial-table .table tbody tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-overdue {
            background: #f8d7da;
            color: #721c24;
        }

        .status-paid {
            background: #d1ecf1;
            color: #0c5460;
        }

        .amount-positive {
            color: #28a745;
            font-weight: 600;
        }

        .amount-negative {
            color: #dc3545;
            font-weight: 600;
        }

        .amount-neutral {
            color: #6c757d;
            font-weight: 600;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h4 {
            margin-bottom: 15px;
            color: #495057;
        }

        .empty-state p {
            margin-bottom: 25px;
            font-size: 1.1rem;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
            border: none;
        }

        .card-header h5 {
            margin: 0;
            font-weight: 600;
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 1px solid #e9ecef;
            padding: 12px 15px;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .modal-content {
            border-radius: 20px;
            border: none;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .modal-header {
            border-radius: 20px 20px 0 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }

        .modal-footer {
            border: none;
            padding: 20px 30px;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 20px;
                margin: 10px;
            }
            
            .page-header h1 {
                font-size: 2rem;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .view-tabs {
                justify-content: center;
            }
            
            .quick-actions {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <h1><i class="fas fa-chart-line"></i> الإدارة المالية المتقدمة</h1>
            <p>نظام شامل للإدارة المالية والمحاسبية مع التقارير المتقدمة</p>
        </div>

        <!-- لوحة المعلومات المالية -->
        <div class="financial-dashboard">
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="dashboard-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <div class="dashboard-value" id="totalAssets">0 ر.س</div>
                    <div class="dashboard-label">إجمالي الأصول</div>
                    <div class="dashboard-change" id="assetsChange">+0%</div>
                </div>
                
                <div class="dashboard-card">
                    <div class="dashboard-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="dashboard-value" id="totalLiabilities">0 ر.س</div>
                    <div class="dashboard-label">إجمالي الخصوم</div>
                    <div class="dashboard-change" id="liabilitiesChange">+0%</div>
                </div>
                
                <div class="dashboard-card">
                    <div class="dashboard-icon">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <div class="dashboard-value" id="totalEquity">0 ر.س</div>
                    <div class="dashboard-label">حقوق الملكية</div>
                    <div class="dashboard-change" id="equityChange">+0%</div>
                </div>
                
                <div class="dashboard-card">
                    <div class="dashboard-icon">
                        <i class="fas fa-arrow-up"></i>
                    </div>
                    <div class="dashboard-value" id="totalRevenue">0 ر.س</div>
                    <div class="dashboard-label">إجمالي الإيرادات</div>
                    <div class="dashboard-change" id="revenueChange">+0%</div>
                </div>
                
                <div class="dashboard-card">
                    <div class="dashboard-icon">
                        <i class="fas fa-arrow-down"></i>
                    </div>
                    <div class="dashboard-value" id="totalExpenses">0 ر.س</div>
                    <div class="dashboard-label">إجمالي المصروفات</div>
                    <div class="dashboard-change" id="expensesChange">+0%</div>
                </div>
                
                <div class="dashboard-card">
                    <div class="dashboard-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <div class="dashboard-value" id="netIncome">0 ر.س</div>
                    <div class="dashboard-label">صافي الربح</div>
                    <div class="dashboard-change" id="profitChange">+0%</div>
                </div>
            </div>
        </div>

        <!-- تبويبات العرض -->
        <div class="view-tabs">
            <a href="#" class="view-tab active" id="dashboardTab" onclick="setView('dashboard')">
                <i class="fas fa-tachometer-alt"></i> لوحة المعلومات
            </a>
            <a href="#" class="view-tab" id="accountsTab" onclick="setView('accounts')">
                <i class="fas fa-list"></i> دليل الحسابات
            </a>
            <a href="#" class="view-tab" id="transactionsTab" onclick="setView('transactions')">
                <i class="fas fa-exchange-alt"></i> المعاملات
            </a>
            <a href="#" class="view-tab" id="invoicesTab" onclick="setView('invoices')">
                <i class="fas fa-file-invoice"></i> الفواتير
            </a>
            <a href="#" class="view-tab" id="budgetsTab" onclick="setView('budgets')">
                <i class="fas fa-calculator"></i> الميزانيات
            </a>
            <a href="#" class="view-tab" id="reportsTab" onclick="setView('reports')">
                <i class="fas fa-chart-bar"></i> التقارير
            </a>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="content-area">
            <!-- عرض لوحة المعلومات -->
            <div id="dashboardView">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-pie"></i> توزيع الأصول</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="assetsDistributionChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-line"></i> الإيرادات والمصروفات</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="revenueExpenseChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-clock"></i> المعاملات الأخيرة</h5>
                            </div>
                            <div class="card-body">
                                <div id="recentTransactions">
                                    <!-- سيتم ملء المعاملات الأخيرة هنا -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- عرض دليل الحسابات -->
            <div id="accountsView" style="display: none;">
                <div class="quick-actions">
                    <button class="quick-action-btn" onclick="showCreateAccountModal()">
                        <i class="fas fa-plus"></i> حساب جديد
                    </button>
                    <button class="quick-action-btn" onclick="showImportAccountsModal()">
                        <i class="fas fa-upload"></i> استيراد
                    </button>
                    <button class="quick-action-btn" onclick="exportAccounts()">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                    <button class="quick-action-btn" onclick="showAccountsSettings()">
                        <i class="fas fa-cog"></i> الإعدادات
                    </button>
                </div>

                <div class="filters-section">
                    <div class="row">
                        <div class="col-md-3">
                            <input type="text" class="form-control" id="accountSearchInput" placeholder="البحث في الحسابات..." onkeyup="searchAccounts()">
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" id="accountTypeFilter" onchange="applyAccountFilters()">
                                <option value="">جميع الأنواع</option>
                                <option value="asset">الأصول</option>
                                <option value="liability">الخصوم</option>
                                <option value="equity">حقوق الملكية</option>
                                <option value="revenue">الإيرادات</option>
                                <option value="expense">المصروفات</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" id="accountStatusFilter" onchange="applyAccountFilters()">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                        <div class="col-md-5">
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-primary" onclick="applyAccountFilters()">
                                    <i class="fas fa-filter"></i> تطبيق
                                </button>
                                <button class="btn btn-outline-secondary" onclick="clearAccountFilters()">
                                    <i class="fas fa-times"></i> مسح
                                </button>
                                <button class="btn btn-outline-success" onclick="refreshAccounts()">
                                    <i class="fas fa-sync"></i> تحديث
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="financial-table">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الكود</th>
                                <th>اسم الحساب</th>
                                <th>النوع</th>
                                <th>الرصيد الحالي</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="accountsTableBody">
                            <!-- سيتم ملء الحسابات هنا -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- عرض المعاملات -->
            <div id="transactionsView" style="display: none;">
                <div class="quick-actions">
                    <button class="quick-action-btn" onclick="showCreateTransactionModal()">
                        <i class="fas fa-plus"></i> معاملة جديدة
                    </button>
                    <button class="quick-action-btn" onclick="showJournalEntry()">
                        <i class="fas fa-book"></i> قيد يومية
                    </button>
                    <button class="quick-action-btn" onclick="showBulkTransactions()">
                        <i class="fas fa-list"></i> معاملات مجمعة
                    </button>
                    <button class="quick-action-btn" onclick="generateTransactionReport()">
                        <i class="fas fa-file-alt"></i> تقرير المعاملات
                    </button>
                </div>

                <div class="filters-section">
                    <div class="row">
                        <div class="col-md-2">
                            <input type="text" class="form-control" id="transactionSearchInput" placeholder="البحث..." onkeyup="searchTransactions()">
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" id="transactionTypeFilter" onchange="applyTransactionFilters()">
                                <option value="">جميع الأنواع</option>
                                <option value="journal">قيد يومية</option>
                                <option value="payment">دفع</option>
                                <option value="receipt">استلام</option>
                                <option value="transfer">تحويل</option>
                                <option value="adjustment">تسوية</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" id="transactionStatusFilter" onchange="applyTransactionFilters()">
                                <option value="">جميع الحالات</option>
                                <option value="pending">معلق</option>
                                <option value="approved">موافق عليه</option>
                                <option value="posted">مرحل</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <input type="date" class="form-control" id="transactionDateFrom" onchange="applyTransactionFilters()">
                        </div>
                        <div class="col-md-2">
                            <input type="date" class="form-control" id="transactionDateTo" onchange="applyTransactionFilters()">
                        </div>
                        <div class="col-md-2">
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-primary btn-sm" onclick="applyTransactionFilters()">
                                    <i class="fas fa-filter"></i>
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="clearTransactionFilters()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="financial-table">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم المعاملة</th>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>الوصف</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="transactionsTableBody">
                            <!-- سيتم ملء المعاملات هنا -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- عرض الفواتير -->
            <div id="invoicesView" style="display: none;">
                <div class="quick-actions">
                    <button class="quick-action-btn" onclick="showCreateInvoiceModal()">
                        <i class="fas fa-plus"></i> فاتورة جديدة
                    </button>
                    <button class="quick-action-btn" onclick="showInvoiceTemplates()">
                        <i class="fas fa-file-invoice"></i> قوالب الفواتير
                    </button>
                    <button class="quick-action-btn" onclick="showRecurringInvoices()">
                        <i class="fas fa-repeat"></i> فواتير متكررة
                    </button>
                    <button class="quick-action-btn" onclick="generateInvoiceReport()">
                        <i class="fas fa-chart-bar"></i> تقرير الفواتير
                    </button>
                </div>

                <div id="invoicesList">
                    <!-- سيتم ملء الفواتير هنا -->
                </div>
            </div>

            <!-- عرض الميزانيات -->
            <div id="budgetsView" style="display: none;">
                <div class="quick-actions">
                    <button class="quick-action-btn" onclick="showCreateBudgetModal()">
                        <i class="fas fa-plus"></i> ميزانية جديدة
                    </button>
                    <button class="quick-action-btn" onclick="showBudgetTemplates()">
                        <i class="fas fa-copy"></i> قوالب الميزانيات
                    </button>
                    <button class="quick-action-btn" onclick="compareBudgets()">
                        <i class="fas fa-balance-scale"></i> مقارنة الميزانيات
                    </button>
                    <button class="quick-action-btn" onclick="generateBudgetReport()">
                        <i class="fas fa-chart-line"></i> تقرير الميزانية
                    </button>
                </div>

                <div id="budgetsList">
                    <!-- سيتم ملء الميزانيات هنا -->
                </div>
            </div>

            <!-- عرض التقارير -->
            <div id="reportsView" style="display: none;">
                <div class="row">
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-balance-scale"></i> الميزانية العمومية</h5>
                            </div>
                            <div class="card-body text-center">
                                <p>عرض الأصول والخصوم وحقوق الملكية</p>
                                <button class="btn btn-primary" onclick="generateBalanceSheet()">
                                    <i class="fas fa-file-pdf"></i> إنشاء التقرير
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-line"></i> قائمة الدخل</h5>
                            </div>
                            <div class="card-body text-center">
                                <p>عرض الإيرادات والمصروفات وصافي الربح</p>
                                <button class="btn btn-primary" onclick="generateIncomeStatement()">
                                    <i class="fas fa-file-pdf"></i> إنشاء التقرير
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-water"></i> التدفقات النقدية</h5>
                            </div>
                            <div class="card-body text-center">
                                <p>عرض التدفقات النقدية الداخلة والخارجة</p>
                                <button class="btn btn-primary" onclick="generateCashFlow()">
                                    <i class="fas fa-file-pdf"></i> إنشاء التقرير
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-list-alt"></i> ميزان المراجعة</h5>
                            </div>
                            <div class="card-body text-center">
                                <p>عرض أرصدة جميع الحسابات</p>
                                <button class="btn btn-primary" onclick="generateTrialBalance()">
                                    <i class="fas fa-file-pdf"></i> إنشاء التقرير
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.min.js"></script>
    <script src="../../core/database.js"></script>
    <script src="../../core/utils.js"></script>
    <script src="advanced-finance.js"></script>

    <script>
        // متغيرات عامة
        let advancedFinanceManager;
        let currentView = 'dashboard';
        let currentFilters = {};
        let charts = {};

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeAdvancedFinance();
            loadData();
            updateDashboard();
            setupEventListeners();
        });

        // تهيئة نظام الإدارة المالية المتقدم
        function initializeAdvancedFinance() {
            advancedFinanceManager = new AdvancedFinanceManager();
            console.log('✅ تم تهيئة واجهة الإدارة المالية المتقدمة');
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // تحديث لوحة المعلومات كل دقيقة
            setInterval(updateDashboard, 60000);
        }

        // تحميل البيانات
        function loadData() {
            switch(currentView) {
                case 'dashboard':
                    loadDashboard();
                    break;
                case 'accounts':
                    loadAccounts();
                    break;
                case 'transactions':
                    loadTransactions();
                    break;
                case 'invoices':
                    loadInvoices();
                    break;
                case 'budgets':
                    loadBudgets();
                    break;
                case 'reports':
                    loadReports();
                    break;
            }
        }

        // تحديث لوحة المعلومات
        function updateDashboard() {
            const summary = advancedFinanceManager.getFinancialSummary();

            // تحديث البطاقات
            document.getElementById('totalAssets').textContent = formatCurrency(summary.balances.totalAssets);
            document.getElementById('totalLiabilities').textContent = formatCurrency(summary.balances.totalLiabilities);
            document.getElementById('totalEquity').textContent = formatCurrency(summary.balances.totalEquity);
            document.getElementById('totalRevenue').textContent = formatCurrency(summary.balances.totalRevenue);
            document.getElementById('totalExpenses').textContent = formatCurrency(summary.balances.totalExpenses);
            document.getElementById('netIncome').textContent = formatCurrency(summary.balances.netIncome);

            // تحديث نسب التغيير (مؤقتاً)
            document.getElementById('assetsChange').textContent = '****%';
            document.getElementById('liabilitiesChange').textContent = '****%';
            document.getElementById('equityChange').textContent = '****%';
            document.getElementById('revenueChange').textContent = '+12.5%';
            document.getElementById('expensesChange').textContent = '****%';
            document.getElementById('profitChange').textContent = '+15.2%';
        }

        // تحميل لوحة المعلومات
        function loadDashboard() {
            updateDashboard();
            createChartsIfNeeded();
            loadRecentTransactions();
        }

        // إنشاء الرسوم البيانية
        function createChartsIfNeeded() {
            // رسم بياني لتوزيع الأصول
            const ctx1 = document.getElementById('assetsDistributionChart');
            if (ctx1 && !charts.assetsDistribution) {
                const summary = advancedFinanceManager.getFinancialSummary();
                charts.assetsDistribution = new Chart(ctx1, {
                    type: 'doughnut',
                    data: {
                        labels: ['الأصول المتداولة', 'الأصول الثابتة', 'الاستثمارات'],
                        datasets: [{
                            data: [
                                summary.balances.totalAssets * 0.6,
                                summary.balances.totalAssets * 0.3,
                                summary.balances.totalAssets * 0.1
                            ],
                            backgroundColor: ['#28a745', '#17a2b8', '#ffc107']
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }

            // رسم بياني للإيرادات والمصروفات
            const ctx2 = document.getElementById('revenueExpenseChart');
            if (ctx2 && !charts.revenueExpense) {
                const summary = advancedFinanceManager.getFinancialSummary();
                charts.revenueExpense = new Chart(ctx2, {
                    type: 'bar',
                    data: {
                        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                        datasets: [{
                            label: 'الإيرادات',
                            data: [65000, 59000, 80000, 81000, 56000, 55000],
                            backgroundColor: '#28a745'
                        }, {
                            label: 'المصروفات',
                            data: [28000, 48000, 40000, 19000, 86000, 27000],
                            backgroundColor: '#dc3545'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
        }

        // تحميل المعاملات الأخيرة
        function loadRecentTransactions() {
            const transactions = Array.from(advancedFinanceManager.transactions.values())
                .sort((a, b) => new Date(b.date) - new Date(a.date))
                .slice(0, 5);

            const container = document.getElementById('recentTransactions');

            if (transactions.length === 0) {
                container.innerHTML = '<p class="text-center text-muted">لا توجد معاملات حديثة</p>';
                return;
            }

            container.innerHTML = `
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>رقم المعاملة</th>
                                <th>التاريخ</th>
                                <th>الوصف</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${transactions.map(transaction => `
                                <tr>
                                    <td>${transaction.number}</td>
                                    <td>${formatDate(transaction.date)}</td>
                                    <td>${transaction.description}</td>
                                    <td class="${transaction.amount >= 0 ? 'amount-positive' : 'amount-negative'}">
                                        ${formatCurrency(Math.abs(transaction.amount))}
                                    </td>
                                    <td>
                                        <span class="status-badge status-${transaction.status}">
                                            ${getStatusLabel(transaction.status)}
                                        </span>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }

        // تعيين العرض
        function setView(view) {
            currentView = view;

            // إخفاء جميع العروض
            document.querySelectorAll('[id$="View"]').forEach(el => {
                el.style.display = 'none';
            });

            // إزالة الفئة النشطة من جميع التبويبات
            document.querySelectorAll('.view-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // عرض العرض المحدد
            document.getElementById(view + 'View').style.display = 'block';
            document.getElementById(view + 'Tab').classList.add('active');

            // تحميل البيانات
            loadData();
        }

        // دوال التنسيق
        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: 'SAR',
                minimumFractionDigits: 2
            }).format(amount);
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('ar-SA');
        }

        function getStatusLabel(status) {
            const labels = {
                'pending': 'معلق',
                'approved': 'موافق عليه',
                'posted': 'مرحل',
                'cancelled': 'ملغي',
                'active': 'نشط',
                'inactive': 'غير نشط',
                'draft': 'مسودة',
                'sent': 'مرسل',
                'paid': 'مدفوع',
                'overdue': 'متأخر'
            };
            return labels[status] || status;
        }

        // دوال العروض الأخرى (ستكون بسيطة للآن)
        function loadAccounts() {
            document.getElementById('accountsTableBody').innerHTML = '<tr><td colspan="6" class="text-center">قريباً...</td></tr>';
        }

        function loadTransactions() {
            document.getElementById('transactionsTableBody').innerHTML = '<tr><td colspan="7" class="text-center">قريباً...</td></tr>';
        }

        function loadInvoices() {
            document.getElementById('invoicesList').innerHTML = '<p class="text-center">قريباً...</p>';
        }

        function loadBudgets() {
            document.getElementById('budgetsList').innerHTML = '<p class="text-center">قريباً...</p>';
        }

        function loadReports() {
            // التقارير جاهزة للاستخدام
        }

        // دوال الإجراءات السريعة (ستكون بسيطة للآن)
        function showCreateAccountModal() {
            showNotification('ميزة إنشاء الحسابات قيد التطوير', 'info');
        }

        function showCreateTransactionModal() {
            showNotification('ميزة إنشاء المعاملات قيد التطوير', 'info');
        }

        function showCreateInvoiceModal() {
            showNotification('ميزة إنشاء الفواتير قيد التطوير', 'info');
        }

        function showCreateBudgetModal() {
            showNotification('ميزة إنشاء الميزانيات قيد التطوير', 'info');
        }

        // دوال التقارير
        function generateBalanceSheet() {
            const report = advancedFinanceManager.generateFinancialReport('balance_sheet');
            showNotification('تم إنشاء تقرير الميزانية العمومية', 'success');
        }

        function generateIncomeStatement() {
            const report = advancedFinanceManager.generateFinancialReport('income_statement');
            showNotification('تم إنشاء تقرير قائمة الدخل', 'success');
        }

        function generateCashFlow() {
            const report = advancedFinanceManager.generateFinancialReport('cash_flow');
            showNotification('تم إنشاء تقرير التدفقات النقدية', 'success');
        }

        function generateTrialBalance() {
            const report = advancedFinanceManager.generateFinancialReport('trial_balance');
            showNotification('تم إنشاء تقرير ميزان المراجعة', 'success');
        }

        // إظهار الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }

        // دوال البحث والفلترة (ستكون بسيطة للآن)
        function searchAccounts() {
            // قريباً
        }

        function applyAccountFilters() {
            // قريباً
        }

        function clearAccountFilters() {
            // قريباً
        }

        function refreshAccounts() {
            loadAccounts();
        }

        function searchTransactions() {
            // قريباً
        }

        function applyTransactionFilters() {
            // قريباً
        }

        function clearTransactionFilters() {
            // قريباً
        }
    </script>
</body>
</html>
