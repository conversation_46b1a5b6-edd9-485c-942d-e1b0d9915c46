<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإدارة المالية المتقدمة - Black Horse POS</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .page-header h1 {
            color: #2c3e50;
            font-weight: 700;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }

        .page-header p {
            color: #6c757d;
            font-size: 1.1rem;
            margin: 0;
        }

        .financial-dashboard {
            margin-bottom: 30px;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .dashboard-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
            pointer-events: none;
        }

        .dashboard-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.9;
        }

        .dashboard-value {
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .dashboard-label {
            font-size: 1rem;
            opacity: 0.9;
        }

        .dashboard-change {
            font-size: 0.9rem;
            margin-top: 10px;
            padding: 5px 10px;
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.2);
        }

        .change-positive {
            color: #28a745;
        }

        .change-negative {
            color: #dc3545;
        }

        .view-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .view-tab {
            padding: 12px 24px;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            text-decoration: none;
            color: #495057;
        }

        .view-tab:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            color: #495057;
            text-decoration: none;
        }

        .view-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }

        .filters-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
        }

        .quick-actions {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .quick-action-btn {
            padding: 12px 24px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
            color: white;
            text-decoration: none;
        }

        .content-area {
            min-height: 400px;
        }

        .financial-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .financial-table .table {
            margin: 0;
        }

        .financial-table .table thead {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .financial-table .table thead th {
            border: none;
            padding: 15px;
            font-weight: 600;
        }

        .financial-table .table tbody td {
            padding: 12px 15px;
            border-color: #e9ecef;
        }

        .financial-table .table tbody tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-overdue {
            background: #f8d7da;
            color: #721c24;
        }

        .status-paid {
            background: #d1ecf1;
            color: #0c5460;
        }

        .amount-positive {
            color: #28a745;
            font-weight: 600;
        }

        .amount-negative {
            color: #dc3545;
            font-weight: 600;
        }

        .amount-neutral {
            color: #6c757d;
            font-weight: 600;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h4 {
            margin-bottom: 15px;
            color: #495057;
        }

        .empty-state p {
            margin-bottom: 25px;
            font-size: 1.1rem;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
            border: none;
        }

        .card-header h5 {
            margin: 0;
            font-weight: 600;
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 1px solid #e9ecef;
            padding: 12px 15px;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .modal-content {
            border-radius: 20px;
            border: none;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .modal-header {
            border-radius: 20px 20px 0 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }

        .modal-footer {
            border: none;
            padding: 20px 30px;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 20px;
                margin: 10px;
            }
            
            .page-header h1 {
                font-size: 2rem;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .view-tabs {
                justify-content: center;
            }
            
            .quick-actions {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <h1><i class="fas fa-chart-line"></i> الإدارة المالية المتقدمة</h1>
            <p>نظام شامل للإدارة المالية والمحاسبية مع التقارير المتقدمة</p>
        </div>

        <!-- لوحة المعلومات المالية -->
        <div class="financial-dashboard">
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="dashboard-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <div class="dashboard-value" id="totalAssets">0 ر.س</div>
                    <div class="dashboard-label">إجمالي الأصول</div>
                    <div class="dashboard-change" id="assetsChange">+0%</div>
                </div>
                
                <div class="dashboard-card">
                    <div class="dashboard-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="dashboard-value" id="totalLiabilities">0 ر.س</div>
                    <div class="dashboard-label">إجمالي الخصوم</div>
                    <div class="dashboard-change" id="liabilitiesChange">+0%</div>
                </div>
                
                <div class="dashboard-card">
                    <div class="dashboard-icon">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <div class="dashboard-value" id="totalEquity">0 ر.س</div>
                    <div class="dashboard-label">حقوق الملكية</div>
                    <div class="dashboard-change" id="equityChange">+0%</div>
                </div>
                
                <div class="dashboard-card">
                    <div class="dashboard-icon">
                        <i class="fas fa-arrow-up"></i>
                    </div>
                    <div class="dashboard-value" id="totalRevenue">0 ر.س</div>
                    <div class="dashboard-label">إجمالي الإيرادات</div>
                    <div class="dashboard-change" id="revenueChange">+0%</div>
                </div>
                
                <div class="dashboard-card">
                    <div class="dashboard-icon">
                        <i class="fas fa-arrow-down"></i>
                    </div>
                    <div class="dashboard-value" id="totalExpenses">0 ر.س</div>
                    <div class="dashboard-label">إجمالي المصروفات</div>
                    <div class="dashboard-change" id="expensesChange">+0%</div>
                </div>
                
                <div class="dashboard-card">
                    <div class="dashboard-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <div class="dashboard-value" id="netIncome">0 ر.س</div>
                    <div class="dashboard-label">صافي الربح</div>
                    <div class="dashboard-change" id="profitChange">+0%</div>
                </div>
            </div>
        </div>

        <!-- تبويبات العرض -->
        <div class="view-tabs">
            <a href="#" class="view-tab active" id="dashboardTab" onclick="setView('dashboard')">
                <i class="fas fa-tachometer-alt"></i> لوحة المعلومات
            </a>
            <a href="#" class="view-tab" id="accountsTab" onclick="setView('accounts')">
                <i class="fas fa-list"></i> دليل الحسابات
            </a>
            <a href="#" class="view-tab" id="transactionsTab" onclick="setView('transactions')">
                <i class="fas fa-exchange-alt"></i> المعاملات
            </a>
            <a href="#" class="view-tab" id="invoicesTab" onclick="setView('invoices')">
                <i class="fas fa-file-invoice"></i> الفواتير
            </a>
            <a href="#" class="view-tab" id="budgetsTab" onclick="setView('budgets')">
                <i class="fas fa-calculator"></i> الميزانيات
            </a>
            <a href="#" class="view-tab" id="reportsTab" onclick="setView('reports')">
                <i class="fas fa-chart-bar"></i> التقارير
            </a>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="content-area">
            <!-- عرض لوحة المعلومات -->
            <div id="dashboardView">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-pie"></i> توزيع الأصول</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="assetsDistributionChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-line"></i> الإيرادات والمصروفات</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="revenueExpenseChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-clock"></i> المعاملات الأخيرة</h5>
                            </div>
                            <div class="card-body">
                                <div id="recentTransactions">
                                    <!-- سيتم ملء المعاملات الأخيرة هنا -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- عرض دليل الحسابات -->
            <div id="accountsView" style="display: none;">
                <div class="quick-actions">
                    <button class="quick-action-btn" onclick="showCreateAccountModal()">
                        <i class="fas fa-plus"></i> حساب جديد
                    </button>
                    <button class="quick-action-btn" onclick="showImportAccountsModal()">
                        <i class="fas fa-upload"></i> استيراد
                    </button>
                    <button class="quick-action-btn" onclick="exportAccounts()">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                    <button class="quick-action-btn" onclick="showAccountsSettings()">
                        <i class="fas fa-cog"></i> الإعدادات
                    </button>
                </div>

                <div class="filters-section">
                    <div class="row">
                        <div class="col-md-3">
                            <input type="text" class="form-control" id="accountSearchInput" placeholder="البحث في الحسابات..." onkeyup="searchAccounts()">
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" id="accountTypeFilter" onchange="applyAccountFilters()">
                                <option value="">جميع الأنواع</option>
                                <option value="asset">الأصول</option>
                                <option value="liability">الخصوم</option>
                                <option value="equity">حقوق الملكية</option>
                                <option value="revenue">الإيرادات</option>
                                <option value="expense">المصروفات</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" id="accountStatusFilter" onchange="applyAccountFilters()">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                        <div class="col-md-5">
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-primary" onclick="applyAccountFilters()">
                                    <i class="fas fa-filter"></i> تطبيق
                                </button>
                                <button class="btn btn-outline-secondary" onclick="clearAccountFilters()">
                                    <i class="fas fa-times"></i> مسح
                                </button>
                                <button class="btn btn-outline-success" onclick="refreshAccounts()">
                                    <i class="fas fa-sync"></i> تحديث
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="financial-table">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الكود</th>
                                <th>اسم الحساب</th>
                                <th>النوع</th>
                                <th>الرصيد الحالي</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="accountsTableBody">
                            <!-- سيتم ملء الحسابات هنا -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- عرض المعاملات -->
            <div id="transactionsView" style="display: none;">
                <div class="quick-actions">
                    <button class="quick-action-btn" onclick="showCreateTransactionModal()">
                        <i class="fas fa-plus"></i> معاملة جديدة
                    </button>
                    <button class="quick-action-btn" onclick="showJournalEntry()">
                        <i class="fas fa-book"></i> قيد يومية
                    </button>
                    <button class="quick-action-btn" onclick="showBulkTransactions()">
                        <i class="fas fa-list"></i> معاملات مجمعة
                    </button>
                    <button class="quick-action-btn" onclick="generateTransactionReport()">
                        <i class="fas fa-file-alt"></i> تقرير المعاملات
                    </button>
                </div>

                <div class="filters-section">
                    <div class="row">
                        <div class="col-md-2">
                            <input type="text" class="form-control" id="transactionSearchInput" placeholder="البحث..." onkeyup="searchTransactions()">
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" id="transactionTypeFilter" onchange="applyTransactionFilters()">
                                <option value="">جميع الأنواع</option>
                                <option value="journal">قيد يومية</option>
                                <option value="payment">دفع</option>
                                <option value="receipt">استلام</option>
                                <option value="transfer">تحويل</option>
                                <option value="adjustment">تسوية</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" id="transactionStatusFilter" onchange="applyTransactionFilters()">
                                <option value="">جميع الحالات</option>
                                <option value="pending">معلق</option>
                                <option value="approved">موافق عليه</option>
                                <option value="posted">مرحل</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <input type="date" class="form-control" id="transactionDateFrom" onchange="applyTransactionFilters()">
                        </div>
                        <div class="col-md-2">
                            <input type="date" class="form-control" id="transactionDateTo" onchange="applyTransactionFilters()">
                        </div>
                        <div class="col-md-2">
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-primary btn-sm" onclick="applyTransactionFilters()">
                                    <i class="fas fa-filter"></i>
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="clearTransactionFilters()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="financial-table">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم المعاملة</th>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>الوصف</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="transactionsTableBody">
                            <!-- سيتم ملء المعاملات هنا -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- عرض الفواتير -->
            <div id="invoicesView" style="display: none;">
                <div class="quick-actions">
                    <button class="quick-action-btn" onclick="showCreateInvoiceModal()">
                        <i class="fas fa-plus"></i> فاتورة جديدة
                    </button>
                    <button class="quick-action-btn" onclick="showInvoiceTemplates()">
                        <i class="fas fa-file-invoice"></i> قوالب الفواتير
                    </button>
                    <button class="quick-action-btn" onclick="showRecurringInvoices()">
                        <i class="fas fa-repeat"></i> فواتير متكررة
                    </button>
                    <button class="quick-action-btn" onclick="generateInvoiceReport()">
                        <i class="fas fa-chart-bar"></i> تقرير الفواتير
                    </button>
                </div>

                <div id="invoicesList">
                    <!-- سيتم ملء الفواتير هنا -->
                </div>
            </div>

            <!-- عرض الميزانيات -->
            <div id="budgetsView" style="display: none;">
                <div class="quick-actions">
                    <button class="quick-action-btn" onclick="showCreateBudgetModal()">
                        <i class="fas fa-plus"></i> ميزانية جديدة
                    </button>
                    <button class="quick-action-btn" onclick="showBudgetTemplates()">
                        <i class="fas fa-copy"></i> قوالب الميزانيات
                    </button>
                    <button class="quick-action-btn" onclick="compareBudgets()">
                        <i class="fas fa-balance-scale"></i> مقارنة الميزانيات
                    </button>
                    <button class="quick-action-btn" onclick="generateBudgetReport()">
                        <i class="fas fa-chart-line"></i> تقرير الميزانية
                    </button>
                </div>

                <div id="budgetsList">
                    <!-- سيتم ملء الميزانيات هنا -->
                </div>
            </div>

            <!-- عرض التقارير -->
            <div id="reportsView" style="display: none;">
                <div class="row">
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-balance-scale"></i> الميزانية العمومية</h5>
                            </div>
                            <div class="card-body text-center">
                                <p>عرض الأصول والخصوم وحقوق الملكية</p>
                                <button class="btn btn-primary" onclick="generateBalanceSheet()">
                                    <i class="fas fa-file-pdf"></i> إنشاء التقرير
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-line"></i> قائمة الدخل</h5>
                            </div>
                            <div class="card-body text-center">
                                <p>عرض الإيرادات والمصروفات وصافي الربح</p>
                                <button class="btn btn-primary" onclick="generateIncomeStatement()">
                                    <i class="fas fa-file-pdf"></i> إنشاء التقرير
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-water"></i> التدفقات النقدية</h5>
                            </div>
                            <div class="card-body text-center">
                                <p>عرض التدفقات النقدية الداخلة والخارجة</p>
                                <button class="btn btn-primary" onclick="generateCashFlow()">
                                    <i class="fas fa-file-pdf"></i> إنشاء التقرير
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-list-alt"></i> ميزان المراجعة</h5>
                            </div>
                            <div class="card-body text-center">
                                <p>عرض أرصدة جميع الحسابات</p>
                                <button class="btn btn-primary" onclick="generateTrialBalance()">
                                    <i class="fas fa-file-pdf"></i> إنشاء التقرير
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.min.js"></script>
    <script src="../../core/database.js"></script>
    <script src="../../core/utils.js"></script>
    <script src="advanced-finance.js"></script>

    <script>
        // متغيرات عامة
        let advancedFinanceManager;
        let currentView = 'dashboard';
        let currentFilters = {};
        let charts = {};

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeAdvancedFinance();
            loadData();
            updateDashboard();
            setupEventListeners();
        });

        // تهيئة نظام الإدارة المالية المتقدم
        function initializeAdvancedFinance() {
            advancedFinanceManager = new AdvancedFinanceManager();
            console.log('✅ تم تهيئة واجهة الإدارة المالية المتقدمة');
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // تحديث لوحة المعلومات كل دقيقة
            setInterval(updateDashboard, 60000);
        }

        // تحميل البيانات
        function loadData() {
            switch(currentView) {
                case 'dashboard':
                    loadDashboard();
                    break;
                case 'accounts':
                    loadAccounts();
                    break;
                case 'transactions':
                    loadTransactions();
                    break;
                case 'invoices':
                    loadInvoices();
                    break;
                case 'budgets':
                    loadBudgets();
                    break;
                case 'reports':
                    loadReports();
                    break;
            }
        }

        // تحديث لوحة المعلومات
        function updateDashboard() {
            const summary = advancedFinanceManager.getFinancialSummary();

            // تحديث البطاقات
            document.getElementById('totalAssets').textContent = formatCurrency(summary.balances.totalAssets);
            document.getElementById('totalLiabilities').textContent = formatCurrency(summary.balances.totalLiabilities);
            document.getElementById('totalEquity').textContent = formatCurrency(summary.balances.totalEquity);
            document.getElementById('totalRevenue').textContent = formatCurrency(summary.balances.totalRevenue);
            document.getElementById('totalExpenses').textContent = formatCurrency(summary.balances.totalExpenses);
            document.getElementById('netIncome').textContent = formatCurrency(summary.balances.netIncome);

            // تحديث نسب التغيير (مؤقتاً)
            document.getElementById('assetsChange').textContent = '****%';
            document.getElementById('liabilitiesChange').textContent = '****%';
            document.getElementById('equityChange').textContent = '****%';
            document.getElementById('revenueChange').textContent = '+12.5%';
            document.getElementById('expensesChange').textContent = '****%';
            document.getElementById('profitChange').textContent = '+15.2%';
        }

        // تحميل لوحة المعلومات
        function loadDashboard() {
            updateDashboard();
            createChartsIfNeeded();
            loadRecentTransactions();
        }

        // إنشاء الرسوم البيانية
        function createChartsIfNeeded() {
            // رسم بياني لتوزيع الأصول
            const ctx1 = document.getElementById('assetsDistributionChart');
            if (ctx1 && !charts.assetsDistribution) {
                const summary = advancedFinanceManager.getFinancialSummary();
                charts.assetsDistribution = new Chart(ctx1, {
                    type: 'doughnut',
                    data: {
                        labels: ['الأصول المتداولة', 'الأصول الثابتة', 'الاستثمارات'],
                        datasets: [{
                            data: [
                                summary.balances.totalAssets * 0.6,
                                summary.balances.totalAssets * 0.3,
                                summary.balances.totalAssets * 0.1
                            ],
                            backgroundColor: ['#28a745', '#17a2b8', '#ffc107']
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }

            // رسم بياني للإيرادات والمصروفات
            const ctx2 = document.getElementById('revenueExpenseChart');
            if (ctx2 && !charts.revenueExpense) {
                const summary = advancedFinanceManager.getFinancialSummary();
                charts.revenueExpense = new Chart(ctx2, {
                    type: 'bar',
                    data: {
                        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                        datasets: [{
                            label: 'الإيرادات',
                            data: [65000, 59000, 80000, 81000, 56000, 55000],
                            backgroundColor: '#28a745'
                        }, {
                            label: 'المصروفات',
                            data: [28000, 48000, 40000, 19000, 86000, 27000],
                            backgroundColor: '#dc3545'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
        }

        // تحميل المعاملات الأخيرة
        function loadRecentTransactions() {
            const transactions = Array.from(advancedFinanceManager.transactions.values())
                .sort((a, b) => new Date(b.date) - new Date(a.date))
                .slice(0, 5);

            const container = document.getElementById('recentTransactions');

            if (transactions.length === 0) {
                container.innerHTML = '<p class="text-center text-muted">لا توجد معاملات حديثة</p>';
                return;
            }

            container.innerHTML = `
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>رقم المعاملة</th>
                                <th>التاريخ</th>
                                <th>الوصف</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${transactions.map(transaction => `
                                <tr>
                                    <td>${transaction.number}</td>
                                    <td>${formatDate(transaction.date)}</td>
                                    <td>${transaction.description}</td>
                                    <td class="${transaction.amount >= 0 ? 'amount-positive' : 'amount-negative'}">
                                        ${formatCurrency(Math.abs(transaction.amount))}
                                    </td>
                                    <td>
                                        <span class="status-badge status-${transaction.status}">
                                            ${getStatusLabel(transaction.status)}
                                        </span>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }

        // تعيين العرض
        function setView(view) {
            currentView = view;

            // إخفاء جميع العروض
            document.querySelectorAll('[id$="View"]').forEach(el => {
                el.style.display = 'none';
            });

            // إزالة الفئة النشطة من جميع التبويبات
            document.querySelectorAll('.view-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // عرض العرض المحدد
            document.getElementById(view + 'View').style.display = 'block';
            document.getElementById(view + 'Tab').classList.add('active');

            // تحميل البيانات
            loadData();
        }

        // دوال التنسيق
        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: 'SAR',
                minimumFractionDigits: 2
            }).format(amount);
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('ar-SA');
        }

        function getStatusLabel(status) {
            const labels = {
                'pending': 'معلق',
                'approved': 'موافق عليه',
                'posted': 'مرحل',
                'cancelled': 'ملغي',
                'active': 'نشط',
                'inactive': 'غير نشط',
                'draft': 'مسودة',
                'sent': 'مرسل',
                'paid': 'مدفوع',
                'overdue': 'متأخر'
            };
            return labels[status] || status;
        }

        // تحميل الحسابات
        function loadAccounts() {
            const accounts = Array.from(advancedFinanceManager.accounts.values());
            const tbody = document.getElementById('accountsTableBody');

            if (accounts.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="text-center">
                            <div class="empty-state">
                                <i class="fas fa-list-alt"></i>
                                <h4>لا توجد حسابات</h4>
                                <p>ابدأ بإنشاء حساب جديد</p>
                                <button class="btn btn-primary" onclick="showCreateAccountModal()">
                                    <i class="fas fa-plus"></i> إنشاء حساب جديد
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = accounts.map(account => `
                <tr>
                    <td><strong>${account.code}</strong></td>
                    <td>${account.name}</td>
                    <td>
                        <span class="badge bg-primary">${getAccountTypeLabel(account.type)}</span>
                    </td>
                    <td class="${account.balances.currentBalance >= 0 ? 'amount-positive' : 'amount-negative'}">
                        ${formatCurrency(Math.abs(account.balances.currentBalance))}
                    </td>
                    <td>
                        <span class="status-badge status-${account.status}">
                            ${getStatusLabel(account.status)}
                        </span>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="viewAccount('${account.id}')" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-warning" onclick="editAccount('${account.id}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-info" onclick="viewAccountTransactions('${account.id}')" title="المعاملات">
                                <i class="fas fa-list"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteAccount('${account.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // تحميل المعاملات
        function loadTransactions() {
            const transactions = Array.from(advancedFinanceManager.transactions.values())
                .sort((a, b) => new Date(b.date) - new Date(a.date));
            const tbody = document.getElementById('transactionsTableBody');

            if (transactions.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center">
                            <div class="empty-state">
                                <i class="fas fa-exchange-alt"></i>
                                <h4>لا توجد معاملات</h4>
                                <p>ابدأ بإنشاء معاملة جديدة</p>
                                <button class="btn btn-primary" onclick="showCreateTransactionModal()">
                                    <i class="fas fa-plus"></i> إنشاء معاملة جديدة
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = transactions.map(transaction => `
                <tr>
                    <td><strong>${transaction.number}</strong></td>
                    <td>${formatDate(transaction.date)}</td>
                    <td>
                        <span class="badge bg-info">${getTransactionTypeLabel(transaction.type)}</span>
                    </td>
                    <td>${transaction.description}</td>
                    <td class="${transaction.amount >= 0 ? 'amount-positive' : 'amount-negative'}">
                        ${formatCurrency(Math.abs(transaction.amount))}
                    </td>
                    <td>
                        <span class="status-badge status-${transaction.status}">
                            ${getStatusLabel(transaction.status)}
                        </span>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="viewTransaction('${transaction.id}')" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-warning" onclick="editTransaction('${transaction.id}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-success" onclick="approveTransaction('${transaction.id}')" title="موافقة">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteTransaction('${transaction.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // تحميل الفواتير
        function loadInvoices() {
            const invoices = Array.from(advancedFinanceManager.invoices.values())
                .sort((a, b) => new Date(b.date) - new Date(a.date));
            const container = document.getElementById('invoicesList');

            if (invoices.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-file-invoice"></i>
                        <h4>لا توجد فواتير</h4>
                        <p>ابدأ بإنشاء فاتورة جديدة</p>
                        <button class="btn btn-primary" onclick="showCreateInvoiceModal()">
                            <i class="fas fa-plus"></i> إنشاء فاتورة جديدة
                        </button>
                    </div>
                `;
                return;
            }

            container.innerHTML = `
                <div class="row">
                    ${invoices.map(invoice => `
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">فاتورة #${invoice.number}</h6>
                                    <span class="status-badge status-${invoice.status}">
                                        ${getStatusLabel(invoice.status)}
                                    </span>
                                </div>
                                <div class="card-body">
                                    <p class="mb-2"><strong>العميل:</strong> ${invoice.customer.name}</p>
                                    <p class="mb-2"><strong>التاريخ:</strong> ${formatDate(invoice.date)}</p>
                                    <p class="mb-2"><strong>تاريخ الاستحقاق:</strong> ${formatDate(invoice.dueDate)}</p>
                                    <p class="mb-2"><strong>المبلغ الإجمالي:</strong>
                                        <span class="amount-positive">${formatCurrency(invoice.totalAmount)}</span>
                                    </p>
                                    <p class="mb-3"><strong>المبلغ المتبقي:</strong>
                                        <span class="amount-${invoice.remainingAmount > 0 ? 'negative' : 'positive'}">
                                            ${formatCurrency(invoice.remainingAmount)}
                                        </span>
                                    </p>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewInvoice('${invoice.id}')">
                                            <i class="fas fa-eye"></i> عرض
                                        </button>
                                        <button class="btn btn-sm btn-outline-warning" onclick="editInvoice('${invoice.id}')">
                                            <i class="fas fa-edit"></i> تعديل
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" onclick="printInvoice('${invoice.id}')">
                                            <i class="fas fa-print"></i> طباعة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // تحميل الميزانيات
        function loadBudgets() {
            const budgets = Array.from(advancedFinanceManager.budgets.values())
                .sort((a, b) => new Date(b.period.startDate) - new Date(a.period.startDate));
            const container = document.getElementById('budgetsList');

            if (budgets.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-calculator"></i>
                        <h4>لا توجد ميزانيات</h4>
                        <p>ابدأ بإنشاء ميزانية جديدة</p>
                        <button class="btn btn-primary" onclick="showCreateBudgetModal()">
                            <i class="fas fa-plus"></i> إنشاء ميزانية جديدة
                        </button>
                    </div>
                `;
                return;
            }

            container.innerHTML = `
                <div class="row">
                    ${budgets.map(budget => `
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">${budget.name}</h6>
                                    <span class="status-badge status-${budget.status}">
                                        ${getStatusLabel(budget.status)}
                                    </span>
                                </div>
                                <div class="card-body">
                                    <p class="mb-2"><strong>الفترة:</strong> ${formatDate(budget.period.startDate)} - ${formatDate(budget.period.endDate)}</p>
                                    <p class="mb-2"><strong>الإيرادات المخططة:</strong>
                                        <span class="amount-positive">${formatCurrency(budget.totals.budgetedRevenue)}</span>
                                    </p>
                                    <p class="mb-2"><strong>الإيرادات الفعلية:</strong>
                                        <span class="amount-positive">${formatCurrency(budget.totals.actualRevenue)}</span>
                                    </p>
                                    <p class="mb-2"><strong>المصروفات المخططة:</strong>
                                        <span class="amount-negative">${formatCurrency(budget.totals.budgetedExpenses)}</span>
                                    </p>
                                    <p class="mb-2"><strong>المصروفات الفعلية:</strong>
                                        <span class="amount-negative">${formatCurrency(budget.totals.actualExpenses)}</span>
                                    </p>
                                    <p class="mb-3"><strong>الانحراف:</strong>
                                        <span class="amount-${budget.totals.variance >= 0 ? 'positive' : 'negative'}">
                                            ${formatCurrency(Math.abs(budget.totals.variance))}
                                            (${budget.totals.variancePercentage.toFixed(1)}%)
                                        </span>
                                    </p>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewBudget('${budget.id}')">
                                            <i class="fas fa-eye"></i> عرض
                                        </button>
                                        <button class="btn btn-sm btn-outline-warning" onclick="editBudget('${budget.id}')">
                                            <i class="fas fa-edit"></i> تعديل
                                        </button>
                                        <button class="btn btn-sm btn-outline-info" onclick="analyzeBudget('${budget.id}')">
                                            <i class="fas fa-chart-line"></i> تحليل
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        function loadReports() {
            // التقارير جاهزة للاستخدام
        }

        // دوال الإجراءات السريعة
        function showCreateAccountModal() {
            const modalHtml = `
                <div class="modal fade" id="createAccountModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="fas fa-plus"></i> إنشاء حساب جديد</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="createAccountForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">كود الحساب *</label>
                                                <input type="text" class="form-control" id="accountCode" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">اسم الحساب *</label>
                                                <input type="text" class="form-control" id="accountName" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">نوع الحساب *</label>
                                                <select class="form-select" id="accountType" required>
                                                    <option value="">اختر النوع</option>
                                                    <option value="asset">أصول</option>
                                                    <option value="liability">خصوم</option>
                                                    <option value="equity">حقوق الملكية</option>
                                                    <option value="revenue">إيرادات</option>
                                                    <option value="expense">مصروفات</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">التصنيف</label>
                                                <select class="form-select" id="accountCategory">
                                                    <option value="">اختر التصنيف</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">الوصف</label>
                                        <textarea class="form-control" id="accountDescription" rows="3"></textarea>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">الرصيد الافتتاحي</label>
                                                <input type="number" class="form-control" id="openingBalance" value="0" step="0.01">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">العملة</label>
                                                <select class="form-select" id="accountCurrency">
                                                    <option value="SAR">ريال سعودي</option>
                                                    <option value="USD">دولار أمريكي</option>
                                                    <option value="EUR">يورو</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-primary" onclick="createAccount()">إنشاء الحساب</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('createAccountModal'));
            modal.show();

            // تحديث التصنيفات عند تغيير النوع
            document.getElementById('accountType').addEventListener('change', updateAccountCategories);
        }

        function showCreateTransactionModal() {
            const modalHtml = `
                <div class="modal fade" id="createTransactionModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="fas fa-plus"></i> إنشاء معاملة جديدة</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="createTransactionForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">نوع المعاملة *</label>
                                                <select class="form-select" id="transactionType" required>
                                                    <option value="">اختر النوع</option>
                                                    <option value="journal">قيد يومية</option>
                                                    <option value="payment">دفع</option>
                                                    <option value="receipt">استلام</option>
                                                    <option value="transfer">تحويل</option>
                                                    <option value="adjustment">تسوية</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">التاريخ *</label>
                                                <input type="date" class="form-control" id="transactionDate" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">الوصف *</label>
                                        <input type="text" class="form-control" id="transactionDescription" required>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">من الحساب</label>
                                                <select class="form-select" id="fromAccount">
                                                    <option value="">اختر الحساب</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">إلى الحساب</label>
                                                <select class="form-select" id="toAccount">
                                                    <option value="">اختر الحساب</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">المبلغ *</label>
                                                <input type="number" class="form-control" id="transactionAmount" required step="0.01">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">العملة</label>
                                                <select class="form-select" id="transactionCurrency">
                                                    <option value="SAR">ريال سعودي</option>
                                                    <option value="USD">دولار أمريكي</option>
                                                    <option value="EUR">يورو</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">المرجع</label>
                                        <input type="text" class="form-control" id="transactionReference">
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-primary" onclick="createTransaction()">إنشاء المعاملة</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('createTransactionModal'));
            modal.show();

            // تعبئة قوائم الحسابات
            populateAccountSelects();

            // تعيين التاريخ الحالي
            document.getElementById('transactionDate').value = new Date().toISOString().split('T')[0];
        }

        function showCreateInvoiceModal() {
            const modalHtml = `
                <div class="modal fade" id="createInvoiceModal" tabindex="-1">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="fas fa-plus"></i> إنشاء فاتورة جديدة</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="createInvoiceForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">رقم الفاتورة *</label>
                                                <input type="text" class="form-control" id="invoiceNumber" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">التاريخ *</label>
                                                <input type="date" class="form-control" id="invoiceDate" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">اسم العميل *</label>
                                                <input type="text" class="form-control" id="customerName" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">شروط الدفع</label>
                                                <select class="form-select" id="paymentTerms">
                                                    <option value="immediate">فوري</option>
                                                    <option value="net_15">خلال 15 يوم</option>
                                                    <option value="net_30">خلال 30 يوم</option>
                                                    <option value="net_60">خلال 60 يوم</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">عنوان العميل</label>
                                        <textarea class="form-control" id="customerAddress" rows="2"></textarea>
                                    </div>

                                    <h6 class="mb-3">بنود الفاتورة</h6>
                                    <div class="table-responsive">
                                        <table class="table table-bordered" id="invoiceItemsTable">
                                            <thead>
                                                <tr>
                                                    <th>الوصف</th>
                                                    <th>الكمية</th>
                                                    <th>سعر الوحدة</th>
                                                    <th>المجموع</th>
                                                    <th>إجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody id="invoiceItemsBody">
                                                <tr>
                                                    <td><input type="text" class="form-control" placeholder="وصف البند"></td>
                                                    <td><input type="number" class="form-control" placeholder="1" min="1"></td>
                                                    <td><input type="number" class="form-control" placeholder="0.00" step="0.01"></td>
                                                    <td class="item-total">0.00</td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-danger" onclick="removeInvoiceItem(this)">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-primary mb-3" onclick="addInvoiceItem()">
                                        <i class="fas fa-plus"></i> إضافة بند
                                    </button>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">نسبة الخصم (%)</label>
                                                <input type="number" class="form-control" id="discountPercentage" value="0" min="0" max="100" step="0.01">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">نسبة الضريبة (%)</label>
                                                <input type="number" class="form-control" id="taxRate" value="15" min="0" max="100" step="0.01">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-8"></div>
                                        <div class="col-md-4">
                                            <div class="card">
                                                <div class="card-body">
                                                    <div class="d-flex justify-content-between">
                                                        <span>المجموع الفرعي:</span>
                                                        <span id="invoiceSubtotal">0.00 ر.س</span>
                                                    </div>
                                                    <div class="d-flex justify-content-between">
                                                        <span>الخصم:</span>
                                                        <span id="invoiceDiscount">0.00 ر.س</span>
                                                    </div>
                                                    <div class="d-flex justify-content-between">
                                                        <span>الضريبة:</span>
                                                        <span id="invoiceTax">0.00 ر.س</span>
                                                    </div>
                                                    <hr>
                                                    <div class="d-flex justify-content-between">
                                                        <strong>المجموع الإجمالي:</strong>
                                                        <strong id="invoiceTotal">0.00 ر.س</strong>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-primary" onclick="createInvoice()">إنشاء الفاتورة</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('createInvoiceModal'));
            modal.show();

            // تعيين التاريخ الحالي ورقم الفاتورة
            document.getElementById('invoiceDate').value = new Date().toISOString().split('T')[0];
            document.getElementById('invoiceNumber').value = 'INV-' + Date.now();

            // إعداد حساب المجاميع
            setupInvoiceCalculations();
        }

        function showCreateBudgetModal() {
            const modalHtml = `
                <div class="modal fade" id="createBudgetModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="fas fa-plus"></i> إنشاء ميزانية جديدة</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="createBudgetForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">اسم الميزانية *</label>
                                                <input type="text" class="form-control" id="budgetName" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">نوع الميزانية</label>
                                                <select class="form-select" id="budgetType">
                                                    <option value="annual">سنوية</option>
                                                    <option value="quarterly">ربع سنوية</option>
                                                    <option value="monthly">شهرية</option>
                                                    <option value="project">مشروع</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">تاريخ البداية *</label>
                                                <input type="date" class="form-control" id="budgetStartDate" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">تاريخ النهاية *</label>
                                                <input type="date" class="form-control" id="budgetEndDate" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">الوصف</label>
                                        <textarea class="form-control" id="budgetDescription" rows="3"></textarea>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">إجمالي الإيرادات المخططة</label>
                                                <input type="number" class="form-control" id="budgetedRevenue" value="0" step="0.01">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">إجمالي المصروفات المخططة</label>
                                                <input type="number" class="form-control" id="budgetedExpenses" value="0" step="0.01">
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-primary" onclick="createBudget()">إنشاء الميزانية</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('createBudgetModal'));
            modal.show();

            // تعيين التواريخ الافتراضية
            const now = new Date();
            const startOfYear = new Date(now.getFullYear(), 0, 1);
            const endOfYear = new Date(now.getFullYear(), 11, 31);

            document.getElementById('budgetStartDate').value = startOfYear.toISOString().split('T')[0];
            document.getElementById('budgetEndDate').value = endOfYear.toISOString().split('T')[0];
        }

        // دوال التقارير
        function generateBalanceSheet() {
            const report = advancedFinanceManager.generateFinancialReport('balance_sheet');
            showNotification('تم إنشاء تقرير الميزانية العمومية', 'success');
        }

        function generateIncomeStatement() {
            const report = advancedFinanceManager.generateFinancialReport('income_statement');
            showNotification('تم إنشاء تقرير قائمة الدخل', 'success');
        }

        function generateCashFlow() {
            const report = advancedFinanceManager.generateFinancialReport('cash_flow');
            showNotification('تم إنشاء تقرير التدفقات النقدية', 'success');
        }

        function generateTrialBalance() {
            const report = advancedFinanceManager.generateFinancialReport('trial_balance');
            showNotification('تم إنشاء تقرير ميزان المراجعة', 'success');
        }

        // إظهار الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }

        // دوال إنشاء البيانات
        function createAccount() {
            const form = document.getElementById('createAccountForm');
            const formData = new FormData(form);

            const accountData = {
                code: document.getElementById('accountCode').value,
                name: document.getElementById('accountName').value,
                type: document.getElementById('accountType').value,
                category: document.getElementById('accountCategory').value,
                description: document.getElementById('accountDescription').value,
                openingBalance: parseFloat(document.getElementById('openingBalance').value) || 0,
                currency: document.getElementById('accountCurrency').value
            };

            try {
                const account = advancedFinanceManager.createAccount(accountData);
                showNotification('تم إنشاء الحساب بنجاح', 'success');
                bootstrap.Modal.getInstance(document.getElementById('createAccountModal')).hide();
                loadAccounts();
                updateDashboard();
            } catch (error) {
                showNotification('خطأ في إنشاء الحساب: ' + error.message, 'error');
            }
        }

        function createTransaction() {
            const transactionData = {
                type: document.getElementById('transactionType').value,
                date: document.getElementById('transactionDate').value,
                description: document.getElementById('transactionDescription').value,
                fromAccountId: document.getElementById('fromAccount').value,
                toAccountId: document.getElementById('toAccount').value,
                amount: parseFloat(document.getElementById('transactionAmount').value),
                currency: document.getElementById('transactionCurrency').value,
                reference: document.getElementById('transactionReference').value
            };

            try {
                const transaction = advancedFinanceManager.createTransaction(transactionData);
                showNotification('تم إنشاء المعاملة بنجاح', 'success');
                bootstrap.Modal.getInstance(document.getElementById('createTransactionModal')).hide();
                loadTransactions();
                updateDashboard();
            } catch (error) {
                showNotification('خطأ في إنشاء المعاملة: ' + error.message, 'error');
            }
        }

        function createInvoice() {
            const items = [];
            const rows = document.querySelectorAll('#invoiceItemsBody tr');

            rows.forEach(row => {
                const inputs = row.querySelectorAll('input');
                if (inputs[0].value && inputs[1].value && inputs[2].value) {
                    items.push({
                        description: inputs[0].value,
                        quantity: parseFloat(inputs[1].value),
                        unitPrice: parseFloat(inputs[2].value)
                    });
                }
            });

            const invoiceData = {
                number: document.getElementById('invoiceNumber').value,
                date: document.getElementById('invoiceDate').value,
                customer: {
                    name: document.getElementById('customerName').value,
                    address: document.getElementById('customerAddress').value
                },
                items: items,
                paymentTerms: document.getElementById('paymentTerms').value,
                discountPercentage: parseFloat(document.getElementById('discountPercentage').value) || 0,
                taxRate: parseFloat(document.getElementById('taxRate').value) || 0
            };

            try {
                const invoice = advancedFinanceManager.createInvoice(invoiceData);
                showNotification('تم إنشاء الفاتورة بنجاح', 'success');
                bootstrap.Modal.getInstance(document.getElementById('createInvoiceModal')).hide();
                loadInvoices();
                updateDashboard();
            } catch (error) {
                showNotification('خطأ في إنشاء الفاتورة: ' + error.message, 'error');
            }
        }

        function createBudget() {
            const budgetData = {
                name: document.getElementById('budgetName').value,
                type: document.getElementById('budgetType').value,
                period: {
                    startDate: document.getElementById('budgetStartDate').value,
                    endDate: document.getElementById('budgetEndDate').value
                },
                description: document.getElementById('budgetDescription').value,
                budgetedRevenue: parseFloat(document.getElementById('budgetedRevenue').value) || 0,
                budgetedExpenses: parseFloat(document.getElementById('budgetedExpenses').value) || 0
            };

            try {
                const budget = advancedFinanceManager.createBudget(budgetData);
                showNotification('تم إنشاء الميزانية بنجاح', 'success');
                bootstrap.Modal.getInstance(document.getElementById('createBudgetModal')).hide();
                loadBudgets();
                updateDashboard();
            } catch (error) {
                showNotification('خطأ في إنشاء الميزانية: ' + error.message, 'error');
            }
        }

        // دوال مساعدة
        function updateAccountCategories() {
            const type = document.getElementById('accountType').value;
            const categorySelect = document.getElementById('accountCategory');

            const categories = {
                'asset': [
                    { value: 'current_assets', text: 'أصول متداولة' },
                    { value: 'fixed_assets', text: 'أصول ثابتة' },
                    { value: 'investments', text: 'استثمارات' }
                ],
                'liability': [
                    { value: 'current_liabilities', text: 'خصوم متداولة' },
                    { value: 'long_term_liabilities', text: 'خصوم طويلة الأجل' }
                ],
                'equity': [
                    { value: 'capital', text: 'رأس المال' },
                    { value: 'retained_earnings', text: 'الأرباح المحتجزة' }
                ],
                'revenue': [
                    { value: 'sales_revenue', text: 'إيرادات المبيعات' },
                    { value: 'service_revenue', text: 'إيرادات الخدمات' },
                    { value: 'other_revenue', text: 'إيرادات أخرى' }
                ],
                'expense': [
                    { value: 'operating_expenses', text: 'مصروفات تشغيلية' },
                    { value: 'administrative_expenses', text: 'مصروفات إدارية' },
                    { value: 'financial_expenses', text: 'مصروفات مالية' }
                ]
            };

            categorySelect.innerHTML = '<option value="">اختر التصنيف</option>';

            if (categories[type]) {
                categories[type].forEach(category => {
                    categorySelect.innerHTML += `<option value="${category.value}">${category.text}</option>`;
                });
            }
        }

        function populateAccountSelects() {
            const accounts = Array.from(advancedFinanceManager.accounts.values());
            const fromSelect = document.getElementById('fromAccount');
            const toSelect = document.getElementById('toAccount');

            const accountOptions = accounts.map(account =>
                `<option value="${account.id}">${account.code} - ${account.name}</option>`
            ).join('');

            fromSelect.innerHTML = '<option value="">اختر الحساب</option>' + accountOptions;
            toSelect.innerHTML = '<option value="">اختر الحساب</option>' + accountOptions;
        }

        function setupInvoiceCalculations() {
            const table = document.getElementById('invoiceItemsTable');
            const discountInput = document.getElementById('discountPercentage');
            const taxInput = document.getElementById('taxRate');

            // حساب المجاميع عند تغيير البيانات
            table.addEventListener('input', calculateInvoiceTotals);
            discountInput.addEventListener('input', calculateInvoiceTotals);
            taxInput.addEventListener('input', calculateInvoiceTotals);
        }

        function addInvoiceItem() {
            const tbody = document.getElementById('invoiceItemsBody');
            const newRow = document.createElement('tr');
            newRow.innerHTML = `
                <td><input type="text" class="form-control" placeholder="وصف البند"></td>
                <td><input type="number" class="form-control" placeholder="1" min="1"></td>
                <td><input type="number" class="form-control" placeholder="0.00" step="0.01"></td>
                <td class="item-total">0.00</td>
                <td>
                    <button type="button" class="btn btn-sm btn-danger" onclick="removeInvoiceItem(this)">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(newRow);
        }

        function removeInvoiceItem(button) {
            const row = button.closest('tr');
            row.remove();
            calculateInvoiceTotals();
        }

        function calculateInvoiceTotals() {
            const rows = document.querySelectorAll('#invoiceItemsBody tr');
            let subtotal = 0;

            rows.forEach(row => {
                const inputs = row.querySelectorAll('input');
                const quantity = parseFloat(inputs[1].value) || 0;
                const unitPrice = parseFloat(inputs[2].value) || 0;
                const total = quantity * unitPrice;

                row.querySelector('.item-total').textContent = total.toFixed(2);
                subtotal += total;
            });

            const discountPercentage = parseFloat(document.getElementById('discountPercentage').value) || 0;
            const taxRate = parseFloat(document.getElementById('taxRate').value) || 0;

            const discountAmount = subtotal * (discountPercentage / 100);
            const taxableAmount = subtotal - discountAmount;
            const taxAmount = taxableAmount * (taxRate / 100);
            const total = taxableAmount + taxAmount;

            document.getElementById('invoiceSubtotal').textContent = subtotal.toFixed(2) + ' ر.س';
            document.getElementById('invoiceDiscount').textContent = discountAmount.toFixed(2) + ' ر.س';
            document.getElementById('invoiceTax').textContent = taxAmount.toFixed(2) + ' ر.س';
            document.getElementById('invoiceTotal').textContent = total.toFixed(2) + ' ر.س';
        }

        // دوال التسميات
        function getAccountTypeLabel(type) {
            const labels = {
                'asset': 'أصول',
                'liability': 'خصوم',
                'equity': 'حقوق الملكية',
                'revenue': 'إيرادات',
                'expense': 'مصروفات'
            };
            return labels[type] || type;
        }

        function getTransactionTypeLabel(type) {
            const labels = {
                'journal': 'قيد يومية',
                'payment': 'دفع',
                'receipt': 'استلام',
                'transfer': 'تحويل',
                'adjustment': 'تسوية'
            };
            return labels[type] || type;
        }

        // دوال البحث والفلترة
        function searchAccounts() {
            const searchTerm = document.getElementById('accountSearchInput').value.toLowerCase();
            const rows = document.querySelectorAll('#accountsTableBody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        }

        function applyAccountFilters() {
            const typeFilter = document.getElementById('accountTypeFilter').value;
            const statusFilter = document.getElementById('accountStatusFilter').value;
            const rows = document.querySelectorAll('#accountsTableBody tr');

            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length < 5) return;

                const typeMatch = !typeFilter || cells[2].textContent.includes(getAccountTypeLabel(typeFilter));
                const statusMatch = !statusFilter || cells[4].textContent.includes(getStatusLabel(statusFilter));

                row.style.display = (typeMatch && statusMatch) ? '' : 'none';
            });
        }

        function clearAccountFilters() {
            document.getElementById('accountSearchInput').value = '';
            document.getElementById('accountTypeFilter').value = '';
            document.getElementById('accountStatusFilter').value = '';
            loadAccounts();
        }

        function refreshAccounts() {
            loadAccounts();
        }

        function searchTransactions() {
            const searchTerm = document.getElementById('transactionSearchInput').value.toLowerCase();
            const rows = document.querySelectorAll('#transactionsTableBody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        }

        function applyTransactionFilters() {
            const typeFilter = document.getElementById('transactionTypeFilter').value;
            const statusFilter = document.getElementById('transactionStatusFilter').value;
            const dateFrom = document.getElementById('transactionDateFrom').value;
            const dateTo = document.getElementById('transactionDateTo').value;
            const rows = document.querySelectorAll('#transactionsTableBody tr');

            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length < 6) return;

                const typeMatch = !typeFilter || cells[2].textContent.includes(getTransactionTypeLabel(typeFilter));
                const statusMatch = !statusFilter || cells[5].textContent.includes(getStatusLabel(statusFilter));

                // فلترة التاريخ
                let dateMatch = true;
                if (dateFrom || dateTo) {
                    const transactionDate = new Date(cells[1].textContent);
                    if (dateFrom && transactionDate < new Date(dateFrom)) dateMatch = false;
                    if (dateTo && transactionDate > new Date(dateTo)) dateMatch = false;
                }

                row.style.display = (typeMatch && statusMatch && dateMatch) ? '' : 'none';
            });
        }

        function clearTransactionFilters() {
            document.getElementById('transactionSearchInput').value = '';
            document.getElementById('transactionTypeFilter').value = '';
            document.getElementById('transactionStatusFilter').value = '';
            document.getElementById('transactionDateFrom').value = '';
            document.getElementById('transactionDateTo').value = '';
            loadTransactions();
        }

        // دوال العرض والتحرير
        function viewAccount(accountId) {
            const account = advancedFinanceManager.accounts.get(accountId);
            if (!account) return;

            const modalHtml = `
                <div class="modal fade" id="viewAccountModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="fas fa-eye"></i> تفاصيل الحساب</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>كود الحساب:</strong> ${account.code}</p>
                                        <p><strong>اسم الحساب:</strong> ${account.name}</p>
                                        <p><strong>النوع:</strong> ${getAccountTypeLabel(account.type)}</p>
                                        <p><strong>التصنيف:</strong> ${account.category}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>الرصيد الحالي:</strong> ${formatCurrency(account.balances.currentBalance)}</p>
                                        <p><strong>الرصيد الافتتاحي:</strong> ${formatCurrency(account.balances.openingBalance)}</p>
                                        <p><strong>العملة:</strong> ${account.currency}</p>
                                        <p><strong>الحالة:</strong> ${getStatusLabel(account.status)}</p>
                                    </div>
                                </div>
                                ${account.description ? `<p><strong>الوصف:</strong> ${account.description}</p>` : ''}
                                <p><strong>تاريخ الإنشاء:</strong> ${formatDate(account.createdAt)}</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                <button type="button" class="btn btn-primary" onclick="editAccount('${accountId}')">تعديل</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('viewAccountModal'));
            modal.show();
        }

        function editAccount(accountId) {
            showNotification('ميزة تعديل الحسابات قيد التطوير', 'info');
        }

        function deleteAccount(accountId) {
            if (confirm('هل أنت متأكد من حذف هذا الحساب؟')) {
                try {
                    advancedFinanceManager.accounts.delete(accountId);
                    showNotification('تم حذف الحساب بنجاح', 'success');
                    loadAccounts();
                    updateDashboard();
                } catch (error) {
                    showNotification('خطأ في حذف الحساب: ' + error.message, 'error');
                }
            }
        }

        function viewAccountTransactions(accountId) {
            const account = advancedFinanceManager.accounts.get(accountId);
            if (!account) return;

            const transactions = Array.from(advancedFinanceManager.transactions.values())
                .filter(t => t.fromAccountId === accountId || t.toAccountId === accountId)
                .sort((a, b) => new Date(b.date) - new Date(a.date));

            const modalHtml = `
                <div class="modal fade" id="accountTransactionsModal" tabindex="-1">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="fas fa-list"></i> معاملات الحساب: ${account.name}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>رقم المعاملة</th>
                                                <th>التاريخ</th>
                                                <th>الوصف</th>
                                                <th>مدين</th>
                                                <th>دائن</th>
                                                <th>الرصيد</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${transactions.map(transaction => {
                                                const isDebit = transaction.toAccountId === accountId;
                                                const amount = transaction.amount;
                                                return `
                                                    <tr>
                                                        <td>${transaction.number}</td>
                                                        <td>${formatDate(transaction.date)}</td>
                                                        <td>${transaction.description}</td>
                                                        <td>${isDebit ? formatCurrency(amount) : '-'}</td>
                                                        <td>${!isDebit ? formatCurrency(amount) : '-'}</td>
                                                        <td>${formatCurrency(account.balances.currentBalance)}</td>
                                                        <td>
                                                            <span class="status-badge status-${transaction.status}">
                                                                ${getStatusLabel(transaction.status)}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                `;
                                            }).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('accountTransactionsModal'));
            modal.show();
        }

        function viewTransaction(transactionId) {
            const transaction = advancedFinanceManager.transactions.get(transactionId);
            if (!transaction) return;

            const fromAccount = advancedFinanceManager.accounts.get(transaction.fromAccountId);
            const toAccount = advancedFinanceManager.accounts.get(transaction.toAccountId);

            const modalHtml = `
                <div class="modal fade" id="viewTransactionModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="fas fa-eye"></i> تفاصيل المعاملة</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>رقم المعاملة:</strong> ${transaction.number}</p>
                                        <p><strong>النوع:</strong> ${getTransactionTypeLabel(transaction.type)}</p>
                                        <p><strong>التاريخ:</strong> ${formatDate(transaction.date)}</p>
                                        <p><strong>المبلغ:</strong> ${formatCurrency(transaction.amount)}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>من الحساب:</strong> ${fromAccount ? fromAccount.name : 'غير محدد'}</p>
                                        <p><strong>إلى الحساب:</strong> ${toAccount ? toAccount.name : 'غير محدد'}</p>
                                        <p><strong>العملة:</strong> ${transaction.currency}</p>
                                        <p><strong>الحالة:</strong> ${getStatusLabel(transaction.status)}</p>
                                    </div>
                                </div>
                                <p><strong>الوصف:</strong> ${transaction.description}</p>
                                ${transaction.reference ? `<p><strong>المرجع:</strong> ${transaction.reference}</p>` : ''}
                                <p><strong>تاريخ الإنشاء:</strong> ${formatDate(transaction.createdAt)}</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                <button type="button" class="btn btn-primary" onclick="editTransaction('${transactionId}')">تعديل</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('viewTransactionModal'));
            modal.show();
        }

        function editTransaction(transactionId) {
            showNotification('ميزة تعديل المعاملات قيد التطوير', 'info');
        }

        function deleteTransaction(transactionId) {
            if (confirm('هل أنت متأكد من حذف هذه المعاملة؟')) {
                try {
                    advancedFinanceManager.transactions.delete(transactionId);
                    showNotification('تم حذف المعاملة بنجاح', 'success');
                    loadTransactions();
                    updateDashboard();
                } catch (error) {
                    showNotification('خطأ في حذف المعاملة: ' + error.message, 'error');
                }
            }
        }

        function approveTransaction(transactionId) {
            try {
                const transaction = advancedFinanceManager.transactions.get(transactionId);
                if (transaction) {
                    transaction.status = 'approved';
                    advancedFinanceManager.saveData();
                    showNotification('تم الموافقة على المعاملة', 'success');
                    loadTransactions();
                }
            } catch (error) {
                showNotification('خطأ في الموافقة على المعاملة: ' + error.message, 'error');
            }
        }

        function viewInvoice(invoiceId) {
            const invoice = advancedFinanceManager.invoices.get(invoiceId);
            if (!invoice) return;

            const modalHtml = `
                <div class="modal fade" id="viewInvoiceModal" tabindex="-1">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="fas fa-eye"></i> عرض الفاتورة #${invoice.number}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="invoice-preview">
                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <h4>فاتورة #${invoice.number}</h4>
                                            <p><strong>التاريخ:</strong> ${formatDate(invoice.date)}</p>
                                            <p><strong>تاريخ الاستحقاق:</strong> ${formatDate(invoice.dueDate)}</p>
                                        </div>
                                        <div class="col-md-6 text-end">
                                            <h5>بيانات العميل</h5>
                                            <p><strong>${invoice.customer.name}</strong></p>
                                            ${invoice.customer.address ? `<p>${invoice.customer.address}</p>` : ''}
                                        </div>
                                    </div>

                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>الوصف</th>
                                                    <th>الكمية</th>
                                                    <th>سعر الوحدة</th>
                                                    <th>المجموع</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${invoice.items.map(item => `
                                                    <tr>
                                                        <td>${item.description}</td>
                                                        <td>${item.quantity}</td>
                                                        <td>${formatCurrency(item.unitPrice)}</td>
                                                        <td>${formatCurrency(item.total)}</td>
                                                    </tr>
                                                `).join('')}
                                            </tbody>
                                        </table>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-8"></div>
                                        <div class="col-md-4">
                                            <div class="card">
                                                <div class="card-body">
                                                    <div class="d-flex justify-content-between">
                                                        <span>المجموع الفرعي:</span>
                                                        <span>${formatCurrency(invoice.subtotal)}</span>
                                                    </div>
                                                    <div class="d-flex justify-content-between">
                                                        <span>الخصم:</span>
                                                        <span>${formatCurrency(invoice.discountAmount)}</span>
                                                    </div>
                                                    <div class="d-flex justify-content-between">
                                                        <span>الضريبة:</span>
                                                        <span>${formatCurrency(invoice.taxAmount)}</span>
                                                    </div>
                                                    <hr>
                                                    <div class="d-flex justify-content-between">
                                                        <strong>المجموع الإجمالي:</strong>
                                                        <strong>${formatCurrency(invoice.totalAmount)}</strong>
                                                    </div>
                                                    <div class="d-flex justify-content-between">
                                                        <span>المبلغ المدفوع:</span>
                                                        <span>${formatCurrency(invoice.paidAmount)}</span>
                                                    </div>
                                                    <div class="d-flex justify-content-between">
                                                        <strong>المبلغ المتبقي:</strong>
                                                        <strong class="amount-${invoice.remainingAmount > 0 ? 'negative' : 'positive'}">
                                                            ${formatCurrency(invoice.remainingAmount)}
                                                        </strong>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                <button type="button" class="btn btn-primary" onclick="editInvoice('${invoiceId}')">تعديل</button>
                                <button type="button" class="btn btn-success" onclick="printInvoice('${invoiceId}')">طباعة</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('viewInvoiceModal'));
            modal.show();
        }

        function editInvoice(invoiceId) {
            showNotification('ميزة تعديل الفواتير قيد التطوير', 'info');
        }

        function printInvoice(invoiceId) {
            const invoice = advancedFinanceManager.invoices.get(invoiceId);
            if (!invoice) return;

            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head>
                    <title>فاتورة #${invoice.number}</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        .invoice-header { text-align: center; margin-bottom: 30px; }
                        .invoice-details { margin-bottom: 20px; }
                        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        th { background-color: #f5f5f5; }
                        .totals { text-align: left; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    <div class="invoice-header">
                        <h1>فاتورة #${invoice.number}</h1>
                        <p>التاريخ: ${formatDate(invoice.date)}</p>
                    </div>

                    <div class="invoice-details">
                        <h3>بيانات العميل:</h3>
                        <p><strong>${invoice.customer.name}</strong></p>
                        ${invoice.customer.address ? `<p>${invoice.customer.address}</p>` : ''}
                    </div>

                    <table>
                        <thead>
                            <tr>
                                <th>الوصف</th>
                                <th>الكمية</th>
                                <th>سعر الوحدة</th>
                                <th>المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${invoice.items.map(item => `
                                <tr>
                                    <td>${item.description}</td>
                                    <td>${item.quantity}</td>
                                    <td>${formatCurrency(item.unitPrice)}</td>
                                    <td>${formatCurrency(item.total)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>

                    <div class="totals">
                        <p>المجموع الفرعي: ${formatCurrency(invoice.subtotal)}</p>
                        <p>الخصم: ${formatCurrency(invoice.discountAmount)}</p>
                        <p>الضريبة: ${formatCurrency(invoice.taxAmount)}</p>
                        <h3>المجموع الإجمالي: ${formatCurrency(invoice.totalAmount)}</h3>
                    </div>
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        function viewBudget(budgetId) {
            showNotification('ميزة عرض الميزانيات قيد التطوير', 'info');
        }

        function editBudget(budgetId) {
            showNotification('ميزة تعديل الميزانيات قيد التطوير', 'info');
        }

        function analyzeBudget(budgetId) {
            showNotification('ميزة تحليل الميزانيات قيد التطوير', 'info');
        }

        // تنظيف النوافذ المنبثقة عند إغلاقها
        document.addEventListener('hidden.bs.modal', function (event) {
            const modal = event.target;
            if (modal && modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        });
    </script>
</body>
</html>
