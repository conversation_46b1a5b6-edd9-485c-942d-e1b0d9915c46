/**
 * Sales Management Module
 * Handles sales invoices, customer management, payments, and sales reporting
 */

class SalesModule {
    constructor() {
        this.currentView = 'invoices';
        this.selectedInvoices = new Set();
        this.filters = {
            customer: '',
            status: 'all',
            payment_method: 'all',
            date_from: '',
            date_to: '',
            search: ''
        };
        this.pagination = {
            page: 1,
            limit: 20,
            total: 0
        };
        this.currentInvoice = null;
        this.invoiceItems = [];
        this.customers = new Map();
        this.products = new Map();
    }

    /**
     * Initialize sales module
     */
    async initialize() {
        console.log('Initializing Sales Module...');
        await this.loadCustomers();
        await this.loadProducts();
        await this.createSampleData();
    }

    /**
     * Show sales module
     */
    async show(params = {}) {
        this.currentView = params.view || 'invoices';
        await this.render();
        await this.loadData();
        this.setupEventListeners();
    }

    /**
     * Render sales module HTML
     */
    async render() {
        const container = document.getElementById('moduleContainer');
        container.innerHTML = `
            <div class="sales-module">
                <!-- Header -->
                <div class="module-header d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="fas fa-shopping-cart me-2"></i>إدارة المبيعات</h2>
                        <p class="text-muted">إدارة فواتير البيع والعملاء والمدفوعات</p>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="Sales.showNewInvoiceModal()">
                            <i class="fas fa-plus me-1"></i>فاتورة جديدة
                        </button>
                        <button class="btn btn-success" onclick="Sales.showQuickSale()">
                            <i class="fas fa-bolt me-1"></i>بيع سريع
                        </button>
                        <button class="btn btn-info" onclick="Sales.exportSales()">
                            <i class="fas fa-download me-1"></i>تصدير
                        </button>
                    </div>
                </div>

                <!-- Navigation Tabs -->
                <ul class="nav nav-tabs mb-4" id="salesTabs">
                    <li class="nav-item">
                        <a class="nav-link ${this.currentView === 'invoices' ? 'active' : ''}" 
                           href="#" onclick="Sales.switchView('invoices')">
                            <i class="fas fa-file-invoice me-1"></i>الفواتير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link ${this.currentView === 'customers' ? 'active' : ''}" 
                           href="#" onclick="Sales.switchView('customers')">
                            <i class="fas fa-users me-1"></i>العملاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link ${this.currentView === 'payments' ? 'active' : ''}" 
                           href="#" onclick="Sales.switchView('payments')">
                            <i class="fas fa-credit-card me-1"></i>المدفوعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link ${this.currentView === 'reports' ? 'active' : ''}" 
                           href="#" onclick="Sales.switchView('reports')">
                            <i class="fas fa-chart-line me-1"></i>التقارير
                        </a>
                    </li>
                </ul>

                <!-- Content Area -->
                <div id="salesContent">
                    ${await this.renderCurrentView()}
                </div>
            </div>

            <!-- Modals -->
            ${this.renderModals()}
        `;
    }

    /**
     * Render current view content
     */
    async renderCurrentView() {
        switch (this.currentView) {
            case 'invoices':
                return await this.renderInvoicesView();
            case 'customers':
                return await this.renderCustomersView();
            case 'payments':
                return await this.renderPaymentsView();
            case 'reports':
                return await this.renderReportsView();
            default:
                return await this.renderInvoicesView();
        }
    }

    /**
     * Render invoices view
     */
    async renderInvoicesView() {
        return `
            <div class="invoices-view">
                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">العميل</label>
                                <select class="form-select" id="customerFilter">
                                    <option value="">جميع العملاء</option>
                                    ${Array.from(this.customers.values()).map(customer => 
                                        `<option value="${customer.id}">${customer.name}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">الحالة</label>
                                <select class="form-select" id="statusFilter">
                                    <option value="all">جميع الحالات</option>
                                    <option value="paid">مدفوعة</option>
                                    <option value="partial">مدفوعة جزئياً</option>
                                    <option value="unpaid">غير مدفوعة</option>
                                    <option value="cancelled">ملغية</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">طريقة الدفع</label>
                                <select class="form-select" id="paymentMethodFilter">
                                    <option value="all">جميع الطرق</option>
                                    <option value="cash">نقدي</option>
                                    <option value="credit">آجل</option>
                                    <option value="vodafone_cash">فودافون كاش</option>
                                    <option value="instapay">انستاباي</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="dateFromFilter">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="dateToFilter">
                            </div>
                            <div class="col-md-1">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-primary w-100" onclick="Sales.applyFilters()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <input type="text" class="form-control" placeholder="البحث في الفواتير..." 
                                       id="searchInput" onkeyup="Sales.handleSearch(event)">
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="btn-group">
                                    <button class="btn btn-outline-secondary" onclick="Sales.selectAllInvoices()">
                                        تحديد الكل
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="Sales.deleteSelectedInvoices()" 
                                            id="deleteSelectedBtn" disabled>
                                        حذف المحدد
                                    </button>
                                    <button class="btn btn-outline-success" onclick="Sales.printSelectedInvoices()" 
                                            id="printSelectedBtn" disabled>
                                        طباعة المحدد
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6>إجمالي المبيعات اليوم</h6>
                                        <h4 id="todaySales">0 ج.م</h4>
                                    </div>
                                    <i class="fas fa-chart-line fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6>الفواتير المدفوعة</h6>
                                        <h4 id="paidInvoices">0</h4>
                                    </div>
                                    <i class="fas fa-check-circle fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6>الفواتير المعلقة</h6>
                                        <h4 id="pendingInvoices">0</h4>
                                    </div>
                                    <i class="fas fa-clock fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6>متوسط قيمة الفاتورة</h6>
                                        <h4 id="avgInvoice">0 ج.م</h4>
                                    </div>
                                    <i class="fas fa-calculator fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invoices Table -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" class="form-check-input" 
                                                   onchange="Sales.toggleSelectAll(this)">
                                        </th>
                                        <th>رقم الفاتورة</th>
                                        <th>التاريخ</th>
                                        <th>العميل</th>
                                        <th>المبلغ الإجمالي</th>
                                        <th>المدفوع</th>
                                        <th>المتبقي</th>
                                        <th>طريقة الدفع</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="invoicesTableBody">
                                    <!-- Invoices will be loaded here -->
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <nav aria-label="صفحات الفواتير" class="mt-3">
                            <ul class="pagination justify-content-center" id="invoicesPagination">
                                <!-- Pagination will be generated here -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render customers view
     */
    async renderCustomersView() {
        return `
            <div class="customers-view">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4>إدارة العملاء</h4>
                    <button class="btn btn-primary" onclick="Sales.showAddCustomerModal()">
                        <i class="fas fa-plus me-1"></i>عميل جديد
                    </button>
                </div>

                <!-- Customers content will be implemented -->
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5>إدارة العملاء</h5>
                        <p class="text-muted">قريباً - ميزة إدارة العملاء قيد التطوير</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render payments view
     */
    async renderPaymentsView() {
        return `
            <div class="payments-view">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4>إدارة المدفوعات</h4>
                    <button class="btn btn-primary" onclick="Sales.showAddPaymentModal()">
                        <i class="fas fa-plus me-1"></i>دفعة جديدة
                    </button>
                </div>

                <!-- Payments content will be implemented -->
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                        <h5>إدارة المدفوعات</h5>
                        <p class="text-muted">قريباً - ميزة إدارة المدفوعات قيد التطوير</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render reports view
     */
    async renderReportsView() {
        return `
            <div class="reports-view">
                <h4 class="mb-4">تقارير المبيعات</h4>

                <!-- Reports content will be implemented -->
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <h5>تقارير المبيعات</h5>
                        <p class="text-muted">قريباً - ميزة التقارير قيد التطوير</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render modals
     */
    renderModals() {
        return `
            <!-- New Invoice Modal -->
            <div class="modal fade" id="newInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">فاتورة بيع جديدة</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <!-- Invoice form will be implemented -->
                            <div class="text-center py-5">
                                <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                                <h5>فاتورة بيع جديدة</h5>
                                <p class="text-muted">قريباً - نموذج الفاتورة قيد التطوير</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Switch between views
     */
    async switchView(view) {
        this.currentView = view;
        await this.show({ view });
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.filters.search = e.target.value;
                    this.loadData();
                }, 300);
            });
        }

        // Filter change listeners
        ['customerFilter', 'statusFilter', 'paymentMethodFilter', 'dateFromFilter', 'dateToFilter'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => this.applyFilters());
            }
        });
    }

    /**
     * Load data for current view
     */
    async loadData() {
        switch (this.currentView) {
            case 'invoices':
                await this.loadInvoices();
                await this.loadInvoiceStatistics();
                break;
            case 'customers':
                await this.loadCustomersData();
                break;
            case 'payments':
                await this.loadPaymentsData();
                break;
            case 'reports':
                await this.loadReportsData();
                break;
        }
    }

    /**
     * Load invoices
     */
    async loadInvoices() {
        try {
            let invoices = await Database.getAll('sales_invoices');

            // Apply filters
            invoices = this.applyInvoiceFilters(invoices);

            // Apply pagination
            const startIndex = (this.pagination.page - 1) * this.pagination.limit;
            const endIndex = startIndex + this.pagination.limit;
            const paginatedInvoices = invoices.slice(startIndex, endIndex);

            this.pagination.total = invoices.length;

            // Render invoices table
            this.renderInvoicesTable(paginatedInvoices);
            this.renderPagination();

        } catch (error) {
            console.error('Error loading invoices:', error);
            Utils.showToast('خطأ', 'فشل في تحميل الفواتير', 'error');
        }
    }

    /**
     * Apply invoice filters
     */
    applyInvoiceFilters(invoices) {
        return invoices.filter(invoice => {
            // Customer filter
            if (this.filters.customer && invoice.customer_id !== this.filters.customer) {
                return false;
            }

            // Status filter
            if (this.filters.status !== 'all' && invoice.status !== this.filters.status) {
                return false;
            }

            // Payment method filter
            if (this.filters.payment_method !== 'all' && invoice.payment_method !== this.filters.payment_method) {
                return false;
            }

            // Date filters
            if (this.filters.date_from && invoice.date < this.filters.date_from) {
                return false;
            }

            if (this.filters.date_to && invoice.date > this.filters.date_to) {
                return false;
            }

            // Search filter
            if (this.filters.search) {
                const searchTerm = this.filters.search.toLowerCase();
                const customer = this.customers.get(invoice.customer_id);
                const customerName = customer ? customer.name.toLowerCase() : '';

                if (!invoice.invoice_number.toLowerCase().includes(searchTerm) &&
                    !customerName.includes(searchTerm) &&
                    !invoice.notes?.toLowerCase().includes(searchTerm)) {
                    return false;
                }
            }

            return true;
        });
    }

    /**
     * Render invoices table
     */
    renderInvoicesTable(invoices) {
        const tbody = document.getElementById('invoicesTableBody');
        if (!tbody) return;

        tbody.innerHTML = invoices.map(invoice => {
            const customer = this.customers.get(invoice.customer_id);
            const customerName = customer ? customer.name : 'عميل محذوف';

            const statusBadge = this.getStatusBadge(invoice.status);
            const paymentMethodText = this.getPaymentMethodText(invoice.payment_method);

            return `
                <tr data-invoice-id="${invoice.id}">
                    <td>
                        <input type="checkbox" class="form-check-input invoice-checkbox"
                               value="${invoice.id}" onchange="Sales.toggleInvoiceSelection(this)">
                    </td>
                    <td>
                        <strong>${invoice.invoice_number}</strong>
                        ${invoice.has_qr ? '<i class="fas fa-qrcode text-info ms-1" title="يحتوي على QR Code"></i>' : ''}
                    </td>
                    <td>${Utils.formatDate(invoice.date)}</td>
                    <td>${customerName}</td>
                    <td>${Utils.formatCurrency(invoice.total_amount)}</td>
                    <td>${Utils.formatCurrency(invoice.paid_amount)}</td>
                    <td>${Utils.formatCurrency(invoice.total_amount - invoice.paid_amount)}</td>
                    <td>${paymentMethodText}</td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="Sales.viewInvoice('${invoice.id}')" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-success" onclick="Sales.printInvoice('${invoice.id}')" title="طباعة">
                                <i class="fas fa-print"></i>
                            </button>
                            <button class="btn btn-outline-info" onclick="Sales.editInvoice('${invoice.id}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="Sales.deleteInvoice('${invoice.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    }

    /**
     * Get status badge HTML
     */
    getStatusBadge(status) {
        const badges = {
            'paid': '<span class="badge bg-success">مدفوعة</span>',
            'partial': '<span class="badge bg-warning">مدفوعة جزئياً</span>',
            'unpaid': '<span class="badge bg-danger">غير مدفوعة</span>',
            'cancelled': '<span class="badge bg-secondary">ملغية</span>'
        };
        return badges[status] || '<span class="badge bg-secondary">غير محدد</span>';
    }

    /**
     * Get payment method text
     */
    getPaymentMethodText(method) {
        const methods = {
            'cash': 'نقدي',
            'credit': 'آجل',
            'vodafone_cash': 'فودافون كاش',
            'instapay': 'انستاباي',
            'mixed': 'مختلط'
        };
        return methods[method] || 'غير محدد';
    }

    /**
     * Load invoice statistics
     */
    async loadInvoiceStatistics() {
        try {
            const invoices = await Database.getAll('sales_invoices');
            const today = new Date().toISOString().split('T')[0];

            // Today's sales
            const todayInvoices = invoices.filter(inv => inv.date === today);
            const todaySales = todayInvoices.reduce((sum, inv) => sum + inv.total_amount, 0);

            // Paid invoices
            const paidInvoices = invoices.filter(inv => inv.status === 'paid').length;

            // Pending invoices
            const pendingInvoices = invoices.filter(inv => inv.status === 'unpaid' || inv.status === 'partial').length;

            // Average invoice value
            const avgInvoice = invoices.length > 0 ?
                invoices.reduce((sum, inv) => sum + inv.total_amount, 0) / invoices.length : 0;

            // Update UI
            const todaySalesEl = document.getElementById('todaySales');
            const paidInvoicesEl = document.getElementById('paidInvoices');
            const pendingInvoicesEl = document.getElementById('pendingInvoices');
            const avgInvoiceEl = document.getElementById('avgInvoice');

            if (todaySalesEl) todaySalesEl.textContent = Utils.formatCurrency(todaySales);
            if (paidInvoicesEl) paidInvoicesEl.textContent = paidInvoices;
            if (pendingInvoicesEl) pendingInvoicesEl.textContent = pendingInvoices;
            if (avgInvoiceEl) avgInvoiceEl.textContent = Utils.formatCurrency(avgInvoice);

        } catch (error) {
            console.error('Error loading statistics:', error);
        }
    }

    /**
     * Render pagination
     */
    renderPagination() {
        const paginationEl = document.getElementById('invoicesPagination');
        if (!paginationEl) return;

        const totalPages = Math.ceil(this.pagination.total / this.pagination.limit);
        const currentPage = this.pagination.page;

        let paginationHTML = '';

        // Previous button
        paginationHTML += `
            <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="Sales.changePage(${currentPage - 1})">السابق</a>
            </li>
        `;

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                paginationHTML += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="Sales.changePage(${i})">${i}</a>
                    </li>
                `;
            } else if (i === currentPage - 3 || i === currentPage + 3) {
                paginationHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }

        // Next button
        paginationHTML += `
            <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="Sales.changePage(${currentPage + 1})">التالي</a>
            </li>
        `;

        paginationEl.innerHTML = paginationHTML;
    }

    /**
     * Change page
     */
    async changePage(page) {
        if (page < 1 || page > Math.ceil(this.pagination.total / this.pagination.limit)) return;
        this.pagination.page = page;
        await this.loadInvoices();
    }

    /**
     * Apply filters
     */
    async applyFilters() {
        // Get filter values
        const customerFilter = document.getElementById('customerFilter');
        const statusFilter = document.getElementById('statusFilter');
        const paymentMethodFilter = document.getElementById('paymentMethodFilter');
        const dateFromFilter = document.getElementById('dateFromFilter');
        const dateToFilter = document.getElementById('dateToFilter');

        if (customerFilter) this.filters.customer = customerFilter.value;
        if (statusFilter) this.filters.status = statusFilter.value;
        if (paymentMethodFilter) this.filters.payment_method = paymentMethodFilter.value;
        if (dateFromFilter) this.filters.date_from = dateFromFilter.value;
        if (dateToFilter) this.filters.date_to = dateToFilter.value;

        // Reset to first page
        this.pagination.page = 1;

        // Reload data
        await this.loadInvoices();
    }

    /**
     * Handle search
     */
    handleSearch(event) {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.filters.search = event.target.value;
            this.pagination.page = 1;
            this.loadInvoices();
        }, 300);
    }

    /**
     * Toggle invoice selection
     */
    toggleInvoiceSelection(checkbox) {
        if (checkbox.checked) {
            this.selectedInvoices.add(checkbox.value);
        } else {
            this.selectedInvoices.delete(checkbox.value);
        }
        this.updateSelectionButtons();
    }

    /**
     * Toggle select all
     */
    toggleSelectAll(checkbox) {
        const invoiceCheckboxes = document.querySelectorAll('.invoice-checkbox');
        invoiceCheckboxes.forEach(cb => {
            cb.checked = checkbox.checked;
            this.toggleInvoiceSelection(cb);
        });
    }

    /**
     * Select all invoices
     */
    selectAllInvoices() {
        const selectAllCheckbox = document.querySelector('thead input[type="checkbox"]');
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = true;
            this.toggleSelectAll(selectAllCheckbox);
        }
    }

    /**
     * Update selection buttons
     */
    updateSelectionButtons() {
        const deleteBtn = document.getElementById('deleteSelectedBtn');
        const printBtn = document.getElementById('printSelectedBtn');
        const hasSelection = this.selectedInvoices.size > 0;

        if (deleteBtn) deleteBtn.disabled = !hasSelection;
        if (printBtn) printBtn.disabled = !hasSelection;
    }

    /**
     * Load customers
     */
    async loadCustomers() {
        try {
            const customers = await Database.getAll('customers');
            this.customers.clear();
            customers.forEach(customer => {
                this.customers.set(customer.id, customer);
            });
        } catch (error) {
            console.error('Error loading customers:', error);
        }
    }

    /**
     * Load products
     */
    async loadProducts() {
        try {
            const products = await Database.getAll('products');
            this.products.clear();
            products.forEach(product => {
                this.products.set(product.id, product);
            });
        } catch (error) {
            console.error('Error loading products:', error);
        }
    }

    /**
     * Create sample data
     */
    async createSampleData() {
        try {
            // Check if data already exists
            const existingInvoices = await Database.getAll('sales_invoices');
            if (existingInvoices.length > 0) return;

            // Create sample customers
            await this.createSampleCustomers();

            // Create sample invoices
            await this.createSampleInvoices();

            // Reload customers
            await this.loadCustomers();

        } catch (error) {
            console.error('Error creating sample data:', error);
        }
    }

    /**
     * Create sample customers
     */
    async createSampleCustomers() {
        const customers = [
            {
                id: Utils.generateId('cust'),
                name: 'أحمد محمد علي',
                phone: '01012345678',
                email: '<EMAIL>',
                address: 'القاهرة، مصر الجديدة',
                credit_limit: 5000,
                current_balance: 0,
                is_active: true,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: Utils.generateId('cust'),
                name: 'فاطمة حسن محمود',
                phone: '01098765432',
                email: '<EMAIL>',
                address: 'الجيزة، المهندسين',
                credit_limit: 3000,
                current_balance: 500,
                is_active: true,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: Utils.generateId('cust'),
                name: 'محمد أحمد السيد',
                phone: '01155555555',
                email: '<EMAIL>',
                address: 'الإسكندرية، سيدي جابر',
                credit_limit: 10000,
                current_balance: 1200,
                is_active: true,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            }
        ];

        for (const customer of customers) {
            await Database.add('customers', customer);
        }
    }

    /**
     * Create sample invoices
     */
    async createSampleInvoices() {
        const customers = await Database.getAll('customers');
        const products = await Database.getAll('products');

        if (customers.length === 0 || products.length === 0) return;

        const invoices = [];
        const today = new Date();

        // Create invoices for the last 30 days
        for (let i = 0; i < 30; i++) {
            const invoiceDate = new Date(today);
            invoiceDate.setDate(today.getDate() - i);

            // Create 1-5 invoices per day
            const invoicesPerDay = Math.floor(Math.random() * 5) + 1;

            for (let j = 0; j < invoicesPerDay; j++) {
                const customer = customers[Math.floor(Math.random() * customers.length)];
                const invoiceNumber = `INV-${invoiceDate.getFullYear()}${(invoiceDate.getMonth() + 1).toString().padStart(2, '0')}${invoiceDate.getDate().toString().padStart(2, '0')}-${(j + 1).toString().padStart(3, '0')}`;

                // Random number of items (1-5)
                const itemsCount = Math.floor(Math.random() * 5) + 1;
                const items = [];
                let subtotal = 0;

                for (let k = 0; k < itemsCount; k++) {
                    const product = products[Math.floor(Math.random() * products.length)];
                    const quantity = Math.floor(Math.random() * 5) + 1;
                    const price = product.selling_price;
                    const total = quantity * price;

                    items.push({
                        product_id: product.id,
                        product_name: product.name,
                        quantity: quantity,
                        unit_price: price,
                        total_price: total,
                        discount: 0
                    });

                    subtotal += total;
                }

                const discount = Math.floor(Math.random() * 100); // Random discount 0-100
                const tax = Math.floor(subtotal * 0.14); // 14% tax
                const totalAmount = subtotal - discount + tax;

                // Random payment method
                const paymentMethods = ['cash', 'credit', 'vodafone_cash', 'instapay'];
                const paymentMethod = paymentMethods[Math.floor(Math.random() * paymentMethods.length)];

                // Random payment status
                let status, paidAmount;
                if (paymentMethod === 'credit') {
                    const rand = Math.random();
                    if (rand < 0.3) {
                        status = 'unpaid';
                        paidAmount = 0;
                    } else if (rand < 0.6) {
                        status = 'partial';
                        paidAmount = Math.floor(totalAmount * (Math.random() * 0.8 + 0.2));
                    } else {
                        status = 'paid';
                        paidAmount = totalAmount;
                    }
                } else {
                    status = 'paid';
                    paidAmount = totalAmount;
                }

                const invoice = {
                    id: Utils.generateId('inv'),
                    invoice_number: invoiceNumber,
                    customer_id: customer.id,
                    date: invoiceDate.toISOString().split('T')[0],
                    time: `${Math.floor(Math.random() * 12) + 8}:${Math.floor(Math.random() * 60).toString().padStart(2, '0')}`,
                    items: items,
                    subtotal: subtotal,
                    discount: discount,
                    tax: tax,
                    total_amount: totalAmount,
                    paid_amount: paidAmount,
                    payment_method: paymentMethod,
                    status: status,
                    notes: Math.random() > 0.7 ? 'ملاحظات تجريبية' : '',
                    has_qr: Math.random() > 0.5,
                    created_by: 'system',
                    created_at: invoiceDate.toISOString(),
                    updated_at: invoiceDate.toISOString()
                };

                invoices.push(invoice);
            }
        }

        // Save all invoices
        for (const invoice of invoices) {
            await Database.add('sales_invoices', invoice);
        }
    }

    // Placeholder methods for UI actions
    async showNewInvoiceModal() {
        const modal = new bootstrap.Modal(document.getElementById('newInvoiceModal'));
        modal.show();
    }

    async showQuickSale() {
        Utils.showToast('قريباً', 'ميزة البيع السريع قيد التطوير', 'info');
    }

    async exportSales() {
        Utils.showToast('قريباً', 'ميزة تصدير المبيعات قيد التطوير', 'info');
    }

    async viewInvoice(invoiceId) {
        Utils.showToast('قريباً', 'ميزة عرض الفاتورة قيد التطوير', 'info');
    }

    async printInvoice(invoiceId) {
        Utils.showToast('قريباً', 'ميزة طباعة الفاتورة قيد التطوير', 'info');
    }

    async editInvoice(invoiceId) {
        Utils.showToast('قريباً', 'ميزة تعديل الفاتورة قيد التطوير', 'info');
    }

    async deleteInvoice(invoiceId) {
        const confirmed = await Utils.showConfirmDialog(
            'تأكيد الحذف',
            'هل أنت متأكد من حذف هذه الفاتورة؟'
        );

        if (confirmed) {
            try {
                await Database.delete('sales_invoices', invoiceId);
                Utils.showToast('تم الحذف', 'تم حذف الفاتورة بنجاح', 'success');
                await this.loadInvoices();
            } catch (error) {
                Utils.showToast('خطأ', 'فشل في حذف الفاتورة', 'error');
            }
        }
    }

    async deleteSelectedInvoices() {
        if (this.selectedInvoices.size === 0) return;

        const confirmed = await Utils.showConfirmDialog(
            'تأكيد الحذف',
            `هل أنت متأكد من حذف ${this.selectedInvoices.size} فاتورة؟`
        );

        if (confirmed) {
            try {
                for (const invoiceId of this.selectedInvoices) {
                    await Database.delete('sales_invoices', invoiceId);
                }

                this.selectedInvoices.clear();
                Utils.showToast('تم الحذف', 'تم حذف الفواتير المحددة بنجاح', 'success');
                await this.loadInvoices();
            } catch (error) {
                Utils.showToast('خطأ', 'فشل في حذف الفواتير', 'error');
            }
        }
    }

    async printSelectedInvoices() {
        Utils.showToast('قريباً', 'ميزة طباعة الفواتير المحددة قيد التطوير', 'info');
    }

    async showAddCustomerModal() {
        Utils.showToast('قريباً', 'ميزة إضافة عميل قيد التطوير', 'info');
    }

    async showAddPaymentModal() {
        Utils.showToast('قريباً', 'ميزة إضافة دفعة قيد التطوير', 'info');
    }

    async loadCustomersData() {
        // Placeholder for customers data loading
    }

    async loadPaymentsData() {
        // Placeholder for payments data loading
    }

    async loadReportsData() {
        // Placeholder for reports data loading
    }
}

// Create global instance
window.Sales = new SalesModule();
