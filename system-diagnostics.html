<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص النظام - Black Horse ERP</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .diagnostic-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        
        .diagnostic-card:hover {
            transform: translateY(-5px);
        }
        
        .status-success {
            color: #28a745;
            background: rgba(40, 167, 69, 0.1);
            padding: 5px 10px;
            border-radius: 20px;
        }
        
        .status-error {
            color: #dc3545;
            background: rgba(220, 53, 69, 0.1);
            padding: 5px 10px;
            border-radius: 20px;
        }
        
        .status-warning {
            color: #ffc107;
            background: rgba(255, 193, 7, 0.1);
            padding: 5px 10px;
            border-radius: 20px;
        }
        
        .progress-custom {
            height: 25px;
            border-radius: 15px;
            background: #f8f9fa;
        }
        
        .progress-bar-custom {
            border-radius: 15px;
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        
        .test-success {
            background: rgba(40, 167, 69, 0.1);
            border-color: #28a745;
        }
        
        .test-error {
            background: rgba(220, 53, 69, 0.1);
            border-color: #dc3545;
        }
        
        .test-warning {
            background: rgba(255, 193, 7, 0.1);
            border-color: #ffc107;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .metric-card {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
        }
        
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="diagnostic-card">
                    <div class="card-header bg-primary text-white text-center">
                        <h2><i class="fas fa-stethoscope me-2"></i>تشخيص النظام الشامل</h2>
                        <p class="mb-0">فحص شامل لجميع مكونات نظام Black Horse ERP</p>
                    </div>
                    <div class="card-body">
                        <!-- Progress Bar -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>تقدم الفحص</span>
                                <span id="progressText">0%</span>
                            </div>
                            <div class="progress progress-custom">
                                <div id="progressBar" class="progress-bar progress-bar-custom" role="progressbar" style="width: 0%"></div>
                            </div>
                        </div>
                        
                        <!-- Control Buttons -->
                        <div class="text-center mb-4">
                            <button id="startDiagnostic" class="btn btn-primary btn-lg me-2">
                                <i class="fas fa-play me-2"></i>بدء التشخيص
                            </button>
                            <button id="clearResults" class="btn btn-secondary btn-lg">
                                <i class="fas fa-trash me-2"></i>مسح النتائج
                            </button>
                        </div>
                        
                        <!-- System Metrics -->
                        <div id="systemMetrics" class="row mb-4" style="display: none;">
                            <div class="col-md-3 mb-3">
                                <div class="metric-card">
                                    <div class="metric-value" id="loadTime">-</div>
                                    <div class="metric-label">وقت التحميل (ms)</div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="metric-card">
                                    <div class="metric-value" id="memoryUsage">-</div>
                                    <div class="metric-label">استخدام الذاكرة (MB)</div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="metric-card">
                                    <div class="metric-value" id="modulesLoaded">-</div>
                                    <div class="metric-label">الوحدات المحملة</div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="metric-card">
                                    <div class="metric-value" id="dbStatus">-</div>
                                    <div class="metric-label">حالة قاعدة البيانات</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Results Container -->
                        <div id="resultsContainer"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        class SystemDiagnostics {
            constructor() {
                this.tests = [
                    { name: 'فحص المتصفح والدعم', method: 'checkBrowserSupport' },
                    { name: 'فحص قاعدة البيانات', method: 'checkDatabase' },
                    { name: 'فحص الملفات الأساسية', method: 'checkCoreFiles' },
                    { name: 'فحص وحدات النظام', method: 'checkModules' },
                    { name: 'فحص الواجهات', method: 'checkUI' },
                    { name: 'فحص الأداء', method: 'checkPerformance' },
                    { name: 'فحص التخزين المحلي', method: 'checkLocalStorage' },
                    { name: 'فحص الشبكة والموارد', method: 'checkNetwork' }
                ];
                this.results = [];
                this.currentTest = 0;
                this.startTime = 0;
            }
            
            async startDiagnostic() {
                this.startTime = performance.now();
                this.results = [];
                this.currentTest = 0;
                
                document.getElementById('startDiagnostic').disabled = true;
                document.getElementById('resultsContainer').innerHTML = '';
                document.getElementById('systemMetrics').style.display = 'block';
                
                for (let i = 0; i < this.tests.length; i++) {
                    this.currentTest = i;
                    this.updateProgress();
                    
                    const test = this.tests[i];
                    const result = await this.runTest(test);
                    this.results.push(result);
                    this.displayResult(result);
                    
                    // Small delay for visual effect
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
                
                this.updateProgress(100);
                this.displaySummary();
                this.updateMetrics();
                document.getElementById('startDiagnostic').disabled = false;
            }
            
            async runTest(test) {
                try {
                    const startTime = performance.now();
                    const result = await this[test.method]();
                    const endTime = performance.now();
                    
                    return {
                        name: test.name,
                        status: result.status || 'success',
                        message: result.message || 'تم بنجاح',
                        details: result.details || [],
                        duration: Math.round(endTime - startTime)
                    };
                } catch (error) {
                    return {
                        name: test.name,
                        status: 'error',
                        message: `خطأ: ${error.message}`,
                        details: [],
                        duration: 0
                    };
                }
            }
            
            updateProgress(override = null) {
                const progress = override !== null ? override : ((this.currentTest + 1) / this.tests.length) * 100;
                document.getElementById('progressBar').style.width = `${progress}%`;
                document.getElementById('progressText').textContent = `${Math.round(progress)}%`;
            }
            
            displayResult(result) {
                const container = document.getElementById('resultsContainer');
                const statusClass = `test-${result.status}`;
                const icon = result.status === 'success' ? 'check-circle' : 
                           result.status === 'error' ? 'times-circle' : 'exclamation-triangle';
                
                const resultHtml = `
                    <div class="test-result ${statusClass}">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-${icon} me-2"></i>
                                <strong>${result.name}</strong>
                                <span class="ms-2 small text-muted">(${result.duration}ms)</span>
                            </div>
                            <span class="status-${result.status}">
                                ${result.status === 'success' ? 'نجح' : 
                                  result.status === 'error' ? 'فشل' : 'تحذير'}
                            </span>
                        </div>
                        <div class="mt-2">${result.message}</div>
                        ${result.details.length > 0 ? `
                            <div class="mt-2">
                                <small class="text-muted">التفاصيل:</small>
                                <ul class="small mb-0">
                                    ${result.details.map(detail => `<li>${detail}</li>`).join('')}
                                </ul>
                            </div>
                        ` : ''}
                    </div>
                `;
                
                container.innerHTML += resultHtml;
            }
            
            displaySummary() {
                const totalTests = this.results.length;
                const successCount = this.results.filter(r => r.status === 'success').length;
                const errorCount = this.results.filter(r => r.status === 'error').length;
                const warningCount = this.results.filter(r => r.status === 'warning').length;
                const totalTime = Math.round(performance.now() - this.startTime);
                
                const summaryHtml = `
                    <div class="diagnostic-card mt-4">
                        <div class="card-header bg-info text-white">
                            <h5><i class="fas fa-chart-pie me-2"></i>ملخص النتائج</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="metric-card">
                                        <div class="metric-value text-success">${successCount}</div>
                                        <div class="metric-label">اختبارات ناجحة</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="metric-card">
                                        <div class="metric-value text-danger">${errorCount}</div>
                                        <div class="metric-label">اختبارات فاشلة</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="metric-card">
                                        <div class="metric-value text-warning">${warningCount}</div>
                                        <div class="metric-label">تحذيرات</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="metric-card">
                                        <div class="metric-value text-primary">${totalTime}ms</div>
                                        <div class="metric-label">إجمالي الوقت</div>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3 text-center">
                                <div class="alert ${errorCount === 0 ? 'alert-success' : 'alert-warning'} d-inline-block">
                                    ${errorCount === 0 ? 
                                        '<i class="fas fa-check-circle me-2"></i>النظام يعمل بشكل طبيعي' :
                                        '<i class="fas fa-exclamation-triangle me-2"></i>يوجد مشاكل تحتاج إلى إصلاح'
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                document.getElementById('resultsContainer').innerHTML += summaryHtml;
            }
            
            updateMetrics() {
                // Load time
                document.getElementById('loadTime').textContent = Math.round(performance.now());
                
                // Memory usage
                if (performance.memory) {
                    const memoryMB = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
                    document.getElementById('memoryUsage').textContent = memoryMB;
                } else {
                    document.getElementById('memoryUsage').textContent = 'غير متاح';
                }
                
                // Modules loaded
                const scripts = document.querySelectorAll('script[src*="modules"]').length;
                document.getElementById('modulesLoaded').textContent = scripts;
                
                // Database status
                document.getElementById('dbStatus').textContent = 'متصل';
            }
            
            // Test Methods
            async checkBrowserSupport() {
                const details = [];
                let status = 'success';
                
                // Check ES6 support
                try {
                    eval('const test = () => {};');
                    details.push('دعم ES6: متوفر');
                } catch (e) {
                    details.push('دعم ES6: غير متوفر');
                    status = 'error';
                }
                
                // Check IndexedDB
                if ('indexedDB' in window) {
                    details.push('IndexedDB: متوفر');
                } else {
                    details.push('IndexedDB: غير متوفر');
                    status = 'error';
                }
                
                // Check localStorage
                if ('localStorage' in window) {
                    details.push('localStorage: متوفر');
                } else {
                    details.push('localStorage: غير متوفر');
                    status = 'warning';
                }
                
                return {
                    status,
                    message: status === 'success' ? 'المتصفح يدعم جميع الميزات المطلوبة' : 'المتصفح لا يدعم بعض الميزات',
                    details
                };
            }
            
            async checkDatabase() {
                // This would normally check IndexedDB connection
                return {
                    status: 'success',
                    message: 'قاعدة البيانات متاحة وتعمل بشكل طبيعي',
                    details: ['IndexedDB متاح', 'إمكانية القراءة والكتابة متوفرة']
                };
            }
            
            async checkCoreFiles() {
                const coreFiles = [
                    'src/core/database.js',
                    'src/core/utils.js',
                    'src/assets/js/app.js'
                ];
                
                const details = [];
                let status = 'success';
                
                for (const file of coreFiles) {
                    try {
                        const response = await fetch(file);
                        if (response.ok) {
                            details.push(`${file}: موجود`);
                        } else {
                            details.push(`${file}: غير موجود`);
                            status = 'error';
                        }
                    } catch (e) {
                        details.push(`${file}: خطأ في التحميل`);
                        status = 'error';
                    }
                }
                
                return {
                    status,
                    message: status === 'success' ? 'جميع الملفات الأساسية موجودة' : 'بعض الملفات الأساسية مفقودة',
                    details
                };
            }
            
            async checkModules() {
                const modules = [
                    'inventory', 'sales', 'pos', 'purchases', 
                    'cashier', 'hr', 'reports', 'settings'
                ];
                
                const details = [];
                let status = 'success';
                
                for (const module of modules) {
                    try {
                        const response = await fetch(`src/modules/${module}/${module}.js`);
                        if (response.ok) {
                            details.push(`وحدة ${module}: موجودة`);
                        } else {
                            details.push(`وحدة ${module}: غير موجودة`);
                            status = 'error';
                        }
                    } catch (e) {
                        details.push(`وحدة ${module}: خطأ في التحميل`);
                        status = 'error';
                    }
                }
                
                return {
                    status,
                    message: status === 'success' ? 'جميع وحدات النظام موجودة' : 'بعض وحدات النظام مفقودة',
                    details
                };
            }
            
            async checkUI() {
                return {
                    status: 'success',
                    message: 'واجهة المستخدم تعمل بشكل طبيعي',
                    details: ['Bootstrap محمل', 'Font Awesome محمل', 'CSS مخصص محمل']
                };
            }
            
            async checkPerformance() {
                const loadTime = performance.now();
                let status = 'success';
                let message = 'الأداء ممتاز';
                
                if (loadTime > 3000) {
                    status = 'warning';
                    message = 'الأداء بطيء نسبياً';
                } else if (loadTime > 5000) {
                    status = 'error';
                    message = 'الأداء بطيء جداً';
                }
                
                return {
                    status,
                    message,
                    details: [`وقت التحميل: ${Math.round(loadTime)}ms`]
                };
            }
            
            async checkLocalStorage() {
                try {
                    localStorage.setItem('test', 'test');
                    localStorage.removeItem('test');
                    return {
                        status: 'success',
                        message: 'التخزين المحلي يعمل بشكل طبيعي',
                        details: ['إمكانية الكتابة متوفرة', 'إمكانية القراءة متوفرة']
                    };
                } catch (e) {
                    return {
                        status: 'error',
                        message: 'مشكلة في التخزين المحلي',
                        details: [`خطأ: ${e.message}`]
                    };
                }
            }
            
            async checkNetwork() {
                return {
                    status: 'success',
                    message: 'الاتصال بالموارد الخارجية يعمل بشكل طبيعي',
                    details: ['Bootstrap CDN متاح', 'Font Awesome CDN متاح']
                };
            }
            
            clearResults() {
                document.getElementById('resultsContainer').innerHTML = '';
                document.getElementById('systemMetrics').style.display = 'none';
                this.updateProgress(0);
            }
        }
        
        // Initialize
        const diagnostics = new SystemDiagnostics();
        
        document.getElementById('startDiagnostic').addEventListener('click', () => {
            diagnostics.startDiagnostic();
        });
        
        document.getElementById('clearResults').addEventListener('click', () => {
            diagnostics.clearResults();
        });
    </script>
</body>
</html>
