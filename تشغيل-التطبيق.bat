@echo off
chcp 65001 >nul
title Black Horse ERP - تشغيل التطبيق

echo.
echo ========================================
echo    🐎 Black Horse ERP System
echo    نظام إدارة موارد المؤسسات
echo ========================================
echo.

echo جاري تشغيل التطبيق...
echo.

REM التحقق من وجود Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ خطأ: Node.js غير مثبت على النظام
    echo يرجى تثبيت Node.js من: https://nodejs.org
    echo.
    pause
    exit /b 1
)

REM التحقق من وجود ملفات التطبيق
if not exist "package.json" (
    echo ❌ خطأ: ملفات التطبيق غير موجودة
    echo تأكد من وجود جميع ملفات النظام
    echo.
    pause
    exit /b 1
)

REM التحقق من تثبيت التبعيات
if not exist "node_modules" (
    echo 📦 جاري تثبيت التبعيات المطلوبة...
    npm install
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ خطأ في تثبيت التبعيات
        pause
        exit /b 1
    )
)

echo ✅ جاري تشغيل Black Horse ERP...
echo.
echo 💡 نصائح:
echo    - استخدم Ctrl+C لإيقاف التطبيق
echo    - لا تغلق هذه النافذة أثناء تشغيل التطبيق
echo.

REM تشغيل التطبيق
npm start

echo.
echo تم إغلاق التطبيق
pause
