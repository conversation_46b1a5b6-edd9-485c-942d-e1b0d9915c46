#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Black Horse ERP - ملف تشغيل النظام الرئيسي
نظام إدارة الأعمال المتكامل

الاستخدام:
    python run.py
    أو
    python run.py --port 3000
"""

import http.server
import socketserver
import webbrowser
import os
import sys
import argparse
import socket
from urllib.parse import urlparse
import threading
import time

class BlackHorseERPHandler(http.server.SimpleHTTPRequestHandler):
    """معالج HTTP مخصص لنظام Black Horse ERP"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def end_headers(self):
        """إضافة headers للسماح بـ CORS وتحسين الأداء"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()
    
    def do_OPTIONS(self):
        """معالجة طلبات OPTIONS للـ CORS"""
        self.send_response(200)
        self.end_headers()
    
    def do_GET(self):
        """معالجة طلبات GET مع إعادة توجيه ذكية"""
        # إعادة توجيه الجذر إلى index.html
        if self.path == '/' or self.path == '':
            self.path = '/index.html'
        
        # التحقق من وجود الملف
        file_path = self.path.lstrip('/')
        if not os.path.exists(file_path) and not file_path.endswith('/'):
            # إذا كان الملف غير موجود، جرب إضافة .html
            if not file_path.endswith('.html'):
                html_path = file_path + '.html'
                if os.path.exists(html_path):
                    self.path = '/' + html_path
        
        return super().do_GET()
    
    def log_message(self, format, *args):
        """تسجيل الرسائل مع الوقت والتفاصيل"""
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {format % args}")

def find_free_port(start_port=8080, max_attempts=20):
    """البحث عن منفذ متاح"""
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    return None

def check_system_files():
    """فحص الملفات الأساسية للنظام"""
    required_files = [
        'index.html',
        'src/core/app.js',
        'src/core/database.js',
        'src/core/utils.js'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    return missing_files

def create_missing_index():
    """إنشاء ملف index.html أساسي إذا كان مفقوداً"""
    if not os.path.exists('index.html'):
        print("⚠️  ملف index.html مفقود - سيتم إنشاؤه...")
        
        index_content = '''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Black Horse ERP - نظام إدارة الأعمال</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 50px auto;
            max-width: 1200px;
            padding: 40px;
        }
        .logo {
            text-align: center;
            margin-bottom: 40px;
        }
        .logo h1 {
            color: #2c3e50;
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .logo p {
            color: #7f8c8d;
            font-size: 1.2rem;
        }
        .module-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border: none;
            border-radius: 15px;
            color: white;
            cursor: pointer;
            margin-bottom: 20px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }
        .module-card i {
            font-size: 3rem;
            margin-bottom: 15px;
        }
        .module-card h4 {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }
        .status-indicator {
            background: #27ae60;
            border-radius: 50%;
            display: inline-block;
            height: 12px;
            margin-left: 10px;
            width: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="main-container">
            <div class="logo">
                <h1><i class="fas fa-horse-head"></i> Black Horse ERP</h1>
                <p>نظام إدارة الأعمال المتكامل</p>
                <span class="status-indicator"></span>
                <small class="text-success">النظام يعمل بنجاح</small>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="module-card" onclick="loadModule('inventory')">
                        <i class="fas fa-boxes"></i>
                        <h4>إدارة المخزون</h4>
                        <p>تتبع المنتجات والمخزون</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="module-card" onclick="loadModule('sales')">
                        <i class="fas fa-chart-line"></i>
                        <h4>المبيعات</h4>
                        <p>إدارة المبيعات والعملاء</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="module-card" onclick="loadModule('pos')">
                        <i class="fas fa-cash-register"></i>
                        <h4>نقاط البيع</h4>
                        <p>نظام POS متطور</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="module-card" onclick="loadModule('purchases')">
                        <i class="fas fa-shopping-cart"></i>
                        <h4>المشتريات</h4>
                        <p>إدارة المشتريات والموردين</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="module-card" onclick="loadModule('cashier')">
                        <i class="fas fa-user-tie"></i>
                        <h4>إدارة الكاشير</h4>
                        <p>مناوبات وإدارة الخزنة</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="module-card" onclick="loadModule('hr')">
                        <i class="fas fa-users"></i>
                        <h4>الموارد البشرية</h4>
                        <p>إدارة الموظفين والرواتب</p>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <a href="system-health-check.html" class="btn btn-outline-primary btn-lg me-3">
                    <i class="fas fa-stethoscope"></i> فحص صحة النظام
                </a>
                <a href="setup-wizard.html" class="btn btn-outline-success btn-lg">
                    <i class="fas fa-cog"></i> معالج الإعداد
                </a>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function loadModule(module) {
            const moduleUrl = `src/modules/${module}/${module}.html`;
            window.open(moduleUrl, '_blank');
        }
        
        // تحميل النظام
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🐎 Black Horse ERP - تم تحميل النظام بنجاح');
            
            // فحص الاتصال
            fetch('src/core/app.js')
                .then(() => {
                    console.log('✅ الاتصال بالملفات يعمل بنجاح');
                })
                .catch(() => {
                    console.warn('⚠️ تحذير: قد تحتاج لتشغيل خادم محلي');
                });
        });
    </script>
</body>
</html>'''
        
        with open('index.html', 'w', encoding='utf-8') as f:
            f.write(index_content)
        
        print("✅ تم إنشاء ملف index.html بنجاح")

def open_browser_delayed(url, delay=2):
    """فتح المتصفح بعد تأخير"""
    def open_browser():
        time.sleep(delay)
        try:
            webbrowser.open(url)
            print(f"🌐 تم فتح المتصفح على: {url}")
        except Exception as e:
            print(f"⚠️  لا يمكن فتح المتصفح تلقائياً: {e}")
            print(f"📋 افتح المتصفح يدوياً على: {url}")
    
    thread = threading.Thread(target=open_browser)
    thread.daemon = True
    thread.start()

def main():
    """الدالة الرئيسية لتشغيل النظام"""
    parser = argparse.ArgumentParser(description='Black Horse ERP - نظام إدارة الأعمال')
    parser.add_argument('--port', '-p', type=int, default=8080, help='رقم المنفذ (افتراضي: 8080)')
    parser.add_argument('--no-browser', action='store_true', help='عدم فتح المتصفح تلقائياً')
    args = parser.parse_args()
    
    print("🐎 Black Horse ERP - نظام إدارة الأعمال المتكامل")
    print("=" * 50)
    
    # فحص الملفات الأساسية
    missing_files = check_system_files()
    if missing_files:
        print("⚠️  ملفات مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
    
    # إنشاء ملف index.html إذا كان مفقوداً
    create_missing_index()
    
    # البحث عن منفذ متاح
    port = find_free_port(args.port)
    if not port:
        print(f"❌ لا يمكن العثور على منفذ متاح بدءاً من {args.port}")
        sys.exit(1)
    
    # إعداد الخادم
    try:
        with socketserver.TCPServer(("", port), BlackHorseERPHandler) as httpd:
            server_url = f"http://localhost:{port}"
            
            print(f"🚀 تم تشغيل الخادم بنجاح!")
            print(f"📍 العنوان: {server_url}")
            print(f"📁 المجلد: {os.getcwd()}")
            print("=" * 50)
            print("🔗 الروابط المهمة:")
            print(f"   🏠 الصفحة الرئيسية: {server_url}")
            print(f"   🔍 فحص الصحة: {server_url}/system-health-check.html")
            print(f"   ⚙️  معالج الإعداد: {server_url}/setup-wizard.html")
            print("=" * 50)
            print("⌨️  اضغط Ctrl+C للإيقاف")
            
            # فتح المتصفح
            if not args.no_browser:
                open_browser_delayed(server_url)
            
            # تشغيل الخادم
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
