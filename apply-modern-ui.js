/**
 * Black Horse POS - Modern UI Application Script
 * تطبيق التصميم الحديث على الواجهة الموجودة
 * Developer: Augment Agent
 */

const ModernUIApplicator = {
    // تهيئة النظام
    init: function() {
        try {
            console.log('🎨 Applying modern UI design...');
            
            // تطبيق الأنماط الحديثة
            this.applyModernStyles();
            
            // تحديث الهيكل العام
            this.updateMainStructure();
            
            // تحديث الشريط العلوي
            this.updateHeader();
            
            // تحديث الأزرار
            this.updateButtons();
            
            // تحديث البطاقات
            this.updateCards();
            
            // تحديث النماذج
            this.updateForms();
            
            // تحديث الجداول
            this.updateTables();
            
            // تحديث النوافذ المنبثقة
            this.updateModals();
            
            // إضافة الإحصائيات الحديثة
            this.addModernStats();
            
            // تطبيق الرسوم المتحركة
            this.applyAnimations();
            
            console.log('✅ Modern UI applied successfully');
        } catch (error) {
            console.error('❌ Error applying modern UI:', error);
        }
    },

    // تطبيق الأنماط الحديثة
    applyModernStyles: function() {
        try {
            // إنشاء عنصر الأنماط
            const styleLink = document.createElement('link');
            styleLink.rel = 'stylesheet';
            styleLink.href = 'modern-ui-design.css';
            document.head.appendChild(styleLink);
            
            // إضافة الخطوط الحديثة
            const fontLink = document.createElement('link');
            fontLink.rel = 'stylesheet';
            fontLink.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap';
            document.head.appendChild(fontLink);
            
            console.log('✅ Modern styles applied');
        } catch (error) {
            console.error('❌ Error applying styles:', error);
        }
    },

    // تحديث الهيكل العام
    updateMainStructure: function() {
        try {
            // إضافة الحاوي الرئيسي الحديث
            const body = document.body;
            const existingContent = body.innerHTML;
            
            body.innerHTML = `
                <div class="main-container fade-in">
                    ${existingContent}
                </div>
            `;
            
            console.log('✅ Main structure updated');
        } catch (error) {
            console.error('❌ Error updating main structure:', error);
        }
    },

    // تحديث الشريط العلوي
    updateHeader: function() {
        try {
            // البحث عن الشريط العلوي الموجود
            let header = document.querySelector('header') || document.querySelector('.header') || document.querySelector('#header');
            
            if (!header) {
                // إنشاء شريط علوي جديد
                header = document.createElement('header');
                document.querySelector('.main-container').prepend(header);
            }
            
            // تطبيق التصميم الحديث
            header.className = 'modern-header';
            header.innerHTML = `
                <div class="header-content">
                    <div class="logo-section">
                        <div class="logo-icon">🐎</div>
                        <div class="logo-text">Black Horse POS</div>
                    </div>
                    <div class="header-actions">
                        <button class="modern-btn btn-ghost" onclick="showUserProfile()">
                            👤 الملف الشخصي
                        </button>
                        <button class="modern-btn btn-ghost" onclick="showNotifications()">
                            🔔 التنبيهات
                        </button>
                        <button class="modern-btn btn-ghost" onclick="showSettings()">
                            ⚙️ الإعدادات
                        </button>
                    </div>
                </div>
            `;
            
            console.log('✅ Header updated');
        } catch (error) {
            console.error('❌ Error updating header:', error);
        }
    },

    // تحديث الأزرار
    updateButtons: function() {
        try {
            const buttons = document.querySelectorAll('button, .btn, input[type="button"], input[type="submit"]');
            
            buttons.forEach(button => {
                // إضافة الفئات الحديثة
                if (!button.classList.contains('modern-btn')) {
                    button.classList.add('modern-btn');
                    
                    // تحديد نوع الزر بناءً على المحتوى أو الفئة
                    if (button.textContent.includes('حفظ') || button.textContent.includes('إضافة') || button.classList.contains('primary')) {
                        button.classList.add('btn-primary');
                    } else if (button.textContent.includes('تصدير') || button.textContent.includes('طباعة') || button.classList.contains('success')) {
                        button.classList.add('btn-secondary');
                    } else if (button.textContent.includes('حذف') || button.classList.contains('danger')) {
                        button.classList.add('btn-accent');
                    } else {
                        button.classList.add('btn-outline');
                    }
                }
            });
            
            console.log(`✅ Updated ${buttons.length} buttons`);
        } catch (error) {
            console.error('❌ Error updating buttons:', error);
        }
    },

    // تحديث البطاقات
    updateCards: function() {
        try {
            // البحث عن العناصر التي يمكن تحويلها إلى بطاقات
            const cardSelectors = ['.card', '.panel', '.section', '.tab-content > div', '.dashboard-item'];
            
            cardSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    if (!element.classList.contains('modern-card')) {
                        element.classList.add('modern-card');
                        
                        // إضافة رأس البطاقة إذا لم يكن موجوداً
                        const title = element.querySelector('h1, h2, h3, h4, h5, h6');
                        if (title && !element.querySelector('.card-header')) {
                            const cardHeader = document.createElement('div');
                            cardHeader.className = 'card-header';
                            cardHeader.innerHTML = `
                                <div class="card-title">
                                    <div class="card-icon">📊</div>
                                    ${title.textContent}
                                </div>
                            `;
                            element.prepend(cardHeader);
                            title.style.display = 'none';
                        }
                    }
                });
            });
            
            console.log('✅ Cards updated');
        } catch (error) {
            console.error('❌ Error updating cards:', error);
        }
    },

    // تحديث النماذج
    updateForms: function() {
        try {
            const forms = document.querySelectorAll('form');
            
            forms.forEach(form => {
                form.classList.add('modern-form');
                
                // تحديث حقول الإدخال
                const inputs = form.querySelectorAll('input, select, textarea');
                inputs.forEach(input => {
                    if (input.type === 'text' || input.type === 'email' || input.type === 'password' || input.type === 'number' || input.tagName === 'TEXTAREA') {
                        input.classList.add('form-input');
                    } else if (input.tagName === 'SELECT') {
                        input.classList.add('form-select');
                    }
                    
                    // إضافة تسميات إذا لم تكن موجودة
                    if (!input.previousElementSibling || input.previousElementSibling.tagName !== 'LABEL') {
                        const label = document.createElement('label');
                        label.className = 'form-label';
                        label.textContent = input.placeholder || input.name || 'حقل';
                        input.parentNode.insertBefore(label, input);
                    }
                    
                    // تجميع التسمية والحقل
                    if (!input.parentElement.classList.contains('form-group')) {
                        const formGroup = document.createElement('div');
                        formGroup.className = 'form-group';
                        input.parentNode.insertBefore(formGroup, input.previousElementSibling);
                        formGroup.appendChild(input.previousElementSibling); // التسمية
                        formGroup.appendChild(input);
                    }
                });
            });
            
            console.log(`✅ Updated ${forms.length} forms`);
        } catch (error) {
            console.error('❌ Error updating forms:', error);
        }
    },

    // تحديث الجداول
    updateTables: function() {
        try {
            const tables = document.querySelectorAll('table');
            
            tables.forEach(table => {
                table.classList.add('modern-table');
                
                // إضافة حاوي للجدول للتمرير الأفقي
                if (!table.parentElement.classList.contains('table-container')) {
                    const container = document.createElement('div');
                    container.className = 'table-container';
                    container.style.overflowX = 'auto';
                    table.parentNode.insertBefore(container, table);
                    container.appendChild(table);
                }
            });
            
            console.log(`✅ Updated ${tables.length} tables`);
        } catch (error) {
            console.error('❌ Error updating tables:', error);
        }
    },

    // تحديث النوافذ المنبثقة
    updateModals: function() {
        try {
            const modals = document.querySelectorAll('.modal, [id*="modal"], [id*="Modal"]');
            
            modals.forEach(modal => {
                modal.classList.add('modern-modal');
                
                // البحث عن محتوى النافذة
                let content = modal.querySelector('.modal-content') || modal.querySelector('.modal-dialog') || modal.firstElementChild;
                
                if (content) {
                    content.classList.add('modal-content');
                    
                    // إضافة رأس النافذة إذا لم يكن موجوداً
                    if (!content.querySelector('.modal-header')) {
                        const header = document.createElement('div');
                        header.className = 'modal-header';
                        header.innerHTML = `
                            <div class="modal-title">نافذة</div>
                            <button class="modal-close" onclick="closeModal('${modal.id}')">&times;</button>
                        `;
                        content.prepend(header);
                    }
                    
                    // تجميع المحتوى في جسم النافذة
                    const existingContent = Array.from(content.children).filter(child => 
                        !child.classList.contains('modal-header') && !child.classList.contains('modal-footer')
                    );
                    
                    if (existingContent.length > 0 && !content.querySelector('.modal-body')) {
                        const body = document.createElement('div');
                        body.className = 'modal-body';
                        existingContent.forEach(child => body.appendChild(child));
                        content.appendChild(body);
                    }
                }
            });
            
            console.log(`✅ Updated ${modals.length} modals`);
        } catch (error) {
            console.error('❌ Error updating modals:', error);
        }
    },

    // إضافة الإحصائيات الحديثة
    addModernStats: function() {
        try {
            // البحث عن منطقة لوحة التحكم
            let dashboard = document.querySelector('.dashboard') || document.querySelector('#dashboard') || document.querySelector('.main-content');
            
            if (dashboard) {
                // إنشاء شبكة الإحصائيات
                const statsGrid = document.createElement('div');
                statsGrid.className = 'stats-grid fade-in';
                statsGrid.innerHTML = `
                    <div class="stat-card">
                        <div class="stat-icon">📦</div>
                        <div class="stat-value" id="totalProducts">0</div>
                        <div class="stat-label">إجمالي المنتجات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">💰</div>
                        <div class="stat-value" id="totalSales">0</div>
                        <div class="stat-label">إجمالي المبيعات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">👥</div>
                        <div class="stat-value" id="totalCustomers">0</div>
                        <div class="stat-label">إجمالي العملاء</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">📈</div>
                        <div class="stat-value" id="todayProfit">0</div>
                        <div class="stat-label">ربح اليوم</div>
                    </div>
                `;
                
                dashboard.prepend(statsGrid);
                
                // تحديث الإحصائيات
                this.updateStatsValues();
            }
            
            console.log('✅ Modern stats added');
        } catch (error) {
            console.error('❌ Error adding modern stats:', error);
        }
    },

    // تحديث قيم الإحصائيات
    updateStatsValues: function() {
        try {
            const products = JSON.parse(localStorage.getItem('products') || '[]');
            const sales = JSON.parse(localStorage.getItem('sales') || '[]');
            const customers = JSON.parse(localStorage.getItem('customers') || '[]');
            
            // تحديث القيم
            const totalProductsEl = document.getElementById('totalProducts');
            if (totalProductsEl) totalProductsEl.textContent = products.length;
            
            const totalSalesEl = document.getElementById('totalSales');
            if (totalSalesEl) {
                const totalSalesValue = sales.reduce((sum, sale) => sum + (sale.total || 0), 0);
                totalSalesEl.textContent = totalSalesValue.toFixed(0) + ' ج.م';
            }
            
            const totalCustomersEl = document.getElementById('totalCustomers');
            if (totalCustomersEl) totalCustomersEl.textContent = customers.length;
            
            const todayProfitEl = document.getElementById('todayProfit');
            if (todayProfitEl) {
                const today = new Date().toISOString().split('T')[0];
                const todaySales = sales.filter(sale => sale.date?.startsWith(today));
                const todayProfit = todaySales.reduce((sum, sale) => sum + (sale.total || 0), 0);
                todayProfitEl.textContent = todayProfit.toFixed(0) + ' ج.م';
            }
            
        } catch (error) {
            console.error('❌ Error updating stats values:', error);
        }
    },

    // تطبيق الرسوم المتحركة
    applyAnimations: function() {
        try {
            // إضافة رسوم متحركة للعناصر عند الظهور
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('fade-in');
                    }
                });
            });
            
            // مراقبة البطاقات والجداول
            document.querySelectorAll('.modern-card, .modern-table, .stat-card').forEach(el => {
                observer.observe(el);
            });
            
            console.log('✅ Animations applied');
        } catch (error) {
            console.error('❌ Error applying animations:', error);
        }
    },

    // تحديث الإحصائيات بشكل دوري
    startStatsUpdater: function() {
        setInterval(() => {
            this.updateStatsValues();
        }, 30000); // كل 30 ثانية
    }
};

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تأخير قصير للتأكد من تحميل جميع العناصر
    setTimeout(() => {
        ModernUIApplicator.init();
        ModernUIApplicator.startStatsUpdater();
    }, 500);
});

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.ModernUIApplicator = ModernUIApplicator;
}

console.log('✅ Modern UI Applicator loaded successfully');
