<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المشاريع - Black Horse ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/custom.css" rel="stylesheet">
    <style>
        .project-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 4px solid #007bff;
        }
        
        .project-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        
        .project-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px 12px 0 0;
        }
        
        .progress-ring {
            width: 60px;
            height: 60px;
            position: relative;
        }
        
        .progress-ring svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }
        
        .progress-ring circle {
            fill: none;
            stroke-width: 4;
        }
        
        .progress-ring .bg {
            stroke: rgba(255,255,255,0.3);
        }
        
        .progress-ring .progress {
            stroke: white;
            stroke-linecap: round;
            transition: stroke-dasharray 0.3s ease;
        }
        
        .kanban-board {
            display: flex;
            gap: 20px;
            overflow-x: auto;
            padding: 20px 0;
            min-height: 600px;
        }
        
        .kanban-column {
            background: #f8f9fa;
            border-radius: 8px;
            min-width: 300px;
            max-width: 300px;
            padding: 15px;
        }
        
        .kanban-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #dee2e6;
        }
        
        .task-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            border-right: 3px solid #007bff;
        }
        
        .task-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .task-card.priority-high {
            border-right-color: #dc3545;
        }
        
        .task-card.priority-medium {
            border-right-color: #ffc107;
        }
        
        .task-card.priority-low {
            border-right-color: #28a745;
        }
        
        .task-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }
        
        .task-meta {
            display: flex;
            justify-content: between;
            align-items: center;
            font-size: 0.85rem;
            color: #6c757d;
        }
        
        .task-assignee {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #007bff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .milestone-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
        }
        
        .milestone-date {
            background: rgba(255,255,255,0.2);
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.85rem;
            display: inline-block;
        }
        
        .time-entry {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
        }
        
        .stats-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: #007bff;
        }
        
        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .view-tabs {
            background: white;
            border-radius: 8px;
            padding: 5px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .view-tab {
            padding: 10px 20px;
            border: none;
            background: none;
            border-radius: 6px;
            transition: all 0.3s ease;
            color: #6c757d;
        }
        
        .view-tab.active {
            background: #007bff;
            color: white;
        }
        
        .project-sidebar {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .team-member {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .team-member:last-child {
            border-bottom: none;
        }
        
        .member-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #007bff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-left: 12px;
        }
        
        .calendar-view {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .calendar-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background: #dee2e6;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .calendar-day {
            background: white;
            padding: 10px;
            min-height: 100px;
            position: relative;
        }
        
        .calendar-day.other-month {
            background: #f8f9fa;
            color: #6c757d;
        }
        
        .calendar-event {
            background: #007bff;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.75rem;
            margin-bottom: 2px;
            cursor: pointer;
        }
        
        .gantt-chart {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow-x: auto;
        }
        
        .gantt-timeline {
            display: flex;
            border-bottom: 2px solid #dee2e6;
            margin-bottom: 20px;
        }
        
        .gantt-month {
            min-width: 120px;
            padding: 10px;
            text-align: center;
            border-left: 1px solid #dee2e6;
            font-weight: 600;
        }
        
        .gantt-task {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            min-height: 40px;
        }
        
        .gantt-task-name {
            min-width: 200px;
            padding: 10px;
            font-weight: 500;
        }
        
        .gantt-bar {
            background: #007bff;
            height: 20px;
            border-radius: 10px;
            position: relative;
            margin: 0 5px;
        }
        
        .gantt-progress {
            background: #28a745;
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }
        
        .filter-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .quick-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .action-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            background: #007bff;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .action-btn:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        
        .action-btn.secondary {
            background: #6c757d;
        }
        
        .action-btn.success {
            background: #28a745;
        }
        
        .drag-placeholder {
            background: #e9ecef;
            border: 2px dashed #adb5bd;
            border-radius: 8px;
            height: 60px;
            margin-bottom: 10px;
        }
        
        .sortable-ghost {
            opacity: 0.5;
        }
        
        .sortable-chosen {
            transform: rotate(5deg);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- رأس الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-project-diagram text-primary"></i> إدارة المشاريع</h2>
                        <p class="text-muted">مركز إدارة جميع المشاريع والمهام</p>
                    </div>
                    <div class="quick-actions">
                        <button class="action-btn" onclick="showCreateProjectModal()">
                            <i class="fas fa-plus"></i> مشروع جديد
                        </button>
                        <button class="action-btn secondary" onclick="showCreateTaskModal()">
                            <i class="fas fa-tasks"></i> مهمة جديدة
                        </button>
                        <button class="action-btn success" onclick="showTimeTrackingModal()">
                            <i class="fas fa-clock"></i> تسجيل وقت
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number" id="totalProjects">0</div>
                    <div class="stats-label">إجمالي المشاريع</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number" id="activeTasks">0</div>
                    <div class="stats-label">المهام النشطة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number" id="completedTasks">0</div>
                    <div class="stats-label">المهام المكتملة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number" id="totalHours">0</div>
                    <div class="stats-label">إجمالي الساعات</div>
                </div>
            </div>
        </div>

        <!-- فلاتر البحث -->
        <div class="filter-panel">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <select class="form-select" id="projectFilter" onchange="applyFilters()">
                        <option value="">جميع المشاريع</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="statusFilter" onchange="applyFilters()">
                        <option value="">جميع الحالات</option>
                        <option value="todo">قائمة المهام</option>
                        <option value="in-progress">قيد التنفيذ</option>
                        <option value="review">مراجعة</option>
                        <option value="done">مكتمل</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="assigneeFilter" onchange="applyFilters()">
                        <option value="">جميع المكلفين</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <input type="text" class="form-control" id="searchInput" 
                           placeholder="البحث في المشاريع والمهام..." onkeyup="searchProjects()">
                </div>
            </div>
        </div>

        <!-- تبويبات العرض -->
        <div class="view-tabs">
            <button class="view-tab active" onclick="setView('projects')" id="projectsTab">
                <i class="fas fa-th-large"></i> المشاريع
            </button>
            <button class="view-tab" onclick="setView('kanban')" id="kanbanTab">
                <i class="fas fa-columns"></i> كانبان
            </button>
            <button class="view-tab" onclick="setView('calendar')" id="calendarTab">
                <i class="fas fa-calendar"></i> التقويم
            </button>
            <button class="view-tab" onclick="setView('gantt')" id="ganttTab">
                <i class="fas fa-chart-gantt"></i> جانت
            </button>
            <button class="view-tab" onclick="setView('reports')" id="reportsTab">
                <i class="fas fa-chart-bar"></i> التقارير
            </button>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="row">
            <!-- عرض المشاريع -->
            <div id="projectsView" class="col-12">
                <div class="row" id="projectsGrid">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>

            <!-- عرض كانبان -->
            <div id="kanbanView" class="col-12" style="display: none;">
                <div class="kanban-board" id="kanbanBoard">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>

            <!-- عرض التقويم -->
            <div id="calendarView" class="col-12" style="display: none;">
                <div class="calendar-view">
                    <div class="calendar-header">
                        <h4 id="calendarMonth"></h4>
                        <div>
                            <button class="btn btn-outline-primary" onclick="previousMonth()">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                            <button class="btn btn-outline-primary" onclick="nextMonth()">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                        </div>
                    </div>
                    <div class="calendar-grid" id="calendarGrid">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <!-- عرض جانت -->
            <div id="ganttView" class="col-12" style="display: none;">
                <div class="gantt-chart">
                    <h4 class="mb-4">مخطط جانت للمشروع</h4>
                    <div class="gantt-timeline" id="ganttTimeline">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                    <div id="ganttTasks">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <!-- عرض التقارير -->
            <div id="reportsView" class="col-12" style="display: none;">
                <div class="row">
                    <div class="col-md-6">
                        <div class="stats-card">
                            <h5>تقدم المشاريع</h5>
                            <canvas id="projectProgressChart"></canvas>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="stats-card">
                            <h5>توزيع المهام</h5>
                            <canvas id="taskDistributionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إنشاء مشروع -->
    <div class="modal fade" id="createProjectModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إنشاء مشروع جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createProjectForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم المشروع</label>
                                    <input type="text" class="form-control" id="projectName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الأولوية</label>
                                    <select class="form-select" id="projectPriority">
                                        <option value="low">منخفضة</option>
                                        <option value="medium" selected>متوسطة</option>
                                        <option value="high">عالية</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الوصف</label>
                            <textarea class="form-control" id="projectDescription" rows="3"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ البداية</label>
                                    <input type="date" class="form-control" id="projectStartDate">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ النهاية</label>
                                    <input type="date" class="form-control" id="projectEndDate">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الميزانية</label>
                                    <input type="number" class="form-control" id="projectBudget" min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">مدير المشروع</label>
                                    <select class="form-select" id="projectManager">
                                        <option value="">اختر مدير المشروع</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="createProject()">
                        <i class="fas fa-plus"></i> إنشاء المشروع
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
    <script src="../../core/database.js"></script>
    <script src="../../core/utils.js"></script>
    <script src="project-management.js"></script>

    <script>
        // متغيرات عامة
        let projectManager;
        let currentView = 'projects';
        let currentProject = null;
        let currentMonth = new Date().getMonth();
        let currentYear = new Date().getFullYear();
        let charts = {};

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeProjectManager();
            loadProjects();
            updateStatistics();
            setupEventListeners();
        });

        // تهيئة مدير المشاريع
        function initializeProjectManager() {
            projectManager = new ProjectManager();
            console.log('✅ تم تهيئة واجهة إدارة المشاريع');
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // تحديث الإحصائيات كل دقيقة
            setInterval(updateStatistics, 60000);
        }

        // تحميل المشاريع
        function loadProjects() {
            const projects = Array.from(projectManager.projects.values());
            displayProjects(projects);
            updateFilters();
        }

        // عرض المشاريع
        function displayProjects(projects) {
            const grid = document.getElementById('projectsGrid');

            if (projects.length === 0) {
                grid.innerHTML = `
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-project-diagram fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد مشاريع</h4>
                        <p class="text-muted">ابدأ بإنشاء مشروع جديد</p>
                        <button class="btn btn-primary" onclick="showCreateProjectModal()">
                            <i class="fas fa-plus"></i> إنشاء مشروع جديد
                        </button>
                    </div>
                `;
                return;
            }

            grid.innerHTML = projects.map(project => createProjectCard(project)).join('');
        }

        // إنشاء بطاقة مشروع
        function createProjectCard(project) {
            const progressPercentage = project.progress || 0;
            const circumference = 2 * Math.PI * 26;
            const strokeDasharray = `${(progressPercentage / 100) * circumference} ${circumference}`;

            const statusColors = {
                'planning': '#6c757d',
                'active': '#007bff',
                'on-hold': '#ffc107',
                'completed': '#28a745',
                'cancelled': '#dc3545'
            };

            const statusLabels = {
                'planning': 'تخطيط',
                'active': 'نشط',
                'on-hold': 'معلق',
                'completed': 'مكتمل',
                'cancelled': 'ملغي'
            };

            return `
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="project-card" style="border-left-color: ${project.color}">
                        <div class="project-header" style="background: linear-gradient(135deg, ${project.color} 0%, ${project.color}aa 100%)">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h5 class="mb-2">${project.name}</h5>
                                    <p class="mb-0 opacity-75">${project.description || 'لا يوجد وصف'}</p>
                                </div>
                                <div class="progress-ring">
                                    <svg>
                                        <circle class="bg" cx="30" cy="30" r="26"></circle>
                                        <circle class="progress" cx="30" cy="30" r="26"
                                                style="stroke-dasharray: ${strokeDasharray}"></circle>
                                    </svg>
                                    <div class="position-absolute top-50 start-50 translate-middle text-white fw-bold">
                                        ${progressPercentage}%
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="p-3">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span class="badge" style="background-color: ${statusColors[project.status]}">
                                    ${statusLabels[project.status]}
                                </span>
                                <span class="text-muted small">
                                    <i class="fas fa-calendar"></i>
                                    ${new Date(project.startDate).toLocaleDateString('ar-EG')}
                                </span>
                            </div>

                            <div class="row text-center mb-3">
                                <div class="col-4">
                                    <div class="fw-bold text-primary">${project.statistics.totalTasks}</div>
                                    <small class="text-muted">المهام</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-success">${project.statistics.completedTasks}</div>
                                    <small class="text-muted">مكتمل</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-warning">${project.statistics.totalHours}</div>
                                    <small class="text-muted">ساعة</small>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex">
                                    ${project.team.slice(0, 3).map(memberId => `
                                        <div class="member-avatar me-1" style="width: 30px; height: 30px; font-size: 0.7rem;">
                                            ${memberId.charAt(0).toUpperCase()}
                                        </div>
                                    `).join('')}
                                    ${project.team.length > 3 ? `<div class="member-avatar" style="width: 30px; height: 30px; font-size: 0.7rem;">+${project.team.length - 3}</div>` : ''}
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="openProject('${project.id}')">
                                            <i class="fas fa-eye"></i> عرض
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="editProject('${project.id}')">
                                            <i class="fas fa-edit"></i> تعديل
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="duplicateProject('${project.id}')">
                                            <i class="fas fa-copy"></i> نسخ
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#" onclick="deleteProject('${project.id}')">
                                            <i class="fas fa-trash"></i> حذف
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            const projects = Array.from(projectManager.projects.values());
            const tasks = Array.from(projectManager.tasks.values());
            const timeEntries = Array.from(projectManager.timeEntries.values());

            document.getElementById('totalProjects').textContent = projects.length;
            document.getElementById('activeTasks').textContent = tasks.filter(t => t.status !== 'done').length;
            document.getElementById('completedTasks').textContent = tasks.filter(t => t.status === 'done').length;
            document.getElementById('totalHours').textContent = timeEntries.reduce((total, entry) => total + entry.hours, 0);
        }

        // تحديث الفلاتر
        function updateFilters() {
            const projects = Array.from(projectManager.projects.values());
            const projectFilter = document.getElementById('projectFilter');
            const assigneeFilter = document.getElementById('assigneeFilter');

            // تحديث فلتر المشاريع
            projectFilter.innerHTML = '<option value="">جميع المشاريع</option>';
            projects.forEach(project => {
                projectFilter.innerHTML += `<option value="${project.id}">${project.name}</option>`;
            });

            // تحديث فلتر المكلفين
            const assignees = new Set();
            projectManager.tasks.forEach(task => {
                if (task.assignee) assignees.add(task.assignee);
            });

            assigneeFilter.innerHTML = '<option value="">جميع المكلفين</option>';
            assignees.forEach(assignee => {
                assigneeFilter.innerHTML += `<option value="${assignee}">${assignee}</option>`;
            });
        }

        // تعيين العرض
        function setView(view) {
            currentView = view;

            // إخفاء جميع العروض
            document.querySelectorAll('[id$="View"]').forEach(el => {
                el.style.display = 'none';
            });

            // إزالة الفئة النشطة من جميع التبويبات
            document.querySelectorAll('.view-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // عرض العرض المحدد
            document.getElementById(view + 'View').style.display = 'block';
            document.getElementById(view + 'Tab').classList.add('active');

            // تحميل البيانات حسب العرض
            switch(view) {
                case 'projects':
                    loadProjects();
                    break;
                case 'kanban':
                    loadKanbanBoard();
                    break;
                case 'calendar':
                    loadCalendar();
                    break;
                case 'gantt':
                    loadGanttChart();
                    break;
                case 'reports':
                    loadReports();
                    break;
            }
        }

        // تحميل لوحة كانبان
        function loadKanbanBoard() {
            const board = document.getElementById('kanbanBoard');
            const columns = [
                { id: 'todo', title: 'قائمة المهام', color: '#6c757d' },
                { id: 'in-progress', title: 'قيد التنفيذ', color: '#007bff' },
                { id: 'review', title: 'مراجعة', color: '#ffc107' },
                { id: 'done', title: 'مكتمل', color: '#28a745' }
            ];

            board.innerHTML = columns.map(column => {
                const tasks = Array.from(projectManager.tasks.values())
                    .filter(task => task.status === column.id)
                    .sort((a, b) => a.position - b.position);

                return `
                    <div class="kanban-column" data-status="${column.id}">
                        <div class="kanban-header">
                            <h6 class="mb-0" style="color: ${column.color}">
                                <i class="fas fa-circle me-2"></i>
                                ${column.title}
                            </h6>
                            <span class="badge bg-secondary">${tasks.length}</span>
                        </div>
                        <div class="kanban-tasks" id="tasks-${column.id}">
                            ${tasks.map(task => createTaskCard(task)).join('')}
                        </div>
                        <button class="btn btn-outline-primary btn-sm w-100 mt-2" onclick="showCreateTaskModal('${column.id}')">
                            <i class="fas fa-plus"></i> إضافة مهمة
                        </button>
                    </div>
                `;
            }).join('');

            // إعداد السحب والإفلات
            setupKanbanDragDrop();
        }

        // إنشاء بطاقة مهمة
        function createTaskCard(task) {
            const priorityClass = `priority-${task.priority}`;
            const project = projectManager.projects.get(task.projectId);

            return `
                <div class="task-card ${priorityClass}" data-task-id="${task.id}" draggable="true">
                    <div class="task-title">${task.title}</div>
                    <div class="task-description text-muted small mb-2">${task.description || ''}</div>
                    <div class="task-meta">
                        <div>
                            <span class="badge bg-light text-dark">${project ? project.name : 'غير محدد'}</span>
                            ${task.dueDate ? `<small class="text-muted d-block mt-1">
                                <i class="fas fa-calendar"></i> ${new Date(task.dueDate).toLocaleDateString('ar-EG')}
                            </small>` : ''}
                        </div>
                        ${task.assignee ? `<div class="task-assignee">${task.assignee.charAt(0).toUpperCase()}</div>` : ''}
                    </div>
                </div>
            `;
        }

        // إعداد السحب والإفلات لكانبان
        function setupKanbanDragDrop() {
            document.querySelectorAll('.kanban-tasks').forEach(column => {
                new Sortable(column, {
                    group: 'kanban',
                    animation: 150,
                    ghostClass: 'sortable-ghost',
                    chosenClass: 'sortable-chosen',
                    onEnd: function(evt) {
                        const taskId = evt.item.dataset.taskId;
                        const newStatus = evt.to.parentElement.dataset.status;
                        const newPosition = evt.newIndex;

                        projectManager.moveTask(taskId, newStatus, newPosition);
                        updateStatistics();
                        showNotification('تم نقل المهمة بنجاح', 'success');
                    }
                });
            });
        }

        // عرض نافذة إنشاء مشروع
        function showCreateProjectModal() {
            new bootstrap.Modal(document.getElementById('createProjectModal')).show();
        }

        // إنشاء مشروع
        function createProject() {
            try {
                const projectData = {
                    name: document.getElementById('projectName').value,
                    description: document.getElementById('projectDescription').value,
                    priority: document.getElementById('projectPriority').value,
                    startDate: document.getElementById('projectStartDate').value,
                    endDate: document.getElementById('projectEndDate').value,
                    budget: parseFloat(document.getElementById('projectBudget').value) || 0,
                    manager: document.getElementById('projectManager').value,
                    team: []
                };

                if (!projectData.name) {
                    showNotification('يرجى إدخال اسم المشروع', 'warning');
                    return;
                }

                const project = projectManager.createProject(projectData);

                bootstrap.Modal.getInstance(document.getElementById('createProjectModal')).hide();
                document.getElementById('createProjectForm').reset();

                loadProjects();
                updateStatistics();
                updateFilters();

                showNotification('تم إنشاء المشروع بنجاح', 'success');

            } catch (error) {
                showNotification('خطأ في إنشاء المشروع: ' + error.message, 'error');
            }
        }

        // فتح مشروع
        function openProject(projectId) {
            currentProject = projectId;
            setView('kanban');

            // تطبيق فلتر المشروع
            document.getElementById('projectFilter').value = projectId;
            applyFilters();
        }

        // تطبيق الفلاتر
        function applyFilters() {
            const projectFilter = document.getElementById('projectFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;
            const assigneeFilter = document.getElementById('assigneeFilter').value;

            let filteredTasks = Array.from(projectManager.tasks.values());

            if (projectFilter) {
                filteredTasks = filteredTasks.filter(task => task.projectId === projectFilter);
            }

            if (statusFilter) {
                filteredTasks = filteredTasks.filter(task => task.status === statusFilter);
            }

            if (assigneeFilter) {
                filteredTasks = filteredTasks.filter(task => task.assignee === assigneeFilter);
            }

            // تحديث العرض حسب النوع الحالي
            if (currentView === 'kanban') {
                loadKanbanBoard();
            }
        }

        // البحث في المشاريع
        function searchProjects() {
            const query = document.getElementById('searchInput').value;
            if (!query.trim()) {
                loadProjects();
                return;
            }

            const results = projectManager.search(query);
            displayProjects(results.projects);
        }

        // عرض الإشعارات
        function showNotification(message, type = 'info') {
            const alertClass = {
                'success': 'alert-success',
                'error': 'alert-danger',
                'warning': 'alert-warning',
                'info': 'alert-info'
            }[type] || 'alert-info';

            const notification = document.createElement('div');
            notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // دوال إضافية (ستكتمل في الجزء التالي)
        function showCreateTaskModal(status = 'todo') {
            // تنفيذ عرض نافذة إنشاء مهمة
        }

        function editProject(projectId) {
            // تنفيذ تعديل المشروع
        }

        function deleteProject(projectId) {
            if (confirm('هل أنت متأكد من حذف هذا المشروع؟')) {
                try {
                    projectManager.deleteProject(projectId);
                    loadProjects();
                    updateStatistics();
                    showNotification('تم حذف المشروع بنجاح', 'success');
                } catch (error) {
                    showNotification('خطأ في حذف المشروع: ' + error.message, 'error');
                }
            }
        }

        function duplicateProject(projectId) {
            // تنفيذ نسخ المشروع
        }

        function loadCalendar() {
            // تنفيذ تحميل التقويم
        }

        function loadGanttChart() {
            // تنفيذ تحميل مخطط جانت
        }

        function loadReports() {
            // تنفيذ تحميل التقارير
        }

        function showTimeTrackingModal() {
            // تنفيذ عرض نافذة تسجيل الوقت
        }

        function previousMonth() {
            // تنفيذ الشهر السابق
        }

        function nextMonth() {
            // تنفيذ الشهر التالي
        }
    </script>
</body>
</html>
