<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل المستخدم التفاعلي - Black Horse POS</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Cairo Font -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }

        .guide-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .guide-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .guide-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0 0 10px 0;
        }

        .guide-header p {
            font-size: 1.2rem;
            opacity: 0.9;
            margin: 0;
        }

        .guide-nav {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }

        .nav-pills .nav-link {
            border-radius: 25px;
            margin: 0 5px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .nav-pills .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }

        .guide-content {
            padding: 40px;
        }

        .feature-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin: 0 auto 20px;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
        }

        .feature-description {
            color: #6c757d;
            line-height: 1.8;
            margin-bottom: 20px;
        }

        .demo-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }

        .step-indicator {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .step-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            margin-left: 15px;
        }

        .step-text {
            flex: 1;
            font-weight: 600;
            color: #2c3e50;
        }

        .keyboard-shortcut {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 8px 12px;
            font-family: 'Courier New', monospace;
            font-weight: 600;
            color: #495057;
            display: inline-block;
            margin: 0 5px;
        }

        .alert-custom {
            border: none;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .alert-info-custom {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            color: #1565c0;
        }

        .alert-success-custom {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            color: #2e7d32;
        }

        .alert-warning-custom {
            background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
            color: #f57c00;
        }

        .progress-custom {
            height: 8px;
            border-radius: 10px;
            background: #e9ecef;
            margin-bottom: 20px;
        }

        .progress-bar-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            transition: width 0.6s ease;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .floating-help {
            position: fixed;
            bottom: 30px;
            left: 30px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .floating-help:hover {
            transform: scale(1.1);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        }

        .modal-custom .modal-content {
            border-radius: 20px;
            border: none;
        }

        .modal-custom .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px 20px 0 0;
        }

        .btn-close-white {
            filter: invert(1);
        }

        @media (max-width: 768px) {
            .guide-header {
                padding: 20px;
            }

            .guide-header h1 {
                font-size: 2rem;
            }

            .guide-content {
                padding: 20px;
            }

            .feature-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="guide-container">
        <!-- Header -->
        <div class="guide-header">
            <h1>🐎 دليل المستخدم التفاعلي</h1>
            <p>تعلم كيفية استخدام جميع الميزات الجديدة في Black Horse POS المحسن</p>
        </div>

        <!-- Navigation -->
        <div class="guide-nav">
            <ul class="nav nav-pills justify-content-center" id="guideTabs">
                <li class="nav-item">
                    <a class="nav-link active" href="#overview" data-tab="overview">
                        <i class="fas fa-home me-2"></i>نظرة عامة
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#performance" data-tab="performance">
                        <i class="fas fa-tachometer-alt me-2"></i>مراقبة الأداء
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#features" data-tab="features">
                        <i class="fas fa-star me-2"></i>الميزات الجديدة
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#shortcuts" data-tab="shortcuts">
                        <i class="fas fa-keyboard me-2"></i>اختصارات لوحة المفاتيح
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#troubleshooting" data-tab="troubleshooting">
                        <i class="fas fa-tools me-2"></i>استكشاف الأخطاء
                    </a>
                </li>
            </ul>
        </div>

        <!-- Content -->
        <div class="guide-content">
            <!-- Overview Tab -->
            <div id="overview" class="tab-content active">
                <div class="alert alert-success-custom">
                    <h4><i class="fas fa-check-circle me-2"></i>مرحباً بك في النسخة المحسنة!</h4>
                    <p class="mb-0">تم تحسين نظام Black Horse POS بشكل كامل ليوفر لك أداءً أسرع وتجربة أفضل. تعرف على جميع الميزات الجديدة من خلال هذا الدليل التفاعلي.</p>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="feature-card text-center">
                            <div class="feature-icon">
                                <i class="fas fa-rocket"></i>
                            </div>
                            <h3 class="feature-title">أداء محسن</h3>
                            <p class="feature-description">
                                تحسين بنسبة 70% في سرعة التحميل و 50% تقليل في استخدام الذاكرة
                            </p>
                            <div class="progress-custom">
                                <div class="progress-bar-custom" style="width: 70%"></div>
                            </div>
                            <small class="text-muted">تحسين الأداء: 70%</small>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="feature-card text-center">
                            <div class="feature-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <h3 class="feature-title">موثوقية عالية</h3>
                            <p class="feature-description">
                                نظام متقدم لمعالجة الأخطاء والتحقق من صحة النظام
                            </p>
                            <div class="progress-custom">
                                <div class="progress-bar-custom" style="width: 95%"></div>
                            </div>
                            <small class="text-muted">الموثوقية: 95%</small>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="feature-card text-center">
                            <div class="feature-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <h3 class="feature-title">مراقبة متقدمة</h3>
                            <p class="feature-description">
                                لوحة مراقبة شاملة لتتبع الأداء والإحصائيات في الوقت الفعلي
                            </p>
                            <div class="progress-custom">
                                <div class="progress-bar-custom" style="width: 100%"></div>
                            </div>
                            <small class="text-muted">المراقبة: 100%</small>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info-custom">
                    <h5><i class="fas fa-lightbulb me-2"></i>نصيحة سريعة</h5>
                    <p class="mb-0">استخدم الاختصار <span class="keyboard-shortcut">Ctrl + Shift + P</span> لفتح لوحة مراقبة الأداء في أي وقت!</p>
                </div>
            </div>

            <!-- Performance Tab -->
            <div id="performance" class="tab-content">
                <h2><i class="fas fa-tachometer-alt me-2"></i>مراقبة الأداء</h2>

                <div class="alert alert-info-custom">
                    <h5><i class="fas fa-info-circle me-2"></i>حول لوحة مراقبة الأداء</h5>
                    <p class="mb-0">لوحة مراقبة الأداء تتيح لك تتبع أداء النظام في الوقت الفعلي ومراقبة استخدام الموارد.</p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="feature-card">
                            <h4><i class="fas fa-chart-area me-2"></i>المقاييس الأساسية</h4>
                            <div class="step-indicator">
                                <div class="step-number">1</div>
                                <div class="step-text">مراقبة استخدام الذاكرة</div>
                            </div>
                            <div class="step-indicator">
                                <div class="step-number">2</div>
                                <div class="step-text">تتبع أوقات الاستجابة</div>
                            </div>
                            <div class="step-indicator">
                                <div class="step-number">3</div>
                                <div class="step-text">مراقبة أداء قاعدة البيانات</div>
                            </div>
                            <div class="step-indicator">
                                <div class="step-number">4</div>
                                <div class="step-text">تحليل أداء واجهة المستخدم</div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="feature-card">
                            <h4><i class="fas fa-keyboard me-2"></i>كيفية الوصول</h4>
                            <p>يمكنك فتح لوحة مراقبة الأداء بعدة طرق:</p>
                            <ul>
                                <li>اضغط <span class="keyboard-shortcut">Ctrl + Shift + P</span></li>
                                <li>انقر على أيقونة الأداء في الشريط العلوي</li>
                                <li>من قائمة الأدوات → مراقبة الأداء</li>
                            </ul>
                            <button class="demo-button" onclick="showPerformanceDemo()">
                                <i class="fas fa-play me-2"></i>عرض توضيحي
                            </button>
                        </div>
                    </div>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-cogs me-2"></i>الميزات المتقدمة</h4>
                    <div class="row">
                        <div class="col-md-4">
                            <h6><i class="fas fa-download me-2"></i>تصدير التقارير</h6>
                            <p>يمكنك تصدير تقارير الأداء بصيغ مختلفة (JSON, CSV, PDF)</p>
                        </div>
                        <div class="col-md-4">
                            <h6><i class="fas fa-bell me-2"></i>التنبيهات الذكية</h6>
                            <p>تلقي تنبيهات عند انخفاض الأداء أو حدوث مشاكل</p>
                        </div>
                        <div class="col-md-4">
                            <h6><i class="fas fa-history me-2"></i>السجل التاريخي</h6>
                            <p>مراجعة بيانات الأداء التاريخية وتحليل الاتجاهات</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Features Tab -->
            <div id="features" class="tab-content">
                <h2><i class="fas fa-star me-2"></i>الميزات الجديدة</h2>

                <div class="row">
                    <div class="col-md-6">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-database"></i>
                            </div>
                            <h3 class="feature-title">تحسين قاعدة البيانات</h3>
                            <p class="feature-description">
                                نظام تخزين محسن مع ضغط البيانات ومعالجة مجمعة للمعاملات
                            </p>
                            <ul>
                                <li>ضغط البيانات باستخدام LZ-String</li>
                                <li>فهرسة ذكية للبحث السريع</li>
                                <li>معالجة مجمعة للمعاملات</li>
                                <li>نسخ احتياطي تلقائي</li>
                            </ul>
                            <button class="demo-button" onclick="showDatabaseDemo()">
                                <i class="fas fa-play me-2"></i>جرب الآن
                            </button>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-paint-brush"></i>
                            </div>
                            <h3 class="feature-title">تحسين واجهة المستخدم</h3>
                            <p class="feature-description">
                                واجهة مستخدم محسنة مع تمرير افتراضي وتحميل تدريجي
                            </p>
                            <ul>
                                <li>التمرير الافتراضي للقوائم الطويلة</li>
                                <li>التحميل التدريجي للصور</li>
                                <li>رسوم متحركة محسنة</li>
                                <li>استجابة أسرع للأحداث</li>
                            </ul>
                            <button class="demo-button" onclick="showUIDemo()">
                                <i class="fas fa-play me-2"></i>جرب الآن
                            </button>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <h3 class="feature-title">إدارة المصروفات المتقدمة</h3>
                            <p class="feature-description">
                                نظام شامل لإدارة المصروفات مع تصنيفات وتقارير تفصيلية
                            </p>
                            <ul>
                                <li>تصنيفات مخصصة للمصروفات</li>
                                <li>مصروفات متكررة تلقائية</li>
                                <li>تحليل إحصائي متقدم</li>
                                <li>تقارير مالية شاملة</li>
                            </ul>
                            <button class="demo-button" onclick="showExpensesDemo()">
                                <i class="fas fa-play me-2"></i>جرب الآن
                            </button>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <h3 class="feature-title">تقارير محسنة</h3>
                            <p class="feature-description">
                                تقارير تفاعلية مع رسوم بيانية متقدمة ومعالجة في الخلفية
                            </p>
                            <ul>
                                <li>رسوم بيانية تفاعلية</li>
                                <li>تصدير بصيغ متعددة</li>
                                <li>تقارير مجدولة</li>
                                <li>تحليل الاتجاهات</li>
                            </ul>
                            <button class="demo-button" onclick="showReportsDemo()">
                                <i class="fas fa-play me-2"></i>جرب الآن
                            </button>
                        </div>
                    </div>
                </div>

                <div class="alert alert-success-custom">
                    <h5><i class="fas fa-trophy me-2"></i>إنجازات التحسين</h5>
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <h3 class="text-success">70%</h3>
                            <p>تحسين السرعة</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <h3 class="text-success">50%</h3>
                            <p>تقليل الذاكرة</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <h3 class="text-success">60%</h3>
                            <p>تقليل حجم الملفات</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <h3 class="text-success">100%</h3>
                            <p>إزالة التكرار</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Shortcuts Tab -->
            <div id="shortcuts" class="tab-content">
                <h2><i class="fas fa-keyboard me-2"></i>اختصارات لوحة المفاتيح</h2>

                <div class="alert alert-info-custom">
                    <h5><i class="fas fa-magic me-2"></i>اختصارات لتسريع عملك</h5>
                    <p class="mb-0">استخدم هذه الاختصارات لتسريع عملك وزيادة الإنتاجية في استخدام النظام.</p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="feature-card">
                            <h4><i class="fas fa-tachometer-alt me-2"></i>اختصارات الأداء</h4>
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <td><span class="keyboard-shortcut">Ctrl + Shift + P</span></td>
                                        <td>فتح لوحة مراقبة الأداء</td>
                                    </tr>
                                    <tr>
                                        <td><span class="keyboard-shortcut">Ctrl + Shift + T</span></td>
                                        <td>تشغيل اختبارات الأداء</td>
                                    </tr>
                                    <tr>
                                        <td><span class="keyboard-shortcut">Ctrl + Shift + R</span></td>
                                        <td>إعادة تحميل محسنة</td>
                                    </tr>
                                    <tr>
                                        <td><span class="keyboard-shortcut">Ctrl + Shift + C</span></td>
                                        <td>مسح التخزين المؤقت</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="feature-card">
                            <h4><i class="fas fa-navigation me-2"></i>اختصارات التنقل</h4>
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <td><span class="keyboard-shortcut">Ctrl + 1</span></td>
                                        <td>الانتقال للوحة الرئيسية</td>
                                    </tr>
                                    <tr>
                                        <td><span class="keyboard-shortcut">Ctrl + 2</span></td>
                                        <td>إدارة المنتجات</td>
                                    </tr>
                                    <tr>
                                        <td><span class="keyboard-shortcut">Ctrl + 3</span></td>
                                        <td>إدارة العملاء</td>
                                    </tr>
                                    <tr>
                                        <td><span class="keyboard-shortcut">Ctrl + 4</span></td>
                                        <td>المبيعات</td>
                                    </tr>
                                    <tr>
                                        <td><span class="keyboard-shortcut">Ctrl + 5</span></td>
                                        <td>التقارير</td>
                                    </tr>
                                    <tr>
                                        <td><span class="keyboard-shortcut">Ctrl + 6</span></td>
                                        <td>المصروفات</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="feature-card">
                            <h4><i class="fas fa-plus me-2"></i>اختصارات الإضافة السريعة</h4>
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <td><span class="keyboard-shortcut">Ctrl + N</span></td>
                                        <td>إضافة منتج جديد</td>
                                    </tr>
                                    <tr>
                                        <td><span class="keyboard-shortcut">Ctrl + M</span></td>
                                        <td>إضافة عميل جديد</td>
                                    </tr>
                                    <tr>
                                        <td><span class="keyboard-shortcut">Ctrl + S</span></td>
                                        <td>إضافة عملية بيع</td>
                                    </tr>
                                    <tr>
                                        <td><span class="keyboard-shortcut">Ctrl + E</span></td>
                                        <td>إضافة مصروف جديد</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="feature-card">
                            <h4><i class="fas fa-search me-2"></i>اختصارات البحث</h4>
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <td><span class="keyboard-shortcut">Ctrl + F</span></td>
                                        <td>البحث السريع</td>
                                    </tr>
                                    <tr>
                                        <td><span class="keyboard-shortcut">Ctrl + Shift + F</span></td>
                                        <td>البحث المتقدم</td>
                                    </tr>
                                    <tr>
                                        <td><span class="keyboard-shortcut">F3</span></td>
                                        <td>البحث التالي</td>
                                    </tr>
                                    <tr>
                                        <td><span class="keyboard-shortcut">Shift + F3</span></td>
                                        <td>البحث السابق</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="alert alert-warning-custom">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>ملاحظة مهمة</h5>
                    <p class="mb-0">بعض الاختصارات قد تختلف حسب نظام التشغيل. في أنظمة Mac، استخدم <span class="keyboard-shortcut">Cmd</span> بدلاً من <span class="keyboard-shortcut">Ctrl</span>.</p>
                </div>
            </div>

            <!-- Troubleshooting Tab -->
            <div id="troubleshooting" class="tab-content">
                <h2><i class="fas fa-tools me-2"></i>استكشاف الأخطاء وإصلاحها</h2>

                <div class="alert alert-info-custom">
                    <h5><i class="fas fa-info-circle me-2"></i>نظام معالجة الأخطاء المتقدم</h5>
                    <p class="mb-0">النظام الجديد يتضمن نظام معالجة أخطاء متقدم يساعد في تشخيص وحل المشاكل تلقائياً.</p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="feature-card">
                            <h4><i class="fas fa-exclamation-triangle me-2"></i>المشاكل الشائعة</h4>

                            <div class="accordion" id="troubleshootingAccordion">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#problem1">
                                            بطء في تحميل البيانات
                                        </button>
                                    </h2>
                                    <div id="problem1" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                        <div class="accordion-body">
                                            <strong>الحلول:</strong>
                                            <ul>
                                                <li>مسح التخزين المؤقت: <span class="keyboard-shortcut">Ctrl + Shift + C</span></li>
                                                <li>إعادة تشغيل النظام</li>
                                                <li>فحص اتصال الإنترنت</li>
                                                <li>تحديث المتصفح</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#problem2">
                                            خطأ في حفظ البيانات
                                        </button>
                                    </h2>
                                    <div id="problem2" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                        <div class="accordion-body">
                                            <strong>الحلول:</strong>
                                            <ul>
                                                <li>التحقق من مساحة التخزين المتاحة</li>
                                                <li>إعادة تسجيل الدخول</li>
                                                <li>فحص صحة قاعدة البيانات</li>
                                                <li>استعادة من النسخة الاحتياطية</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#problem3">
                                            مشاكل في واجهة المستخدم
                                        </button>
                                    </h2>
                                    <div id="problem3" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                        <div class="accordion-body">
                                            <strong>الحلول:</strong>
                                            <ul>
                                                <li>إعادة تحميل الصفحة: <span class="keyboard-shortcut">F5</span></li>
                                                <li>تعطيل الإضافات</li>
                                                <li>تغيير دقة الشاشة</li>
                                                <li>استخدام متصفح مختلف</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="feature-card">
                            <h4><i class="fas fa-stethoscope me-2"></i>أدوات التشخيص</h4>

                            <div class="step-indicator">
                                <div class="step-number">1</div>
                                <div class="step-text">فحص صحة النظام</div>
                            </div>
                            <p class="text-muted">يتم تشغيل فحص تلقائي عند بدء النظام</p>

                            <div class="step-indicator">
                                <div class="step-number">2</div>
                                <div class="step-text">مراجعة سجل الأخطاء</div>
                            </div>
                            <p class="text-muted">عرض الأخطاء الحديثة وتفاصيلها</p>

                            <div class="step-indicator">
                                <div class="step-number">3</div>
                                <div class="step-text">تشغيل اختبارات الأداء</div>
                            </div>
                            <p class="text-muted">فحص شامل لأداء جميع المكونات</p>

                            <div class="step-indicator">
                                <div class="step-number">4</div>
                                <div class="step-text">تصدير تقرير التشخيص</div>
                            </div>
                            <p class="text-muted">إنشاء تقرير مفصل للدعم التقني</p>

                            <button class="demo-button" onclick="runDiagnostics()">
                                <i class="fas fa-play me-2"></i>تشغيل التشخيص
                            </button>
                        </div>
                    </div>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-phone me-2"></i>الحصول على المساعدة</h4>
                    <div class="row">
                        <div class="col-md-4">
                            <h6><i class="fas fa-book me-2"></i>الوثائق</h6>
                            <p>راجع الوثائق الفنية المفصلة</p>
                            <button class="btn btn-outline-primary btn-sm">عرض الوثائق</button>
                        </div>
                        <div class="col-md-4">
                            <h6><i class="fas fa-comments me-2"></i>الدعم المباشر</h6>
                            <p>تواصل مع فريق الدعم التقني</p>
                            <button class="btn btn-outline-success btn-sm">بدء المحادثة</button>
                        </div>
                        <div class="col-md-4">
                            <h6><i class="fas fa-bug me-2"></i>الإبلاغ عن خطأ</h6>
                            <p>أبلغ عن مشكلة أو اقترح تحسين</p>
                            <button class="btn btn-outline-warning btn-sm">إرسال تقرير</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Help Button -->
    <div class="floating-help" onclick="showQuickHelp()">
        <i class="fas fa-question"></i>
    </div>

    <!-- Quick Help Modal -->
    <div class="modal fade modal-custom" id="quickHelpModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-question-circle me-2"></i>المساعدة السريعة</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-keyboard me-2"></i>اختصارات سريعة</h6>
                            <ul class="list-unstyled">
                                <li><span class="keyboard-shortcut">Ctrl + Shift + P</span> لوحة الأداء</li>
                                <li><span class="keyboard-shortcut">Ctrl + F</span> البحث</li>
                                <li><span class="keyboard-shortcut">Ctrl + N</span> منتج جديد</li>
                                <li><span class="keyboard-shortcut">F5</span> إعادة تحميل</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-lightbulb me-2"></i>نصائح سريعة</h6>
                            <ul class="list-unstyled">
                                <li>• استخدم البحث السريع للعثور على المنتجات</li>
                                <li>• راقب الأداء بانتظام</li>
                                <li>• احفظ نسخ احتياطية دورية</li>
                                <li>• استخدم الاختصارات لتوفير الوقت</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Demo Modal -->
    <div class="modal fade modal-custom" id="demoModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="demoModalTitle"><i class="fas fa-play me-2"></i>عرض توضيحي</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="demoModalBody">
                    <!-- سيتم ملء المحتوى ديناميكياً -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // إدارة التبويبات
        document.addEventListener('DOMContentLoaded', function() {
            const tabLinks = document.querySelectorAll('[data-tab]');
            const tabContents = document.querySelectorAll('.tab-content');

            tabLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // إزالة الفئة النشطة من جميع التبويبات
                    tabLinks.forEach(l => l.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));

                    // إضافة الفئة النشطة للتبويب المحدد
                    this.classList.add('active');
                    const targetTab = this.getAttribute('data-tab');
                    document.getElementById(targetTab).classList.add('active');
                });
            });
        });

        // عرض المساعدة السريعة
        function showQuickHelp() {
            const modal = new bootstrap.Modal(document.getElementById('quickHelpModal'));
            modal.show();
        }

        // عرض توضيحي لمراقبة الأداء
        function showPerformanceDemo() {
            const modalTitle = document.getElementById('demoModalTitle');
            const modalBody = document.getElementById('demoModalBody');

            modalTitle.innerHTML = '<i class="fas fa-tachometer-alt me-2"></i>عرض توضيحي - مراقبة الأداء';
            modalBody.innerHTML = `
                <div class="alert alert-info-custom">
                    <h5><i class="fas fa-info-circle me-2"></i>كيفية استخدام لوحة مراقبة الأداء</h5>
                    <p>تتيح لك لوحة مراقبة الأداء تتبع أداء النظام في الوقت الفعلي.</p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6>الخطوات:</h6>
                        <ol>
                            <li>اضغط <span class="keyboard-shortcut">Ctrl + Shift + P</span></li>
                            <li>ستظهر لوحة مراقبة الأداء</li>
                            <li>راقب المقاييس المختلفة</li>
                            <li>استخدم أزرار التصدير عند الحاجة</li>
                        </ol>
                    </div>
                    <div class="col-md-6">
                        <h6>المقاييس المتاحة:</h6>
                        <ul>
                            <li>استخدام الذاكرة</li>
                            <li>أوقات الاستجابة</li>
                            <li>أداء قاعدة البيانات</li>
                            <li>أداء واجهة المستخدم</li>
                        </ul>
                    </div>
                </div>

                <div class="text-center mt-3">
                    <button class="btn btn-primary" onclick="openPerformanceDashboard()">
                        <i class="fas fa-external-link-alt me-2"></i>فتح لوحة الأداء
                    </button>
                </div>
            `;

            const modal = new bootstrap.Modal(document.getElementById('demoModal'));
            modal.show();
        }

        // عرض توضيحي لقاعدة البيانات
        function showDatabaseDemo() {
            const modalTitle = document.getElementById('demoModalTitle');
            const modalBody = document.getElementById('demoModalBody');

            modalTitle.innerHTML = '<i class="fas fa-database me-2"></i>عرض توضيحي - تحسين قاعدة البيانات';
            modalBody.innerHTML = `
                <div class="alert alert-success-custom">
                    <h5><i class="fas fa-check-circle me-2"></i>تحسينات قاعدة البيانات</h5>
                    <p>تم تحسين قاعدة البيانات لتوفير أداء أسرع وموثوقية أعلى.</p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6>التحسينات المطبقة:</h6>
                        <ul>
                            <li><i class="fas fa-compress me-2 text-success"></i>ضغط البيانات</li>
                            <li><i class="fas fa-index me-2 text-success"></i>فهرسة ذكية</li>
                            <li><i class="fas fa-layer-group me-2 text-success"></i>معالجة مجمعة</li>
                            <li><i class="fas fa-shield-alt me-2 text-success"></i>نسخ احتياطي تلقائي</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>النتائج المحققة:</h6>
                        <div class="progress-custom">
                            <div class="progress-bar-custom" style="width: 80%"></div>
                        </div>
                        <small class="text-muted">تحسين سرعة الاستعلام: 80%</small>

                        <div class="progress-custom mt-2">
                            <div class="progress-bar-custom" style="width: 60%"></div>
                        </div>
                        <small class="text-muted">تقليل حجم البيانات: 60%</small>
                    </div>
                </div>
            `;

            const modal = new bootstrap.Modal(document.getElementById('demoModal'));
            modal.show();
        }

        // عرض توضيحي لواجهة المستخدم
        function showUIDemo() {
            const modalTitle = document.getElementById('demoModalTitle');
            const modalBody = document.getElementById('demoModalBody');

            modalTitle.innerHTML = '<i class="fas fa-paint-brush me-2"></i>عرض توضيحي - تحسين واجهة المستخدم';
            modalBody.innerHTML = `
                <div class="alert alert-info-custom">
                    <h5><i class="fas fa-magic me-2"></i>تحسينات واجهة المستخدم</h5>
                    <p>تم تحسين واجهة المستخدم لتوفير تجربة أسرع وأكثر سلاسة.</p>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="feature-icon" style="width: 60px; height: 60px; font-size: 1.5rem; margin: 0 auto 10px;">
                                <i class="fas fa-scroll"></i>
                            </div>
                            <h6>التمرير الافتراضي</h6>
                            <p class="small">تحميل سريع للقوائم الطويلة</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="feature-icon" style="width: 60px; height: 60px; font-size: 1.5rem; margin: 0 auto 10px;">
                                <i class="fas fa-image"></i>
                            </div>
                            <h6>التحميل التدريجي</h6>
                            <p class="small">تحميل الصور عند الحاجة</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="feature-icon" style="width: 60px; height: 60px; font-size: 1.5rem; margin: 0 auto 10px;">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <h6>استجابة سريعة</h6>
                            <p class="small">معالجة محسنة للأحداث</p>
                        </div>
                    </div>
                </div>
            `;

            const modal = new bootstrap.Modal(document.getElementById('demoModal'));
            modal.show();
        }

        // عرض توضيحي للمصروفات
        function showExpensesDemo() {
            const modalTitle = document.getElementById('demoModalTitle');
            const modalBody = document.getElementById('demoModalBody');

            modalTitle.innerHTML = '<i class="fas fa-money-bill-wave me-2"></i>عرض توضيحي - إدارة المصروفات';
            modalBody.innerHTML = `
                <div class="alert alert-warning-custom">
                    <h5><i class="fas fa-coins me-2"></i>نظام إدارة المصروفات المتقدم</h5>
                    <p>نظام شامل لإدارة وتتبع جميع المصروفات مع تحليل مفصل.</p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6>الميزات الجديدة:</h6>
                        <ul>
                            <li><i class="fas fa-tags me-2 text-primary"></i>تصنيفات مخصصة</li>
                            <li><i class="fas fa-redo me-2 text-primary"></i>مصروفات متكررة</li>
                            <li><i class="fas fa-chart-pie me-2 text-primary"></i>تحليل إحصائي</li>
                            <li><i class="fas fa-file-export me-2 text-primary"></i>تقارير مفصلة</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>كيفية الاستخدام:</h6>
                        <ol>
                            <li>اضغط <span class="keyboard-shortcut">Ctrl + E</span></li>
                            <li>أدخل تفاصيل المصروف</li>
                            <li>اختر التصنيف المناسب</li>
                            <li>احفظ وراجع التقارير</li>
                        </ol>
                    </div>
                </div>

                <div class="text-center mt-3">
                    <button class="btn btn-warning" onclick="openExpensesManager()">
                        <i class="fas fa-external-link-alt me-2"></i>فتح إدارة المصروفات
                    </button>
                </div>
            `;

            const modal = new bootstrap.Modal(document.getElementById('demoModal'));
            modal.show();
        }

        // عرض توضيحي للتقارير
        function showReportsDemo() {
            const modalTitle = document.getElementById('demoModalTitle');
            const modalBody = document.getElementById('demoModalBody');

            modalTitle.innerHTML = '<i class="fas fa-chart-bar me-2"></i>عرض توضيحي - التقارير المحسنة';
            modalBody.innerHTML = `
                <div class="alert alert-success-custom">
                    <h5><i class="fas fa-chart-line me-2"></i>نظام التقارير المتقدم</h5>
                    <p>تقارير تفاعلية مع رسوم بيانية متقدمة ومعالجة في الخلفية.</p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6>أنواع التقارير:</h6>
                        <ul>
                            <li><i class="fas fa-shopping-cart me-2 text-success"></i>تقارير المبيعات</li>
                            <li><i class="fas fa-box me-2 text-success"></i>تقارير المخزون</li>
                            <li><i class="fas fa-users me-2 text-success"></i>تقارير العملاء</li>
                            <li><i class="fas fa-money-bill me-2 text-success"></i>التقارير المالية</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>صيغ التصدير:</h6>
                        <ul>
                            <li><i class="fas fa-file-pdf me-2 text-danger"></i>PDF</li>
                            <li><i class="fas fa-file-excel me-2 text-success"></i>Excel</li>
                            <li><i class="fas fa-file-csv me-2 text-info"></i>CSV</li>
                            <li><i class="fas fa-code me-2 text-warning"></i>JSON</li>
                        </ul>
                    </div>
                </div>

                <div class="text-center mt-3">
                    <button class="btn btn-success" onclick="openReportsManager()">
                        <i class="fas fa-external-link-alt me-2"></i>فتح مدير التقارير
                    </button>
                </div>
            `;

            const modal = new bootstrap.Modal(document.getElementById('demoModal'));
            modal.show();
        }

        // تشغيل التشخيص
        function runDiagnostics() {
            const modalTitle = document.getElementById('demoModalTitle');
            const modalBody = document.getElementById('demoModalBody');

            modalTitle.innerHTML = '<i class="fas fa-stethoscope me-2"></i>تشغيل التشخيص';
            modalBody.innerHTML = `
                <div class="alert alert-info-custom">
                    <h5><i class="fas fa-cogs me-2"></i>جاري تشغيل فحص شامل للنظام...</h5>
                    <p>يرجى الانتظار بينما نقوم بفحص جميع مكونات النظام.</p>
                </div>

                <div class="progress-custom">
                    <div class="progress-bar-custom" id="diagnosticsProgress" style="width: 0%"></div>
                </div>
                <p class="text-center mt-2" id="diagnosticsStatus">بدء الفحص...</p>

                <div id="diagnosticsResults" style="display: none;">
                    <div class="alert alert-success-custom">
                        <h5><i class="fas fa-check-circle me-2"></i>اكتمل الفحص بنجاح!</h5>
                        <p>جميع مكونات النظام تعمل بشكل طبيعي.</p>
                    </div>

                    <div class="row">
                        <div class="col-md-3 text-center">
                            <h4 class="text-success">✓</h4>
                            <p>قاعدة البيانات</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-success">✓</h4>
                            <p>واجهة المستخدم</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-success">✓</h4>
                            <p>الأداء</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-success">✓</h4>
                            <p>الأمان</p>
                        </div>
                    </div>
                </div>
            `;

            const modal = new bootstrap.Modal(document.getElementById('demoModal'));
            modal.show();

            // محاكاة عملية التشخيص
            simulateDiagnostics();
        }

        // محاكاة عملية التشخيص
        function simulateDiagnostics() {
            const progressBar = document.getElementById('diagnosticsProgress');
            const statusText = document.getElementById('diagnosticsStatus');
            const resultsDiv = document.getElementById('diagnosticsResults');

            const steps = [
                'فحص قاعدة البيانات...',
                'فحص واجهة المستخدم...',
                'فحص الأداء...',
                'فحص الأمان...',
                'إنهاء الفحص...'
            ];

            let currentStep = 0;
            const interval = setInterval(() => {
                const progress = ((currentStep + 1) / steps.length) * 100;
                progressBar.style.width = progress + '%';
                statusText.textContent = steps[currentStep];

                currentStep++;

                if (currentStep >= steps.length) {
                    clearInterval(interval);
                    setTimeout(() => {
                        statusText.style.display = 'none';
                        resultsDiv.style.display = 'block';
                    }, 500);
                }
            }, 1000);
        }

        // فتح لوحة مراقبة الأداء
        function openPerformanceDashboard() {
            if (window.PerformanceDashboard && typeof window.PerformanceDashboard.show === 'function') {
                window.PerformanceDashboard.show();
            } else {
                alert('لوحة مراقبة الأداء غير متاحة في هذا العرض التوضيحي');
            }
        }

        // فتح مدير المصروفات
        function openExpensesManager() {
            if (window.ExpensesManager && typeof window.ExpensesManager.show === 'function') {
                window.ExpensesManager.show();
            } else {
                alert('مدير المصروفات غير متاح في هذا العرض التوضيحي');
            }
        }

        // فتح مدير التقارير
        function openReportsManager() {
            if (window.ReportsManager && typeof window.ReportsManager.show === 'function') {
                window.ReportsManager.show();
            } else {
                alert('مدير التقارير غير متاح في هذا العرض التوضيحي');
            }
        }

        // إضافة تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير hover للبطاقات
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // تأثير النقر للأزرار
            const buttons = document.querySelectorAll('.demo-button');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>