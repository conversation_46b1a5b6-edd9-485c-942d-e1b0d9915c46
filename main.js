const { app, BrowserWindow, Menu, ipcMain, dialog, shell, screen } = require('electron');
const path = require('path');
const fs = require('fs');
const { autoUpdater } = require('electron-updater');

// تعطيل تحذيرات الأمان في بيئة التطوير
process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true';

// متغيرات عامة
let mainWindow;
let splashWindow;
let isDevMode = process.argv.includes('--dev');

// إعدادات التطبيق
const APP_CONFIG = {
    name: 'Black Horse ERP',
    version: '1.0.0',
    minWidth: 1200,
    minHeight: 800,
    defaultWidth: 1400,
    defaultHeight: 900
};

// إنشاء نافذة البداية
function createSplashWindow() {
    splashWindow = new BrowserWindow({
        width: 400,
        height: 300,
        frame: false,
        alwaysOnTop: true,
        transparent: true,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true
        }
    });

    // HTML للشاشة الترحيبية
    const splashHTML = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body {
                margin: 0;
                padding: 0;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                height: 100vh;
                text-align: center;
                border-radius: 15px;
            }
            .logo {
                font-size: 3rem;
                margin-bottom: 20px;
                animation: pulse 2s infinite;
            }
            .title {
                font-size: 1.5rem;
                font-weight: bold;
                margin-bottom: 10px;
            }
            .subtitle {
                font-size: 1rem;
                opacity: 0.8;
                margin-bottom: 30px;
            }
            .loading {
                width: 200px;
                height: 4px;
                background: rgba(255,255,255,0.3);
                border-radius: 2px;
                overflow: hidden;
            }
            .loading-bar {
                width: 0%;
                height: 100%;
                background: white;
                border-radius: 2px;
                animation: loading 3s ease-in-out infinite;
            }
            @keyframes pulse {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.1); }
            }
            @keyframes loading {
                0% { width: 0%; }
                100% { width: 100%; }
            }
        </style>
    </head>
    <body>
        <div class="logo">🐎</div>
        <div class="title">Black Horse ERP</div>
        <div class="subtitle">نظام إدارة موارد المؤسسات</div>
        <div class="loading">
            <div class="loading-bar"></div>
        </div>
    </body>
    </html>
    `;

    splashWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(splashHTML));

    // إخفاء الشاشة الترحيبية بعد 3 ثوان
    setTimeout(() => {
        if (splashWindow) {
            splashWindow.close();
            splashWindow = null;
        }
        createMainWindow();
    }, 3000);
}

// إنشاء النافذة الرئيسية
function createMainWindow() {
    // الحصول على أبعاد الشاشة
    const { width, height } = screen.getPrimaryDisplay().workAreaSize;

    mainWindow = new BrowserWindow({
        width: Math.min(APP_CONFIG.defaultWidth, width),
        height: Math.min(APP_CONFIG.defaultHeight, height),
        minWidth: APP_CONFIG.minWidth,
        minHeight: APP_CONFIG.minHeight,
        show: false,
        frame: true,
        titleBarStyle: 'default',
        backgroundColor: '#2c3e50',
        icon: path.join(__dirname, 'assets/icons/icon.png'),
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true,
            preload: path.join(__dirname, 'preload.js'),
            webSecurity: false,
            allowRunningInsecureContent: true,
            experimentalFeatures: true
        },
        autoHideMenuBar: false,
        resizable: true,
        maximizable: true,
        minimizable: true,
        closable: true,
        fullscreenable: true,
        skipTaskbar: false,
        kiosk: false,
        alwaysOnTop: false,
        acceptFirstMouse: true,
        disableAutoHideCursor: false,
        enableLargerThanScreen: false,
        hasShadow: true,
        opacity: 1.0,
        transparent: false,
        type: 'normal',
        visualEffectState: 'active'
    });

    // تحميل الواجهة الرئيسية المحسنة لسطح المكتب
    const indexPath = path.join(__dirname, 'desktop-app.html');
    if (fs.existsSync(indexPath)) {
        mainWindow.loadFile(indexPath);
    } else {
        // إنشاء واجهة افتراضية محسنة لسطح المكتب
        createDesktopInterface();
    }

    // إظهار النافذة عند الانتهاء من التحميل
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        
        // فتح أدوات المطور في وضع التطوير
        if (isDevMode) {
            mainWindow.webContents.openDevTools();
        }
    });

    // التعامل مع إغلاق النافذة
    mainWindow.on('closed', () => {
        mainWindow = null;
    });

    // التعامل مع الروابط الخارجية
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });

    // معالجة أحداث النافذة الجديدة
    ipcMain.on('window-minimize', () => {
        if (mainWindow) mainWindow.minimize();
    });

    ipcMain.on('window-maximize', () => {
        if (mainWindow) {
            if (mainWindow.isMaximized()) {
                mainWindow.restore();
            } else {
                mainWindow.maximize();
            }
        }
    });

    ipcMain.on('window-restore', () => {
        if (mainWindow) mainWindow.restore();
    });

    ipcMain.on('window-close', () => {
        if (mainWindow) mainWindow.close();
    });

    ipcMain.on('toggle-fullscreen', () => {
        if (mainWindow) {
            mainWindow.setFullScreen(!mainWindow.isFullScreen());
        }
    });

    // معالجة تحميل المديولات
    ipcMain.on('load-module', (event, moduleName) => {
        console.log('تحميل المديول:', moduleName);
        // يمكن إضافة منطق تحميل المديول هنا
    });

    ipcMain.on('open-module-window', (event, moduleName) => {
        openModuleWindow(moduleName);
    });

    // معالجة النسخ الاحتياطي
    ipcMain.on('create-backup', () => {
        createBackup();
    });

    // إنشاء القائمة
    createMenu();
}

// إنشاء قائمة التطبيق
function createMenu() {
    const template = [
        {
            label: 'ملف',
            submenu: [
                {
                    label: 'جديد',
                    accelerator: 'CmdOrCtrl+N',
                    click: () => {
                        // إضافة وظيفة جديد
                    }
                },
                {
                    label: 'فتح',
                    accelerator: 'CmdOrCtrl+O',
                    click: async () => {
                        const result = await dialog.showOpenDialog(mainWindow, {
                            properties: ['openFile'],
                            filters: [
                                { name: 'ملفات HTML', extensions: ['html', 'htm'] },
                                { name: 'جميع الملفات', extensions: ['*'] }
                            ]
                        });
                        
                        if (!result.canceled && result.filePaths.length > 0) {
                            mainWindow.loadFile(result.filePaths[0]);
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'إعدادات',
                    accelerator: 'CmdOrCtrl+,',
                    click: () => {
                        // فتح نافذة الإعدادات
                        mainWindow.webContents.send('open-settings');
                    }
                },
                { type: 'separator' },
                {
                    label: 'خروج',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'تحرير',
            submenu: [
                { label: 'تراجع', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
                { label: 'إعادة', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
                { type: 'separator' },
                { label: 'قص', accelerator: 'CmdOrCtrl+X', role: 'cut' },
                { label: 'نسخ', accelerator: 'CmdOrCtrl+C', role: 'copy' },
                { label: 'لصق', accelerator: 'CmdOrCtrl+V', role: 'paste' },
                { label: 'تحديد الكل', accelerator: 'CmdOrCtrl+A', role: 'selectall' }
            ]
        },
        {
            label: 'عرض',
            submenu: [
                { label: 'إعادة تحميل', accelerator: 'CmdOrCtrl+R', role: 'reload' },
                { label: 'إعادة تحميل قسري', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
                { label: 'أدوات المطور', accelerator: 'F12', role: 'toggleDevTools' },
                { type: 'separator' },
                { label: 'تكبير', accelerator: 'CmdOrCtrl+Plus', role: 'zoomin' },
                { label: 'تصغير', accelerator: 'CmdOrCtrl+-', role: 'zoomout' },
                { label: 'حجم طبيعي', accelerator: 'CmdOrCtrl+0', role: 'resetzoom' },
                { type: 'separator' },
                { label: 'ملء الشاشة', accelerator: 'F11', role: 'togglefullscreen' }
            ]
        },
        {
            label: 'نافذة',
            submenu: [
                { label: 'تصغير', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
                { label: 'إغلاق', accelerator: 'CmdOrCtrl+W', role: 'close' }
            ]
        },
        {
            label: 'مساعدة',
            submenu: [
                {
                    label: 'حول البرنامج',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'حول Black Horse ERP',
                            message: `${APP_CONFIG.name} v${APP_CONFIG.version}`,
                            detail: 'نظام إدارة موارد المؤسسات الشامل\nمطور بواسطة فريق Black Horse',
                            buttons: ['موافق']
                        });
                    }
                },
                {
                    label: 'التحقق من التحديثات',
                    click: () => {
                        autoUpdater.checkForUpdatesAndNotify();
                    }
                },
                { type: 'separator' },
                {
                    label: 'موقع الدعم',
                    click: () => {
                        shell.openExternal('https://blackhorse.com/support');
                    }
                }
            ]
        }
    ];

    // تعديل القائمة لنظام macOS
    if (process.platform === 'darwin') {
        template.unshift({
            label: app.getName(),
            submenu: [
                { label: 'حول ' + app.getName(), role: 'about' },
                { type: 'separator' },
                { label: 'الخدمات', role: 'services', submenu: [] },
                { type: 'separator' },
                { label: 'إخفاء ' + app.getName(), accelerator: 'Command+H', role: 'hide' },
                { label: 'إخفاء الآخرين', accelerator: 'Command+Shift+H', role: 'hideothers' },
                { label: 'إظهار الكل', role: 'unhide' },
                { type: 'separator' },
                { label: 'خروج', accelerator: 'Command+Q', click: () => app.quit() }
            ]
        });
    }

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

// التعامل مع أحداث التطبيق
app.whenReady().then(() => {
    createSplashWindow();
    
    // إعداد التحديثات التلقائية
    if (!isDevMode) {
        autoUpdater.checkForUpdatesAndNotify();
    }
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createMainWindow();
    }
});

// التعامل مع أحداث IPC
ipcMain.handle('get-app-version', () => {
    return app.getVersion();
});

ipcMain.handle('get-app-path', () => {
    return app.getAppPath();
});

ipcMain.handle('show-message-box', async (event, options) => {
    const result = await dialog.showMessageBox(mainWindow, options);
    return result;
});

ipcMain.handle('show-save-dialog', async (event, options) => {
    const result = await dialog.showSaveDialog(mainWindow, options);
    return result;
});

ipcMain.handle('show-open-dialog', async (event, options) => {
    const result = await dialog.showOpenDialog(mainWindow, options);
    return result;
});

// التعامل مع التحديثات
autoUpdater.on('checking-for-update', () => {
    console.log('جاري البحث عن تحديثات...');
});

autoUpdater.on('update-available', (info) => {
    console.log('تحديث متاح:', info);
});

autoUpdater.on('update-not-available', (info) => {
    console.log('لا توجد تحديثات متاحة:', info);
});

autoUpdater.on('error', (err) => {
    console.log('خطأ في التحديث:', err);
});

autoUpdater.on('download-progress', (progressObj) => {
    let log_message = "سرعة التحميل: " + progressObj.bytesPerSecond;
    log_message = log_message + ' - تم تحميل ' + progressObj.percent + '%';
    log_message = log_message + ' (' + progressObj.transferred + "/" + progressObj.total + ')';
    console.log(log_message);
});

autoUpdater.on('update-downloaded', (info) => {
    console.log('تم تحميل التحديث:', info);
    autoUpdater.quitAndInstall();
});

// دالة إنشاء واجهة سطح المكتب الافتراضية
function createDesktopInterface() {
    const desktopHTML = `
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Black Horse ERP - نظام إدارة موارد المؤسسات</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Segoe UI', 'Cairo', 'Tahoma', sans-serif;
                background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
                color: #ecf0f1;
                height: 100vh;
                overflow: hidden;
                user-select: none;
                -webkit-app-region: no-drag;
            }

            .desktop-container {
                display: flex;
                height: 100vh;
                position: relative;
            }

            .sidebar {
                width: 280px;
                background: rgba(52, 73, 94, 0.95);
                backdrop-filter: blur(10px);
                border-left: 1px solid rgba(236, 240, 241, 0.1);
                display: flex;
                flex-direction: column;
                transition: all 0.3s ease;
            }

            .sidebar.collapsed {
                width: 70px;
            }

            .logo-section {
                padding: 20px;
                text-align: center;
                border-bottom: 1px solid rgba(236, 240, 241, 0.1);
                background: rgba(41, 128, 185, 0.1);
            }

            .logo {
                font-size: 2.5rem;
                margin-bottom: 10px;
                animation: pulse 2s infinite;
            }

            .logo-text {
                font-size: 1.2rem;
                font-weight: bold;
                color: #3498db;
                margin-bottom: 5px;
            }

            .logo-subtitle {
                font-size: 0.9rem;
                color: #bdc3c7;
            }

            .nav-menu {
                flex: 1;
                padding: 20px 0;
                overflow-y: auto;
            }

            .nav-item {
                display: flex;
                align-items: center;
                padding: 15px 20px;
                margin: 5px 15px;
                border-radius: 10px;
                cursor: pointer;
                transition: all 0.3s ease;
                position: relative;
            }

            .nav-item:hover {
                background: rgba(52, 152, 219, 0.2);
                transform: translateX(-5px);
            }

            .nav-item.active {
                background: linear-gradient(135deg, #3498db, #2980b9);
                box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
            }

            .nav-icon {
                font-size: 1.3rem;
                margin-left: 15px;
                min-width: 20px;
            }

            .nav-text {
                font-size: 1rem;
                font-weight: 500;
            }

            .main-content {
                flex: 1;
                display: flex;
                flex-direction: column;
                background: rgba(236, 240, 241, 0.05);
            }

            .header {
                height: 70px;
                background: rgba(44, 62, 80, 0.95);
                backdrop-filter: blur(10px);
                border-bottom: 1px solid rgba(236, 240, 241, 0.1);
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 0 30px;
                -webkit-app-region: drag;
            }

            .header-left {
                display: flex;
                align-items: center;
                -webkit-app-region: no-drag;
            }

            .menu-toggle {
                background: none;
                border: none;
                color: #ecf0f1;
                font-size: 1.2rem;
                cursor: pointer;
                padding: 10px;
                border-radius: 5px;
                transition: all 0.3s ease;
                margin-left: 15px;
            }

            .menu-toggle:hover {
                background: rgba(52, 152, 219, 0.2);
            }

            .page-title {
                font-size: 1.3rem;
                font-weight: bold;
                color: #3498db;
                margin-right: 20px;
            }

            .header-right {
                display: flex;
                align-items: center;
                gap: 15px;
                -webkit-app-region: no-drag;
            }

            .header-btn {
                background: rgba(52, 152, 219, 0.2);
                border: none;
                color: #ecf0f1;
                padding: 8px 15px;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.3s ease;
                font-size: 0.9rem;
            }

            .header-btn:hover {
                background: rgba(52, 152, 219, 0.4);
                transform: translateY(-2px);
            }

            .content-area {
                flex: 1;
                padding: 30px;
                overflow-y: auto;
                background: rgba(236, 240, 241, 0.02);
            }

            .dashboard-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 25px;
                margin-bottom: 30px;
            }

            .dashboard-card {
                background: rgba(52, 73, 94, 0.8);
                backdrop-filter: blur(10px);
                border-radius: 15px;
                padding: 25px;
                border: 1px solid rgba(236, 240, 241, 0.1);
                transition: all 0.3s ease;
                cursor: pointer;
                position: relative;
                overflow: hidden;
            }

            .dashboard-card::before {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                width: 100%;
                height: 3px;
                background: linear-gradient(90deg, #3498db, #2980b9);
                transform: scaleX(0);
                transition: transform 0.3s ease;
            }

            .dashboard-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
                border-color: rgba(52, 152, 219, 0.3);
            }

            .dashboard-card:hover::before {
                transform: scaleX(1);
            }

            .card-header {
                display: flex;
                align-items: center;
                margin-bottom: 20px;
            }

            .card-icon {
                font-size: 2rem;
                margin-left: 15px;
                color: #3498db;
            }

            .card-title {
                font-size: 1.2rem;
                font-weight: bold;
                color: #ecf0f1;
            }

            .card-description {
                color: #bdc3c7;
                line-height: 1.6;
                margin-bottom: 20px;
            }

            .card-stats {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .stat-value {
                font-size: 1.5rem;
                font-weight: bold;
                color: #3498db;
            }

            .stat-label {
                font-size: 0.9rem;
                color: #95a5a6;
            }

            .quick-actions {
                background: rgba(52, 73, 94, 0.6);
                backdrop-filter: blur(10px);
                border-radius: 15px;
                padding: 25px;
                border: 1px solid rgba(236, 240, 241, 0.1);
            }

            .quick-actions h3 {
                color: #3498db;
                margin-bottom: 20px;
                font-size: 1.3rem;
            }

            .actions-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 15px;
            }

            .action-btn {
                background: rgba(52, 152, 219, 0.1);
                border: 2px solid rgba(52, 152, 219, 0.3);
                color: #3498db;
                padding: 15px;
                border-radius: 10px;
                cursor: pointer;
                transition: all 0.3s ease;
                text-align: center;
                font-weight: 500;
            }

            .action-btn:hover {
                background: rgba(52, 152, 219, 0.2);
                border-color: #3498db;
                transform: translateY(-3px);
            }

            .status-bar {
                height: 30px;
                background: rgba(44, 62, 80, 0.95);
                border-top: 1px solid rgba(236, 240, 241, 0.1);
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 0 20px;
                font-size: 0.8rem;
                color: #95a5a6;
            }

            @keyframes pulse {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.05); }
            }

            @keyframes slideIn {
                from { opacity: 0; transform: translateX(30px); }
                to { opacity: 1; transform: translateX(0); }
            }

            .slide-in {
                animation: slideIn 0.5s ease-out;
            }

            /* تحسينات للشاشات الصغيرة */
            @media (max-width: 768px) {
                .sidebar {
                    width: 70px;
                }

                .dashboard-grid {
                    grid-template-columns: 1fr;
                }

                .actions-grid {
                    grid-template-columns: repeat(2, 1fr);
                }
            }

            /* تأثيرات التمرير */
            ::-webkit-scrollbar {
                width: 8px;
            }

            ::-webkit-scrollbar-track {
                background: rgba(44, 62, 80, 0.3);
            }

            ::-webkit-scrollbar-thumb {
                background: rgba(52, 152, 219, 0.5);
                border-radius: 4px;
            }

            ::-webkit-scrollbar-thumb:hover {
                background: rgba(52, 152, 219, 0.7);
            }
        </style>
    </head>
    <body>
        <div class="desktop-container">
            <!-- الشريط الجانبي -->
            <div class="sidebar" id="sidebar">
                <div class="logo-section">
                    <div class="logo">🐎</div>
                    <div class="logo-text">Black Horse</div>
                    <div class="logo-subtitle">نظام إدارة المؤسسات</div>
                </div>

                <div class="nav-menu">
                    <div class="nav-item active" onclick="loadModule('dashboard')">
                        <div class="nav-icon">🏠</div>
                        <div class="nav-text">الرئيسية</div>
                    </div>
                    <div class="nav-item" onclick="loadModule('inventory')">
                        <div class="nav-icon">📦</div>
                        <div class="nav-text">المخزون</div>
                    </div>
                    <div class="nav-item" onclick="loadModule('sales')">
                        <div class="nav-icon">💰</div>
                        <div class="nav-text">المبيعات</div>
                    </div>
                    <div class="nav-item" onclick="loadModule('pos')">
                        <div class="nav-icon">🛒</div>
                        <div class="nav-text">نقاط البيع</div>
                    </div>
                    <div class="nav-item" onclick="loadModule('purchases')">
                        <div class="nav-icon">📋</div>
                        <div class="nav-text">المشتريات</div>
                    </div>
                    <div class="nav-item" onclick="loadModule('hr')">
                        <div class="nav-icon">👥</div>
                        <div class="nav-text">الموارد البشرية</div>
                    </div>
                    <div class="nav-item" onclick="loadModule('cashier')">
                        <div class="nav-icon">💳</div>
                        <div class="nav-text">الصرافين</div>
                    </div>
                    <div class="nav-item" onclick="loadModule('reports')">
                        <div class="nav-icon">📊</div>
                        <div class="nav-text">التقارير</div>
                    </div>
                    <div class="nav-item" onclick="loadModule('settings')">
                        <div class="nav-icon">⚙️</div>
                        <div class="nav-text">الإعدادات</div>
                    </div>
                </div>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="main-content">
                <div class="header">
                    <div class="header-left">
                        <button class="menu-toggle" onclick="toggleSidebar()">☰</button>
                        <div class="page-title" id="pageTitle">لوحة التحكم الرئيسية</div>
                    </div>
                    <div class="header-right">
                        <button class="header-btn" onclick="showNotifications()">🔔 الإشعارات</button>
                        <button class="header-btn" onclick="showProfile()">👤 الملف الشخصي</button>
                        <button class="header-btn" onclick="minimizeApp()">➖</button>
                        <button class="header-btn" onclick="maximizeApp()">⬜</button>
                        <button class="header-btn" onclick="closeApp()">✖️</button>
                    </div>
                </div>

                <div class="content-area" id="contentArea">
                    <!-- محتوى لوحة التحكم -->
                    <div class="dashboard-grid">
                        <div class="dashboard-card" onclick="loadModule('inventory')">
                            <div class="card-header">
                                <div class="card-icon">📦</div>
                                <div class="card-title">إدارة المخزون</div>
                            </div>
                            <div class="card-description">
                                إدارة شاملة للمنتجات والمخزون مع تتبع الكميات والتنبيهات
                            </div>
                            <div class="card-stats">
                                <div>
                                    <div class="stat-value">1,234</div>
                                    <div class="stat-label">منتج</div>
                                </div>
                                <div>
                                    <div class="stat-value">89%</div>
                                    <div class="stat-label">متوفر</div>
                                </div>
                            </div>
                        </div>

                        <div class="dashboard-card" onclick="loadModule('sales')">
                            <div class="card-header">
                                <div class="card-icon">💰</div>
                                <div class="card-title">إدارة المبيعات</div>
                            </div>
                            <div class="card-description">
                                تتبع المبيعات والفواتير وإدارة العملاء والحسابات
                            </div>
                            <div class="card-stats">
                                <div>
                                    <div class="stat-value">45,678</div>
                                    <div class="stat-label">ج.م</div>
                                </div>
                                <div>
                                    <div class="stat-value">156</div>
                                    <div class="stat-label">فاتورة</div>
                                </div>
                            </div>
                        </div>

                        <div class="dashboard-card" onclick="loadModule('pos')">
                            <div class="card-header">
                                <div class="card-icon">🛒</div>
                                <div class="card-title">نقاط البيع</div>
                            </div>
                            <div class="card-description">
                                نظام نقاط بيع سريع مع دعم الباركود والطباعة الحرارية
                            </div>
                            <div class="card-stats">
                                <div>
                                    <div class="stat-value">23</div>
                                    <div class="stat-label">معاملة اليوم</div>
                                </div>
                                <div>
                                    <div class="stat-value">3</div>
                                    <div class="stat-label">نقطة بيع</div>
                                </div>
                            </div>
                        </div>

                        <div class="dashboard-card" onclick="loadModule('hr')">
                            <div class="card-header">
                                <div class="card-icon">👥</div>
                                <div class="card-title">الموارد البشرية</div>
                            </div>
                            <div class="card-description">
                                إدارة الموظفين والحضور والرواتب والإجازات
                            </div>
                            <div class="card-stats">
                                <div>
                                    <div class="stat-value">24</div>
                                    <div class="stat-label">موظف</div>
                                </div>
                                <div>
                                    <div class="stat-value">92%</div>
                                    <div class="stat-label">حضور</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="quick-actions">
                        <h3>الإجراءات السريعة</h3>
                        <div class="actions-grid">
                            <div class="action-btn" onclick="quickAction('new-sale')">
                                💰 بيع جديد
                            </div>
                            <div class="action-btn" onclick="quickAction('add-product')">
                                📦 إضافة منتج
                            </div>
                            <div class="action-btn" onclick="quickAction('new-customer')">
                                👤 عميل جديد
                            </div>
                            <div class="action-btn" onclick="quickAction('daily-report')">
                                📊 تقرير يومي
                            </div>
                            <div class="action-btn" onclick="quickAction('backup')">
                                💾 نسخة احتياطية
                            </div>
                            <div class="action-btn" onclick="quickAction('settings')">
                                ⚙️ الإعدادات
                            </div>
                        </div>
                    </div>
                </div>

                <div class="status-bar">
                    <div>Black Horse ERP v1.0.0 - جاهز</div>
                    <div id="statusTime"></div>
                </div>
            </div>
        </div>

        <script>
            // تحديث الوقت في شريط الحالة
            function updateTime() {
                const now = new Date();
                const timeString = now.toLocaleString('ar-EG', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
                document.getElementById('statusTime').textContent = timeString;
            }

            // تبديل الشريط الجانبي
            function toggleSidebar() {
                const sidebar = document.getElementById('sidebar');
                sidebar.classList.toggle('collapsed');
            }

            // تحميل المديولات
            function loadModule(moduleName) {
                // إزالة الحالة النشطة من جميع العناصر
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });

                // إضافة الحالة النشطة للعنصر المحدد
                event.target.closest('.nav-item').classList.add('active');

                // تحديث عنوان الصفحة
                const titles = {
                    'dashboard': 'لوحة التحكم الرئيسية',
                    'inventory': 'إدارة المخزون',
                    'sales': 'إدارة المبيعات',
                    'pos': 'نقاط البيع',
                    'purchases': 'إدارة المشتريات',
                    'hr': 'الموارد البشرية',
                    'cashier': 'إدارة الصرافين',
                    'reports': 'التقارير والتحليلات',
                    'settings': 'إعدادات النظام'
                };

                document.getElementById('pageTitle').textContent = titles[moduleName] || 'Black Horse ERP';

                // تحميل محتوى المديول
                if (moduleName !== 'dashboard') {
                    // استخدام Electron API لفتح المديول في نافذة جديدة أو تحميله في المحتوى
                    if (window.electronAPI) {
                        window.electronAPI.send('load-module', moduleName);
                    } else {
                        // fallback للتطوير
                        window.open(\`src/modules/\${moduleName}/\${moduleName}.html\`, '_blank');
                    }
                }
            }

            // الإجراءات السريعة
            function quickAction(action) {
                console.log('تنفيذ الإجراء:', action);
                // تنفيذ الإجراءات السريعة
                switch(action) {
                    case 'new-sale':
                        loadModule('pos');
                        break;
                    case 'add-product':
                        loadModule('inventory');
                        break;
                    case 'new-customer':
                        loadModule('sales');
                        break;
                    case 'daily-report':
                        loadModule('reports');
                        break;
                    case 'backup':
                        if (window.electronAPI) {
                            window.electronAPI.send('create-backup');
                        }
                        break;
                    case 'settings':
                        loadModule('settings');
                        break;
                }
            }

            // وظائف النافذة
            function minimizeApp() {
                if (window.electronAPI) {
                    window.electronAPI.send('window-minimize');
                }
            }

            function maximizeApp() {
                if (window.electronAPI) {
                    window.electronAPI.send('window-maximize');
                }
            }

            function closeApp() {
                if (window.electronAPI) {
                    window.electronAPI.send('window-close');
                } else {
                    window.close();
                }
            }

            function showNotifications() {
                alert('الإشعارات - قريباً');
            }

            function showProfile() {
                alert('الملف الشخصي - قريباً');
            }

            // تهيئة التطبيق
            document.addEventListener('DOMContentLoaded', function() {
                updateTime();
                setInterval(updateTime, 1000);

                // إضافة تأثيرات الحركة
                document.querySelectorAll('.dashboard-card').forEach((card, index) => {
                    setTimeout(() => {
                        card.classList.add('slide-in');
                    }, index * 100);
                });
            });

            // معالجة أحداث لوحة المفاتيح
            document.addEventListener('keydown', function(e) {
                // Ctrl+M لتبديل الشريط الجانبي
                if (e.ctrlKey && e.key === 'm') {
                    e.preventDefault();
                    toggleSidebar();
                }

                // F11 للشاشة الكاملة
                if (e.key === 'F11') {
                    e.preventDefault();
                    if (window.electronAPI) {
                        window.electronAPI.send('toggle-fullscreen');
                    }
                }

                // Ctrl+Q للخروج
                if (e.ctrlKey && e.key === 'q') {
                    e.preventDefault();
                    closeApp();
                }
            });
        </script>
    </body>
    </html>
    `;

    mainWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(desktopHTML));
}

// فتح نافذة مديول جديدة
function openModuleWindow(moduleName) {
    const moduleWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        parent: mainWindow,
        modal: false,
        show: false,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true,
            webSecurity: false
        },
        title: `Black Horse ERP - ${getModuleTitle(moduleName)}`,
        icon: path.join(__dirname, 'assets', 'icon.png')
    });

    const modulePath = path.join(__dirname, 'src', 'modules', moduleName, `${moduleName}.html`);

    if (fs.existsSync(modulePath)) {
        moduleWindow.loadFile(modulePath);
    } else {
        // إنشاء صفحة خطأ
        const errorHTML = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>خطأ - المديول غير موجود</title>
            <style>
                body {
                    font-family: 'Segoe UI', 'Cairo', sans-serif;
                    text-align: center;
                    padding: 50px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }
                .error-container {
                    background: rgba(255,255,255,0.1);
                    padding: 40px;
                    border-radius: 20px;
                    backdrop-filter: blur(10px);
                }
            </style>
        </head>
        <body>
            <div class="error-container">
                <h1>🚫 المديول غير موجود</h1>
                <p>المديول "${moduleName}" غير متاح حالياً</p>
                <button onclick="window.close()" style="padding: 10px 20px; margin-top: 20px; border: none; border-radius: 5px; background: #4CAF50; color: white; cursor: pointer;">إغلاق</button>
            </div>
        </body>
        </html>
        `;
        moduleWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(errorHTML));
    }

    moduleWindow.once('ready-to-show', () => {
        moduleWindow.show();
    });

    moduleWindow.on('closed', () => {
        // تنظيف المراجع
    });
}

// الحصول على عنوان المديول
function getModuleTitle(moduleName) {
    const titles = {
        'inventory': 'إدارة المخزون',
        'sales': 'إدارة المبيعات',
        'pos': 'نقاط البيع',
        'purchases': 'إدارة المشتريات',
        'hr': 'الموارد البشرية',
        'cashier': 'إدارة الصرافين',
        'reports': 'التقارير والتحليلات',
        'settings': 'إعدادات النظام'
    };
    return titles[moduleName] || moduleName;
}

// إنشاء نسخة احتياطية
function createBackup() {
    const { dialog } = require('electron');
    const archiver = require('archiver');

    dialog.showSaveDialog(mainWindow, {
        title: 'حفظ النسخة الاحتياطية',
        defaultPath: `backup-${new Date().toISOString().split('T')[0]}.zip`,
        filters: [
            { name: 'ملفات مضغوطة', extensions: ['zip'] }
        ]
    }).then(result => {
        if (!result.canceled) {
            const output = fs.createWriteStream(result.filePath);
            const archive = archiver('zip', { zlib: { level: 9 } });

            output.on('close', () => {
                console.log('تم إنشاء النسخة الاحتياطية بنجاح');
                if (mainWindow) {
                    mainWindow.webContents.send('backup-completed', result.filePath);
                }
            });

            archive.on('error', (err) => {
                console.error('خطأ في إنشاء النسخة الاحتياطية:', err);
            });

            archive.pipe(output);
            archive.directory('src/', 'src/');
            archive.directory('data/', 'data/');
            archive.file('package.json', { name: 'package.json' });
            archive.finalize();
        }
    }).catch(err => {
        console.error('خطأ في حفظ النسخة الاحتياطية:', err);
    });
}

// تصدير المتغيرات للاستخدام في ملفات أخرى
module.exports = {
    mainWindow,
    APP_CONFIG,
    createDesktopInterface,
    openModuleWindow,
    createBackup
};
