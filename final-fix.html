<!DOCTYPE html>
<html>
<head>
    <title>Final Fix Test</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>اختبار الإصلاح النهائي</h1>
    
    <div>
        <h2>إضافة منتج</h2>
        <form id="testForm">
            <div>
                <label>اسم المنتج:</label>
                <input type="text" id="productName" placeholder="اسم المنتج">
            </div>
            <div>
                <label>السعر:</label>
                <input type="number" id="productPrice" placeholder="100">
            </div>
            <div>
                <label>الكمية:</label>
                <input type="number" id="productStock" placeholder="10">
            </div>
            <div>
                <label>الباركود:</label>
                <input type="text" id="productBarcode" placeholder="سيتم توليده تلقائياً">
                <button type="button" onclick="generateBarcode()">توليد باركود</button>
            </div>
            <div>
                <button type="button" onclick="addProduct()">حفظ المنتج</button>
            </div>
        </form>
    </div>
    
    <div id="results">
        <h2>النتائج</h2>
        <div id="output"></div>
    </div>

    <script>
        // Simple working functions
        function addProduct() {
            console.log('🔄 addProduct called');
            
            const name = document.getElementById('productName').value;
            const price = document.getElementById('productPrice').value;
            const stock = document.getElementById('productStock').value;
            const barcode = document.getElementById('productBarcode').value;
            
            if (!name || !price || !stock) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }
            
            const product = {
                name: name,
                price: parseFloat(price),
                stock: parseInt(stock),
                barcode: barcode || generateBarcodeValue(),
                id: Date.now()
            };
            
            // Save to localStorage
            let products = JSON.parse(localStorage.getItem('products') || '[]');
            products.push(product);
            localStorage.setItem('products', JSON.stringify(products));
            
            // Show result
            document.getElementById('output').innerHTML = 
                '<p>✅ تم إضافة المنتج بنجاح:</p>' +
                '<pre>' + JSON.stringify(product, null, 2) + '</pre>';
            
            // Clear form
            document.getElementById('testForm').reset();
            
            alert('تم إضافة المنتج بنجاح!');
        }
        
        function generateBarcode() {
            console.log('🔄 generateBarcode called');
            
            const barcode = generateBarcodeValue();
            document.getElementById('productBarcode').value = barcode;
            
            alert('تم توليد الباركود: ' + barcode);
        }
        
        function generateBarcodeValue() {
            return '2' + Date.now().toString().slice(-11);
        }
        
        // Test on load
        window.onload = function() {
            console.log('✅ Test page loaded');
            alert('صفحة الاختبار جاهزة - جرب إضافة منتج');
        };
    </script>
</body>
</html>
