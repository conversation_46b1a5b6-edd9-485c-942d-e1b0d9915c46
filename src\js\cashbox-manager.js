// نظام إدارة حالة الصندوق الشامل
class CashboxManager {
    constructor() {
        this.cashboxData = this.loadCashboxData();
        this.paymentMethods = {
            'cash': 'نقدي',
            'visa': 'فيزا',
            'vodafone_cash': 'فودافون كاش',
            'instapay': 'انستاباي',
            'bank_transfer': 'تحويل بنكي',
            'check': 'شيك'
        };
        this.initializeCashbox();
    }

    // تهيئة الصندوق
    initializeCashbox() {
        if (!this.cashboxData.balances) {
            this.cashboxData.balances = {};
            Object.keys(this.paymentMethods).forEach(method => {
                this.cashboxData.balances[method] = 0;
            });
        }
        
        if (!this.cashboxData.transactions) {
            this.cashboxData.transactions = [];
        }
        
        if (!this.cashboxData.dailyReports) {
            this.cashboxData.dailyReports = {};
        }
        
        this.saveCashboxData();
    }

    // تحميل بيانات الصندوق
    loadCashboxData() {
        try {
            const data = localStorage.getItem('blackhorse_cashbox');
            return data ? JSON.parse(data) : {};
        } catch (error) {
            console.error('خطأ في تحميل بيانات الصندوق:', error);
            return {};
        }
    }

    // حفظ بيانات الصندوق
    saveCashboxData() {
        try {
            localStorage.setItem('blackhorse_cashbox', JSON.stringify(this.cashboxData));
        } catch (error) {
            console.error('خطأ في حفظ بيانات الصندوق:', error);
        }
    }

    // إضافة معاملة (بيع)
    addSaleTransaction(amount, paymentMethod, description = 'بيع') {
        const transaction = {
            id: this.generateTransactionId(),
            type: 'sale',
            amount: parseFloat(amount),
            paymentMethod: paymentMethod,
            description: description,
            timestamp: new Date().toISOString(),
            date: new Date().toLocaleDateString('ar-EG')
        };

        // إضافة المبلغ للرصيد
        this.cashboxData.balances[paymentMethod] += transaction.amount;
        
        // إضافة المعاملة للسجل
        this.cashboxData.transactions.push(transaction);
        
        this.saveCashboxData();
        this.updateDailyReport();
        
        return transaction;
    }

    // إضافة معاملة (مصروف)
    addExpenseTransaction(amount, paymentMethod, description = 'مصروف') {
        const transaction = {
            id: this.generateTransactionId(),
            type: 'expense',
            amount: parseFloat(amount),
            paymentMethod: paymentMethod,
            description: description,
            timestamp: new Date().toISOString(),
            date: new Date().toLocaleDateString('ar-EG')
        };

        // خصم المبلغ من الرصيد
        this.cashboxData.balances[paymentMethod] -= transaction.amount;
        
        // إضافة المعاملة للسجل
        this.cashboxData.transactions.push(transaction);
        
        this.saveCashboxData();
        this.updateDailyReport();
        
        return transaction;
    }

    // إضافة معاملة (شراء)
    addPurchaseTransaction(amount, paymentMethod, description = 'شراء') {
        const transaction = {
            id: this.generateTransactionId(),
            type: 'purchase',
            amount: parseFloat(amount),
            paymentMethod: paymentMethod,
            description: description,
            timestamp: new Date().toISOString(),
            date: new Date().toLocaleDateString('ar-EG')
        };

        // خصم المبلغ من الرصيد
        this.cashboxData.balances[paymentMethod] -= transaction.amount;
        
        // إضافة المعاملة للسجل
        this.cashboxData.transactions.push(transaction);
        
        this.saveCashboxData();
        this.updateDailyReport();
        
        return transaction;
    }

    // الحصول على رصيد طريقة دفع معينة
    getBalance(paymentMethod) {
        return this.cashboxData.balances[paymentMethod] || 0;
    }

    // الحصول على إجمالي الرصيد
    getTotalBalance() {
        return Object.values(this.cashboxData.balances).reduce((total, balance) => total + balance, 0);
    }

    // الحصول على معاملات اليوم
    getTodayTransactions() {
        const today = new Date().toLocaleDateString('ar-EG');
        return this.cashboxData.transactions.filter(transaction => transaction.date === today);
    }

    // الحصول على معاملات فترة معينة
    getTransactionsByDateRange(startDate, endDate) {
        return this.cashboxData.transactions.filter(transaction => {
            const transactionDate = new Date(transaction.timestamp);
            return transactionDate >= startDate && transactionDate <= endDate;
        });
    }

    // تحديث التقرير اليومي
    updateDailyReport() {
        const today = new Date().toLocaleDateString('ar-EG');
        const todayTransactions = this.getTodayTransactions();
        
        const report = {
            date: today,
            sales: {
                count: 0,
                total: 0,
                byPaymentMethod: {}
            },
            expenses: {
                count: 0,
                total: 0,
                byPaymentMethod: {}
            },
            purchases: {
                count: 0,
                total: 0,
                byPaymentMethod: {}
            }
        };

        // تهيئة طرق الدفع
        Object.keys(this.paymentMethods).forEach(method => {
            report.sales.byPaymentMethod[method] = 0;
            report.expenses.byPaymentMethod[method] = 0;
            report.purchases.byPaymentMethod[method] = 0;
        });

        // حساب الإحصائيات
        todayTransactions.forEach(transaction => {
            if (transaction.type === 'sale') {
                report.sales.count++;
                report.sales.total += transaction.amount;
                report.sales.byPaymentMethod[transaction.paymentMethod] += transaction.amount;
            } else if (transaction.type === 'expense') {
                report.expenses.count++;
                report.expenses.total += transaction.amount;
                report.expenses.byPaymentMethod[transaction.paymentMethod] += transaction.amount;
            } else if (transaction.type === 'purchase') {
                report.purchases.count++;
                report.purchases.total += transaction.amount;
                report.purchases.byPaymentMethod[transaction.paymentMethod] += transaction.amount;
            }
        });

        this.cashboxData.dailyReports[today] = report;
        this.saveCashboxData();
    }

    // الحصول على التقرير اليومي
    getDailyReport(date = null) {
        const targetDate = date || new Date().toLocaleDateString('ar-EG');
        return this.cashboxData.dailyReports[targetDate] || null;
    }

    // توليد معرف معاملة
    generateTransactionId() {
        return 'TXN_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // تصدير البيانات
    exportData() {
        return {
            balances: this.cashboxData.balances,
            transactions: this.cashboxData.transactions,
            dailyReports: this.cashboxData.dailyReports,
            exportDate: new Date().toISOString()
        };
    }

    // استيراد البيانات
    importData(data) {
        try {
            this.cashboxData = {
                balances: data.balances || {},
                transactions: data.transactions || [],
                dailyReports: data.dailyReports || {}
            };
            this.saveCashboxData();
            return true;
        } catch (error) {
            console.error('خطأ في استيراد البيانات:', error);
            return false;
        }
    }

    // إعادة تعيين الصندوق
    resetCashbox() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع بيانات الصندوق؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            this.cashboxData = {};
            this.initializeCashbox();
            return true;
        }
        return false;
    }
}

// إنشاء مثيل عام لإدارة الصندوق
window.cashboxManager = new CashboxManager();

// دالة عرض حالة الصندوق
function showCashboxStatus() {
    const balances = window.cashboxManager.cashboxData.balances;
    const totalBalance = window.cashboxManager.getTotalBalance();
    const todayReport = window.cashboxManager.getDailyReport();
    
    let statusHTML = `
        <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); max-width: 800px; margin: 20px auto;">
            <h3 style="color: #333; margin-bottom: 25px; text-align: center;">
                <i class="fas fa-wallet" style="color: #9C27B0;"></i> حالة الصندوق
            </h3>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px;">
    `;
    
    Object.keys(window.cashboxManager.paymentMethods).forEach(method => {
        const balance = balances[method] || 0;
        const methodName = window.cashboxManager.paymentMethods[method];
        statusHTML += `
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                <div style="font-size: 1.2em; margin-bottom: 10px;">${methodName}</div>
                <div style="font-size: 1.5em; font-weight: bold;">${balance.toFixed(2)} ج.م</div>
            </div>
        `;
    });
    
    statusHTML += `
            </div>
            
            <div style="background: linear-gradient(135deg, #4CAF50, #45a049); color: white; padding: 20px; border-radius: 10px; text-align: center; margin-bottom: 20px;">
                <div style="font-size: 1.3em; margin-bottom: 10px;">إجمالي الرصيد</div>
                <div style="font-size: 2em; font-weight: bold;">${totalBalance.toFixed(2)} ج.م</div>
            </div>
    `;
    
    if (todayReport) {
        statusHTML += `
            <div style="background: #f5f5f5; padding: 20px; border-radius: 10px;">
                <h4 style="color: #333; margin-bottom: 15px;">تقرير اليوم</h4>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">
                    <div style="text-align: center;">
                        <div style="color: #4CAF50; font-size: 1.5em; font-weight: bold;">${todayReport.sales.total.toFixed(2)} ج.م</div>
                        <div style="color: #666;">المبيعات (${todayReport.sales.count})</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: #f44336; font-size: 1.5em; font-weight: bold;">${todayReport.expenses.total.toFixed(2)} ج.م</div>
                        <div style="color: #666;">المصروفات (${todayReport.expenses.count})</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="color: #FF9800; font-size: 1.5em; font-weight: bold;">${todayReport.purchases.total.toFixed(2)} ج.م</div>
                        <div style="color: #666;">المشتريات (${todayReport.purchases.count})</div>
                    </div>
                </div>
            </div>
        `;
    }
    
    statusHTML += `
            <div style="text-align: center; margin-top: 20px;">
                <button onclick="showDashboard()" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 12px 25px; border-radius: 8px; cursor: pointer; margin: 5px;">
                    <i class="fas fa-home"></i> العودة للرئيسية
                </button>
                <button onclick="window.cashboxManager.resetCashbox(); showCashboxStatus();" style="background: linear-gradient(135deg, #f44336, #d32f2f); color: white; border: none; padding: 12px 25px; border-radius: 8px; cursor: pointer; margin: 5px;">
                    <i class="fas fa-refresh"></i> إعادة تعيين
                </button>
            </div>
        </div>
    `;
    
    document.getElementById('contentArea').innerHTML = statusHTML;
    showNotification('تم تحديث حالة الصندوق', 'success');
}
