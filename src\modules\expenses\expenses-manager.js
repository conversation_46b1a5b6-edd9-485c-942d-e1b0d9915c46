/**
 * Black Horse POS - Expenses Manager (Optimized)
 * مدير المصروفات المحسن
 * Developer: Augment Agent
 */

class ExpensesManager {
    constructor() {
        this.expenses = [];
        this.categories = [
            'إيجار', 'كهرباء', 'مياه', 'هاتف', 'إنترنت',
            'رواتب', 'مواد خام', 'صيانة', 'تسويق', 'أخرى'
        ];
        this.isInitialized = false;
        this.autoSaveEnabled = true;
        this.autoSaveInterval = 30000; // 30 ثانية
        this.autoSaveTimer = null;
        
        console.log('💸 Expenses Manager initialized');
    }

    // تهيئة مدير المصروفات
    async init() {
        try {
            console.log('🚀 Initializing Expenses Manager...');
            
            // تحميل المصروفات من التخزين المؤقت أولاً
            if (window.PerformanceOptimizer) {
                const cachedExpenses = window.PerformanceOptimizer.getCachedData('expenses');
                if (cachedExpenses) {
                    this.expenses = cachedExpenses;
                    console.log('📂 Expenses loaded from cache');
                    this.isInitialized = true;
                    this.setupAutoSave();
                    return;
                }
            }
            
            // تحميل من قاعدة البيانات
            await this.loadExpenses();
            
            // إعداد الحفظ التلقائي
            this.setupAutoSave();
            
            this.isInitialized = true;
            console.log('✅ Expenses Manager ready');
            
        } catch (error) {
            console.error('❌ Error initializing Expenses Manager:', error);
            this.isInitialized = true;
        }
    }

    // تحميل المصروفات
    async loadExpenses() {
        try {
            if (window.UnifiedStorage) {
                this.expenses = await window.UnifiedStorage.load('expenses', []);
            } else {
                // fallback إلى localStorage
                const savedExpenses = localStorage.getItem('expenses');
                this.expenses = savedExpenses ? JSON.parse(savedExpenses) : [];
            }
            
            // حفظ في التخزين المؤقت
            if (window.PerformanceOptimizer) {
                window.PerformanceOptimizer.cacheData('expenses', this.expenses);
            }
            
            console.log(`📊 Loaded ${this.expenses.length} expense records`);
            
        } catch (error) {
            console.error('❌ Error loading expenses:', error);
            this.expenses = [];
        }
    }

    // حفظ المصروفات
    async saveExpenses() {
        try {
            if (window.UnifiedStorage) {
                await window.UnifiedStorage.save('expenses', this.expenses);
            } else {
                // fallback إلى localStorage
                localStorage.setItem('expenses', JSON.stringify(this.expenses));
            }
            
            // تحديث التخزين المؤقت
            if (window.PerformanceOptimizer) {
                window.PerformanceOptimizer.cacheData('expenses', this.expenses);
            }
            
            console.log('💾 Expenses saved successfully');
            
        } catch (error) {
            console.error('❌ Error saving expenses:', error);
            throw error;
        }
    }

    // إعداد الحفظ التلقائي
    setupAutoSave() {
        try {
            if (!this.autoSaveEnabled) return;
            
            // إلغاء المؤقت السابق إذا كان موجوداً
            if (this.autoSaveTimer) {
                clearInterval(this.autoSaveTimer);
            }
            
            // إعداد مؤقت جديد
            this.autoSaveTimer = setInterval(async () => {
                try {
                    await this.saveExpenses();
                    console.log('💾 Auto-save completed');
                } catch (error) {
                    console.error('❌ Auto-save failed:', error);
                }
            }, this.autoSaveInterval);
            
            console.log('⏰ Auto-save enabled');
            
        } catch (error) {
            console.error('❌ Error setting up auto-save:', error);
        }
    }

    // إضافة مصروف جديد
    async addExpense(expenseData) {
        try {
            // التحقق من صحة البيانات
            if (!this.validateExpenseData(expenseData)) {
                throw new Error('Invalid expense data');
            }
            
            const expense = {
                id: this.generateExpenseId(),
                description: expenseData.description,
                amount: parseFloat(expenseData.amount),
                category: expenseData.category || 'أخرى',
                date: expenseData.date || new Date().toISOString(),
                paymentMethod: expenseData.paymentMethod || 'cash',
                receipt: expenseData.receipt || null,
                notes: expenseData.notes || '',
                tags: expenseData.tags || [],
                recurring: expenseData.recurring || false,
                recurringPeriod: expenseData.recurringPeriod || null,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            
            // إضافة المصروف للقائمة
            this.expenses.unshift(expense); // إضافة في المقدمة
            
            // حفظ المصروفات
            await this.saveExpenses();
            
            console.log(`➕ Expense added: ${expense.description} - ${expense.amount}`);
            return expense;
            
        } catch (error) {
            console.error('❌ Error adding expense:', error);
            throw error;
        }
    }

    // التحقق من صحة بيانات المصروف
    validateExpenseData(data) {
        try {
            if (!data) return false;
            if (!data.description || data.description.trim() === '') return false;
            if (!data.amount || isNaN(parseFloat(data.amount)) || parseFloat(data.amount) <= 0) return false;
            
            return true;
            
        } catch (error) {
            console.error('❌ Error validating expense data:', error);
            return false;
        }
    }

    // توليد معرف مصروف
    generateExpenseId() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substr(2, 5);
        return `EXP-${timestamp}-${random}`;
    }

    // تحديث مصروف
    async updateExpense(expenseId, updateData) {
        try {
            const expenseIndex = this.expenses.findIndex(exp => exp.id === expenseId);
            
            if (expenseIndex === -1) {
                throw new Error('Expense not found');
            }
            
            // تحديث البيانات
            const expense = this.expenses[expenseIndex];
            Object.keys(updateData).forEach(key => {
                if (key !== 'id' && key !== 'createdAt') {
                    expense[key] = updateData[key];
                }
            });
            
            expense.updatedAt = new Date().toISOString();
            
            // حفظ التغييرات
            await this.saveExpenses();
            
            console.log(`🔄 Expense updated: ${expenseId}`);
            return expense;
            
        } catch (error) {
            console.error('❌ Error updating expense:', error);
            throw error;
        }
    }

    // حذف مصروف
    async deleteExpense(expenseId) {
        try {
            const expenseIndex = this.expenses.findIndex(exp => exp.id === expenseId);
            
            if (expenseIndex === -1) {
                throw new Error('Expense not found');
            }
            
            const deletedExpense = this.expenses.splice(expenseIndex, 1)[0];
            
            // حفظ التغييرات
            await this.saveExpenses();
            
            console.log(`🗑️ Expense deleted: ${expenseId}`);
            return deletedExpense;
            
        } catch (error) {
            console.error('❌ Error deleting expense:', error);
            throw error;
        }
    }

    // البحث في المصروفات
    searchExpenses(query, filters = {}) {
        try {
            let results = [...this.expenses];
            
            // البحث النصي
            if (query && query.trim()) {
                const searchTerm = query.toLowerCase().trim();
                results = results.filter(expense => 
                    expense.description.toLowerCase().includes(searchTerm) ||
                    expense.category.toLowerCase().includes(searchTerm) ||
                    expense.notes.toLowerCase().includes(searchTerm) ||
                    expense.tags.some(tag => tag.toLowerCase().includes(searchTerm))
                );
            }
            
            // تطبيق المرشحات
            if (filters.category) {
                results = results.filter(expense => expense.category === filters.category);
            }
            
            if (filters.dateFrom) {
                results = results.filter(expense => new Date(expense.date) >= new Date(filters.dateFrom));
            }
            
            if (filters.dateTo) {
                results = results.filter(expense => new Date(expense.date) <= new Date(filters.dateTo));
            }
            
            if (filters.minAmount) {
                results = results.filter(expense => expense.amount >= filters.minAmount);
            }
            
            if (filters.maxAmount) {
                results = results.filter(expense => expense.amount <= filters.maxAmount);
            }
            
            if (filters.paymentMethod) {
                results = results.filter(expense => expense.paymentMethod === filters.paymentMethod);
            }
            
            if (filters.recurring !== undefined) {
                results = results.filter(expense => expense.recurring === filters.recurring);
            }
            
            console.log(`🔍 Expenses search: ${results.length} results found`);
            return results;
            
        } catch (error) {
            console.error('❌ Error searching expenses:', error);
            return [];
        }
    }

    // الحصول على إحصائيات المصروفات
    getExpensesStats(period = 'month') {
        try {
            const now = new Date();
            let startDate;
            
            switch (period) {
                case 'today':
                    startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    break;
                case 'week':
                    startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    break;
                case 'month':
                    startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                    break;
                case 'year':
                    startDate = new Date(now.getFullYear(), 0, 1);
                    break;
                default:
                    startDate = new Date(0);
            }
            
            const periodExpenses = this.expenses.filter(expense => 
                new Date(expense.date) >= startDate
            );
            
            const stats = {
                period,
                totalExpenses: periodExpenses.length,
                totalAmount: periodExpenses.reduce((sum, expense) => sum + expense.amount, 0),
                averageExpense: periodExpenses.length > 0 ? 
                    periodExpenses.reduce((sum, expense) => sum + expense.amount, 0) / periodExpenses.length : 0,
                categories: this.getCategoryStats(periodExpenses),
                paymentMethods: this.getPaymentMethodStats(periodExpenses),
                topExpenses: this.getTopExpenses(periodExpenses),
                recurringExpenses: periodExpenses.filter(exp => exp.recurring).length
            };
            
            return stats;
            
        } catch (error) {
            console.error('❌ Error getting expenses stats:', error);
            return null;
        }
    }

    // إحصائيات الفئات
    getCategoryStats(expenses) {
        try {
            const categories = {};
            
            expenses.forEach(expense => {
                if (!categories[expense.category]) {
                    categories[expense.category] = { count: 0, total: 0 };
                }
                categories[expense.category].count++;
                categories[expense.category].total += expense.amount;
            });
            
            return categories;
            
        } catch (error) {
            console.error('❌ Error getting category stats:', error);
            return {};
        }
    }

    // إحصائيات طرق الدفع
    getPaymentMethodStats(expenses) {
        try {
            const methods = {};
            
            expenses.forEach(expense => {
                if (!methods[expense.paymentMethod]) {
                    methods[expense.paymentMethod] = { count: 0, total: 0 };
                }
                methods[expense.paymentMethod].count++;
                methods[expense.paymentMethod].total += expense.amount;
            });
            
            return methods;
            
        } catch (error) {
            console.error('❌ Error getting payment method stats:', error);
            return {};
        }
    }

    // أكبر المصروفات
    getTopExpenses(expenses, limit = 10) {
        try {
            return expenses
                .sort((a, b) => b.amount - a.amount)
                .slice(0, limit)
                .map(expense => ({
                    id: expense.id,
                    description: expense.description,
                    amount: expense.amount,
                    category: expense.category,
                    date: expense.date
                }));
                
        } catch (error) {
            console.error('❌ Error getting top expenses:', error);
            return [];
        }
    }

    // إضافة فئة جديدة
    addCategory(categoryName) {
        try {
            if (!categoryName || categoryName.trim() === '') {
                throw new Error('Category name is required');
            }
            
            const trimmedName = categoryName.trim();
            
            if (this.categories.includes(trimmedName)) {
                throw new Error('Category already exists');
            }
            
            this.categories.push(trimmedName);
            console.log(`📁 Category added: ${trimmedName}`);
            
            return this.categories;
            
        } catch (error) {
            console.error('❌ Error adding category:', error);
            throw error;
        }
    }

    // حذف فئة
    removeCategory(categoryName) {
        try {
            const index = this.categories.indexOf(categoryName);
            
            if (index === -1) {
                throw new Error('Category not found');
            }
            
            // التحقق من وجود مصروفات بهذه الفئة
            const expensesWithCategory = this.expenses.filter(exp => exp.category === categoryName);
            
            if (expensesWithCategory.length > 0) {
                throw new Error('Cannot delete category with existing expenses');
            }
            
            this.categories.splice(index, 1);
            console.log(`🗑️ Category removed: ${categoryName}`);
            
            return this.categories;
            
        } catch (error) {
            console.error('❌ Error removing category:', error);
            throw error;
        }
    }

    // الحصول على الفئات
    getCategories() {
        return [...this.categories];
    }

    // إنشاء مصروف متكرر
    async createRecurringExpense(expenseData, period) {
        try {
            const recurringExpense = {
                ...expenseData,
                recurring: true,
                recurringPeriod: period // 'daily', 'weekly', 'monthly', 'yearly'
            };
            
            const expense = await this.addExpense(recurringExpense);
            
            // جدولة المصروفات المستقبلية (مبسط)
            this.scheduleRecurringExpense(expense);
            
            console.log(`🔄 Recurring expense created: ${expense.description}`);
            return expense;
            
        } catch (error) {
            console.error('❌ Error creating recurring expense:', error);
            throw error;
        }
    }

    // جدولة المصروف المتكرر
    scheduleRecurringExpense(expense) {
        try {
            // هذا مبسط - في التطبيق الحقيقي يجب استخدام نظام جدولة أكثر تطوراً
            console.log(`📅 Scheduled recurring expense: ${expense.description} (${expense.recurringPeriod})`);
            
        } catch (error) {
            console.error('❌ Error scheduling recurring expense:', error);
        }
    }

    // تصدير المصروفات
    async exportExpenses(format = 'json', filters = {}) {
        try {
            const expenses = this.searchExpenses('', filters);
            
            console.log(`📤 Exporting ${expenses.length} expenses as ${format}`);
            
            switch (format) {
                case 'json':
                    return this.exportAsJSON(expenses);
                case 'csv':
                    return this.exportAsCSV(expenses);
                default:
                    throw new Error(`Unsupported format: ${format}`);
            }
            
        } catch (error) {
            console.error('❌ Error exporting expenses:', error);
            throw error;
        }
    }

    // تصدير كـ JSON
    exportAsJSON(expenses) {
        try {
            const jsonData = JSON.stringify(expenses, null, 2);
            const blob = new Blob([jsonData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `expenses_${Date.now()}.json`;
            a.click();
            
            URL.revokeObjectURL(url);
            console.log('✅ Expenses exported as JSON');
            
        } catch (error) {
            console.error('❌ Error exporting as JSON:', error);
            throw error;
        }
    }

    // تصدير كـ CSV
    exportAsCSV(expenses) {
        try {
            let csvContent = 'ID,Description,Amount,Category,Date,Payment Method,Notes\n';
            
            expenses.forEach(expense => {
                const row = [
                    expense.id,
                    `"${expense.description}"`,
                    expense.amount,
                    expense.category,
                    expense.date,
                    expense.paymentMethod,
                    `"${expense.notes}"`
                ].join(',');
                
                csvContent += row + '\n';
            });
            
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `expenses_${Date.now()}.csv`;
            a.click();
            
            URL.revokeObjectURL(url);
            console.log('✅ Expenses exported as CSV');
            
        } catch (error) {
            console.error('❌ Error exporting as CSV:', error);
            throw error;
        }
    }

    // الحصول على جميع المصروفات
    getAllExpenses() {
        return this.expenses;
    }

    // الحصول على مصروف بالمعرف
    getExpenseById(expenseId) {
        try {
            return this.expenses.find(expense => expense.id === expenseId) || null;
            
        } catch (error) {
            console.error('❌ Error getting expense by ID:', error);
            return null;
        }
    }

    // تنظيف الموارد
    cleanup() {
        try {
            if (this.autoSaveTimer) {
                clearInterval(this.autoSaveTimer);
                this.autoSaveTimer = null;
            }
            
            console.log('✅ Expenses Manager cleaned up');
            
        } catch (error) {
            console.error('❌ Error cleaning up Expenses Manager:', error);
        }
    }
}

// إنشاء instance عام
window.ExpensesManager = new ExpensesManager();

// تصدير للاستخدام في الوحدات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ExpensesManager;
}

console.log('✅ Expenses Manager loaded successfully');
