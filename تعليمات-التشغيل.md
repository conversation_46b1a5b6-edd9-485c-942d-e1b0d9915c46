# 🚀 تعليمات تشغيل Black Horse ERP

## 📋 طرق التشغيل

### الطريقة الأولى: تشغيل سريع (Windows)
```
انقر نقراً مزدوجاً على ملف: تشغيل.bat
```

### الطريقة الثانية: تشغيل بـ Python
```bash
python run.py
```

### الطريقة الثالثة: تشغيل مع منفذ مخصص
```bash
python run.py --port 3000
```

### الطريقة الرابعة: تشغيل بدون فتح المتصفح
```bash
python run.py --no-browser
```

## 🔧 متطلبات النظام

### Python
- **الإصدار المطلوب**: Python 3.6 أو أحدث
- **التحميل**: https://www.python.org/downloads/
- **مهم**: تأكد من تحديد "Add Python to PATH" أثناء التثبيت

### المتصفحات المدعومة
- ✅ Google Chrome (مُوصى به)
- ✅ Mozilla Firefox
- ✅ Microsoft Edge
- ✅ Safari

## 🎯 بعد التشغيل

### الروابط المهمة:
- **🏠 الصفحة الرئيسية**: `http://localhost:8080`
- **🔍 فحص صحة النظام**: `http://localhost:8080/system-health-check.html`
- **⚙️ معالج الإعداد**: `http://localhost:8080/setup-wizard.html`

### العلامات الإيجابية:
- ✅ ظهور رسالة "تم تشغيل الخادم بنجاح"
- ✅ فتح المتصفح تلقائياً
- ✅ تحميل الصفحة الرئيسية بنجاح
- ✅ ظهور "النظام يعمل بنجاح" في الصفحة

## 🚨 حل المشاكل الشائعة

### مشكلة: "Python غير مثبت"
**الحل**:
1. حمل Python من الرابط أعلاه
2. ثبته مع تحديد "Add to PATH"
3. أعد تشغيل الكمبيوتر
4. جرب مرة أخرى

### مشكلة: "المنفذ مستخدم"
**الحل**:
```bash
python run.py --port 3000
```

### مشكلة: "الصفحات لا تعمل"
**الحل**:
1. تأكد من تشغيل الخادم أولاً
2. استخدم الروابط الكاملة (http://localhost:8080)
3. لا تفتح الملفات مباشرة من المجلد

### مشكلة: "ملفات مفقودة"
**الحل**:
- النظام سينشئ الملفات المفقودة تلقائياً
- إذا استمرت المشكلة، تأكد من وجود مجلد `src`

## 📊 فحص صحة النظام

بعد التشغيل، افتح:
```
http://localhost:8080/system-health-check.html
```

**النتيجة المتوقعة**: 100/100 نقطة صحة

## 🛑 إيقاف النظام

اضغط `Ctrl + C` في نافذة الأوامر لإيقاف الخادم

## 📞 الدعم

إذا واجهت مشاكل:
1. تأكد من اتباع التعليمات بدقة
2. تحقق من رسائل الخطأ في نافذة الأوامر
3. جرب إعادة تشغيل الكمبيوتر
4. تأكد من أن Python مثبت بشكل صحيح

---

<div align="center">
  <strong>🐎 Black Horse ERP</strong><br>
  <em>نظام إدارة الأعمال المتكامل</em>
</div>
