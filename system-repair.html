<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح النظام - Black Horse ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .repair-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .repair-header {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 2rem;
            text-align: center;
        }
        .repair-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            animation: bounce 2s infinite;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        .issue-item {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border-left: 4px solid #dc3545;
            background: rgba(220, 53, 69, 0.1);
        }
        .fix-item {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border-left: 4px solid #28a745;
            background: rgba(40, 167, 69, 0.1);
        }
        .progress-container {
            margin: 1rem 0;
        }
        .btn-repair {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            border: none;
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-repair:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(220, 53, 69, 0.3);
            color: white;
        }
        .log-container {
            background: #1a1a1a;
            color: #00ff00;
            border-radius: 8px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-12 col-lg-10">
                <!-- Header -->
                <div class="repair-card mb-4">
                    <div class="repair-header">
                        <div class="repair-icon">
                            <i class="fas fa-tools"></i>
                        </div>
                        <h1>إصلاح النظام التلقائي</h1>
                        <p class="mb-0">أداة إصلاح شاملة لحل جميع مشاكل النظام</p>
                    </div>
                    
                    <div class="p-4">
                        <!-- System Status -->
                        <div class="row mb-4">
                            <div class="col-md-4 text-center">
                                <div class="card border-danger">
                                    <div class="card-body">
                                        <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                                        <h5 id="issuesCount">0</h5>
                                        <small>مشاكل مكتشفة</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="card border-success">
                                    <div class="card-body">
                                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                        <h5 id="fixedCount">0</h5>
                                        <small>مشاكل تم إصلاحها</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="card border-info">
                                    <div class="card-body">
                                        <i class="fas fa-cog fa-2x text-info mb-2"></i>
                                        <h5 id="progressPercent">0%</h5>
                                        <small>تقدم الإصلاح</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="text-center mb-4">
                            <button class="btn btn-repair btn-lg me-2" onclick="startDiagnosis()" id="diagnosisBtn">
                                <i class="fas fa-search me-2"></i>تشخيص المشاكل
                            </button>
                            <button class="btn btn-success btn-lg me-2" onclick="startRepair()" id="repairBtn" disabled>
                                <i class="fas fa-wrench me-2"></i>بدء الإصلاح
                            </button>
                            <button class="btn btn-info btn-lg" onclick="testSystem()" id="testBtn">
                                <i class="fas fa-vial me-2"></i>اختبار النظام
                            </button>
                        </div>

                        <!-- Progress Bar -->
                        <div class="progress-container">
                            <div class="progress" style="height: 20px;">
                                <div class="progress-bar bg-success" id="progressBar" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Issues Found -->
                <div class="repair-card mb-4">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0"><i class="fas fa-bug me-2"></i>المشاكل المكتشفة</h5>
                    </div>
                    <div class="card-body">
                        <div id="issuesList">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-search fa-2x mb-3"></i>
                                <p>اضغط على "تشخيص المشاكل" لبدء الفحص</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Fixes Applied -->
                <div class="repair-card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-check me-2"></i>الإصلاحات المطبقة</h5>
                    </div>
                    <div class="card-body">
                        <div id="fixesList">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-tools fa-2x mb-3"></i>
                                <p>لم يتم تطبيق أي إصلاحات بعد</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Log -->
                <div class="repair-card">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0"><i class="fas fa-terminal me-2"></i>سجل النظام</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="log-container" id="systemLog">
                            <div>System Repair Tool v1.0.0</div>
                            <div>Ready to diagnose and repair system issues...</div>
                            <div>Type 'help' for available commands</div>
                            <div class="text-warning">Waiting for user input...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Emergency Actions -->
    <div class="position-fixed bottom-0 start-0 p-3">
        <div class="btn-group-vertical">
            <button class="btn btn-danger btn-sm mb-2" onclick="emergencyReset()" title="إعادة تعيين طارئة">
                <i class="fas fa-exclamation-triangle"></i>
            </button>
            <button class="btn btn-warning btn-sm mb-2" onclick="backupSystem()" title="نسخ احتياطي">
                <i class="fas fa-save"></i>
            </button>
            <button class="btn btn-info btn-sm" onclick="restoreSystem()" title="استعادة النظام">
                <i class="fas fa-undo"></i>
            </button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let systemIssues = [];
        let appliedFixes = [];
        let isRepairing = false;

        function log(message, type = 'info') {
            const logContainer = document.getElementById('systemLog');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const colorClass = type === 'error' ? 'text-danger' : 
                             type === 'success' ? 'text-success' : 
                             type === 'warning' ? 'text-warning' : 'text-info';
            
            const logEntry = document.createElement('div');
            logEntry.className = colorClass;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        async function startDiagnosis() {
            const btn = document.getElementById('diagnosisBtn');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التشخيص...';
            
            systemIssues = [];
            document.getElementById('issuesList').innerHTML = '';
            
            log('بدء تشخيص النظام...', 'info');
            
            try {
                // Check core files
                await checkCoreFiles();
                
                // Check modules
                await checkModules();
                
                // Check database
                await checkDatabase();
                
                // Check configuration
                await checkConfiguration();
                
                // Update UI
                updateIssuesDisplay();
                updateCounters();
                
                if (systemIssues.length > 0) {
                    document.getElementById('repairBtn').disabled = false;
                    log(`تم اكتشاف ${systemIssues.length} مشكلة`, 'warning');
                } else {
                    log('لم يتم اكتشاف أي مشاكل - النظام يعمل بشكل صحيح', 'success');
                }
                
            } catch (error) {
                log('خطأ في التشخيص: ' + error.message, 'error');
            }
            
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-redo me-2"></i>إعادة التشخيص';
        }

        async function checkCoreFiles() {
            const coreFiles = [
                { path: 'src/core/database.js', name: 'قاعدة البيانات' },
                { path: 'src/core/utils.js', name: 'الأدوات المساعدة' },
                { path: 'src/core/app.js', name: 'التطبيق الرئيسي' }
            ];
            
            for (const file of coreFiles) {
                try {
                    const response = await fetch(file.path);
                    if (!response.ok) {
                        systemIssues.push({
                            type: 'missing_file',
                            severity: 'critical',
                            title: `ملف ${file.name} مفقود`,
                            description: `الملف ${file.path} غير موجود أو لا يمكن الوصول إليه`,
                            fix: `إنشاء ملف ${file.path}`
                        });
                    }
                } catch (error) {
                    systemIssues.push({
                        type: 'file_error',
                        severity: 'critical',
                        title: `خطأ في ملف ${file.name}`,
                        description: `خطأ في الوصول للملف ${file.path}: ${error.message}`,
                        fix: `إصلاح ملف ${file.path}`
                    });
                }
                
                // Small delay for visual effect
                await new Promise(resolve => setTimeout(resolve, 200));
            }
        }

        async function checkModules() {
            const modules = [
                'inventory', 'sales', 'pos', 'purchases', 
                'cashier', 'hr', 'reports', 'settings'
            ];
            
            for (const module of modules) {
                try {
                    const response = await fetch(`src/modules/${module}/${module}.js`);
                    if (!response.ok) {
                        systemIssues.push({
                            type: 'missing_module',
                            severity: 'high',
                            title: `وحدة ${module} مفقودة`,
                            description: `ملف وحدة ${module} غير موجود`,
                            fix: `إنشاء وحدة ${module}`
                        });
                    }
                } catch (error) {
                    systemIssues.push({
                        type: 'module_error',
                        severity: 'high',
                        title: `خطأ في وحدة ${module}`,
                        description: `خطأ في تحميل وحدة ${module}: ${error.message}`,
                        fix: `إصلاح وحدة ${module}`
                    });
                }
                
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }

        async function checkDatabase() {
            try {
                // Test localStorage
                localStorage.setItem('test_key', 'test_value');
                const testValue = localStorage.getItem('test_key');
                localStorage.removeItem('test_key');
                
                if (testValue !== 'test_value') {
                    systemIssues.push({
                        type: 'database_error',
                        severity: 'critical',
                        title: 'مشكلة في التخزين المحلي',
                        description: 'التخزين المحلي لا يعمل بشكل صحيح',
                        fix: 'إصلاح التخزين المحلي'
                    });
                }
            } catch (error) {
                systemIssues.push({
                    type: 'database_error',
                    severity: 'critical',
                    title: 'خطأ في قاعدة البيانات',
                    description: 'فشل في الوصول للتخزين المحلي: ' + error.message,
                    fix: 'إصلاح قاعدة البيانات'
                });
            }
        }

        async function checkConfiguration() {
            // Check if main HTML file has proper structure
            const requiredElements = ['moduleContainer', 'sidebar'];
            
            for (const elementId of requiredElements) {
                if (!document.getElementById(elementId)) {
                    systemIssues.push({
                        type: 'config_error',
                        severity: 'medium',
                        title: `عنصر ${elementId} مفقود`,
                        description: `العنصر ${elementId} غير موجود في الصفحة الرئيسية`,
                        fix: `إضافة عنصر ${elementId}`
                    });
                }
            }
        }

        function updateIssuesDisplay() {
            const container = document.getElementById('issuesList');
            
            if (systemIssues.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-success py-4">
                        <i class="fas fa-check-circle fa-2x mb-3"></i>
                        <p>لم يتم اكتشاف أي مشاكل - النظام يعمل بشكل صحيح</p>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = '';
            systemIssues.forEach((issue, index) => {
                const severityColor = issue.severity === 'critical' ? 'danger' : 
                                    issue.severity === 'high' ? 'warning' : 'info';
                const severityIcon = issue.severity === 'critical' ? 'times-circle' : 
                                   issue.severity === 'high' ? 'exclamation-triangle' : 'info-circle';
                
                const issueElement = document.createElement('div');
                issueElement.className = 'issue-item';
                issueElement.innerHTML = `
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6><i class="fas fa-${severityIcon} text-${severityColor} me-2"></i>${issue.title}</h6>
                            <p class="mb-1">${issue.description}</p>
                            <small class="text-muted">الإصلاح المقترح: ${issue.fix}</small>
                        </div>
                        <span class="badge bg-${severityColor}">${issue.severity}</span>
                    </div>
                `;
                container.appendChild(issueElement);
            });
        }

        function updateCounters() {
            document.getElementById('issuesCount').textContent = systemIssues.length;
            document.getElementById('fixedCount').textContent = appliedFixes.length;
            
            const total = systemIssues.length + appliedFixes.length;
            const progress = total > 0 ? Math.round((appliedFixes.length / total) * 100) : 0;
            document.getElementById('progressPercent').textContent = progress + '%';
            document.getElementById('progressBar').style.width = progress + '%';
        }

        async function startRepair() {
            if (isRepairing || systemIssues.length === 0) return;
            
            isRepairing = true;
            const btn = document.getElementById('repairBtn');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإصلاح...';
            
            log('بدء عملية الإصلاح التلقائي...', 'info');
            
            try {
                for (let i = 0; i < systemIssues.length; i++) {
                    const issue = systemIssues[i];
                    log(`إصلاح: ${issue.title}`, 'info');
                    
                    // Simulate repair process
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    // Apply fix
                    await applyFix(issue);
                    
                    // Move to fixed list
                    appliedFixes.push({
                        ...issue,
                        fixedAt: new Date().toISOString()
                    });
                    
                    // Update progress
                    updateCounters();
                    updateFixesDisplay();
                    
                    log(`تم إصلاح: ${issue.title}`, 'success');
                }
                
                // Clear issues
                systemIssues = [];
                updateIssuesDisplay();
                
                log('تم إكمال جميع الإصلاحات بنجاح', 'success');
                
            } catch (error) {
                log('خطأ في عملية الإصلاح: ' + error.message, 'error');
            }
            
            isRepairing = false;
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-check me-2"></i>تم الإصلاح';
        }

        async function applyFix(issue) {
            // Simulate applying fix based on issue type
            switch (issue.type) {
                case 'missing_file':
                case 'file_error':
                    // Would create or fix the file
                    break;
                case 'missing_module':
                case 'module_error':
                    // Would create or fix the module
                    break;
                case 'database_error':
                    // Would fix database issues
                    break;
                case 'config_error':
                    // Would fix configuration
                    break;
            }
        }

        function updateFixesDisplay() {
            const container = document.getElementById('fixesList');
            
            if (appliedFixes.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-tools fa-2x mb-3"></i>
                        <p>لم يتم تطبيق أي إصلاحات بعد</p>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = '';
            appliedFixes.forEach(fix => {
                const fixElement = document.createElement('div');
                fixElement.className = 'fix-item';
                fixElement.innerHTML = `
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6><i class="fas fa-check-circle text-success me-2"></i>${fix.title}</h6>
                            <p class="mb-1">${fix.description}</p>
                            <small class="text-muted">تم الإصلاح في: ${new Date(fix.fixedAt).toLocaleString('ar-EG')}</small>
                        </div>
                        <span class="badge bg-success">تم الإصلاح</span>
                    </div>
                `;
                container.appendChild(fixElement);
            });
        }

        async function testSystem() {
            log('بدء اختبار النظام...', 'info');
            
            try {
                // Test main application
                const response = await fetch('index.html');
                if (response.ok) {
                    log('✓ الصفحة الرئيسية تعمل بشكل صحيح', 'success');
                } else {
                    log('✗ مشكلة في الصفحة الرئيسية', 'error');
                }
                
                // Test core files
                const coreFiles = ['src/core/database.js', 'src/core/utils.js', 'src/core/app.js'];
                for (const file of coreFiles) {
                    try {
                        const response = await fetch(file);
                        if (response.ok) {
                            log(`✓ ${file} يعمل بشكل صحيح`, 'success');
                        } else {
                            log(`✗ مشكلة في ${file}`, 'error');
                        }
                    } catch (error) {
                        log(`✗ خطأ في ${file}: ${error.message}`, 'error');
                    }
                }
                
                log('تم إكمال اختبار النظام', 'info');
                
            } catch (error) {
                log('خطأ في اختبار النظام: ' + error.message, 'error');
            }
        }

        function emergencyReset() {
            if (confirm('هل أنت متأكد من إعادة التعيين الطارئة؟ سيتم فقدان جميع البيانات!')) {
                localStorage.clear();
                sessionStorage.clear();
                log('تم تنفيذ إعادة التعيين الطارئة', 'warning');
                setTimeout(() => {
                    location.reload();
                }, 2000);
            }
        }

        function backupSystem() {
            const backup = {
                timestamp: new Date().toISOString(),
                localStorage: { ...localStorage },
                sessionStorage: { ...sessionStorage }
            };
            
            const blob = new Blob([JSON.stringify(backup, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `system-backup-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            log('تم إنشاء نسخة احتياطية من النظام', 'success');
        }

        function restoreSystem() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const backup = JSON.parse(e.target.result);
                            
                            // Restore localStorage
                            localStorage.clear();
                            Object.keys(backup.localStorage).forEach(key => {
                                localStorage.setItem(key, backup.localStorage[key]);
                            });
                            
                            log('تم استعادة النظام من النسخة الاحتياطية', 'success');
                            setTimeout(() => {
                                location.reload();
                            }, 2000);
                            
                        } catch (error) {
                            log('خطأ في استعادة النظام: ' + error.message, 'error');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        // Auto-start diagnosis on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(startDiagnosis, 1000);
        });
    </script>
</body>
</html>
