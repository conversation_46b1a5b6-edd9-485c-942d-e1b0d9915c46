/**
 * Black Horse POS - Performance Tests
 * اختبارات الأداء الشاملة
 * Developer: Augment Agent
 */

class PerformanceTests {
    constructor() {
        this.testResults = [];
        this.isRunning = false;
        this.startTime = null;
        this.endTime = null;

        // أهداف الأداء المطلوبة
        this.performanceTargets = {
            loadTimeImprovement: 70, // 70% تحسن في وقت التحميل
            memoryReduction: 50,     // 50% تقليل في استخدام الذاكرة
            fileSizeReduction: 60,   // 60% تقليل في حجم الملفات
            duplicateElimination: 100 // 100% إزالة الدوال المكررة
        };

        console.log('🧪 Performance Tests initialized');
    }

    // تشغيل جميع اختبارات الأداء
    async runAllTests() {
        try {
            console.log('🚀 Starting comprehensive performance tests...');

            this.isRunning = true;
            this.startTime = performance.now();
            this.testResults = [];

            // اختبار أوقات التحميل
            await this.testLoadTimes();

            // اختبار استخدام الذاكرة
            await this.testMemoryUsage();

            // اختبار أحجام الملفات
            await this.testFileSizes();

            // اختبار أداء قاعدة البيانات
            await this.testDatabasePerformance();

            // اختبار أداء واجهة المستخدم
            await this.testUIPerformance();

            // اختبار التخزين المؤقت
            await this.testCachePerformance();

            // اختبار الوحدات المحسنة
            await this.testOptimizedModules();

            this.endTime = performance.now();
            this.isRunning = false;

            // إنشاء التقرير النهائي
            const report = this.generatePerformanceReport();

            console.log('✅ Performance tests completed');
            return report;

        } catch (error) {
            console.error('❌ Error running performance tests:', error);
            this.isRunning = false;
            return null;
        }
    }

    // اختبار أوقات التحميل
    async testLoadTimes() {
        try {
            console.log('⏱️ Testing load times...');

            const testResults = {
                name: 'Load Times',
                tests: []
            };

            // اختبار تحميل الصفحة الرئيسية
            const pageLoadTest = await this.measurePageLoadTime();
            testResults.tests.push(pageLoadTest);

            // اختبار تحميل الوحدات
            const moduleLoadTest = await this.measureModuleLoadTimes();
            testResults.tests.push(moduleLoadTest);

            // اختبار تحميل قاعدة البيانات
            const dbLoadTest = await this.measureDatabaseLoadTime();
            testResults.tests.push(dbLoadTest);

            this.testResults.push(testResults);
            console.log('✅ Load time tests completed');

        } catch (error) {
            console.error('❌ Error testing load times:', error);
        }
    }

    // قياس وقت تحميل الصفحة
    async measurePageLoadTime() {
        try {
            const startTime = performance.now();

            // محاكاة تحميل الصفحة
            await new Promise(resolve => {
                if (document.readyState === 'complete') {
                    resolve();
                } else {
                    window.addEventListener('load', resolve, { once: true });
                }
            });

            const endTime = performance.now();
            const loadTime = endTime - startTime;

            return {
                name: 'Page Load Time',
                duration: Math.round(loadTime),
                status: loadTime < 2000 ? 'PASS' : 'FAIL',
                target: '< 2000ms',
                actual: `${Math.round(loadTime)}ms`
            };

        } catch (error) {
            console.error('❌ Error measuring page load time:', error);
            return {
                name: 'Page Load Time',
                duration: 0,
                status: 'ERROR',
                error: error.message
            };
        }
    }

    // قياس أوقات تحميل الوحدات
    async measureModuleLoadTimes() {
        try {
            const modules = [
                'ProductsManager',
                'CustomersManager',
                'SalesManager',
                'ReportsManager',
                'ExpensesManager'
            ];

            const moduleTests = [];

            for (const moduleName of modules) {
                const startTime = performance.now();

                // التحقق من تحميل الوحدة
                const moduleExists = window[moduleName] !== undefined;

                const endTime = performance.now();
                const loadTime = endTime - startTime;

                moduleTests.push({
                    module: moduleName,
                    loaded: moduleExists,
                    loadTime: Math.round(loadTime),
                    status: moduleExists ? 'PASS' : 'FAIL'
                });
            }

            return {
                name: 'Module Load Times',
                modules: moduleTests,
                status: moduleTests.every(m => m.status === 'PASS') ? 'PASS' : 'FAIL'
            };

        } catch (error) {
            console.error('❌ Error measuring module load times:', error);
            return {
                name: 'Module Load Times',
                status: 'ERROR',
                error: error.message
            };
        }
    }

    // قياس وقت تحميل قاعدة البيانات
    async measureDatabaseLoadTime() {
        try {
            const startTime = performance.now();

            // اختبار تحميل البيانات من قاعدة البيانات
            if (window.UnifiedStorage) {
                await window.UnifiedStorage.load('products', []);
                await window.UnifiedStorage.load('customers', []);
                await window.UnifiedStorage.load('sales', []);
            }

            const endTime = performance.now();
            const loadTime = endTime - startTime;

            return {
                name: 'Database Load Time',
                duration: Math.round(loadTime),
                status: loadTime < 500 ? 'PASS' : 'FAIL',
                target: '< 500ms',
                actual: `${Math.round(loadTime)}ms`
            };

        } catch (error) {
            console.error('❌ Error measuring database load time:', error);
            return {
                name: 'Database Load Time',
                duration: 0,
                status: 'ERROR',
                error: error.message
            };
        }
    }

    // اختبار استخدام الذاكرة
    async testMemoryUsage() {
        try {
            console.log('💾 Testing memory usage...');

            const testResults = {
                name: 'Memory Usage',
                tests: []
            };

            // قياس استخدام الذاكرة الحالي
            const memoryTest = this.measureMemoryUsage();
            testResults.tests.push(memoryTest);

            // اختبار تسريب الذاكرة
            const leakTest = await this.testMemoryLeaks();
            testResults.tests.push(leakTest);

            this.testResults.push(testResults);
            console.log('✅ Memory usage tests completed');

        } catch (error) {
            console.error('❌ Error testing memory usage:', error);
        }
    }

    // قياس استخدام الذاكرة
    measureMemoryUsage() {
        try {
            if (performance.memory) {
                const memory = performance.memory;
                const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
                const totalMB = Math.round(memory.totalJSHeapSize / 1024 / 1024);
                const limitMB = Math.round(memory.jsHeapSizeLimit / 1024 / 1024);

                return {
                    name: 'Current Memory Usage',
                    used: `${usedMB} MB`,
                    total: `${totalMB} MB`,
                    limit: `${limitMB} MB`,
                    usage: Math.round((usedMB / limitMB) * 100),
                    status: usedMB < 100 ? 'PASS' : 'WARN'
                };
            } else {
                return {
                    name: 'Current Memory Usage',
                    status: 'UNAVAILABLE',
                    message: 'Performance.memory not supported'
                };
            }

        } catch (error) {
            console.error('❌ Error measuring memory usage:', error);
            return {
                name: 'Current Memory Usage',
                status: 'ERROR',
                error: error.message
            };
        }
    }

    // اختبار تسريب الذاكرة
    async testMemoryLeaks() {
        try {
            const initialMemory = performance.memory ?
                performance.memory.usedJSHeapSize : 0;

            // تشغيل عمليات متكررة لاختبار التسريب
            for (let i = 0; i < 100; i++) {
                // إنشاء وحذف بيانات مؤقتة
                const tempData = new Array(1000).fill(Math.random());
                tempData.length = 0;

                // تشغيل garbage collection إذا كان متاحاً
                if (window.gc) {
                    window.gc();
                }

                await new Promise(resolve => setTimeout(resolve, 10));
            }

            const finalMemory = performance.memory ?
                performance.memory.usedJSHeapSize : 0;

            const memoryDiff = finalMemory - initialMemory;
            const memoryDiffMB = Math.round(memoryDiff / 1024 / 1024);

            return {
                name: 'Memory Leak Test',
                initialMemory: Math.round(initialMemory / 1024 / 1024),
                finalMemory: Math.round(finalMemory / 1024 / 1024),
                difference: `${memoryDiffMB} MB`,
                status: memoryDiffMB < 10 ? 'PASS' : 'FAIL'
            };

        } catch (error) {
            console.error('❌ Error testing memory leaks:', error);
            return {
                name: 'Memory Leak Test',
                status: 'ERROR',
                error: error.message
            };
        }
    }

    // اختبار أحجام الملفات
    async testFileSizes() {
        try {
            console.log('📦 Testing file sizes...');

            const testResults = {
                name: 'File Sizes',
                tests: []
            };

            // اختبار ضغط CSS
            const cssTest = await this.testCSSCompression();
            testResults.tests.push(cssTest);

            // اختبار ضغط JavaScript
            const jsTest = await this.testJSCompression();
            testResults.tests.push(jsTest);

            this.testResults.push(testResults);
            console.log('✅ File size tests completed');

        } catch (error) {
            console.error('❌ Error testing file sizes:', error);
        }
    }

    // اختبار ضغط CSS
    async testCSSCompression() {
        try {
            const styleSheets = document.querySelectorAll('link[rel="stylesheet"]');
            let totalOriginal = 0;
            let totalCompressed = 0;

            for (const sheet of styleSheets) {
                try {
                    const response = await fetch(sheet.href);
                    const cssContent = await response.text();

                    // محاكاة الضغط
                    const compressed = cssContent
                        .replace(/\/\*[\s\S]*?\*\//g, '')
                        .replace(/\s+/g, ' ')
                        .trim();

                    totalOriginal += cssContent.length;
                    totalCompressed += compressed.length;
                } catch (error) {
                    console.warn('⚠️ Could not test CSS file:', sheet.href);
                }
            }

            const compressionRatio = totalOriginal > 0 ?
                ((totalOriginal - totalCompressed) / totalOriginal) * 100 : 0;

            return {
                name: 'CSS Compression',
                originalSize: `${Math.round(totalOriginal / 1024)} KB`,
                compressedSize: `${Math.round(totalCompressed / 1024)} KB`,
                compressionRatio: `${Math.round(compressionRatio)}%`,
                status: compressionRatio >= 30 ? 'PASS' : 'FAIL'
            };

        } catch (error) {
            console.error('❌ Error testing CSS compression:', error);
            return {
                name: 'CSS Compression',
                status: 'ERROR',
                error: error.message
            };
        }
    }

    // اختبار ضغط JavaScript
    async testJSCompression() {
        try {
            const scripts = document.querySelectorAll('script[src]');
            let totalOriginal = 0;
            let totalCompressed = 0;

            for (const script of scripts) {
                try {
                    const response = await fetch(script.src);
                    const jsContent = await response.text();

                    // محاكاة الضغط
                    const compressed = jsContent
                        .replace(/\/\/.*$/gm, '')
                        .replace(/\/\*[\s\S]*?\*\//g, '')
                        .replace(/\s+/g, ' ')
                        .trim();

                    totalOriginal += jsContent.length;
                    totalCompressed += compressed.length;
                } catch (error) {
                    console.warn('⚠️ Could not test JS file:', script.src);
                }
            }

            const compressionRatio = totalOriginal > 0 ?
                ((totalOriginal - totalCompressed) / totalOriginal) * 100 : 0;

            return {
                name: 'JavaScript Compression',
                originalSize: `${Math.round(totalOriginal / 1024)} KB`,
                compressedSize: `${Math.round(totalCompressed / 1024)} KB`,
                compressionRatio: `${Math.round(compressionRatio)}%`,
                status: compressionRatio >= 25 ? 'PASS' : 'FAIL'
            };

        } catch (error) {
            console.error('❌ Error testing JS compression:', error);
            return {
                name: 'JavaScript Compression',
                status: 'ERROR',
                error: error.message
            };
        }
    }

    // اختبار أداء قاعدة البيانات
    async testDatabasePerformance() {
        try {
            console.log('🗃️ Testing database performance...');

            const testResults = {
                name: 'Database Performance',
                tests: []
            };

            // اختبار سرعة الكتابة
            const writeTest = await this.testDatabaseWrite();
            testResults.tests.push(writeTest);

            // اختبار سرعة القراءة
            const readTest = await this.testDatabaseRead();
            testResults.tests.push(readTest);

            // اختبار البحث
            const searchTest = await this.testDatabaseSearch();
            testResults.tests.push(searchTest);

            this.testResults.push(testResults);
            console.log('✅ Database performance tests completed');

        } catch (error) {
            console.error('❌ Error testing database performance:', error);
        }
    }

    // اختبار سرعة الكتابة في قاعدة البيانات
    async testDatabaseWrite() {
        try {
            const testData = Array.from({ length: 100 }, (_, i) => ({
                id: `test-${i}`,
                name: `Test Item ${i}`,
                value: Math.random() * 1000
            }));

            const startTime = performance.now();

            if (window.UnifiedStorage) {
                await window.UnifiedStorage.save('performance_test', testData);
            }

            const endTime = performance.now();
            const duration = endTime - startTime;

            return {
                name: 'Database Write Speed',
                records: testData.length,
                duration: `${Math.round(duration)}ms`,
                recordsPerSecond: Math.round((testData.length / duration) * 1000),
                status: duration < 100 ? 'PASS' : 'FAIL'
            };

        } catch (error) {
            console.error('❌ Error testing database write:', error);
            return {
                name: 'Database Write Speed',
                status: 'ERROR',
                error: error.message
            };
        }
    }

    // اختبار سرعة القراءة من قاعدة البيانات
    async testDatabaseRead() {
        try {
            const startTime = performance.now();

            let data = [];
            if (window.UnifiedStorage) {
                data = await window.UnifiedStorage.load('performance_test', []);
            }

            const endTime = performance.now();
            const duration = endTime - startTime;

            return {
                name: 'Database Read Speed',
                records: data.length,
                duration: `${Math.round(duration)}ms`,
                recordsPerSecond: data.length > 0 ? Math.round((data.length / duration) * 1000) : 0,
                status: duration < 50 ? 'PASS' : 'FAIL'
            };

        } catch (error) {
            console.error('❌ Error testing database read:', error);
            return {
                name: 'Database Read Speed',
                status: 'ERROR',
                error: error.message
            };
        }
    }

    // اختبار البحث في قاعدة البيانات
    async testDatabaseSearch() {
        try {
            const startTime = performance.now();

            // محاكاة البحث
            if (window.ProductsManager && window.ProductsManager.searchProducts) {
                await window.ProductsManager.searchProducts('test');
            }

            const endTime = performance.now();
            const duration = endTime - startTime;

            return {
                name: 'Database Search Speed',
                duration: `${Math.round(duration)}ms`,
                status: duration < 100 ? 'PASS' : 'FAIL'
            };

        } catch (error) {
            console.error('❌ Error testing database search:', error);
            return {
                name: 'Database Search Speed',
                status: 'ERROR',
                error: error.message
            };
        }
    }

    // اختبار أداء واجهة المستخدم
    async testUIPerformance() {
        try {
            console.log('🎨 Testing UI performance...');

            const testResults = {
                name: 'UI Performance',
                tests: []
            };

            // اختبار FPS
            const fpsTest = await this.testFrameRate();
            testResults.tests.push(fpsTest);

            // اختبار وقت الاستجابة
            const responseTest = await this.testUIResponseTime();
            testResults.tests.push(responseTest);

            // اختبار التمرير السلس
            const scrollTest = await this.testSmoothScrolling();
            testResults.tests.push(scrollTest);

            this.testResults.push(testResults);
            console.log('✅ UI performance tests completed');

        } catch (error) {
            console.error('❌ Error testing UI performance:', error);
        }
    }

    // اختبار معدل الإطارات
    async testFrameRate() {
        try {
            let frameCount = 0;
            const startTime = performance.now();

            // قياس FPS لمدة ثانية واحدة
            const measureFrames = () => {
                frameCount++;
                const currentTime = performance.now();

                if (currentTime - startTime < 1000) {
                    requestAnimationFrame(measureFrames);
                }
            };

            requestAnimationFrame(measureFrames);

            // انتظار انتهاء القياس
            await new Promise(resolve => setTimeout(resolve, 1100));

            return {
                name: 'Frame Rate (FPS)',
                fps: frameCount,
                status: frameCount >= 30 ? 'PASS' : 'FAIL',
                target: '≥ 30 FPS',
                actual: `${frameCount} FPS`
            };

        } catch (error) {
            console.error('❌ Error testing frame rate:', error);
            return {
                name: 'Frame Rate (FPS)',
                status: 'ERROR',
                error: error.message
            };
        }
    }

    // اختبار وقت استجابة واجهة المستخدم
    async testUIResponseTime() {
        try {
            const button = document.createElement('button');
            button.textContent = 'Test Button';
            button.style.position = 'absolute';
            button.style.left = '-9999px';
            document.body.appendChild(button);

            const startTime = performance.now();

            // محاكاة النقر
            button.click();

            const endTime = performance.now();
            const responseTime = endTime - startTime;

            // تنظيف
            document.body.removeChild(button);

            return {
                name: 'UI Response Time',
                duration: `${Math.round(responseTime)}ms`,
                status: responseTime < 16 ? 'PASS' : 'FAIL', // 60fps = 16.67ms per frame
                target: '< 16ms',
                actual: `${Math.round(responseTime)}ms`
            };

        } catch (error) {
            console.error('❌ Error testing UI response time:', error);
            return {
                name: 'UI Response Time',
                status: 'ERROR',
                error: error.message
            };
        }
    }

    // اختبار التمرير السلس
    async testSmoothScrolling() {
        try {
            // إنشاء عنصر طويل للاختبار
            const testElement = document.createElement('div');
            testElement.style.height = '2000px';
            testElement.style.position = 'absolute';
            testElement.style.left = '-9999px';
            document.body.appendChild(testElement);

            const startTime = performance.now();

            // محاكاة التمرير
            window.scrollTo({ top: 500, behavior: 'smooth' });

            // انتظار انتهاء التمرير
            await new Promise(resolve => setTimeout(resolve, 500));

            const endTime = performance.now();
            const scrollTime = endTime - startTime;

            // تنظيف
            document.body.removeChild(testElement);
            window.scrollTo({ top: 0 });

            return {
                name: 'Smooth Scrolling',
                duration: `${Math.round(scrollTime)}ms`,
                status: scrollTime < 600 ? 'PASS' : 'FAIL'
            };

        } catch (error) {
            console.error('❌ Error testing smooth scrolling:', error);
            return {
                name: 'Smooth Scrolling',
                status: 'ERROR',
                error: error.message
            };
        }
    }

    // اختبار أداء التخزين المؤقت
    async testCachePerformance() {
        try {
            console.log('🗄️ Testing cache performance...');

            const testResults = {
                name: 'Cache Performance',
                tests: []
            };

            // اختبار سرعة التخزين المؤقت
            const cacheSpeedTest = await this.testCacheSpeed();
            testResults.tests.push(cacheSpeedTest);

            // اختبار معدل نجاح التخزين المؤقت
            const cacheHitTest = await this.testCacheHitRate();
            testResults.tests.push(cacheHitTest);

            this.testResults.push(testResults);
            console.log('✅ Cache performance tests completed');

        } catch (error) {
            console.error('❌ Error testing cache performance:', error);
        }
    }

    // اختبار سرعة التخزين المؤقت
    async testCacheSpeed() {
        try {
            const testData = { test: 'cache performance data' };

            // اختبار الكتابة في التخزين المؤقت
            const writeStartTime = performance.now();

            if (window.PerformanceOptimizer) {
                window.PerformanceOptimizer.cacheData('test_key', testData);
            }

            const writeEndTime = performance.now();
            const writeTime = writeEndTime - writeStartTime;

            // اختبار القراءة من التخزين المؤقت
            const readStartTime = performance.now();

            let cachedData = null;
            if (window.PerformanceOptimizer) {
                cachedData = window.PerformanceOptimizer.getCachedData('test_key');
            }

            const readEndTime = performance.now();
            const readTime = readEndTime - readStartTime;

            return {
                name: 'Cache Speed',
                writeTime: `${Math.round(writeTime)}ms`,
                readTime: `${Math.round(readTime)}ms`,
                dataRetrieved: cachedData !== null,
                status: (writeTime < 10 && readTime < 5) ? 'PASS' : 'FAIL'
            };

        } catch (error) {
            console.error('❌ Error testing cache speed:', error);
            return {
                name: 'Cache Speed',
                status: 'ERROR',
                error: error.message
            };
        }
    }

    // اختبار معدل نجاح التخزين المؤقت
    async testCacheHitRate() {
        try {
            let hits = 0;
            let misses = 0;

            // اختبار عدة مفاتيح
            const testKeys = ['key1', 'key2', 'key3', 'key4', 'key5'];

            // إضافة بعض البيانات للتخزين المؤقت
            if (window.PerformanceOptimizer) {
                window.PerformanceOptimizer.cacheData('key1', 'data1');
                window.PerformanceOptimizer.cacheData('key3', 'data3');
                window.PerformanceOptimizer.cacheData('key5', 'data5');
            }

            // اختبار الوصول للمفاتيح
            for (const key of testKeys) {
                if (window.PerformanceOptimizer) {
                    const data = window.PerformanceOptimizer.getCachedData(key);
                    if (data !== null) {
                        hits++;
                    } else {
                        misses++;
                    }
                }
            }

            const hitRate = hits + misses > 0 ? (hits / (hits + misses)) * 100 : 0;

            return {
                name: 'Cache Hit Rate',
                hits: hits,
                misses: misses,
                hitRate: `${Math.round(hitRate)}%`,
                status: hitRate >= 60 ? 'PASS' : 'FAIL'
            };

        } catch (error) {
            console.error('❌ Error testing cache hit rate:', error);
            return {
                name: 'Cache Hit Rate',
                status: 'ERROR',
                error: error.message
            };
        }
    }

    // اختبار الوحدات المحسنة
    async testOptimizedModules() {
        try {
            console.log('🚀 Testing optimized modules...');

            const testResults = {
                name: 'Optimized Modules',
                tests: []
            };

            // اختبار وحدة إدارة المنتجات
            const productsTest = await this.testProductsManager();
            testResults.tests.push(productsTest);

            // اختبار وحدة إدارة العملاء
            const customersTest = await this.testCustomersManager();
            testResults.tests.push(customersTest);

            // اختبار وحدة إدارة المبيعات
            const salesTest = await this.testSalesManager();
            testResults.tests.push(salesTest);

            // اختبار وحدة التقارير
            const reportsTest = await this.testReportsManager();
            testResults.tests.push(reportsTest);

            // اختبار وحدة المصروفات
            const expensesTest = await this.testExpensesManager();
            testResults.tests.push(expensesTest);

            this.testResults.push(testResults);
            console.log('✅ Optimized modules tests completed');

        } catch (error) {
            console.error('❌ Error testing optimized modules:', error);
        }
    }

    // اختبار وحدة إدارة المنتجات
    async testProductsManager() {
        try {
            const startTime = performance.now();

            if (window.ProductsManager) {
                // اختبار إضافة منتج
                await window.ProductsManager.addProduct({
                    name: 'Test Product',
                    price: 100,
                    quantity: 10
                });

                // اختبار البحث
                await window.ProductsManager.searchProducts('Test');

                // اختبار الحصول على الإحصائيات
                window.ProductsManager.getProductsStats();
            }

            const endTime = performance.now();
            const duration = endTime - startTime;

            return {
                name: 'Products Manager',
                duration: `${Math.round(duration)}ms`,
                loaded: !!window.ProductsManager,
                status: (window.ProductsManager && duration < 100) ? 'PASS' : 'FAIL'
            };

        } catch (error) {
            console.error('❌ Error testing Products Manager:', error);
            return {
                name: 'Products Manager',
                status: 'ERROR',
                error: error.message
            };
        }
    }

    // اختبار وحدة إدارة العملاء
    async testCustomersManager() {
        try {
            const startTime = performance.now();

            if (window.CustomersManager) {
                // اختبار إضافة عميل
                await window.CustomersManager.addCustomer({
                    name: 'Test Customer',
                    phone: '1234567890'
                });

                // اختبار البحث
                await window.CustomersManager.searchCustomers('Test');
            }

            const endTime = performance.now();
            const duration = endTime - startTime;

            return {
                name: 'Customers Manager',
                duration: `${Math.round(duration)}ms`,
                loaded: !!window.CustomersManager,
                status: (window.CustomersManager && duration < 100) ? 'PASS' : 'FAIL'
            };

        } catch (error) {
            console.error('❌ Error testing Customers Manager:', error);
            return {
                name: 'Customers Manager',
                status: 'ERROR',
                error: error.message
            };
        }
    }

    // اختبار وحدة إدارة المبيعات
    async testSalesManager() {
        try {
            const startTime = performance.now();

            if (window.SalesManager) {
                // اختبار إنشاء مبيعة جديدة
                window.SalesManager.createNewSale();

                // اختبار الحصول على الإحصائيات
                window.SalesManager.getSalesStats();
            }

            const endTime = performance.now();
            const duration = endTime - startTime;

            return {
                name: 'Sales Manager',
                duration: `${Math.round(duration)}ms`,
                loaded: !!window.SalesManager,
                status: (window.SalesManager && duration < 100) ? 'PASS' : 'FAIL'
            };

        } catch (error) {
            console.error('❌ Error testing Sales Manager:', error);
            return {
                name: 'Sales Manager',
                status: 'ERROR',
                error: error.message
            };
        }
    }

    // اختبار وحدة التقارير
    async testReportsManager() {
        try {
            const startTime = performance.now();

            if (window.ReportsManager) {
                // اختبار إنشاء تقرير المبيعات
                await window.ReportsManager.generateSalesReport('today');

                // اختبار إنشاء تقرير المخزون
                await window.ReportsManager.generateInventoryReport();
            }

            const endTime = performance.now();
            const duration = endTime - startTime;

            return {
                name: 'Reports Manager',
                duration: `${Math.round(duration)}ms`,
                loaded: !!window.ReportsManager,
                status: (window.ReportsManager && duration < 200) ? 'PASS' : 'FAIL'
            };

        } catch (error) {
            console.error('❌ Error testing Reports Manager:', error);
            return {
                name: 'Reports Manager',
                status: 'ERROR',
                error: error.message
            };
        }
    }

    // اختبار وحدة المصروفات
    async testExpensesManager() {
        try {
            const startTime = performance.now();

            if (window.ExpensesManager) {
                // اختبار إضافة مصروف
                await window.ExpensesManager.addExpense({
                    description: 'Test Expense',
                    amount: 50,
                    category: 'أخرى'
                });

                // اختبار الحصول على الإحصائيات
                window.ExpensesManager.getExpensesStats();
            }

            const endTime = performance.now();
            const duration = endTime - startTime;

            return {
                name: 'Expenses Manager',
                duration: `${Math.round(duration)}ms`,
                loaded: !!window.ExpensesManager,
                status: (window.ExpensesManager && duration < 100) ? 'PASS' : 'FAIL'
            };

        } catch (error) {
            console.error('❌ Error testing Expenses Manager:', error);
            return {
                name: 'Expenses Manager',
                status: 'ERROR',
                error: error.message
            };
        }
    }

    // إنشاء التقرير النهائي
    generatePerformanceReport() {
        try {
            const totalDuration = this.endTime - this.startTime;

            // حساب النتائج الإجمالية
            const totalTests = this.testResults.reduce((sum, category) => {
                return sum + (category.tests ? category.tests.length : 0);
            }, 0);

            const passedTests = this.testResults.reduce((sum, category) => {
                if (category.tests) {
                    return sum + category.tests.filter(test => test.status === 'PASS').length;
                }
                return sum;
            }, 0);

            const failedTests = this.testResults.reduce((sum, category) => {
                if (category.tests) {
                    return sum + category.tests.filter(test => test.status === 'FAIL').length;
                }
                return sum;
            }, 0);

            const errorTests = this.testResults.reduce((sum, category) => {
                if (category.tests) {
                    return sum + category.tests.filter(test => test.status === 'ERROR').length;
                }
                return sum;
            }, 0);

            const successRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;

            // تقييم الأداء العام
            let overallGrade = 'F';
            if (successRate >= 90) overallGrade = 'A';
            else if (successRate >= 80) overallGrade = 'B';
            else if (successRate >= 70) overallGrade = 'C';
            else if (successRate >= 60) overallGrade = 'D';

            const report = {
                timestamp: new Date().toISOString(),
                duration: `${Math.round(totalDuration)}ms`,
                summary: {
                    totalTests,
                    passedTests,
                    failedTests,
                    errorTests,
                    successRate: `${Math.round(successRate)}%`,
                    overallGrade
                },
                categories: this.testResults,
                performanceTargets: this.performanceTargets,
                recommendations: this.generateRecommendations()
            };

            console.log('📊 Performance Report Generated:', report);
            return report;

        } catch (error) {
            console.error('❌ Error generating performance report:', error);
            return null;
        }
    }

    // إنشاء التوصيات
    generateRecommendations() {
        try {
            const recommendations = [];

            // تحليل النتائج وإنشاء التوصيات
            this.testResults.forEach(category => {
                if (category.tests) {
                    category.tests.forEach(test => {
                        if (test.status === 'FAIL') {
                            switch (test.name) {
                                case 'Page Load Time':
                                    recommendations.push('تحسين تحميل الصفحة عبر تقليل حجم الملفات وتحسين الصور');
                                    break;
                                case 'Memory Leak Test':
                                    recommendations.push('مراجعة الكود لإصلاح تسريبات الذاكرة');
                                    break;
                                case 'Frame Rate (FPS)':
                                    recommendations.push('تحسين الرسوميات وتقليل العمليات المعقدة في الواجهة');
                                    break;
                                case 'Cache Hit Rate':
                                    recommendations.push('تحسين استراتيجية التخزين المؤقت');
                                    break;
                            }
                        }
                    });
                }
            });

            // إضافة توصيات عامة
            if (recommendations.length === 0) {
                recommendations.push('الأداء ممتاز! استمر في المراقبة المنتظمة');
            }

            return recommendations;

        } catch (error) {
            console.error('❌ Error generating recommendations:', error);
            return ['حدث خطأ في إنشاء التوصيات'];
        }
    }

    // تصدير التقرير
    exportReport(report) {
        try {
            const jsonData = JSON.stringify(report, null, 2);
            const blob = new Blob([jsonData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `performance_report_${Date.now()}.json`;
            a.click();

            URL.revokeObjectURL(url);
            console.log('📤 Performance report exported');

        } catch (error) {
            console.error('❌ Error exporting report:', error);
        }
    }

    // الحصول على حالة الاختبار
    getTestStatus() {
        return {
            isRunning: this.isRunning,
            startTime: this.startTime,
            currentTime: performance.now(),
            progress: this.testResults.length
        };
    }
}

// إنشاء instance عام
window.PerformanceTests = new PerformanceTests();

// تصدير للاستخدام في الوحدات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceTests;
}

console.log('✅ Performance Tests loaded successfully');