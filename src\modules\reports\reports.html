<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير - Black Horse ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .module-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .report-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
        }
        .btn-gradient:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            color: white;
        }
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .kpi-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        .filter-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .nav-tabs .nav-link {
            border: none;
            color: #667eea;
            font-weight: 600;
        }
        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="module-card p-4 mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-chart-bar me-2"></i>التقارير والإحصائيات</h2>
                    <p class="text-muted mb-0">تقارير شاملة وإحصائيات تفصيلية لجميع العمليات</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-success" onclick="exportReport()">
                        <i class="fas fa-download me-2"></i>تصدير
                    </button>
                    <button class="btn btn-gradient" onclick="scheduleReport()">
                        <i class="fas fa-clock me-2"></i>جدولة التقرير
                    </button>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="filter-section">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="fromDate">
                </div>
                <div class="col-md-3">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="toDate">
                </div>
                <div class="col-md-3">
                    <label class="form-label">نوع التقرير</label>
                    <select class="form-select" id="reportType">
                        <option value="all">جميع التقارير</option>
                        <option value="sales">المبيعات</option>
                        <option value="purchases">المشتريات</option>
                        <option value="inventory">المخزون</option>
                        <option value="financial">المالية</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-gradient d-block w-100" onclick="generateReports()">
                        <i class="fas fa-search me-2"></i>إنشاء التقارير
                    </button>
                </div>
            </div>
        </div>

        <!-- KPI Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="kpi-card">
                    <i class="fas fa-chart-line fa-2x mb-2"></i>
                    <h4 id="totalRevenue">0</h4>
                    <p>إجمالي الإيرادات</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="kpi-card" style="background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);">
                    <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                    <h4 id="totalSales">0</h4>
                    <p>إجمالي المبيعات</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="kpi-card" style="background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);">
                    <i class="fas fa-boxes fa-2x mb-2"></i>
                    <h4 id="totalProducts">0</h4>
                    <p>إجمالي المنتجات</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="kpi-card" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h4 id="totalCustomers">0</h4>
                    <p>إجمالي العملاء</p>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="module-card p-4">
            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs mb-4" id="reportsTabs">
                <li class="nav-item">
                    <a class="nav-link active" data-bs-toggle="tab" href="#salesReportsTab">تقارير المبيعات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#inventoryReportsTab">تقارير المخزون</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#financialReportsTab">التقارير المالية</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#customReportsTab">تقارير مخصصة</a>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Sales Reports Tab -->
                <div class="tab-pane fade show active" id="salesReportsTab">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="chart-container">
                                <h6>مبيعات الأسبوع</h6>
                                <canvas id="weeklySalesChart"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="chart-container">
                                <h6>أفضل المنتجات مبيعاً</h6>
                                <canvas id="topProductsChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="chart-container">
                                <h6>مبيعات الشهر</h6>
                                <canvas id="monthlySalesChart"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="chart-container">
                                <h6>أفضل العملاء</h6>
                                <canvas id="topCustomersChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Inventory Reports Tab -->
                <div class="tab-pane fade" id="inventoryReportsTab">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="chart-container">
                                <h6>حالة المخزون</h6>
                                <canvas id="inventoryStatusChart"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="chart-container">
                                <h6>المنتجات منخفضة المخزون</h6>
                                <div id="lowStockProducts"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="chart-container">
                                <h6>حركة المخزون</h6>
                                <canvas id="inventoryMovementChart"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="chart-container">
                                <h6>قيمة المخزون</h6>
                                <canvas id="inventoryValueChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Financial Reports Tab -->
                <div class="tab-pane fade" id="financialReportsTab">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="chart-container">
                                <h6>الإيرادات والمصروفات</h6>
                                <canvas id="revenueExpenseChart"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="chart-container">
                                <h6>الربحية</h6>
                                <canvas id="profitabilityChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="chart-container">
                                <h6>التدفق النقدي</h6>
                                <canvas id="cashFlowChart"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="chart-container">
                                <h6>طرق الدفع</h6>
                                <canvas id="paymentMethodsChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Custom Reports Tab -->
                <div class="tab-pane fade" id="customReportsTab">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="report-card" onclick="generateCustomReport('daily-summary')">
                                <i class="fas fa-calendar-day fa-2x text-primary mb-3"></i>
                                <h5>تقرير يومي</h5>
                                <p>ملخص شامل للعمليات اليومية</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="report-card" onclick="generateCustomReport('weekly-summary')">
                                <i class="fas fa-calendar-week fa-2x text-success mb-3"></i>
                                <h5>تقرير أسبوعي</h5>
                                <p>تحليل الأداء الأسبوعي</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="report-card" onclick="generateCustomReport('monthly-summary')">
                                <i class="fas fa-calendar-alt fa-2x text-warning mb-3"></i>
                                <h5>تقرير شهري</h5>
                                <p>إحصائيات شاملة للشهر</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="report-card" onclick="generateCustomReport('customer-analysis')">
                                <i class="fas fa-users fa-2x text-info mb-3"></i>
                                <h5>تحليل العملاء</h5>
                                <p>تقرير مفصل عن سلوك العملاء</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="report-card" onclick="generateCustomReport('product-performance')">
                                <i class="fas fa-chart-line fa-2x text-danger mb-3"></i>
                                <h5>أداء المنتجات</h5>
                                <p>تحليل أداء المنتجات والمبيعات</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="report-card" onclick="generateCustomReport('financial-analysis')">
                                <i class="fas fa-dollar-sign fa-2x text-secondary mb-3"></i>
                                <h5>التحليل المالي</h5>
                                <p>تقرير مالي شامل ومفصل</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Modal -->
    <div class="modal fade" id="exportModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تصدير التقرير</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">نوع الملف</label>
                        <select class="form-select" id="exportFormat">
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                            <option value="csv">CSV</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">اسم الملف</label>
                        <input type="text" class="form-control" id="exportFileName" value="تقرير">
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeCharts">
                            <label class="form-check-label" for="includeCharts">
                                تضمين الرسوم البيانية
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-gradient" onclick="confirmExport()">تصدير</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Schedule Report Modal -->
    <div class="modal fade" id="scheduleModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">جدولة التقرير</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">نوع التقرير</label>
                        <select class="form-select" id="scheduleReportType">
                            <option value="daily">يومي</option>
                            <option value="weekly">أسبوعي</option>
                            <option value="monthly">شهري</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">وقت الإرسال</label>
                        <input type="time" class="form-control" id="scheduleTime">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="scheduleEmail">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-gradient" onclick="saveSchedule()">حفظ الجدولة</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="reports.js"></script>
</body>
</html>
