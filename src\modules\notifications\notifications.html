<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الإشعارات - Black Horse ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/custom.css" rel="stylesheet">
    <style>
        .notification-item {
            border-left: 4px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .notification-item.unread {
            background-color: #f8f9fa;
            border-left-color: #007bff;
        }

        .notification-item.priority-critical {
            border-left-color: #dc3545;
        }

        .notification-item.priority-high {
            border-left-color: #fd7e14;
        }

        .notification-item.priority-medium {
            border-left-color: #ffc107;
        }

        .notification-item.priority-low {
            border-left-color: #6c757d;
        }

        .notification-item:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .notification-actions {
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .notification-item:hover .notification-actions {
            opacity: 1;
        }

        .channel-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        .notification-container {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 9999;
            max-width: 400px;
        }

        .notification-toast {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            margin-bottom: 10px;
            padding: 15px;
            transform: translateX(-100%);
            opacity: 0;
            transition: all 0.3s ease;
        }

        .notification-toast.show {
            transform: translateX(0);
            opacity: 1;
        }

        .notification-toast.hide {
            transform: translateX(-100%);
            opacity: 0;
        }

        .notification-toast.notification-critical {
            border-left: 4px solid #dc3545;
        }

        .notification-toast.notification-high {
            border-left: 4px solid #fd7e14;
        }

        .notification-toast.notification-medium {
            border-left: 4px solid #ffc107;
        }

        .notification-toast.notification-low {
            border-left: 4px solid #6c757d;
        }

        .notification-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .notification-header i {
            margin-left: 8px;
        }

        .notification-body {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }

        .notification-actions {
            margin-bottom: 8px;
        }

        .notification-actions .btn {
            margin-left: 5px;
        }

        .notification-time {
            font-size: 0.75rem;
            color: #adb5bd;
            text-align: left;
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .filter-tabs .nav-link {
            border-radius: 25px;
            margin-left: 5px;
            transition: all 0.3s ease;
        }

        .filter-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: transparent;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- رأس الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-bell text-primary"></i> إدارة الإشعارات</h2>
                        <p class="text-muted">مركز إدارة جميع إشعارات النظام</p>
                    </div>
                    <div>
                        <button class="btn btn-outline-primary me-2" onclick="markAllAsRead()">
                            <i class="fas fa-check-double"></i> تحديد الكل كمقروء
                        </button>
                        <button class="btn btn-outline-danger me-2" onclick="deleteAllNotifications()">
                            <i class="fas fa-trash"></i> حذف الكل
                        </button>
                        <button class="btn btn-primary" onclick="showSettingsModal()">
                            <i class="fas fa-cog"></i> الإعدادات
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات الإشعارات -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-bell fa-2x text-primary mb-2"></i>
                        <h4 id="totalNotifications">0</h4>
                        <small class="text-muted">إجمالي الإشعارات</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-envelope fa-2x text-warning mb-2"></i>
                        <h4 id="unreadNotifications">0</h4>
                        <small class="text-muted">غير مقروءة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                        <h4 id="criticalNotifications">0</h4>
                        <small class="text-muted">حرجة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-clock fa-2x text-info mb-2"></i>
                        <h4 id="todayNotifications">0</h4>
                        <small class="text-muted">اليوم</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- فلاتر الإشعارات -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <ul class="nav nav-pills filter-tabs" id="filterTabs">
                                    <li class="nav-item">
                                        <a class="nav-link active" href="#" onclick="filterNotifications('all')">
                                            الكل
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#" onclick="filterNotifications('unread')">
                                            غير مقروءة
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#" onclick="filterNotifications('critical')">
                                            حرجة
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#" onclick="filterNotifications('today')">
                                            اليوم
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <div class="row">
                                    <div class="col-md-6">
                                        <select class="form-select" id="channelFilter" onchange="filterByChannel()">
                                            <option value="">جميع القنوات</option>
                                            <option value="sales">المبيعات</option>
                                            <option value="inventory">المخزون</option>
                                            <option value="hr">الموارد البشرية</option>
                                            <option value="system">النظام</option>
                                            <option value="security">الأمان</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" id="searchInput"
                                               placeholder="البحث في الإشعارات..."
                                               onkeyup="searchNotifications()">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قائمة الإشعارات -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">الإشعارات</h5>
                    </div>
                    <div class="card-body p-0">
                        <div id="notificationsList">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>

                        <!-- حالة فارغة -->
                        <div id="emptyState" class="empty-state" style="display: none;">
                            <i class="fas fa-bell-slash"></i>
                            <h4>لا توجد إشعارات</h4>
                            <p>لم يتم العثور على إشعارات تطابق المعايير المحددة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة الإعدادات -->
    <div class="modal fade" id="settingsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إعدادات الإشعارات</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <!-- الإعدادات العامة -->
                        <div class="col-md-6">
                            <h6>الإعدادات العامة</h6>
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="notificationsEnabled" checked>
                                    <label class="form-check-label" for="notificationsEnabled">
                                        تفعيل الإشعارات
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="soundEnabled" checked>
                                    <label class="form-check-label" for="soundEnabled">
                                        تفعيل الأصوات
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="desktopEnabled" checked>
                                    <label class="form-check-label" for="desktopEnabled">
                                        إشعارات سطح المكتب
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="autoMarkRead">
                                    <label class="form-check-label" for="autoMarkRead">
                                        تحديد كمقروء تلقائياً
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الحد الأقصى للإشعارات</label>
                                <input type="number" class="form-control" id="maxNotifications" value="100" min="10" max="1000">
                            </div>
                        </div>

                        <!-- قنوات التوصيل -->
                        <div class="col-md-6">
                            <h6>قنوات التوصيل</h6>
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="emailEnabled">
                                    <label class="form-check-label" for="emailEnabled">
                                        البريد الإلكتروني
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="smsEnabled">
                                    <label class="form-check-label" for="smsEnabled">
                                        رسائل SMS
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="whatsappEnabled">
                                    <label class="form-check-label" for="whatsappEnabled">
                                        واتساب
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات القنوات -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6>إعدادات القنوات</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>القناة</th>
                                            <th>مفعلة</th>
                                            <th>الأولوية</th>
                                            <th>اللون</th>
                                        </tr>
                                    </thead>
                                    <tbody id="channelSettings">
                                        <!-- سيتم ملؤها بـ JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveNotificationSettings()">
                        <i class="fas fa-save"></i> حفظ الإعدادات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تفاصيل الإشعار -->
    <div class="modal fade" id="notificationModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="notificationModalTitle"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="notificationModalBody">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
                <div class="modal-footer" id="notificationModalFooter">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- حاوي الإشعارات المنبثقة -->
    <div id="notification-container" class="notification-container"></div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../core/database.js"></script>
    <script src="../../core/utils.js"></script>
    <script src="notifications.js"></script>

    <script>
        // متغيرات عامة
        let notificationManager;
        let currentFilter = 'all';
        let currentChannel = '';
        let searchTerm = '';

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeNotificationManager();
            loadNotifications();
            updateStatistics();
            setupEventListeners();
        });

        // تهيئة مدير الإشعارات
        function initializeNotificationManager() {
            notificationManager = new NotificationManager();

            // إعداد مستمعي الأحداث
            notificationManager.on('notificationSent', updateDisplay);
            notificationManager.on('notificationRead', updateDisplay);
            notificationManager.on('notificationDeleted', updateDisplay);
            notificationManager.on('allNotificationsRead', updateDisplay);
            notificationManager.on('allNotificationsDeleted', updateDisplay);

            console.log('✅ تم تهيئة مدير الإشعارات');
        }

        // تحميل الإشعارات
        function loadNotifications() {
            displayNotifications();
            updateStatistics();
        }

        // عرض الإشعارات
        function displayNotifications() {
            const container = document.getElementById('notificationsList');
            const emptyState = document.getElementById('emptyState');

            // تطبيق الفلاتر
            let notifications = filterNotifications();

            if (notifications.length === 0) {
                container.innerHTML = '';
                emptyState.style.display = 'block';
                return;
            }

            emptyState.style.display = 'none';

            // ترتيب الإشعارات حسب التاريخ
            notifications.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

            container.innerHTML = notifications.map(notification => createNotificationHTML(notification)).join('');
        }

        // إنشاء HTML للإشعار
        function createNotificationHTML(notification) {
            const channel = notificationManager.channels.get(notification.channel);
            const timeAgo = getTimeAgo(notification.timestamp);
            const isUnread = !notification.read;

            return `
                <div class="notification-item p-3 border-bottom ${isUnread ? 'unread' : ''} priority-${notification.priority}"
                     data-notification-id="${notification.id}">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-start">
                                <div class="me-3">
                                    <i class="${notification.icon}" style="color: ${channel.color}; font-size: 1.2rem;"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-1">
                                        <h6 class="mb-0 me-2">${notification.title}</h6>
                                        <span class="badge channel-badge" style="background-color: ${channel.color}">
                                            ${channel.name}
                                        </span>
                                        ${notification.priority === 'critical' ? '<span class="badge bg-danger ms-1">حرج</span>' : ''}
                                        ${isUnread ? '<span class="badge bg-primary ms-1">جديد</span>' : ''}
                                    </div>
                                    <p class="text-muted mb-1">${notification.body}</p>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        ${timeAgo}
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="notification-actions text-end">
                                ${!notification.read ? `
                                    <button class="btn btn-sm btn-outline-primary me-1" onclick="markAsRead('${notification.id}')">
                                        <i class="fas fa-check"></i> تحديد كمقروء
                                    </button>
                                ` : ''}
                                <button class="btn btn-sm btn-outline-info me-1" onclick="viewNotificationDetails('${notification.id}')">
                                    <i class="fas fa-eye"></i> عرض
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteNotification('${notification.id}')">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            </div>
                            ${notification.actions.length > 0 ? `
                                <div class="mt-2">
                                    ${notification.actions.map(action => `
                                        <button class="btn btn-sm btn-primary me-1" onclick="handleNotificationAction('${notification.id}', '${action.action}')">
                                            ${action.label}
                                        </button>
                                    `).join('')}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;
        }

        // تطبيق الفلاتر
        function filterNotifications() {
            let notifications = [...notificationManager.notifications];

            // فلتر حسب النوع
            switch (currentFilter) {
                case 'unread':
                    notifications = notifications.filter(n => !n.read);
                    break;
                case 'critical':
                    notifications = notifications.filter(n => n.priority === 'critical');
                    break;
                case 'today':
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);
                    notifications = notifications.filter(n => new Date(n.timestamp) >= today);
                    break;
            }

            // فلتر حسب القناة
            if (currentChannel) {
                notifications = notifications.filter(n => n.channel === currentChannel);
            }

            // فلتر حسب البحث
            if (searchTerm) {
                const term = searchTerm.toLowerCase();
                notifications = notifications.filter(n =>
                    n.title.toLowerCase().includes(term) ||
                    n.body.toLowerCase().includes(term)
                );
            }

            return notifications;
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            const total = notificationManager.notifications.length;
            const unread = notificationManager.getUnreadCount();
            const critical = notificationManager.getByPriority('critical').length;

            // إحصائيات اليوم
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const todayCount = notificationManager.notifications.filter(n =>
                new Date(n.timestamp) >= today
            ).length;

            document.getElementById('totalNotifications').textContent = total;
            document.getElementById('unreadNotifications').textContent = unread;
            document.getElementById('criticalNotifications').textContent = critical;
            document.getElementById('todayNotifications').textContent = todayCount;
        }

        // تحديث العرض
        function updateDisplay() {
            displayNotifications();
            updateStatistics();
        }

        // فلترة الإشعارات
        function filterNotifications(type) {
            currentFilter = type;

            // تحديث التبويبات
            document.querySelectorAll('.filter-tabs .nav-link').forEach(link => {
                link.classList.remove('active');
            });
            event.target.classList.add('active');

            displayNotifications();
        }

        // فلترة حسب القناة
        function filterByChannel() {
            currentChannel = document.getElementById('channelFilter').value;
            displayNotifications();
        }

        // البحث في الإشعارات
        function searchNotifications() {
            searchTerm = document.getElementById('searchInput').value;
            displayNotifications();
        }

        // تحديد كمقروء
        function markAsRead(notificationId) {
            notificationManager.markAsRead(notificationId);
            showNotification('تم تحديد الإشعار كمقروء', 'success');
        }

        // تحديد الكل كمقروء
        function markAllAsRead() {
            const updated = notificationManager.markAllAsRead();
            if (updated > 0) {
                showNotification(`تم تحديد ${updated} إشعار كمقروء`, 'success');
            } else {
                showNotification('جميع الإشعارات مقروءة بالفعل', 'info');
            }
        }

        // حذف إشعار
        function deleteNotification(notificationId) {
            if (confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
                notificationManager.delete(notificationId);
                showNotification('تم حذف الإشعار', 'success');
            }
        }

        // حذف جميع الإشعارات
        function deleteAllNotifications() {
            if (confirm('هل أنت متأكد من حذف جميع الإشعارات؟ لا يمكن التراجع عن هذا الإجراء.')) {
                const count = notificationManager.deleteAll();
                showNotification(`تم حذف ${count} إشعار`, 'success');
            }
        }

        // عرض تفاصيل الإشعار
        function viewNotificationDetails(notificationId) {
            const notification = notificationManager.notifications.find(n => n.id == notificationId);
            if (!notification) return;

            const channel = notificationManager.channels.get(notification.channel);
            const modal = document.getElementById('notificationModal');

            document.getElementById('notificationModalTitle').innerHTML = `
                <i class="${notification.icon}" style="color: ${channel.color}"></i>
                ${notification.title}
            `;

            document.getElementById('notificationModalBody').innerHTML = `
                <div class="mb-3">
                    <strong>المحتوى:</strong>
                    <p class="mt-2">${notification.body}</p>
                </div>
                <div class="mb-3">
                    <strong>القناة:</strong>
                    <span class="badge" style="background-color: ${channel.color}">${channel.name}</span>
                </div>
                <div class="mb-3">
                    <strong>الأولوية:</strong>
                    <span class="badge bg-${getPriorityColor(notification.priority)}">${getPriorityText(notification.priority)}</span>
                </div>
                <div class="mb-3">
                    <strong>التاريخ:</strong>
                    <span>${new Date(notification.timestamp).toLocaleString('ar-EG')}</span>
                </div>
                <div class="mb-3">
                    <strong>الحالة:</strong>
                    <span class="badge bg-${notification.read ? 'success' : 'warning'}">
                        ${notification.read ? 'مقروء' : 'غير مقروء'}
                    </span>
                </div>
                ${Object.keys(notification.data).length > 0 ? `
                    <div class="mb-3">
                        <strong>البيانات الإضافية:</strong>
                        <pre class="bg-light p-2 mt-2 rounded">${JSON.stringify(notification.data, null, 2)}</pre>
                    </div>
                ` : ''}
            `;

            document.getElementById('notificationModalFooter').innerHTML = `
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                ${!notification.read ? `
                    <button type="button" class="btn btn-primary" onclick="markAsRead('${notification.id}'); bootstrap.Modal.getInstance(document.getElementById('notificationModal')).hide();">
                        <i class="fas fa-check"></i> تحديد كمقروء
                    </button>
                ` : ''}
                <button type="button" class="btn btn-danger" onclick="deleteNotification('${notification.id}'); bootstrap.Modal.getInstance(document.getElementById('notificationModal')).hide();">
                    <i class="fas fa-trash"></i> حذف
                </button>
            `;

            new bootstrap.Modal(modal).show();

            // تحديد كمقروء تلقائياً
            if (!notification.read) {
                setTimeout(() => {
                    notificationManager.markAsRead(notificationId);
                }, 1000);
            }
        }

        // معالجة إجراء الإشعار
        function handleNotificationAction(notificationId, action) {
            notificationManager.handleAction(notificationId, action);
        }

        // عرض نافذة الإعدادات
        function showSettingsModal() {
            loadNotificationSettings();
            new bootstrap.Modal(document.getElementById('settingsModal')).show();
        }

        // تحميل إعدادات الإشعارات
        function loadNotificationSettings() {
            const settings = notificationManager.settings;

            document.getElementById('notificationsEnabled').checked = settings.enabled;
            document.getElementById('soundEnabled').checked = settings.sound;
            document.getElementById('desktopEnabled').checked = settings.desktop;
            document.getElementById('emailEnabled').checked = settings.email;
            document.getElementById('smsEnabled').checked = settings.sms;
            document.getElementById('whatsappEnabled').checked = settings.whatsapp;
            document.getElementById('autoMarkRead').checked = settings.autoMarkRead;
            document.getElementById('maxNotifications').value = settings.maxNotifications;

            // تحميل إعدادات القنوات
            loadChannelSettings();
        }

        // تحميل إعدادات القنوات
        function loadChannelSettings() {
            const tbody = document.getElementById('channelSettings');
            tbody.innerHTML = '';

            notificationManager.channels.forEach((channel, key) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <i class="${channel.icon}" style="color: ${channel.color}"></i>
                        ${channel.name}
                    </td>
                    <td>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox"
                                   id="channel_${key}" ${channel.enabled ? 'checked' : ''}>
                        </div>
                    </td>
                    <td>
                        <select class="form-select form-select-sm" id="priority_${key}">
                            <option value="low" ${channel.priority === 'low' ? 'selected' : ''}>منخفضة</option>
                            <option value="medium" ${channel.priority === 'medium' ? 'selected' : ''}>متوسطة</option>
                            <option value="high" ${channel.priority === 'high' ? 'selected' : ''}>عالية</option>
                            <option value="critical" ${channel.priority === 'critical' ? 'selected' : ''}>حرجة</option>
                        </select>
                    </td>
                    <td>
                        <input type="color" class="form-control form-control-color"
                               id="color_${key}" value="${channel.color}">
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // حفظ إعدادات الإشعارات
        function saveNotificationSettings() {
            // حفظ الإعدادات العامة
            notificationManager.settings = {
                ...notificationManager.settings,
                enabled: document.getElementById('notificationsEnabled').checked,
                sound: document.getElementById('soundEnabled').checked,
                desktop: document.getElementById('desktopEnabled').checked,
                email: document.getElementById('emailEnabled').checked,
                sms: document.getElementById('smsEnabled').checked,
                whatsapp: document.getElementById('whatsappEnabled').checked,
                autoMarkRead: document.getElementById('autoMarkRead').checked,
                maxNotifications: parseInt(document.getElementById('maxNotifications').value)
            };

            // حفظ إعدادات القنوات
            notificationManager.channels.forEach((channel, key) => {
                channel.enabled = document.getElementById(`channel_${key}`).checked;
                channel.priority = document.getElementById(`priority_${key}`).value;
                channel.color = document.getElementById(`color_${key}`).value;
            });

            notificationManager.saveSettings();

            bootstrap.Modal.getInstance(document.getElementById('settingsModal')).hide();
            showNotification('تم حفظ الإعدادات بنجاح', 'success');
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // تحديث العرض عند تغيير حجم النافذة
            window.addEventListener('resize', updateDisplay);

            // اختصارات لوحة المفاتيح
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey) {
                    switch (e.key) {
                        case 'a':
                            e.preventDefault();
                            markAllAsRead();
                            break;
                        case 'r':
                            e.preventDefault();
                            loadNotifications();
                            break;
                    }
                }
            });
        }

        // دوال مساعدة
        function getTimeAgo(timestamp) {
            const now = new Date();
            const time = new Date(timestamp);
            const diff = now - time;

            const minutes = Math.floor(diff / 60000);
            const hours = Math.floor(diff / 3600000);
            const days = Math.floor(diff / 86400000);

            if (minutes < 1) return 'الآن';
            if (minutes < 60) return `منذ ${minutes} دقيقة`;
            if (hours < 24) return `منذ ${hours} ساعة`;
            return `منذ ${days} يوم`;
        }

        function getPriorityColor(priority) {
            const colors = {
                'low': 'secondary',
                'medium': 'warning',
                'high': 'primary',
                'critical': 'danger'
            };
            return colors[priority] || 'secondary';
        }

        function getPriorityText(priority) {
            const texts = {
                'low': 'منخفضة',
                'medium': 'متوسطة',
                'high': 'عالية',
                'critical': 'حرجة'
            };
            return texts[priority] || 'غير محدد';
        }

        // عرض الإشعارات
        function showNotification(message, type = 'info') {
            const alertClass = {
                'success': 'alert-success',
                'error': 'alert-danger',
                'warning': 'alert-warning',
                'info': 'alert-info'
            }[type] || 'alert-info';

            const notification = document.createElement('div');
            notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // إنشاء إشعارات تجريبية
        function createTestNotifications() {
            // إشعار مبيعة جديدة
            notificationManager.send('new_sale', {
                amount: '1,250',
                customer: 'أحمد محمد'
            });

            // إشعار مخزون منخفض
            notificationManager.send('low_stock', {
                product: 'لابتوب HP',
                quantity: '3'
            });

            // إشعار حضور موظف
            notificationManager.send('employee_attendance', {
                employee: 'سارة أحمد',
                action: 'الحضور',
                time: '09:15 ص'
            });

            // إشعار خطأ في النظام
            notificationManager.send('system_error', {
                module: 'المبيعات',
                error: 'فشل في الاتصال بقاعدة البيانات'
            });

            // إشعار أمني
            notificationManager.send('suspicious_login', {
                location: 'القاهرة، مصر'
            });
        }

        // إضافة زر الاختبار للتطوير
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            document.addEventListener('DOMContentLoaded', function() {
                const testButton = document.createElement('button');
                testButton.className = 'btn btn-warning position-fixed';
                testButton.style.cssText = 'bottom: 20px; left: 20px; z-index: 9999;';
                testButton.innerHTML = '<i class="fas fa-flask"></i> إنشاء إشعارات تجريبية';
                testButton.onclick = createTestNotifications;
                document.body.appendChild(testButton);
            });
        }
    </script>
</body>
</html>