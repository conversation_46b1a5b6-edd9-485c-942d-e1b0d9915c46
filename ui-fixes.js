/**
 * Black Horse POS - UI Fixes
 * إصلاح مشاكل واجهة المستخدم
 * Developer: Augment Agent
 */

// ===== UI FIXES SYSTEM =====
const UIFixes = {
    // قائمة المشاكل المكتشفة
    detectedIssues: [],
    
    // تهيئة النظام
    init: function() {
        try {
            console.log('🎨 Initializing UI Fixes...');
            
            // فحص المشاكل
            this.detectUIIssues();
            
            // إصلاح مشاكل التخطيط
            this.fixLayoutIssues();
            
            // إصلاح مشاكل الاستجابة
            this.fixResponsiveIssues();
            
            // إصلاح مشاكل RTL
            this.fixRTLIssues();
            
            // تحسين التفاعل
            this.enhanceInteractivity();
            
            // إصلاح مشاكل الألوان والخطوط
            this.fixStylingIssues();
            
            // إضافة تحسينات إضافية
            this.addEnhancements();
            
            console.log('✅ UI fixes applied successfully');
        } catch (error) {
            console.error('❌ Error applying UI fixes:', error);
        }
    },
    
    // فحص مشاكل الواجهة
    detectUIIssues: function() {
        try {
            console.log('🔍 Detecting UI issues...');
            
            // فحص العناصر المكسورة
            const brokenElements = document.querySelectorAll('[style*="display: none"]');
            if (brokenElements.length > 0) {
                this.detectedIssues.push(`Found ${brokenElements.length} hidden elements`);
            }
            
            // فحص الصور المكسورة
            const images = document.querySelectorAll('img');
            images.forEach(img => {
                img.addEventListener('error', () => {
                    this.detectedIssues.push(`Broken image: ${img.src}`);
                    this.fixBrokenImage(img);
                });
            });
            
            // فحص الجداول الفارغة
            const emptyTables = document.querySelectorAll('table tbody:empty');
            if (emptyTables.length > 0) {
                this.detectedIssues.push(`Found ${emptyTables.length} empty tables`);
            }
            
            console.log(`📊 Detected ${this.detectedIssues.length} UI issues`);
        } catch (error) {
            console.error('❌ Error detecting UI issues:', error);
        }
    },
    
    // إصلاح مشاكل التخطيط
    fixLayoutIssues: function() {
        try {
            console.log('📐 Fixing layout issues...');
            
            // إصلاح مشاكل الفلكس بوكس
            const flexContainers = document.querySelectorAll('.d-flex, .flex, .dashboard-grid');
            flexContainers.forEach(container => {
                if (!container.style.display) {
                    container.style.display = 'flex';
                }
                if (!container.style.flexWrap) {
                    container.style.flexWrap = 'wrap';
                }
            });
            
            // إصلاح مشاكل الشبكة
            const gridContainers = document.querySelectorAll('.grid, .dashboard-grid');
            gridContainers.forEach(container => {
                container.style.display = 'grid';
                container.style.gap = '1rem';
            });
            
            // إصلاح مشاكل الارتفاع
            const fullHeightElements = document.querySelectorAll('.h-100, .full-height');
            fullHeightElements.forEach(element => {
                element.style.minHeight = '100vh';
            });
            
            // إصلاح مشاكل الفيض
            const overflowElements = document.querySelectorAll('.table-responsive, .overflow-auto');
            overflowElements.forEach(element => {
                element.style.overflowX = 'auto';
                element.style.overflowY = 'auto';
            });
            
            console.log('✅ Layout issues fixed');
        } catch (error) {
            console.error('❌ Error fixing layout issues:', error);
        }
    },
    
    // إصلاح مشاكل الاستجابة
    fixResponsiveIssues: function() {
        try {
            console.log('📱 Fixing responsive issues...');
            
            // إضافة CSS للاستجابة
            const responsiveCSS = `
                <style id="responsive-fixes">
                    /* إصلاحات الاستجابة */
                    @media (max-width: 768px) {
                        .sidebar {
                            transform: translateX(-100%);
                            transition: transform 0.3s ease;
                        }
                        
                        .sidebar.active {
                            transform: translateX(0);
                        }
                        
                        .main-content {
                            margin-right: 0;
                            padding: 1rem;
                        }
                        
                        .dashboard-grid {
                            grid-template-columns: 1fr;
                            gap: 1rem;
                        }
                        
                        .table-responsive {
                            font-size: 0.875rem;
                        }
                        
                        .btn-group {
                            flex-direction: column;
                        }
                        
                        .modal-dialog {
                            margin: 1rem;
                            max-width: calc(100% - 2rem);
                        }
                        
                        .nav-dropdown .dropdown-menu {
                            position: static;
                            display: block;
                            box-shadow: none;
                            border: none;
                            background: transparent;
                        }
                    }
                    
                    @media (max-width: 576px) {
                        .page-header {
                            flex-direction: column;
                            gap: 1rem;
                        }
                        
                        .page-actions {
                            width: 100%;
                        }
                        
                        .btn {
                            width: 100%;
                            margin-bottom: 0.5rem;
                        }
                        
                        .card {
                            margin-bottom: 1rem;
                        }
                        
                        .stats-grid {
                            grid-template-columns: 1fr;
                        }
                    }
                </style>
            `;
            
            document.head.insertAdjacentHTML('beforeend', responsiveCSS);
            
            // إضافة زر القائمة للجوال
            this.addMobileMenuToggle();
            
            console.log('✅ Responsive issues fixed');
        } catch (error) {
            console.error('❌ Error fixing responsive issues:', error);
        }
    },
    
    // إصلاح مشاكل RTL
    fixRTLIssues: function() {
        try {
            console.log('🔄 Fixing RTL issues...');
            
            // إضافة CSS لـ RTL
            const rtlCSS = `
                <style id="rtl-fixes">
                    /* إصلاحات RTL */
                    body {
                        direction: rtl;
                        text-align: right;
                    }
                    
                    .sidebar {
                        right: 0;
                        left: auto;
                    }
                    
                    .main-content {
                        margin-right: 250px;
                        margin-left: 0;
                    }
                    
                    .dropdown-menu {
                        right: 0;
                        left: auto;
                    }
                    
                    .modal-header .close {
                        margin-left: auto;
                        margin-right: -1rem;
                    }
                    
                    .btn-group .btn:first-child {
                        border-top-right-radius: 0.25rem;
                        border-bottom-right-radius: 0.25rem;
                        border-top-left-radius: 0;
                        border-bottom-left-radius: 0;
                    }
                    
                    .btn-group .btn:last-child {
                        border-top-left-radius: 0.25rem;
                        border-bottom-left-radius: 0.25rem;
                        border-top-right-radius: 0;
                        border-bottom-right-radius: 0;
                    }
                    
                    .text-left {
                        text-align: right !important;
                    }
                    
                    .text-right {
                        text-align: left !important;
                    }
                    
                    .float-left {
                        float: right !important;
                    }
                    
                    .float-right {
                        float: left !important;
                    }
                    
                    .mr-auto {
                        margin-left: auto !important;
                        margin-right: 0 !important;
                    }
                    
                    .ml-auto {
                        margin-right: auto !important;
                        margin-left: 0 !important;
                    }
                </style>
            `;
            
            document.head.insertAdjacentHTML('beforeend', rtlCSS);
            
            console.log('✅ RTL issues fixed');
        } catch (error) {
            console.error('❌ Error fixing RTL issues:', error);
        }
    },
    
    // تحسين التفاعل
    enhanceInteractivity: function() {
        try {
            console.log('⚡ Enhancing interactivity...');
            
            // إضافة تأثيرات الهوفر
            const interactiveElements = document.querySelectorAll('.btn, .card, .nav-link, .dropdown-item');
            interactiveElements.forEach(element => {
                element.style.transition = 'all 0.3s ease';
                
                element.addEventListener('mouseenter', () => {
                    element.style.transform = 'translateY(-2px)';
                    element.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
                });
                
                element.addEventListener('mouseleave', () => {
                    element.style.transform = 'translateY(0)';
                    element.style.boxShadow = '';
                });
            });
            
            // إضافة تأثيرات النقر
            const clickableElements = document.querySelectorAll('.btn, .card[onclick], [onclick]');
            clickableElements.forEach(element => {
                element.addEventListener('mousedown', () => {
                    element.style.transform = 'scale(0.98)';
                });
                
                element.addEventListener('mouseup', () => {
                    element.style.transform = '';
                });
            });
            
            // تحسين النماذج
            const formInputs = document.querySelectorAll('input, select, textarea');
            formInputs.forEach(input => {
                input.addEventListener('focus', () => {
                    input.style.borderColor = '#007bff';
                    input.style.boxShadow = '0 0 0 0.2rem rgba(0,123,255,0.25)';
                });
                
                input.addEventListener('blur', () => {
                    input.style.borderColor = '';
                    input.style.boxShadow = '';
                });
            });
            
            console.log('✅ Interactivity enhanced');
        } catch (error) {
            console.error('❌ Error enhancing interactivity:', error);
        }
    },
    
    // إصلاح مشاكل الألوان والخطوط
    fixStylingIssues: function() {
        try {
            console.log('🎨 Fixing styling issues...');
            
            // إصلاح الألوان
            const stylingCSS = `
                <style id="styling-fixes">
                    /* إصلاحات الألوان والخطوط */
                    :root {
                        --primary-color: #007bff;
                        --secondary-color: #6c757d;
                        --success-color: #28a745;
                        --danger-color: #dc3545;
                        --warning-color: #ffc107;
                        --info-color: #17a2b8;
                        --light-color: #f8f9fa;
                        --dark-color: #343a40;
                        --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    }
                    
                    body {
                        font-family: var(--font-family);
                        line-height: 1.6;
                        color: var(--dark-color);
                    }
                    
                    .btn-primary {
                        background-color: var(--primary-color);
                        border-color: var(--primary-color);
                    }
                    
                    .btn-primary:hover {
                        background-color: #0056b3;
                        border-color: #0056b3;
                    }
                    
                    .text-primary {
                        color: var(--primary-color) !important;
                    }
                    
                    .bg-primary {
                        background-color: var(--primary-color) !important;
                    }
                    
                    .border-primary {
                        border-color: var(--primary-color) !important;
                    }
                    
                    /* إصلاح مشاكل التباين */
                    .text-muted {
                        color: #6c757d !important;
                    }
                    
                    .bg-light {
                        background-color: var(--light-color) !important;
                    }
                    
                    /* إصلاح الخطوط العربية */
                    .arabic-text {
                        font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                        font-weight: 400;
                    }
                    
                    h1, h2, h3, h4, h5, h6 {
                        font-weight: 600;
                        margin-bottom: 1rem;
                    }
                </style>
            `;
            
            document.head.insertAdjacentHTML('beforeend', stylingCSS);
            
            console.log('✅ Styling issues fixed');
        } catch (error) {
            console.error('❌ Error fixing styling issues:', error);
        }
    },
    
    // إضافة تحسينات إضافية
    addEnhancements: function() {
        try {
            console.log('✨ Adding UI enhancements...');
            
            // إضافة مؤشر التحميل العام
            this.addGlobalLoadingIndicator();
            
            // إضافة تحسينات الجداول
            this.enhanceTables();
            
            // إضافة تحسينات النماذج
            this.enhanceForms();
            
            // إضافة تحسينات الإشعارات
            this.enhanceNotifications();
            
            console.log('✅ UI enhancements added');
        } catch (error) {
            console.error('❌ Error adding UI enhancements:', error);
        }
    },
    
    // إضافة زر القائمة للجوال
    addMobileMenuToggle: function() {
        try {
            const header = document.querySelector('.header, .navbar');
            if (header && !document.getElementById('mobile-menu-toggle')) {
                const toggleButton = document.createElement('button');
                toggleButton.id = 'mobile-menu-toggle';
                toggleButton.className = 'btn btn-outline-primary d-md-none';
                toggleButton.innerHTML = '<i class="fas fa-bars"></i>';
                toggleButton.style.cssText = `
                    position: fixed;
                    top: 1rem;
                    right: 1rem;
                    z-index: 1050;
                    border-radius: 50%;
                    width: 50px;
                    height: 50px;
                `;
                
                toggleButton.addEventListener('click', () => {
                    const sidebar = document.querySelector('.sidebar');
                    if (sidebar) {
                        sidebar.classList.toggle('active');
                    }
                });
                
                document.body.appendChild(toggleButton);
            }
        } catch (error) {
            console.error('❌ Error adding mobile menu toggle:', error);
        }
    },
    
    // إضافة مؤشر التحميل العام
    addGlobalLoadingIndicator: function() {
        try {
            if (!document.getElementById('global-loading')) {
                const loadingIndicator = document.createElement('div');
                loadingIndicator.id = 'global-loading';
                loadingIndicator.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(255, 255, 255, 0.9);
                    display: none;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                `;
                
                loadingIndicator.innerHTML = `
                    <div style="text-align: center;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">جاري التحميل...</span>
                        </div>
                        <div style="margin-top: 1rem; font-weight: 600;">جاري التحميل...</div>
                    </div>
                `;
                
                document.body.appendChild(loadingIndicator);
            }
        } catch (error) {
            console.error('❌ Error adding global loading indicator:', error);
        }
    },
    
    // تحسين الجداول
    enhanceTables: function() {
        try {
            const tables = document.querySelectorAll('table');
            tables.forEach(table => {
                // إضافة فئات Bootstrap
                table.classList.add('table', 'table-striped', 'table-hover');
                
                // إضافة استجابة
                if (!table.closest('.table-responsive')) {
                    const wrapper = document.createElement('div');
                    wrapper.className = 'table-responsive';
                    table.parentNode.insertBefore(wrapper, table);
                    wrapper.appendChild(table);
                }
            });
        } catch (error) {
            console.error('❌ Error enhancing tables:', error);
        }
    },
    
    // تحسين النماذج
    enhanceForms: function() {
        try {
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                const inputs = form.querySelectorAll('input, select, textarea');
                inputs.forEach(input => {
                    input.classList.add('form-control');
                    
                    // إضافة تحقق بصري
                    input.addEventListener('input', () => {
                        if (input.checkValidity()) {
                            input.classList.remove('is-invalid');
                            input.classList.add('is-valid');
                        } else {
                            input.classList.remove('is-valid');
                            input.classList.add('is-invalid');
                        }
                    });
                });
            });
        } catch (error) {
            console.error('❌ Error enhancing forms:', error);
        }
    },
    
    // تحسين الإشعارات
    enhanceNotifications: function() {
        try {
            // إضافة حاوي الإشعارات إذا لم يكن موجوداً
            if (!document.getElementById('notifications-container')) {
                const container = document.createElement('div');
                container.id = 'notifications-container';
                container.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 1060;
                    max-width: 350px;
                `;
                document.body.appendChild(container);
            }
        } catch (error) {
            console.error('❌ Error enhancing notifications:', error);
        }
    },
    
    // إصلاح الصور المكسورة
    fixBrokenImage: function(img) {
        try {
            img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyOEMyNCA0IDI4IDIwIDI4IDIwQzI4IDIwIDI0IDM2IDIwIDI4WiIgZmlsbD0iIzk5OTk5OSIvPgo8L3N2Zz4K';
            img.alt = 'صورة غير متاحة';
            img.title = 'صورة غير متاحة';
        } catch (error) {
            console.error('❌ Error fixing broken image:', error);
        }
    }
};

// تهيئة تلقائية عند تحميل الملف
if (typeof window !== 'undefined') {
    window.UIFixes = UIFixes;
    
    // تهيئة عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            UIFixes.init();
        }, 2500); // انتظار حتى تحميل جميع الأنظمة الأخرى
    });
}

console.log('📦 UI Fixes loaded');
