/**
 * Black Horse POS - Database Optimizer
 * محسن قاعدة البيانات
 * Developer: Augment Agent
 */

class DatabaseOptimizer {
    constructor() {
        this.indexedDBSupported = false;
        this.compressionEnabled = true;
        this.batchSize = 100;
        this.queryCache = new Map();
        this.transactionQueue = [];
        this.isProcessingQueue = false;
        
        console.log('🗄️ Database Optimizer initialized');
        this.init();
    }

    // تهيئة محسن قاعدة البيانات
    async init() {
        try {
            // التحقق من دعم IndexedDB
            this.checkIndexedDBSupport();
            
            // إعداد فهارس قاعدة البيانات
            await this.setupDatabaseIndexes();
            
            // إعداد ضغط البيانات
            this.setupDataCompression();
            
            // إعداد معالجة المعاملات المجمعة
            this.setupBatchProcessing();
            
            // إعداد تنظيف دوري
            this.setupPeriodicCleanup();
            
            console.log('✅ Database Optimizer ready');
            
        } catch (error) {
            console.error('❌ Error initializing Database Optimizer:', error);
        }
    }

    // التحقق من دعم IndexedDB
    checkIndexedDBSupport() {
        try {
            this.indexedDBSupported = 'indexedDB' in window;
            
            if (this.indexedDBSupported) {
                console.log('✅ IndexedDB supported');
            } else {
                console.warn('⚠️ IndexedDB not supported, falling back to localStorage');
            }
            
        } catch (error) {
            console.error('❌ Error checking IndexedDB support:', error);
            this.indexedDBSupported = false;
        }
    }

    // إعداد فهارس قاعدة البيانات
    async setupDatabaseIndexes() {
        try {
            if (!this.indexedDBSupported) return;
            
            console.log('📊 Setting up database indexes...');
            
            const dbName = 'BlackHorsePOS';
            const dbVersion = 2;
            
            return new Promise((resolve, reject) => {
                const request = indexedDB.open(dbName, dbVersion);
                
                request.onerror = () => {
                    console.error('❌ Error opening database for indexing');
                    reject(request.error);
                };
                
                request.onsuccess = () => {
                    console.log('✅ Database indexes ready');
                    resolve(request.result);
                };
                
                request.onupgradeneeded = (event) => {
                    const db = event.target.result;
                    
                    // إنشاء object stores مع فهارس محسنة
                    this.createOptimizedStores(db);
                };
            });
            
        } catch (error) {
            console.error('❌ Error setting up database indexes:', error);
        }
    }

    // إنشاء stores محسنة
    createOptimizedStores(db) {
        try {
            // Products store مع فهارس
            if (!db.objectStoreNames.contains('products')) {
                const productsStore = db.createObjectStore('products', { keyPath: 'id' });
                productsStore.createIndex('name', 'name', { unique: false });
                productsStore.createIndex('barcode', 'barcode', { unique: true });
                productsStore.createIndex('category', 'category', { unique: false });
                productsStore.createIndex('price', 'price', { unique: false });
                productsStore.createIndex('stock', 'stock', { unique: false });
                console.log('📦 Products store created with indexes');
            }
            
            // Customers store مع فهارس
            if (!db.objectStoreNames.contains('customers')) {
                const customersStore = db.createObjectStore('customers', { keyPath: 'id' });
                customersStore.createIndex('name', 'name', { unique: false });
                customersStore.createIndex('phone', 'phone', { unique: true });
                customersStore.createIndex('email', 'email', { unique: true });
                customersStore.createIndex('balance', 'balance', { unique: false });
                console.log('👥 Customers store created with indexes');
            }
            
            // Sales store مع فهارس
            if (!db.objectStoreNames.contains('sales')) {
                const salesStore = db.createObjectStore('sales', { keyPath: 'id' });
                salesStore.createIndex('date', 'date', { unique: false });
                salesStore.createIndex('customerId', 'customerId', { unique: false });
                salesStore.createIndex('total', 'total', { unique: false });
                salesStore.createIndex('status', 'status', { unique: false });
                console.log('💰 Sales store created with indexes');
            }
            
            // Settings store
            if (!db.objectStoreNames.contains('settings')) {
                db.createObjectStore('settings', { keyPath: 'key' });
                console.log('⚙️ Settings store created');
            }
            
        } catch (error) {
            console.error('❌ Error creating optimized stores:', error);
        }
    }

    // إعداد ضغط البيانات
    setupDataCompression() {
        try {
            if (!this.compressionEnabled) return;
            
            console.log('🗜️ Setting up data compression...');
            
            // استخدام LZ-string للضغط إذا كان متاحاً
            if (typeof LZString !== 'undefined') {
                this.compressData = (data) => {
                    try {
                        return LZString.compress(JSON.stringify(data));
                    } catch (error) {
                        console.warn('⚠️ Compression failed, using uncompressed data');
                        return JSON.stringify(data);
                    }
                };
                
                this.decompressData = (compressedData) => {
                    try {
                        const decompressed = LZString.decompress(compressedData);
                        return decompressed ? JSON.parse(decompressed) : null;
                    } catch (error) {
                        console.warn('⚠️ Decompression failed, trying as uncompressed');
                        try {
                            return JSON.parse(compressedData);
                        } catch (parseError) {
                            console.error('❌ Failed to parse data:', parseError);
                            return null;
                        }
                    }
                };
                
                console.log('✅ Data compression enabled');
            } else {
                // fallback بدون ضغط
                this.compressData = (data) => JSON.stringify(data);
                this.decompressData = (data) => JSON.parse(data);
                console.log('ℹ️ Compression library not available, using uncompressed storage');
            }
            
        } catch (error) {
            console.error('❌ Error setting up data compression:', error);
            // fallback بدون ضغط
            this.compressData = (data) => JSON.stringify(data);
            this.decompressData = (data) => JSON.parse(data);
        }
    }

    // حفظ محسن للبيانات
    async optimizedSave(storeName, data) {
        try {
            console.log(`💾 Optimized save to ${storeName}...`);
            
            if (this.indexedDBSupported) {
                return await this.saveToIndexedDB(storeName, data);
            } else {
                return await this.saveToLocalStorage(storeName, data);
            }
            
        } catch (error) {
            console.error(`❌ Error in optimized save to ${storeName}:`, error);
            // fallback إلى localStorage
            return await this.saveToLocalStorage(storeName, data);
        }
    }

    // حفظ إلى IndexedDB
    async saveToIndexedDB(storeName, data) {
        try {
            const dbName = 'BlackHorsePOS';
            
            return new Promise((resolve, reject) => {
                const request = indexedDB.open(dbName);
                
                request.onerror = () => reject(request.error);
                
                request.onsuccess = () => {
                    const db = request.result;
                    const transaction = db.transaction([storeName], 'readwrite');
                    const store = transaction.objectStore(storeName);
                    
                    // حفظ البيانات بشكل مجمع
                    if (Array.isArray(data)) {
                        // حفظ مجمع للمصفوفات
                        this.batchSaveToStore(store, data).then(resolve).catch(reject);
                    } else {
                        // حفظ عنصر واحد
                        const saveRequest = store.put(data);
                        saveRequest.onsuccess = () => resolve(data);
                        saveRequest.onerror = () => reject(saveRequest.error);
                    }
                };
            });
            
        } catch (error) {
            console.error('❌ Error saving to IndexedDB:', error);
            throw error;
        }
    }

    // حفظ مجمع إلى store
    async batchSaveToStore(store, dataArray) {
        try {
            const promises = [];
            
            for (let i = 0; i < dataArray.length; i += this.batchSize) {
                const batch = dataArray.slice(i, i + this.batchSize);
                
                const batchPromise = new Promise((resolve, reject) => {
                    let completed = 0;
                    
                    batch.forEach(item => {
                        const request = store.put(item);
                        request.onsuccess = () => {
                            completed++;
                            if (completed === batch.length) {
                                resolve();
                            }
                        };
                        request.onerror = () => reject(request.error);
                    });
                });
                
                promises.push(batchPromise);
            }
            
            await Promise.all(promises);
            console.log(`✅ Batch save completed: ${dataArray.length} items`);
            
        } catch (error) {
            console.error('❌ Error in batch save:', error);
            throw error;
        }
    }

    // حفظ إلى localStorage مع ضغط
    async saveToLocalStorage(storeName, data) {
        try {
            const compressedData = this.compressData(data);
            localStorage.setItem(storeName, compressedData);
            
            console.log(`✅ Saved to localStorage: ${storeName}`);
            return data;
            
        } catch (error) {
            console.error('❌ Error saving to localStorage:', error);
            throw error;
        }
    }

    // تحميل محسن للبيانات
    async optimizedLoad(storeName, defaultValue = null) {
        try {
            console.log(`📂 Optimized load from ${storeName}...`);
            
            // التحقق من التخزين المؤقت أولاً
            if (window.PerformanceOptimizer) {
                const cachedData = window.PerformanceOptimizer.getCachedData(storeName);
                if (cachedData) {
                    console.log(`📂 Data loaded from cache: ${storeName}`);
                    return cachedData;
                }
            }
            
            let data;
            
            if (this.indexedDBSupported) {
                data = await this.loadFromIndexedDB(storeName);
            } else {
                data = await this.loadFromLocalStorage(storeName);
            }
            
            // حفظ في التخزين المؤقت
            if (data && window.PerformanceOptimizer) {
                window.PerformanceOptimizer.cacheData(storeName, data);
            }
            
            return data || defaultValue;
            
        } catch (error) {
            console.error(`❌ Error in optimized load from ${storeName}:`, error);
            // fallback إلى localStorage
            try {
                return await this.loadFromLocalStorage(storeName) || defaultValue;
            } catch (fallbackError) {
                console.error('❌ Fallback load also failed:', fallbackError);
                return defaultValue;
            }
        }
    }

    // تحميل من IndexedDB
    async loadFromIndexedDB(storeName) {
        try {
            const dbName = 'BlackHorsePOS';
            
            return new Promise((resolve, reject) => {
                const request = indexedDB.open(dbName);
                
                request.onerror = () => reject(request.error);
                
                request.onsuccess = () => {
                    const db = request.result;
                    
                    if (!db.objectStoreNames.contains(storeName)) {
                        resolve(null);
                        return;
                    }
                    
                    const transaction = db.transaction([storeName], 'readonly');
                    const store = transaction.objectStore(storeName);
                    const getAllRequest = store.getAll();
                    
                    getAllRequest.onsuccess = () => {
                        resolve(getAllRequest.result);
                    };
                    
                    getAllRequest.onerror = () => {
                        reject(getAllRequest.error);
                    };
                };
            });
            
        } catch (error) {
            console.error('❌ Error loading from IndexedDB:', error);
            throw error;
        }
    }

    // تحميل من localStorage مع إلغاء ضغط
    async loadFromLocalStorage(storeName) {
        try {
            const compressedData = localStorage.getItem(storeName);
            if (!compressedData) return null;
            
            const data = this.decompressData(compressedData);
            console.log(`✅ Loaded from localStorage: ${storeName}`);
            return data;
            
        } catch (error) {
            console.error('❌ Error loading from localStorage:', error);
            throw error;
        }
    }

    // إعداد معالجة المعاملات المجمعة
    setupBatchProcessing() {
        try {
            console.log('⚡ Setting up batch processing...');
            
            // معالجة دورية للمعاملات المجمعة
            setInterval(() => {
                this.processBatchQueue();
            }, 1000); // كل ثانية
            
            console.log('✅ Batch processing ready');
            
        } catch (error) {
            console.error('❌ Error setting up batch processing:', error);
        }
    }

    // إضافة معاملة للطابور
    addToBatchQueue(operation) {
        try {
            this.transactionQueue.push({
                ...operation,
                timestamp: Date.now()
            });
            
            console.log(`📝 Added to batch queue: ${operation.type}`);
            
        } catch (error) {
            console.error('❌ Error adding to batch queue:', error);
        }
    }

    // معالجة طابور المعاملات
    async processBatchQueue() {
        try {
            if (this.isProcessingQueue || this.transactionQueue.length === 0) {
                return;
            }
            
            this.isProcessingQueue = true;
            console.log(`⚡ Processing batch queue: ${this.transactionQueue.length} operations`);
            
            const operations = [...this.transactionQueue];
            this.transactionQueue = [];
            
            // تجميع العمليات حسب النوع
            const groupedOperations = this.groupOperationsByType(operations);
            
            // معالجة كل مجموعة
            for (const [type, ops] of Object.entries(groupedOperations)) {
                await this.processBatchOperations(type, ops);
            }
            
            console.log('✅ Batch queue processed');
            
        } catch (error) {
            console.error('❌ Error processing batch queue:', error);
        } finally {
            this.isProcessingQueue = false;
        }
    }

    // تجميع العمليات حسب النوع
    groupOperationsByType(operations) {
        const grouped = {};
        
        operations.forEach(op => {
            if (!grouped[op.type]) {
                grouped[op.type] = [];
            }
            grouped[op.type].push(op);
        });
        
        return grouped;
    }

    // معالجة عمليات مجمعة
    async processBatchOperations(type, operations) {
        try {
            switch (type) {
                case 'save':
                    await this.processBatchSaves(operations);
                    break;
                case 'delete':
                    await this.processBatchDeletes(operations);
                    break;
                case 'update':
                    await this.processBatchUpdates(operations);
                    break;
                default:
                    console.warn(`⚠️ Unknown batch operation type: ${type}`);
            }
            
        } catch (error) {
            console.error(`❌ Error processing batch operations (${type}):`, error);
        }
    }

    // معالجة عمليات الحفظ المجمعة
    async processBatchSaves(operations) {
        try {
            const savesByStore = {};
            
            operations.forEach(op => {
                if (!savesByStore[op.storeName]) {
                    savesByStore[op.storeName] = [];
                }
                savesByStore[op.storeName].push(op.data);
            });
            
            for (const [storeName, dataArray] of Object.entries(savesByStore)) {
                await this.optimizedSave(storeName, dataArray);
            }
            
            console.log(`✅ Batch saves completed: ${operations.length} operations`);
            
        } catch (error) {
            console.error('❌ Error in batch saves:', error);
        }
    }

    // معالجة عمليات الحذف المجمعة
    async processBatchDeletes(operations) {
        // سيتم تطوير هذه الوظيفة لاحقاً
        console.log(`ℹ️ Batch deletes: ${operations.length} operations (to be implemented)`);
    }

    // معالجة عمليات التحديث المجمعة
    async processBatchUpdates(operations) {
        // سيتم تطوير هذه الوظيفة لاحقاً
        console.log(`ℹ️ Batch updates: ${operations.length} operations (to be implemented)`);
    }

    // إعداد تنظيف دوري
    setupPeriodicCleanup() {
        try {
            console.log('🧹 Setting up periodic cleanup...');
            
            // تنظيف يومي
            setInterval(() => {
                this.performDatabaseCleanup();
            }, 24 * 60 * 60 * 1000); // كل 24 ساعة
            
            console.log('✅ Periodic cleanup scheduled');
            
        } catch (error) {
            console.error('❌ Error setting up periodic cleanup:', error);
        }
    }

    // تنظيف قاعدة البيانات
    async performDatabaseCleanup() {
        try {
            console.log('🧹 Performing database cleanup...');
            
            // تنظيف البيانات القديمة
            await this.cleanupOldData();
            
            // ضغط قاعدة البيانات
            await this.compactDatabase();
            
            // تنظيف التخزين المؤقت
            if (window.PerformanceOptimizer) {
                window.PerformanceOptimizer.cleanupCache();
            }
            
            console.log('✅ Database cleanup completed');
            
        } catch (error) {
            console.error('❌ Error in database cleanup:', error);
        }
    }

    // تنظيف البيانات القديمة
    async cleanupOldData() {
        try {
            // حذف المبيعات القديمة (أكثر من سنة)
            const oneYearAgo = new Date();
            oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
            
            // سيتم تطوير منطق التنظيف لاحقاً
            console.log('ℹ️ Old data cleanup (to be implemented)');
            
        } catch (error) {
            console.error('❌ Error cleaning up old data:', error);
        }
    }

    // ضغط قاعدة البيانات
    async compactDatabase() {
        try {
            if (!this.indexedDBSupported) return;
            
            // سيتم تطوير منطق الضغط لاحقاً
            console.log('ℹ️ Database compaction (to be implemented)');
            
        } catch (error) {
            console.error('❌ Error compacting database:', error);
        }
    }

    // الحصول على إحصائيات قاعدة البيانات
    async getDatabaseStats() {
        try {
            const stats = {
                indexedDBSupported: this.indexedDBSupported,
                compressionEnabled: this.compressionEnabled,
                queueSize: this.transactionQueue.length,
                isProcessingQueue: this.isProcessingQueue
            };
            
            // حساب أحجام البيانات
            if (this.indexedDBSupported) {
                // سيتم تطوير حساب أحجام IndexedDB لاحقاً
                stats.indexedDBSize = 'To be calculated';
            }
            
            // حساب حجم localStorage
            let localStorageSize = 0;
            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    localStorageSize += localStorage[key].length;
                }
            }
            stats.localStorageSize = localStorageSize;
            
            return stats;
            
        } catch (error) {
            console.error('❌ Error getting database stats:', error);
            return null;
        }
    }
}

// إنشاء instance عام
window.DatabaseOptimizer = new DatabaseOptimizer();

// تصدير للاستخدام في الوحدات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DatabaseOptimizer;
}

console.log('✅ Database Optimizer loaded successfully');
