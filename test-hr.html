<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار وحدة الموارد البشرية - Black Horse ERP</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .avatar-circle {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }
        
        .module-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .nav-tabs .nav-link {
            border: none;
            border-radius: 10px 10px 0 0;
            margin-left: 5px;
            color: #6c757d;
        }
        
        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        
        .test-controls {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-controls h6 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .test-log {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            height: 200px;
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }
        
        .test-log.show {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Test Controls -->
        <div class="test-controls">
            <h6><i class="fas fa-flask me-2"></i>أدوات الاختبار</h6>
            <div class="btn-group-vertical w-100">
                <button class="btn btn-sm btn-primary mb-1" onclick="testHRModule()">
                    <i class="fas fa-play me-1"></i>اختبار الوحدة
                </button>
                <button class="btn btn-sm btn-success mb-1" onclick="testEmployeeOperations()">
                    <i class="fas fa-users me-1"></i>اختبار الموظفين
                </button>
                <button class="btn btn-sm btn-info mb-1" onclick="testAttendanceOperations()">
                    <i class="fas fa-clock me-1"></i>اختبار الحضور
                </button>
                <button class="btn btn-sm btn-warning mb-1" onclick="testPayrollOperations()">
                    <i class="fas fa-money-bill-wave me-1"></i>اختبار الرواتب
                </button>
                <button class="btn btn-sm btn-secondary mb-1" onclick="toggleTestLog()">
                    <i class="fas fa-terminal me-1"></i>سجل الاختبار
                </button>
                <button class="btn btn-sm btn-danger" onclick="clearTestData()">
                    <i class="fas fa-trash me-1"></i>مسح البيانات
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div id="moduleContainer" style="margin-left: 200px; margin-bottom: 220px;">
            <!-- HR Module will be loaded here -->
        </div>

        <!-- Test Log -->
        <div id="testLog" class="test-log">
            <div id="logContent"></div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Mock Database and Utils -->
    <script>
        // Mock Database
        window.Database = {
            employees: {
                data: new Map(),
                async add(item) { this.data.set(item.id, item); return item; },
                async getAll() { return Array.from(this.data.values()); },
                async getById(id) { return this.data.get(id); },
                async update(id, item) { this.data.set(id, item); return item; },
                async delete(id) { return this.data.delete(id); }
            },
            departments: {
                data: new Map(),
                async add(item) { this.data.set(item.id, item); return item; },
                async getAll() { return Array.from(this.data.values()); },
                async getById(id) { return this.data.get(id); },
                async update(id, item) { this.data.set(id, item); return item; },
                async delete(id) { return this.data.delete(id); }
            },
            positions: {
                data: new Map(),
                async add(item) { this.data.set(item.id, item); return item; },
                async getAll() { return Array.from(this.data.values()); },
                async getById(id) { return this.data.get(id); },
                async update(id, item) { this.data.set(id, item); return item; },
                async delete(id) { return this.data.delete(id); }
            },
            attendance: {
                data: new Map(),
                async add(item) { this.data.set(item.id, item); return item; },
                async getAll() { return Array.from(this.data.values()); },
                async getById(id) { return this.data.get(id); },
                async update(id, item) { this.data.set(id, item); return item; },
                async delete(id) { return this.data.delete(id); }
            },
            payroll: {
                data: new Map(),
                async add(item) { this.data.set(item.id, item); return item; },
                async getAll() { return Array.from(this.data.values()); },
                async getById(id) { return this.data.get(id); },
                async update(id, item) { this.data.set(id, item); return item; },
                async delete(id) { return this.data.delete(id); }
            },
            evaluations: {
                data: new Map(),
                async add(item) { this.data.set(item.id, item); return item; },
                async getAll() { return Array.from(this.data.values()); },
                async getById(id) { return this.data.get(id); },
                async update(id, item) { this.data.set(id, item); return item; },
                async delete(id) { return this.data.delete(id); }
            }
        };

        // Mock Utils
        window.Utils = {
            generateId() {
                return 'id_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
            },
            
            formatCurrency(amount) {
                return new Intl.NumberFormat('ar-EG', {
                    style: 'currency',
                    currency: 'EGP'
                }).format(amount || 0);
            },
            
            showAlert(message, type = 'info') {
                const alertClass = {
                    'success': 'alert-success',
                    'error': 'alert-danger',
                    'warning': 'alert-warning',
                    'info': 'alert-info'
                }[type] || 'alert-info';
                
                const alertHtml = `
                    <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
                         style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `;
                
                document.body.insertAdjacentHTML('beforeend', alertHtml);
                
                setTimeout(() => {
                    const alert = document.querySelector('.alert');
                    if (alert) alert.remove();
                }, 5000);
                
                this.log(`Alert: ${message} (${type})`);
            },
            
            debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            },
            
            log(message) {
                const logContent = document.getElementById('logContent');
                if (logContent) {
                    const timestamp = new Date().toLocaleTimeString('ar-EG');
                    logContent.innerHTML += `<div>[${timestamp}] ${message}</div>`;
                    logContent.scrollTop = logContent.scrollHeight;
                }
                console.log(message);
            }
        };
    </script>

    <!-- HR Module -->
    <script src="src/modules/hr/hr.js"></script>

    <!-- Test Functions -->
    <script>
        let testLogVisible = false;

        async function testHRModule() {
            Utils.log('بدء اختبار وحدة الموارد البشرية...');
            
            try {
                // Initialize HR module
                await HR.initialize();
                Utils.log('✓ تم تهيئة الوحدة بنجاح');
                
                // Show HR module
                await HR.show();
                Utils.log('✓ تم عرض الوحدة بنجاح');
                
                Utils.log('✓ اختبار الوحدة مكتمل بنجاح');
                Utils.showAlert('تم اختبار وحدة الموارد البشرية بنجاح', 'success');
                
            } catch (error) {
                Utils.log('✗ خطأ في اختبار الوحدة: ' + error.message);
                Utils.showAlert('فشل في اختبار الوحدة: ' + error.message, 'error');
            }
        }

        async function testEmployeeOperations() {
            Utils.log('بدء اختبار عمليات الموظفين...');
            
            try {
                // Test employee data loading
                await HR.loadEmployeesData();
                Utils.log('✓ تم تحميل بيانات الموظفين');
                
                // Test filtering
                HR.filters.search = 'أحمد';
                await HR.applyFilters();
                Utils.log('✓ تم اختبار البحث والتصفية');
                
                // Clear filters
                await HR.clearFilters();
                Utils.log('✓ تم مسح المرشحات');
                
                Utils.log('✓ اختبار عمليات الموظفين مكتمل');
                Utils.showAlert('تم اختبار عمليات الموظفين بنجاح', 'success');
                
            } catch (error) {
                Utils.log('✗ خطأ في اختبار عمليات الموظفين: ' + error.message);
                Utils.showAlert('فشل في اختبار عمليات الموظفين: ' + error.message, 'error');
            }
        }

        async function testAttendanceOperations() {
            Utils.log('بدء اختبار عمليات الحضور...');
            
            try {
                // Switch to attendance view
                await HR.switchView('attendance');
                Utils.log('✓ تم التبديل إلى عرض الحضور');
                
                // Test attendance data loading
                await HR.loadAttendanceData();
                Utils.log('✓ تم تحميل بيانات الحضور');
                
                Utils.log('✓ اختبار عمليات الحضور مكتمل');
                Utils.showAlert('تم اختبار عمليات الحضور بنجاح', 'success');
                
            } catch (error) {
                Utils.log('✗ خطأ في اختبار عمليات الحضور: ' + error.message);
                Utils.showAlert('فشل في اختبار عمليات الحضور: ' + error.message, 'error');
            }
        }

        async function testPayrollOperations() {
            Utils.log('بدء اختبار عمليات الرواتب...');
            
            try {
                // Switch to payroll view
                await HR.switchView('payroll');
                Utils.log('✓ تم التبديل إلى عرض الرواتب');
                
                // Test payroll data loading
                await HR.loadPayrollData();
                Utils.log('✓ تم تحميل بيانات الرواتب');
                
                Utils.log('✓ اختبار عمليات الرواتب مكتمل');
                Utils.showAlert('تم اختبار عمليات الرواتب بنجاح', 'success');
                
            } catch (error) {
                Utils.log('✗ خطأ في اختبار عمليات الرواتب: ' + error.message);
                Utils.showAlert('فشل في اختبار عمليات الرواتب: ' + error.message, 'error');
            }
        }

        function toggleTestLog() {
            const testLog = document.getElementById('testLog');
            testLogVisible = !testLogVisible;
            
            if (testLogVisible) {
                testLog.classList.add('show');
            } else {
                testLog.classList.remove('show');
            }
        }

        async function clearTestData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات التجريبية؟')) {
                Database.employees.data.clear();
                Database.departments.data.clear();
                Database.positions.data.clear();
                Database.attendance.data.clear();
                Database.payroll.data.clear();
                Database.evaluations.data.clear();
                
                Utils.log('تم مسح جميع البيانات التجريبية');
                Utils.showAlert('تم مسح البيانات بنجاح', 'success');
                
                // Reinitialize
                await testHRModule();
            }
        }

        // Auto-start test when page loads
        window.addEventListener('load', async () => {
            Utils.log('تم تحميل صفحة اختبار وحدة الموارد البشرية');
            await testHRModule();
        });
    </script>
</body>
</html>
