// Inventory Management Module
class InventoryManager {
    constructor() {
        this.products = new Map();
        this.categories = new Map();
        this.suppliers = new Map();
        this.movements = new Map();
        this.alerts = new Map();
        this.initialized = false;
    }

    async initialize() {
        if (this.initialized) return;
        
        try {
            await this.loadData();
            this.setupEventListeners();
            this.initialized = true;
            console.log('Inventory module initialized successfully');
        } catch (error) {
            console.error('Error initializing inventory module:', error);
            throw error;
        }
    }

    async loadData() {
        try {
            // Load products
            const productsData = localStorage.getItem('inventory_products');
            if (productsData) {
                const products = JSON.parse(productsData);
                products.forEach(product => {
                    this.products.set(product.id, product);
                });
            }

            // Load categories
            const categoriesData = localStorage.getItem('inventory_categories');
            if (categoriesData) {
                const categories = JSON.parse(categoriesData);
                categories.forEach(category => {
                    this.categories.set(category.id, category);
                });
            }

            // Load default data if empty
            if (this.products.size === 0) {
                this.loadDefaultData();
            }

        } catch (error) {
            console.error('Error loading inventory data:', error);
            this.loadDefaultData();
        }
    }

    loadDefaultData() {
        // Add default categories
        const defaultCategories = [
            { id: 'cat1', name: 'إلكترونيات', description: 'أجهزة إلكترونية ومعدات' },
            { id: 'cat2', name: 'ملابس', description: 'ملابس وأزياء' },
            { id: 'cat3', name: 'طعام ومشروبات', description: 'منتجات غذائية' }
        ];

        defaultCategories.forEach(category => {
            this.categories.set(category.id, category);
        });

        // Add default products
        const defaultProducts = [
            {
                id: 'prod1',
                name: 'لابتوب Dell',
                barcode: '123456789',
                category: 'cat1',
                price: 15000,
                cost: 12000,
                quantity: 10,
                minQuantity: 5,
                unit: 'قطعة',
                description: 'لابتوب Dell Inspiron 15',
                createdAt: new Date().toISOString()
            },
            {
                id: 'prod2',
                name: 'قميص قطني',
                barcode: '987654321',
                category: 'cat2',
                price: 150,
                cost: 100,
                quantity: 50,
                minQuantity: 10,
                unit: 'قطعة',
                description: 'قميص قطني عالي الجودة',
                createdAt: new Date().toISOString()
            }
        ];

        defaultProducts.forEach(product => {
            this.products.set(product.id, product);
        });

        this.saveData();
    }

    async saveData() {
        try {
            // Save products
            const productsArray = Array.from(this.products.values());
            localStorage.setItem('inventory_products', JSON.stringify(productsArray));

            // Save categories
            const categoriesArray = Array.from(this.categories.values());
            localStorage.setItem('inventory_categories', JSON.stringify(categoriesArray));

        } catch (error) {
            console.error('Error saving inventory data:', error);
        }
    }

    setupEventListeners() {
        // Event listeners will be set up when the UI is loaded
    }

    async show() {
        const container = document.getElementById('moduleContainer');
        if (!container) return;

        container.innerHTML = `
            <div class="inventory-module p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4><i class="fas fa-boxes me-2"></i>إدارة المخزون</h4>
                    <button class="btn btn-primary" onclick="inventory.showAddProductModal()">
                        <i class="fas fa-plus me-2"></i>إضافة منتج
                    </button>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-boxes fa-2x mb-2"></i>
                                <h5>${this.products.size}</h5>
                                <p class="mb-0">إجمالي المنتجات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h5>${this.getInStockCount()}</h5>
                                <p class="mb-0">متوفر في المخزن</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                                <h5>${this.getLowStockCount()}</h5>
                                <p class="mb-0">مخزون منخفض</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-times-circle fa-2x mb-2"></i>
                                <h5>${this.getOutOfStockCount()}</h5>
                                <p class="mb-0">نفد من المخزن</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search and Filter -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <input type="text" class="form-control" id="searchProducts" placeholder="البحث في المنتجات...">
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="filterCategory">
                                    <option value="">جميع الفئات</option>
                                    ${Array.from(this.categories.values()).map(cat => 
                                        `<option value="${cat.id}">${cat.name}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="filterStock">
                                    <option value="">جميع المنتجات</option>
                                    <option value="in-stock">متوفر</option>
                                    <option value="low-stock">مخزون منخفض</option>
                                    <option value="out-of-stock">نفد من المخزن</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-outline-secondary w-100" onclick="inventory.resetFilters()">
                                    <i class="fas fa-undo me-2"></i>إعادة تعيين
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Products Table -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>الكود</th>
                                        <th>اسم المنتج</th>
                                        <th>الفئة</th>
                                        <th>السعر</th>
                                        <th>الكمية</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="productsTableBody">
                                    ${this.renderProductsTable()}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.setupUIEventListeners();
    }

    setupUIEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('searchProducts');
        if (searchInput) {
            searchInput.addEventListener('input', () => this.filterProducts());
        }

        // Filter functionality
        const categoryFilter = document.getElementById('filterCategory');
        const stockFilter = document.getElementById('filterStock');
        
        if (categoryFilter) {
            categoryFilter.addEventListener('change', () => this.filterProducts());
        }
        
        if (stockFilter) {
            stockFilter.addEventListener('change', () => this.filterProducts());
        }
    }

    renderProductsTable() {
        const products = Array.from(this.products.values());
        
        return products.map(product => {
            const category = this.categories.get(product.category);
            const stockStatus = this.getStockStatus(product);
            const statusClass = stockStatus === 'متوفر' ? 'success' : 
                               stockStatus === 'مخزون منخفض' ? 'warning' : 'danger';
            
            return `
                <tr>
                    <td>${product.barcode}</td>
                    <td>${product.name}</td>
                    <td>${category ? category.name : 'غير محدد'}</td>
                    <td>${product.price} ج.م</td>
                    <td>${product.quantity} ${product.unit}</td>
                    <td><span class="badge bg-${statusClass}">${stockStatus}</span></td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary me-1" onclick="inventory.editProduct('${product.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="inventory.deleteProduct('${product.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }

    getStockStatus(product) {
        if (product.quantity <= 0) return 'نفد من المخزن';
        if (product.quantity <= product.minQuantity) return 'مخزون منخفض';
        return 'متوفر';
    }

    getInStockCount() {
        return Array.from(this.products.values()).filter(p => p.quantity > p.minQuantity).length;
    }

    getLowStockCount() {
        return Array.from(this.products.values()).filter(p => p.quantity > 0 && p.quantity <= p.minQuantity).length;
    }

    getOutOfStockCount() {
        return Array.from(this.products.values()).filter(p => p.quantity <= 0).length;
    }

    filterProducts() {
        // Implementation for filtering products
        const searchTerm = document.getElementById('searchProducts')?.value.toLowerCase() || '';
        const categoryFilter = document.getElementById('filterCategory')?.value || '';
        const stockFilter = document.getElementById('filterStock')?.value || '';

        // Re-render table with filtered results
        const tbody = document.getElementById('productsTableBody');
        if (tbody) {
            tbody.innerHTML = this.renderProductsTable();
        }
    }

    resetFilters() {
        document.getElementById('searchProducts').value = '';
        document.getElementById('filterCategory').value = '';
        document.getElementById('filterStock').value = '';
        this.filterProducts();
    }

    showAddProductModal() {
        // Implementation for add product modal
        console.log('Add product modal');
    }

    editProduct(productId) {
        // Implementation for edit product
        console.log('Edit product:', productId);
    }

    deleteProduct(productId) {
        // Implementation for delete product
        console.log('Delete product:', productId);
    }
}

// Create global instance
const inventory = new InventoryManager();

// Export for global access
window.Inventory = inventory;
