// Debug System - نظام التشخيص
console.log('🔍 Loading Debug System...');

// Debug Functions
const DebugSystem = {
    
    // Check if all required functions exist
    checkFunctions: function() {
        console.log('\n🔍 === FUNCTION AVAILABILITY CHECK ===');
        
        const requiredFunctions = [
            'addProduct',
            'generateBarcode', 
            'showModal',
            'closeModal',
            'showNotification'
        ];
        
        requiredFunctions.forEach(funcName => {
            const exists = typeof window[funcName] === 'function';
            const status = exists ? '✅' : '❌';
            console.log(`${status} ${funcName}: ${exists ? 'Available' : 'Missing'}`);
            
            if (!exists) {
                // Try to find in global scope
                if (typeof eval(funcName) === 'function') {
                    console.log(`   📝 Found ${funcName} in global scope, adding to window`);
                    window[funcName] = eval(funcName);
                }
            }
        });
    },
    
    // Check if all required elements exist
    checkElements: function() {
        console.log('\n🔍 === ELEMENT AVAILABILITY CHECK ===');
        
        const requiredElements = [
            'addProductForm',
            'productName',
            'productPrice',
            'productStock',
            'productBarcode',
            'addProductModal'
        ];
        
        requiredElements.forEach(elementId => {
            const element = document.getElementById(elementId);
            const exists = element !== null;
            const status = exists ? '✅' : '❌';
            console.log(`${status} ${elementId}: ${exists ? 'Found' : 'Missing'}`);
            
            if (exists) {
                console.log(`   📝 Type: ${element.tagName}, Value: "${element.value || 'empty'}"`);
            }
        });
    },
    
    // Check button onclick handlers
    checkButtons: function() {
        console.log('\n🔍 === BUTTON HANDLERS CHECK ===');
        
        // Find add product button
        const addProductBtn = document.querySelector('[onclick*="addProduct"]');
        if (addProductBtn) {
            console.log('✅ Add Product Button found');
            console.log('   📝 onclick:', addProductBtn.getAttribute('onclick'));
            console.log('   📝 Text:', addProductBtn.textContent.trim());
        } else {
            console.log('❌ Add Product Button not found');
        }
        
        // Find generate barcode button
        const generateBarcodeBtn = document.querySelector('[onclick*="generateBarcode"]');
        if (generateBarcodeBtn) {
            console.log('✅ Generate Barcode Button found');
            console.log('   📝 onclick:', generateBarcodeBtn.getAttribute('onclick'));
            console.log('   📝 Text:', generateBarcodeBtn.textContent.trim());
        } else {
            console.log('❌ Generate Barcode Button not found');
        }
    },
    
    // Test addProduct function manually
    testAddProduct: function() {
        console.log('\n🧪 === TESTING ADD PRODUCT ===');
        
        try {
            // Fill form with test data
            const nameField = document.getElementById('productName');
            const priceField = document.getElementById('productPrice');
            const stockField = document.getElementById('productStock');
            const barcodeField = document.getElementById('productBarcode');
            
            if (nameField) nameField.value = 'منتج تجريبي';
            if (priceField) priceField.value = '100';
            if (stockField) stockField.value = '10';
            if (barcodeField) barcodeField.value = '1234567890123';
            
            console.log('📝 Test data filled');
            
            // Try to call addProduct
            if (typeof window.addProduct === 'function') {
                console.log('🔄 Calling addProduct...');
                window.addProduct();
            } else {
                console.log('❌ addProduct function not available');
            }
            
        } catch (error) {
            console.error('❌ Error testing addProduct:', error);
        }
    },
    
    // Test generateBarcode function
    testGenerateBarcode: function() {
        console.log('\n🧪 === TESTING GENERATE BARCODE ===');
        
        try {
            if (typeof window.generateBarcode === 'function') {
                console.log('🔄 Calling generateBarcode...');
                window.generateBarcode();
                
                const barcodeField = document.getElementById('productBarcode');
                if (barcodeField) {
                    console.log('📊 Generated barcode:', barcodeField.value);
                }
            } else {
                console.log('❌ generateBarcode function not available');
            }
        } catch (error) {
            console.error('❌ Error testing generateBarcode:', error);
        }
    },
    
    // Check console for errors
    checkConsoleErrors: function() {
        console.log('\n🔍 === CONSOLE ERROR CHECK ===');
        console.log('📝 Check browser console for any red error messages');
        console.log('📝 Common issues:');
        console.log('   - Script loading errors');
        console.log('   - Function not defined errors');
        console.log('   - Element not found errors');
    },
    
    // Run all diagnostics
    runFullDiagnostic: function() {
        console.log('🚀 === STARTING FULL DIAGNOSTIC ===');
        
        this.checkFunctions();
        this.checkElements();
        this.checkButtons();
        this.checkConsoleErrors();
        
        console.log('\n🏁 === DIAGNOSTIC COMPLETE ===');
        console.log('📝 To test manually:');
        console.log('   DebugSystem.testAddProduct()');
        console.log('   DebugSystem.testGenerateBarcode()');
    },
    
    // Quick fix attempt
    quickFix: function() {
        console.log('\n🔧 === ATTEMPTING QUICK FIX ===');
        
        try {
            // Ensure functions are available globally
            if (typeof addProduct === 'function' && typeof window.addProduct !== 'function') {
                window.addProduct = addProduct;
                console.log('✅ Added addProduct to window');
            }
            
            if (typeof generateBarcode === 'function' && typeof window.generateBarcode !== 'function') {
                window.generateBarcode = generateBarcode;
                console.log('✅ Added generateBarcode to window');
            }
            
            if (typeof showModal === 'function' && typeof window.showModal !== 'function') {
                window.showModal = showModal;
                console.log('✅ Added showModal to window');
            }
            
            if (typeof closeModal === 'function' && typeof window.closeModal !== 'function') {
                window.closeModal = closeModal;
                console.log('✅ Added closeModal to window');
            }
            
            if (typeof showNotification === 'function' && typeof window.showNotification !== 'function') {
                window.showNotification = showNotification;
                console.log('✅ Added showNotification to window');
            }
            
            // Re-attach button handlers
            const addProductBtn = document.querySelector('[onclick*="addProduct"]');
            if (addProductBtn) {
                addProductBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (typeof window.addProduct === 'function') {
                        window.addProduct();
                    } else {
                        console.error('❌ addProduct still not available');
                    }
                });
                console.log('✅ Re-attached addProduct button handler');
            }
            
            const generateBarcodeBtn = document.querySelector('[onclick*="generateBarcode"]');
            if (generateBarcodeBtn) {
                generateBarcodeBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (typeof window.generateBarcode === 'function') {
                        window.generateBarcode();
                    } else {
                        console.error('❌ generateBarcode still not available');
                    }
                });
                console.log('✅ Re-attached generateBarcode button handler');
            }
            
            console.log('🏁 Quick fix completed');
            
        } catch (error) {
            console.error('❌ Error in quick fix:', error);
        }
    }
};

// Auto-run diagnostic when page loads
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        DebugSystem.runFullDiagnostic();
        DebugSystem.quickFix();
    }, 2000);
});

// Make debug system globally available
window.DebugSystem = DebugSystem;

console.log('✅ Debug System loaded successfully');
console.log('📝 Use DebugSystem.runFullDiagnostic() to check everything');
console.log('📝 Use DebugSystem.quickFix() to attempt automatic fixes');
