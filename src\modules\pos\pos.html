<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نقاط البيع - Black Horse ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .pos-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            height: calc(100vh - 40px);
        }
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            max-height: 400px;
            overflow-y: auto;
        }
        .product-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .product-card:hover {
            transform: translateY(-5px);
            border-color: #667eea;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .cart-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
        }
        .btn-pos {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 10px;
        }
        .btn-pos:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            color: white;
        }
        .total-display {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .payment-buttons {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }
        .payment-btn {
            padding: 15px;
            border-radius: 10px;
            border: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .payment-btn.cash {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        .payment-btn.card {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
        }
        .payment-btn.vodafone {
            background: linear-gradient(135deg, #e60012 0%, #ff1744 100%);
            color: white;
        }
        .payment-btn.instapay {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            color: white;
        }
        .search-box {
            position: relative;
        }
        .search-box input {
            padding-right: 40px;
        }
        .search-box i {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container-fluid p-3">
        <div class="pos-container p-4">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-cash-register me-2"></i>نقاط البيع</h2>
                    <p class="text-muted mb-0">نظام كاشير متكامل</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" onclick="openDrawer()">
                        <i class="fas fa-box-open me-2"></i>فتح الدرج
                    </button>
                    <button class="btn btn-outline-success" onclick="showReports()">
                        <i class="fas fa-chart-bar me-2"></i>التقارير
                    </button>
                </div>
            </div>

            <div class="row h-100">
                <!-- Products Section -->
                <div class="col-md-7">
                    <!-- Search -->
                    <div class="search-box mb-3">
                        <input type="text" class="form-control" placeholder="البحث عن منتج أو مسح الباركود..." id="productSearch">
                        <i class="fas fa-search"></i>
                    </div>

                    <!-- Categories -->
                    <div class="mb-3">
                        <div class="btn-group" role="group">
                            <button class="btn btn-outline-primary active" onclick="filterProducts('all')">الكل</button>
                            <button class="btn btn-outline-primary" onclick="filterProducts('food')">أطعمة</button>
                            <button class="btn btn-outline-primary" onclick="filterProducts('drinks')">مشروبات</button>
                            <button class="btn btn-outline-primary" onclick="filterProducts('electronics')">إلكترونيات</button>
                        </div>
                    </div>

                    <!-- Products Grid -->
                    <div class="product-grid" id="productsGrid">
                        <!-- Products will be loaded here -->
                    </div>
                </div>

                <!-- Cart Section -->
                <div class="col-md-5">
                    <div class="h-100 d-flex flex-column">
                        <!-- Cart Header -->
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5>سلة المشتريات</h5>
                            <button class="btn btn-outline-danger btn-sm" onclick="clearCart()">
                                <i class="fas fa-trash me-1"></i>مسح الكل
                            </button>
                        </div>

                        <!-- Cart Items -->
                        <div class="flex-grow-1 overflow-auto mb-3" id="cartItems" style="max-height: 300px;">
                            <!-- Cart items will be displayed here -->
                        </div>

                        <!-- Total -->
                        <div class="total-display mb-3">
                            <div>الإجمالي: <span id="totalAmount">0.00</span> جنيه</div>
                        </div>

                        <!-- Customer Info -->
                        <div class="mb-3">
                            <select class="form-select" id="customerSelect">
                                <option value="">عميل عادي</option>
                                <!-- Customers will be loaded here -->
                            </select>
                        </div>

                        <!-- Payment Methods -->
                        <div class="payment-buttons mb-3">
                            <button class="payment-btn cash" onclick="processPayment('cash')">
                                <i class="fas fa-money-bill-wave me-2"></i>نقدي
                            </button>
                            <button class="payment-btn card" onclick="processPayment('card')">
                                <i class="fas fa-credit-card me-2"></i>بطاقة
                            </button>
                            <button class="payment-btn vodafone" onclick="processPayment('vodafone')">
                                <i class="fas fa-mobile-alt me-2"></i>فودافون كاش
                            </button>
                            <button class="payment-btn instapay" onclick="processPayment('instapay')">
                                <i class="fas fa-qrcode me-2"></i>إنستاباي
                            </button>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-grid gap-2">
                            <button class="btn-pos" onclick="completeSale()" id="completeSaleBtn" disabled>
                                <i class="fas fa-check me-2"></i>إتمام البيع
                            </button>
                            <button class="btn btn-outline-secondary" onclick="holdSale()">
                                <i class="fas fa-pause me-2"></i>تعليق البيع
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Modal -->
    <div class="modal fade" id="paymentModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الدفع</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <h4>المبلغ المطلوب: <span id="paymentAmount">0.00</span> جنيه</h4>
                        <p>طريقة الدفع: <span id="paymentMethodText"></span></p>
                    </div>
                    <div id="cashPaymentSection" style="display: none;">
                        <div class="mb-3">
                            <label class="form-label">المبلغ المدفوع</label>
                            <input type="number" class="form-control" id="paidAmount" step="0.01">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الباقي</label>
                            <input type="number" class="form-control" id="changeAmount" readonly>
                        </div>
                    </div>
                    <div id="cardPaymentSection" style="display: none;">
                        <div class="text-center">
                            <i class="fas fa-credit-card fa-3x text-primary mb-3"></i>
                            <p>يرجى إدخال البطاقة في الجهاز</p>
                        </div>
                    </div>
                    <div id="mobilePaymentSection" style="display: none;">
                        <div class="mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="mobileNumber">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="confirmPayment()">تأكيد الدفع</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Receipt Modal -->
    <div class="modal fade" id="receiptModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إيصال البيع</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="receiptContent">
                    <!-- Receipt content will be generated here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="printReceipt()">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Hold Sales Modal -->
    <div class="modal fade" id="holdSalesModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">المبيعات المعلقة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الوقت</th>
                                    <th>العميل</th>
                                    <th>عدد الأصناف</th>
                                    <th>المبلغ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="holdSalesTableBody">
                                <!-- Hold sales will be displayed here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="pos.js"></script>
</body>
</html>
