<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نقاط البيع - POS</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', 'Tahoma', sans-serif;
            background: #f5f5f5;
            color: #333;
            direction: rtl;
            padding: 20px;
        }
        
        .pos-container {
            display: grid;
            grid-template-columns: 1fr 400px;
            height: 80vh;
            gap: 20px;
        }
        
        .products-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .cart-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }
        
        .section-title {
            font-size: 1.5em;
            margin-bottom: 20px;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            max-height: 60vh;
            overflow-y: auto;
        }
        
        .product-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .cart-items {
            flex: 1;
            max-height: 40vh;
            overflow-y: auto;
            margin-bottom: 20px;
        }
        
        .cart-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #e0e0e0;
            margin-bottom: 10px;
        }
        
        .cart-total {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .total-amount {
            font-size: 1.5em;
            font-weight: bold;
        }
        
        .payment-methods {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .payment-btn {
            background: linear-gradient(135deg, #FF9800, #F57C00);
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }
        
        .payment-btn.selected {
            background: linear-gradient(135deg, #4CAF50, #45a049);
        }
        
        .checkout-btn {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .checkout-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="pos-container">
        <!-- قسم المنتجات -->
        <div class="products-section">
            <h2 class="section-title">المنتجات</h2>
            <div class="products-grid" id="productsGrid"></div>
        </div>
        
        <!-- قسم السلة والدفع -->
        <div class="cart-section">
            <h2 class="section-title">السلة</h2>
            <div class="cart-items" id="cartItems">
                <div style="text-align: center; color: #666; padding: 50px 20px;">
                    <p>السلة فارغة</p>
                </div>
            </div>
            
            <div class="cart-total">
                <div>الإجمالي</div>
                <div class="total-amount" id="totalAmount">0.00 ج.م</div>
            </div>
            
            <div class="payment-methods">
                <button class="payment-btn selected" data-method="cash">نقدي</button>
                <button class="payment-btn" data-method="visa">فيزا</button>
                <button class="payment-btn" data-method="vodafone_cash">فودافون كاش</button>
                <button class="payment-btn" data-method="instapay">انستاباي</button>
            </div>
            
            <button class="checkout-btn" id="checkoutBtn" disabled>إتمام البيع</button>
        </div>
    </div>

    <script>
        const sampleProducts = [
            { id: 1, name: 'شاي أحمر', price: 25.00 },
            { id: 2, name: 'قهوة تركية', price: 45.00 },
            { id: 3, name: 'سكر أبيض', price: 18.50 },
            { id: 4, name: 'أرز أبيض', price: 32.00 },
            { id: 5, name: 'زيت طبخ', price: 55.00 },
            { id: 6, name: 'معجون طماطم', price: 12.00 }
        ];

        let cart = [];
        let selectedPaymentMethod = 'cash';
        let total = 0;

        function loadProducts() {
            const productsGrid = document.getElementById('productsGrid');
            productsGrid.innerHTML = '';
            
            sampleProducts.forEach(product => {
                const productCard = document.createElement('div');
                productCard.className = 'product-card';
                productCard.innerHTML = `
                    <div style="font-weight: bold; margin-bottom: 8px;">${product.name}</div>
                    <div style="font-size: 1.2em; color: #FFD700;">${product.price.toFixed(2)} ج.م</div>
                `;
                productCard.onclick = () => addToCart(product);
                productsGrid.appendChild(productCard);
            });
        }

        function addToCart(product) {
            const existingItem = cart.find(item => item.id === product.id);
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({ ...product, quantity: 1 });
            }
            updateCart();
        }

        function updateCart() {
            const cartItems = document.getElementById('cartItems');
            const totalAmount = document.getElementById('totalAmount');
            const checkoutBtn = document.getElementById('checkoutBtn');
            
            if (cart.length === 0) {
                cartItems.innerHTML = '<div style="text-align: center; color: #666; padding: 50px 20px;"><p>السلة فارغة</p></div>';
                total = 0;
                checkoutBtn.disabled = true;
            } else {
                cartItems.innerHTML = '';
                total = 0;
                
                cart.forEach(item => {
                    const itemTotal = item.price * item.quantity;
                    total += itemTotal;
                    
                    const cartItem = document.createElement('div');
                    cartItem.className = 'cart-item';
                    cartItem.innerHTML = `
                        <div>
                            <div style="font-weight: bold;">${item.name}</div>
                            <div style="color: #666;">${item.price.toFixed(2)} ج.م × ${item.quantity} = ${itemTotal.toFixed(2)} ج.م</div>
                        </div>
                        <button onclick="removeFromCart(${item.id})" style="background: #f44336; color: white; border: none; padding: 5px 10px; border-radius: 5px; cursor: pointer;">حذف</button>
                    `;
                    cartItems.appendChild(cartItem);
                });
                
                checkoutBtn.disabled = false;
            }
            
            totalAmount.textContent = `${total.toFixed(2)} ج.م`;
        }

        function removeFromCart(productId) {
            cart = cart.filter(item => item.id !== productId);
            updateCart();
        }

        function selectPaymentMethod(method) {
            selectedPaymentMethod = method;
            document.querySelectorAll('.payment-btn').forEach(btn => btn.classList.remove('selected'));
            document.querySelector(`[data-method="${method}"]`).classList.add('selected');
        }

        function checkout() {
            if (cart.length === 0) return;
            
            // إضافة المعاملة لنظام الصندوق
            if (window.parent && window.parent.cashboxManager) {
                window.parent.cashboxManager.addSaleTransaction(total, selectedPaymentMethod, `بيع POS - ${cart.length} منتج`);
                
                if (window.parent.updateCashboxDisplay) {
                    window.parent.updateCashboxDisplay();
                }
                
                if (window.parent.showNotification) {
                    window.parent.showNotification(`تم إتمام البيع بنجاح - ${total.toFixed(2)} ج.م`, 'success');
                }
            }
            
            alert(`تم إتمام البيع بنجاح!\nالمبلغ: ${total.toFixed(2)} ج.م`);
            cart = [];
            updateCart();
        }

        document.addEventListener('DOMContentLoaded', function() {
            loadProducts();
            
            document.querySelectorAll('.payment-btn').forEach(btn => {
                btn.addEventListener('click', () => selectPaymentMethod(btn.dataset.method));
            });
            
            document.getElementById('checkoutBtn').addEventListener('click', checkout);
        });
    </script>
</body>
</html>
