/**
 * Main Application Controller
 * Black Horse ERP System
 */

class BlackHorseApp {
    constructor() {
        this.modules = new Map();
        this.currentModule = null;
        this.isInitialized = false;
        this.database = null;
        this.utils = null;
        this.user = null;
        this.settings = {};
    }

    /**
     * Initialize the application
     */
    async initialize() {
        try {
            console.log('🚀 Initializing Black Horse ERP...');
            
            // Initialize core components
            await this.initializeCore();
            
            // Load user settings
            await this.loadSettings();
            
            // Register modules
            this.registerModules();
            
            // Setup global event listeners
            this.setupGlobalEvents();
            
            // Initialize default module
            await this.loadModule('dashboard');
            
            this.isInitialized = true;
            console.log('✅ Black Horse ERP initialized successfully');
            
            // Show success notification
            this.showNotification('تم تحميل النظام بنجاح', 'success');
            
        } catch (error) {
            console.error('❌ Failed to initialize application:', error);
            this.showNotification('فشل في تحميل النظام: ' + error.message, 'error');
            throw error;
        }
    }

    /**
     * Initialize core components
     */
    async initializeCore() {
        // Initialize database
        if (typeof DatabaseManager !== 'undefined') {
            this.database = new DatabaseManager();
            await this.database.initialize();
            console.log('✅ Database initialized');
        } else {
            console.warn('⚠️ DatabaseManager not found, using localStorage fallback');
            this.database = {
                save: (key, data) => localStorage.setItem(key, JSON.stringify(data)),
                load: (key) => {
                    const data = localStorage.getItem(key);
                    return data ? JSON.parse(data) : null;
                },
                delete: (key) => localStorage.removeItem(key),
                clear: () => localStorage.clear()
            };
        }

        // Initialize utilities
        if (typeof Utils !== 'undefined') {
            this.utils = Utils;
            console.log('✅ Utils initialized');
        } else {
            console.warn('⚠️ Utils not found, creating basic utilities');
            this.utils = {
                generateId: () => 'id_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now(),
                formatCurrency: (amount) => new Intl.NumberFormat('ar-EG', {
                    style: 'currency',
                    currency: 'EGP'
                }).format(amount || 0),
                formatDate: (date) => new Date(date).toLocaleDateString('ar-EG'),
                formatNumber: (number) => new Intl.NumberFormat('ar-EG').format(number || 0)
            };
        }

        // Make core components globally available
        window.App = this;
        window.Database = this.database;
        window.Utils = this.utils;
    }

    /**
     * Load application settings
     */
    async loadSettings() {
        try {
            const savedSettings = this.database.load('app_settings');
            this.settings = {
                theme: 'light',
                language: 'ar',
                currency: 'EGP',
                dateFormat: 'dd/mm/yyyy',
                timezone: 'Africa/Cairo',
                companyName: 'Black Horse',
                companyLogo: '',
                ...savedSettings
            };
            console.log('✅ Settings loaded');
        } catch (error) {
            console.warn('⚠️ Failed to load settings, using defaults');
            this.settings = {
                theme: 'light',
                language: 'ar',
                currency: 'EGP',
                dateFormat: 'dd/mm/yyyy',
                timezone: 'Africa/Cairo',
                companyName: 'Black Horse',
                companyLogo: ''
            };
        }
    }

    /**
     * Register all available modules
     */
    registerModules() {
        const moduleConfigs = [
            { id: 'dashboard', name: 'لوحة التحكم', icon: 'fas fa-tachometer-alt', class: null },
            { id: 'inventory', name: 'المخزون', icon: 'fas fa-boxes', class: 'InventoryModule' },
            { id: 'sales', name: 'المبيعات', icon: 'fas fa-shopping-cart', class: 'SalesModule' },
            { id: 'pos', name: 'نقاط البيع', icon: 'fas fa-cash-register', class: 'POSModule' },
            { id: 'purchases', name: 'المشتريات', icon: 'fas fa-truck', class: 'PurchasesModule' },
            { id: 'cashier', name: 'الصندوق', icon: 'fas fa-money-bill-wave', class: 'CashierModule' },
            { id: 'hr', name: 'الموارد البشرية', icon: 'fas fa-users', class: 'HRModule' },
            { id: 'reports', name: 'التقارير', icon: 'fas fa-chart-bar', class: 'ReportsModule' },
            { id: 'settings', name: 'الإعدادات', icon: 'fas fa-cog', class: 'SettingsModule' },
            { id: 'crm', name: 'إدارة العملاء', icon: 'fas fa-handshake', class: 'CRMModule' },
            { id: 'project-management', name: 'إدارة المشاريع', icon: 'fas fa-project-diagram', class: 'ProjectManagementModule' },
            { id: 'advanced-finance', name: 'المالية المتقدمة', icon: 'fas fa-chart-line', class: 'AdvancedFinanceManager' }
        ];

        moduleConfigs.forEach(config => {
            this.modules.set(config.id, config);
        });

        console.log(`✅ Registered ${this.modules.size} modules`);
    }

    /**
     * Load and display a module
     */
    async loadModule(moduleId, params = {}) {
        try {
            console.log(`🔄 Loading module: ${moduleId}`);
            
            const moduleConfig = this.modules.get(moduleId);
            if (!moduleConfig) {
                throw new Error(`Module ${moduleId} not found`);
            }

            // Show loading indicator
            this.showLoading(true);

            // Handle dashboard separately
            if (moduleId === 'dashboard') {
                await this.loadDashboard();
                this.currentModule = null;
                this.showLoading(false);
                return;
            }

            // Load module script if needed
            await this.loadModuleScript(moduleId);

            // Initialize module instance
            let moduleInstance = null;
            if (moduleConfig.class && window[moduleConfig.class]) {
                moduleInstance = new window[moduleConfig.class]();
                await moduleInstance.initialize();
                await moduleInstance.show(params);
            } else {
                throw new Error(`Module class ${moduleConfig.class} not found`);
            }

            this.currentModule = {
                id: moduleId,
                instance: moduleInstance,
                config: moduleConfig
            };

            // Update navigation
            this.updateNavigation(moduleId);

            console.log(`✅ Module ${moduleId} loaded successfully`);
            this.showLoading(false);

        } catch (error) {
            console.error(`❌ Failed to load module ${moduleId}:`, error);
            this.showNotification(`فشل في تحميل وحدة ${moduleId}: ${error.message}`, 'error');
            this.showLoading(false);
            throw error;
        }
    }

    /**
     * Load module script dynamically
     */
    async loadModuleScript(moduleId) {
        const scriptPath = `src/modules/${moduleId}/${moduleId}.js`;
        
        // Check if script is already loaded
        const existingScript = document.querySelector(`script[src="${scriptPath}"]`);
        if (existingScript) {
            return;
        }

        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = scriptPath;
            script.onload = () => {
                console.log(`✅ Script loaded: ${scriptPath}`);
                resolve();
            };
            script.onerror = (error) => {
                console.error(`❌ Failed to load script: ${scriptPath}`, error);
                reject(new Error(`Failed to load script: ${scriptPath}`));
            };
            document.head.appendChild(script);
        });
    }

    /**
     * Load dashboard
     */
    async loadDashboard() {
        const container = document.getElementById('moduleContainer');
        if (!container) {
            throw new Error('Module container not found');
        }

        container.innerHTML = `
            <div class="dashboard">
                <div class="row mb-4">
                    <div class="col-12">
                        <h2><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</h2>
                        <p class="text-muted">مرحباً بك في نظام Black Horse ERP</p>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>المبيعات اليوم</h4>
                                        <h2 id="todaySales">0</h2>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-shopping-cart fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>المنتجات</h4>
                                        <h2 id="totalProducts">0</h2>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-boxes fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>العملاء</h4>
                                        <h2 id="totalCustomers">0</h2>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>الموردين</h4>
                                        <h2 id="totalSuppliers">0</h2>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-truck fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-line me-2"></i>مبيعات الأسبوع</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="salesChart" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-exclamation-triangle me-2"></i>تنبيهات المخزون</h5>
                            </div>
                            <div class="card-body">
                                <div id="stockAlerts">
                                    <p class="text-muted">لا توجد تنبيهات</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Load dashboard data
        await this.loadDashboardData();
    }

    /**
     * Load dashboard data
     */
    async loadDashboardData() {
        try {
            // Load statistics
            const sales = this.database.load('sales') || [];
            const products = this.database.load('products') || [];
            const customers = this.database.load('customers') || [];
            const suppliers = this.database.load('suppliers') || [];

            // Update counters
            document.getElementById('todaySales').textContent = this.utils.formatCurrency(
                sales.filter(s => new Date(s.date).toDateString() === new Date().toDateString())
                     .reduce((sum, s) => sum + s.total, 0)
            );
            document.getElementById('totalProducts').textContent = products.length;
            document.getElementById('totalCustomers').textContent = customers.length;
            document.getElementById('totalSuppliers').textContent = suppliers.length;

        } catch (error) {
            console.error('Failed to load dashboard data:', error);
        }
    }

    /**
     * Update navigation active state
     */
    updateNavigation(activeModuleId) {
        // Remove active class from all nav items
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });

        // Add active class to current module
        const activeLink = document.querySelector(`[data-module="${activeModuleId}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
    }

    /**
     * Setup global event listeners
     */
    setupGlobalEvents() {
        // Handle navigation clicks
        document.addEventListener('click', (e) => {
            const moduleLink = e.target.closest('[data-module]');
            if (moduleLink) {
                e.preventDefault();
                const moduleId = moduleLink.getAttribute('data-module');
                this.loadModule(moduleId);
            }
        });

        // Handle keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey) {
                switch (e.key) {
                    case 's':
                        e.preventDefault();
                        this.saveCurrentModule();
                        break;
                    case 'n':
                        e.preventDefault();
                        this.createNew();
                        break;
                }
            }
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }

    /**
     * Show loading indicator
     */
    showLoading(show = true) {
        const loader = document.getElementById('loadingIndicator');
        if (loader) {
            loader.style.display = show ? 'block' : 'none';
        }
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info', duration = 5000) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto remove after duration
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    }

    /**
     * Save current module data
     */
    async saveCurrentModule() {
        if (this.currentModule && this.currentModule.instance && this.currentModule.instance.save) {
            try {
                await this.currentModule.instance.save();
                this.showNotification('تم الحفظ بنجاح', 'success');
            } catch (error) {
                this.showNotification('فشل في الحفظ: ' + error.message, 'error');
            }
        }
    }

    /**
     * Create new item in current module
     */
    async createNew() {
        if (this.currentModule && this.currentModule.instance && this.currentModule.instance.createNew) {
            try {
                await this.currentModule.instance.createNew();
            } catch (error) {
                this.showNotification('فشل في الإنشاء: ' + error.message, 'error');
            }
        }
    }

    /**
     * Handle window resize
     */
    handleResize() {
        // Update layout based on screen size
        const sidebar = document.getElementById('sidebar');
        if (sidebar && window.innerWidth < 768) {
            sidebar.classList.add('collapsed');
        }
    }

    /**
     * Get application info
     */
    getInfo() {
        return {
            name: 'Black Horse ERP',
            version: '1.0.0',
            modules: Array.from(this.modules.keys()),
            currentModule: this.currentModule?.id || null,
            isInitialized: this.isInitialized
        };
    }
}

// Initialize app when DOM is ready
document.addEventListener('DOMContentLoaded', async () => {
    try {
        window.BlackHorseApp = new BlackHorseApp();
        await window.BlackHorseApp.initialize();
    } catch (error) {
        console.error('Failed to start application:', error);
        
        // Show error message to user
        const container = document.getElementById('moduleContainer') || document.body;
        container.innerHTML = `
            <div class="alert alert-danger m-4">
                <h4><i class="fas fa-exclamation-triangle me-2"></i>خطأ في تشغيل النظام</h4>
                <p>فشل في تحميل النظام: ${error.message}</p>
                <button class="btn btn-primary" onclick="location.reload()">
                    <i class="fas fa-redo me-2"></i>إعادة المحاولة
                </button>
            </div>
        `;
    }
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BlackHorseApp;
}
