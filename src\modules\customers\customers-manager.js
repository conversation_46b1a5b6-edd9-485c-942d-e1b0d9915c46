/**
 * Black Horse POS - Customers Manager
 * مدير العملاء المحسن
 * Developer: Augment Agent
 */

class CustomersManager {
    constructor() {
        this.customers = [];
        this.filteredCustomers = [];
        this.currentPage = 1;
        this.itemsPerPage = 15;
        this.searchQuery = '';
        this.sortBy = 'name';
        this.sortOrder = 'asc';
        
        console.log('👥 Customers Manager initialized');
        this.init();
    }

    // تهيئة مدير العملاء
    async init() {
        try {
            // تحميل العملاء من التخزين المؤقت أولاً
            if (window.PerformanceOptimizer) {
                const cachedCustomers = window.PerformanceOptimizer.getCachedData('customers');
                if (cachedCustomers) {
                    this.customers = cachedCustomers;
                    console.log('📂 Customers loaded from cache');
                    return;
                }
            }
            
            // تحميل من قاعدة البيانات
            await this.loadCustomers();
            
            console.log('✅ Customers Manager ready');
            
        } catch (error) {
            console.error('❌ Error initializing Customers Manager:', error);
        }
    }

    // تحميل العملاء
    async loadCustomers() {
        try {
            console.log('📂 Loading customers...');
            
            if (window.UnifiedStorage) {
                this.customers = await window.UnifiedStorage.load('customers', []);
            } else {
                // fallback للطريقة التقليدية
                const data = localStorage.getItem('customers');
                this.customers = data ? JSON.parse(data) : [];
            }
            
            // حفظ في التخزين المؤقت
            if (window.PerformanceOptimizer) {
                window.PerformanceOptimizer.cacheData('customers', this.customers);
            }
            
            this.filteredCustomers = [...this.customers];
            
            console.log(`✅ Loaded ${this.customers.length} customers`);
            
        } catch (error) {
            console.error('❌ Error loading customers:', error);
            this.customers = [];
            this.filteredCustomers = [];
        }
    }

    // حفظ العملاء
    async saveCustomers() {
        try {
            console.log('💾 Saving customers...');
            
            if (window.UnifiedStorage) {
                await window.UnifiedStorage.save('customers', this.customers);
            } else {
                // fallback للطريقة التقليدية
                localStorage.setItem('customers', JSON.stringify(this.customers));
            }
            
            // تحديث التخزين المؤقت
            if (window.PerformanceOptimizer) {
                window.PerformanceOptimizer.cacheData('customers', this.customers);
            }
            
            console.log('✅ Customers saved successfully');
            
        } catch (error) {
            console.error('❌ Error saving customers:', error);
            throw error;
        }
    }

    // إضافة عميل جديد
    async addCustomer(customerData) {
        try {
            console.log('➕ Adding new customer...');
            
            // التحقق من صحة البيانات
            if (window.Validators && !window.Validators.validateCustomer(customerData)) {
                throw new Error('بيانات العميل غير صحيحة');
            }
            
            // التحقق من عدم تكرار رقم الهاتف
            if (customerData.phone && this.customers.some(c => c.phone === customerData.phone)) {
                throw new Error('رقم الهاتف مسجل مسبقاً');
            }
            
            // إنشاء عميل جديد
            const newCustomer = {
                id: Date.now().toString(),
                ...customerData,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                totalPurchases: 0,
                lastPurchase: null,
                balance: parseFloat(customerData.balance) || 0
            };
            
            // إضافة للقائمة
            this.customers.push(newCustomer);
            this.filteredCustomers = [...this.customers];
            
            // حفظ التغييرات
            await this.saveCustomers();
            
            // تحديث الواجهة
            this.refreshCustomersDisplay();
            
            // إظهار رسالة نجاح
            if (window.Utils) {
                window.Utils.showMessage('تم إضافة العميل بنجاح', 'success');
            }
            
            console.log('✅ Customer added successfully:', newCustomer.id);
            return newCustomer;
            
        } catch (error) {
            console.error('❌ Error adding customer:', error);
            if (window.Utils) {
                window.Utils.showMessage('خطأ في إضافة العميل: ' + error.message, 'error');
            }
            throw error;
        }
    }

    // تعديل عميل
    async editCustomer(customerId, updatedData) {
        try {
            console.log(`✏️ Editing customer: ${customerId}`);
            
            const customerIndex = this.customers.findIndex(c => c.id === customerId);
            if (customerIndex === -1) {
                throw new Error('العميل غير موجود');
            }
            
            // التحقق من صحة البيانات
            if (window.Validators && !window.Validators.validateCustomer(updatedData)) {
                throw new Error('بيانات العميل غير صحيحة');
            }
            
            // التحقق من عدم تكرار رقم الهاتف
            if (updatedData.phone && 
                this.customers.some(c => c.phone === updatedData.phone && c.id !== customerId)) {
                throw new Error('رقم الهاتف مسجل مسبقاً');
            }
            
            // تحديث العميل
            this.customers[customerIndex] = {
                ...this.customers[customerIndex],
                ...updatedData,
                updatedAt: new Date().toISOString(),
                balance: parseFloat(updatedData.balance) || this.customers[customerIndex].balance
            };
            
            this.filteredCustomers = [...this.customers];
            
            // حفظ التغييرات
            await this.saveCustomers();
            
            // تحديث الواجهة
            this.refreshCustomersDisplay();
            
            // إظهار رسالة نجاح
            if (window.Utils) {
                window.Utils.showMessage('تم تحديث العميل بنجاح', 'success');
            }
            
            console.log('✅ Customer updated successfully:', customerId);
            return this.customers[customerIndex];
            
        } catch (error) {
            console.error('❌ Error editing customer:', error);
            if (window.Utils) {
                window.Utils.showMessage('خطأ في تعديل العميل: ' + error.message, 'error');
            }
            throw error;
        }
    }

    // حذف عميل
    async deleteCustomer(customerId) {
        try {
            console.log(`🗑️ Deleting customer: ${customerId}`);
            
            const customerIndex = this.customers.findIndex(c => c.id === customerId);
            if (customerIndex === -1) {
                throw new Error('العميل غير موجود');
            }
            
            // تأكيد الحذف
            if (window.Utils && !window.Utils.confirm('هل أنت متأكد من حذف هذا العميل؟')) {
                return null;
            }
            
            // حذف العميل
            const deletedCustomer = this.customers.splice(customerIndex, 1)[0];
            this.filteredCustomers = [...this.customers];
            
            // حفظ التغييرات
            await this.saveCustomers();
            
            // تحديث الواجهة
            this.refreshCustomersDisplay();
            
            // إظهار رسالة نجاح
            if (window.Utils) {
                window.Utils.showMessage('تم حذف العميل بنجاح', 'success');
            }
            
            console.log('✅ Customer deleted successfully:', customerId);
            return deletedCustomer;
            
        } catch (error) {
            console.error('❌ Error deleting customer:', error);
            if (window.Utils) {
                window.Utils.showMessage('خطأ في حذف العميل: ' + error.message, 'error');
            }
            throw error;
        }
    }

    // البحث في العملاء
    searchCustomers(query) {
        try {
            this.searchQuery = query.toLowerCase();
            
            if (!this.searchQuery) {
                this.filteredCustomers = [...this.customers];
            } else {
                this.filteredCustomers = this.customers.filter(customer => 
                    customer.name.toLowerCase().includes(this.searchQuery) ||
                    customer.phone?.toLowerCase().includes(this.searchQuery) ||
                    customer.email?.toLowerCase().includes(this.searchQuery) ||
                    customer.address?.toLowerCase().includes(this.searchQuery)
                );
            }
            
            this.currentPage = 1;
            this.refreshCustomersDisplay();
            
            console.log(`🔍 Search results: ${this.filteredCustomers.length} customers found`);
            
        } catch (error) {
            console.error('❌ Error searching customers:', error);
        }
    }

    // ترتيب العملاء
    sortCustomers(sortBy, sortOrder = 'asc') {
        try {
            this.sortBy = sortBy;
            this.sortOrder = sortOrder;
            
            this.filteredCustomers.sort((a, b) => {
                let valueA = a[sortBy];
                let valueB = b[sortBy];
                
                // معالجة القيم الرقمية
                if (sortBy === 'balance' || sortBy === 'totalPurchases') {
                    valueA = parseFloat(valueA) || 0;
                    valueB = parseFloat(valueB) || 0;
                } else {
                    valueA = valueA?.toString().toLowerCase() || '';
                    valueB = valueB?.toString().toLowerCase() || '';
                }
                
                if (sortOrder === 'asc') {
                    return valueA > valueB ? 1 : -1;
                } else {
                    return valueA < valueB ? 1 : -1;
                }
            });
            
            this.refreshCustomersDisplay();
            
            console.log(`📊 Customers sorted by ${sortBy} (${sortOrder})`);
            
        } catch (error) {
            console.error('❌ Error sorting customers:', error);
        }
    }

    // تحديث عرض العملاء
    refreshCustomersDisplay() {
        try {
            const contentDiv = document.getElementById('customers-content');
            if (!contentDiv) return;
            
            // حساب الصفحات
            const totalPages = Math.ceil(this.filteredCustomers.length / this.itemsPerPage);
            const startIndex = (this.currentPage - 1) * this.itemsPerPage;
            const endIndex = startIndex + this.itemsPerPage;
            const currentCustomers = this.filteredCustomers.slice(startIndex, endIndex);
            
            // إنشاء HTML للعملاء
            let html = `
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <input type="text" class="form-control" placeholder="البحث في العملاء..." 
                               onkeyup="window.CustomersManager.searchCustomers(this.value)" style="width: 300px;">
                    </div>
                    <div>
                        <select class="form-select" onchange="window.CustomersManager.sortCustomers(this.value)" style="width: 200px;">
                            <option value="name">ترتيب حسب الاسم</option>
                            <option value="balance">ترتيب حسب الرصيد</option>
                            <option value="totalPurchases">ترتيب حسب المشتريات</option>
                            <option value="createdAt">ترتيب حسب التاريخ</option>
                        </select>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>الرصيد</th>
                                <th>إجمالي المشتريات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            if (currentCustomers.length === 0) {
                html += `
                    <tr>
                        <td colspan="6" class="text-center text-muted">
                            ${this.searchQuery ? 'لا توجد عملاء تطابق البحث' : 'لا توجد عملاء'}
                        </td>
                    </tr>
                `;
            } else {
                currentCustomers.forEach(customer => {
                    html += `
                        <tr>
                            <td>${customer.name}</td>
                            <td>${customer.phone || '-'}</td>
                            <td>${customer.email || '-'}</td>
                            <td>
                                <span class="badge ${customer.balance >= 0 ? 'bg-success' : 'bg-danger'}">
                                    ${window.Utils ? window.Utils.formatCurrency(customer.balance) : customer.balance + ' ج.م'}
                                </span>
                            </td>
                            <td>${window.Utils ? window.Utils.formatCurrency(customer.totalPurchases) : customer.totalPurchases + ' ج.م'}</td>
                            <td>
                                <button class="btn btn-sm btn-primary me-1" onclick="window.CustomersManager.showEditModal('${customer.id}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="window.CustomersManager.deleteCustomer('${customer.id}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                });
            }
            
            html += `
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <nav aria-label="Customers pagination">
                    <ul class="pagination justify-content-center">
            `;
            
            // أزرار الصفحات
            for (let i = 1; i <= totalPages; i++) {
                html += `
                    <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="window.CustomersManager.goToPage(${i})">${i}</a>
                    </li>
                `;
            }
            
            html += `
                    </ul>
                </nav>
                
                <div class="text-center text-muted">
                    عرض ${startIndex + 1} - ${Math.min(endIndex, this.filteredCustomers.length)} من ${this.filteredCustomers.length} عميل
                </div>
            `;
            
            contentDiv.innerHTML = html;
            
        } catch (error) {
            console.error('❌ Error refreshing customers display:', error);
        }
    }

    // الانتقال لصفحة معينة
    goToPage(page) {
        this.currentPage = page;
        this.refreshCustomersDisplay();
    }

    // تحميل صفحة العملاء
    async loadCustomersPage() {
        try {
            console.log('👥 Loading customers page...');
            
            // تحميل العملاء إذا لم تكن محملة
            if (this.customers.length === 0) {
                await this.loadCustomers();
            }
            
            // عرض العملاء
            this.refreshCustomersDisplay();
            
            console.log('✅ Customers page loaded');
            
        } catch (error) {
            console.error('❌ Error loading customers page:', error);
            
            const contentDiv = document.getElementById('customers-content');
            if (contentDiv) {
                contentDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        خطأ في تحميل العملاء. يرجى المحاولة مرة أخرى.
                    </div>
                `;
            }
        }
    }

    // إظهار نموذج التعديل
    showEditModal(customerId) {
        // سيتم تطوير هذه الوظيفة لاحقاً
        if (window.Utils) {
            window.Utils.showMessage('سيتم تطوير نموذج التعديل قريباً', 'info');
        }
    }

    // الحصول على عميل بالمعرف
    getCustomerById(customerId) {
        return this.customers.find(c => c.id === customerId);
    }

    // تحديث رصيد العميل
    async updateCustomerBalance(customerId, amount, type = 'add') {
        try {
            const customer = this.getCustomerById(customerId);
            if (!customer) {
                throw new Error('العميل غير موجود');
            }
            
            if (type === 'add') {
                customer.balance += amount;
            } else if (type === 'subtract') {
                customer.balance -= amount;
            } else {
                customer.balance = amount;
            }
            
            customer.updatedAt = new Date().toISOString();
            
            await this.saveCustomers();
            
            console.log(`✅ Customer balance updated: ${customerId}, new balance: ${customer.balance}`);
            
        } catch (error) {
            console.error('❌ Error updating customer balance:', error);
            throw error;
        }
    }

    // تحديث إجمالي مشتريات العميل
    async updateCustomerPurchases(customerId, amount) {
        try {
            const customer = this.getCustomerById(customerId);
            if (!customer) {
                throw new Error('العميل غير موجود');
            }
            
            customer.totalPurchases += amount;
            customer.lastPurchase = new Date().toISOString();
            customer.updatedAt = new Date().toISOString();
            
            await this.saveCustomers();
            
            console.log(`✅ Customer purchases updated: ${customerId}, total: ${customer.totalPurchases}`);
            
        } catch (error) {
            console.error('❌ Error updating customer purchases:', error);
            throw error;
        }
    }

    // الحصول على إحصائيات العملاء
    getCustomersStats() {
        return {
            total: this.customers.length,
            withBalance: this.customers.filter(c => c.balance > 0).length,
            withDebt: this.customers.filter(c => c.balance < 0).length,
            totalBalance: this.customers.reduce((sum, c) => sum + c.balance, 0),
            totalPurchases: this.customers.reduce((sum, c) => sum + c.totalPurchases, 0)
        };
    }
}

// إنشاء instance عام
window.CustomersManager = new CustomersManager();

// تصدير للاستخدام في الوحدات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CustomersManager;
}

console.log('✅ Customers Manager loaded successfully');
