<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐎 Black Horse ERP - نظام إدارة الأعمال المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            margin: 0 auto;
            max-width: 1400px;
            padding: 50px;
            animation: fadeInUp 0.8s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 4rem;
            font-weight: bold;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.5rem;
            margin-bottom: 20px;
        }
        
        .status-badge {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            border-radius: 25px;
            color: white;
            display: inline-block;
            font-size: 1.1rem;
            font-weight: bold;
            padding: 10px 25px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .module-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 20px;
            color: white;
            cursor: pointer;
            padding: 35px;
            text-align: center;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }
        
        .module-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .module-card:hover::before {
            opacity: 1;
        }
        
        .module-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }
        
        .module-card i {
            font-size: 4rem;
            margin-bottom: 20px;
            display: block;
        }
        
        .module-card h4 {
            font-size: 1.8rem;
            margin-bottom: 15px;
            font-weight: bold;
        }
        
        .module-card p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 0;
        }
        
        .tools-section {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            margin-top: 40px;
        }
        
        .tools-section h3 {
            color: white;
            font-size: 2.5rem;
            margin-bottom: 30px;
            font-weight: bold;
        }
        
        .tool-btn {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 15px;
            color: white;
            font-size: 1.2rem;
            font-weight: bold;
            margin: 10px;
            padding: 15px 30px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .tool-btn:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            color: white;
            transform: translateY(-3px);
        }
        
        .footer {
            text-align: center;
            margin-top: 50px;
            padding-top: 30px;
            border-top: 2px solid #ecf0f1;
        }
        
        .footer p {
            color: #7f8c8d;
            font-size: 1.1rem;
            margin: 0;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="header">
            <h1><i class="fas fa-horse-head"></i> Black Horse ERP</h1>
            <p class="subtitle">نظام إدارة الأعمال المتكامل والشامل</p>
            <div class="status-badge">
                <i class="fas fa-check-circle"></i> النظام جاهز للعمل
            </div>
        </div>
        
        <div class="loading" id="loadingDiv">
            <div class="spinner"></div>
            <p>جاري تحميل الوحدة...</p>
        </div>
        
        <div class="modules-grid">
            <div class="module-card" onclick="loadModule('inventory')">
                <i class="fas fa-boxes"></i>
                <h4>إدارة المخزون</h4>
                <p>تتبع شامل للمنتجات والمخزون مع الباركود والتنبيهات</p>
            </div>
            
            <div class="module-card" onclick="loadModule('sales')">
                <i class="fas fa-chart-line"></i>
                <h4>إدارة المبيعات</h4>
                <p>إدارة المبيعات والعملاء والفواتير مع التقارير التفصيلية</p>
            </div>
            
            <div class="module-card" onclick="loadModule('pos')">
                <i class="fas fa-cash-register"></i>
                <h4>نقاط البيع POS</h4>
                <p>نظام POS متطور مع طرق دفع متعددة وطباعة حرارية</p>
            </div>
            
            <div class="module-card" onclick="loadModule('purchases')">
                <i class="fas fa-shopping-cart"></i>
                <h4>إدارة المشتريات</h4>
                <p>إدارة المشتريات والموردين وأوامر الشراء</p>
            </div>
            
            <div class="module-card" onclick="loadModule('cashier')">
                <i class="fas fa-user-tie"></i>
                <h4>إدارة الكاشير</h4>
                <p>مناوبات الكاشيرات وإدارة الخزنة والمعاملات</p>
            </div>
            
            <div class="module-card" onclick="loadModule('hr')">
                <i class="fas fa-users"></i>
                <h4>الموارد البشرية</h4>
                <p>إدارة الموظفين والحضور والرواتب والإجازات</p>
            </div>
            
            <div class="module-card" onclick="loadModule('reports')">
                <i class="fas fa-chart-bar"></i>
                <h4>التقارير والتحليلات</h4>
                <p>تقارير شاملة ولوحات معلومات تفاعلية</p>
            </div>
            
            <div class="module-card" onclick="loadModule('settings')">
                <i class="fas fa-cogs"></i>
                <h4>إعدادات النظام</h4>
                <p>إعدادات شاملة للنظام والمستخدمين والأمان</p>
            </div>
        </div>
        
        <div class="tools-section">
            <h3><i class="fas fa-tools"></i> أدوات النظام</h3>
            <button class="btn tool-btn" onclick="openTool('system-health-check.html')">
                <i class="fas fa-stethoscope"></i> فحص صحة النظام
            </button>
            <button class="btn tool-btn" onclick="openTool('setup-wizard.html')">
                <i class="fas fa-magic"></i> معالج الإعداد
            </button>
            <button class="btn tool-btn" onclick="openTool('system-repair.html')">
                <i class="fas fa-wrench"></i> إصلاح النظام
            </button>
            <button class="btn tool-btn" onclick="openTool('test-system.html')">
                <i class="fas fa-vial"></i> اختبار النظام
            </button>
        </div>
        
        <div class="footer">
            <p><strong>🐎 Black Horse ERP</strong> - نظام إدارة الأعمال المتكامل</p>
            <p>مطور بـ ❤️ للشركات العربية | الإصدار 2.0</p>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحميل وحدة معينة
        function loadModule(moduleName) {
            showLoading();
            
            const moduleUrl = `src/modules/${moduleName}/${moduleName}.html`;
            
            // محاولة فتح الوحدة
            setTimeout(() => {
                try {
                    window.open(moduleUrl, '_blank');
                    hideLoading();
                    showNotification(`تم فتح وحدة ${getModuleArabicName(moduleName)} بنجاح`, 'success');
                } catch (error) {
                    hideLoading();
                    showNotification(`خطأ في فتح الوحدة: ${error.message}`, 'error');
                }
            }, 500);
        }
        
        // فتح أداة معينة
        function openTool(toolFile) {
            showLoading();
            
            setTimeout(() => {
                try {
                    window.open(toolFile, '_blank');
                    hideLoading();
                    showNotification('تم فتح الأداة بنجاح', 'success');
                } catch (error) {
                    hideLoading();
                    showNotification(`خطأ في فتح الأداة: ${error.message}`, 'error');
                }
            }, 300);
        }
        
        // إظهار التحميل
        function showLoading() {
            document.getElementById('loadingDiv').style.display = 'block';
        }
        
        // إخفاء التحميل
        function hideLoading() {
            document.getElementById('loadingDiv').style.display = 'none';
        }
        
        // الحصول على الاسم العربي للوحدة
        function getModuleArabicName(moduleName) {
            const names = {
                'inventory': 'إدارة المخزون',
                'sales': 'إدارة المبيعات',
                'pos': 'نقاط البيع',
                'purchases': 'إدارة المشتريات',
                'cashier': 'إدارة الكاشير',
                'hr': 'الموارد البشرية',
                'reports': 'التقارير والتحليلات',
                'settings': 'إعدادات النظام'
            };
            return names[moduleName] || moduleName;
        }
        
        // إظهار إشعار
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.zIndex = '9999';
            notification.style.minWidth = '300px';
            
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(notification);
            
            // إزالة الإشعار بعد 3 ثوان
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }
        
        // فحص حالة النظام عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🐎 Black Horse ERP - تم تحميل النظام بنجاح');
            
            // فحص الاتصال بالملفات
            const testFiles = [
                'src/core/app.js',
                'src/core/database.js',
                'src/core/utils.js'
            ];
            
            let workingFiles = 0;
            
            testFiles.forEach(file => {
                fetch(file)
                    .then(response => {
                        if (response.ok) {
                            workingFiles++;
                            console.log(`✅ ${file} - متاح`);
                        }
                    })
                    .catch(error => {
                        console.warn(`⚠️ ${file} - غير متاح:`, error.message);
                    })
                    .finally(() => {
                        if (workingFiles === testFiles.length) {
                            showNotification('جميع ملفات النظام متاحة وتعمل بنجاح', 'success');
                        }
                    });
            });
            
            // رسالة ترحيب
            setTimeout(() => {
                showNotification('مرحباً بك في نظام Black Horse ERP', 'success');
            }, 1000);
        });
        
        // تأثيرات بصرية إضافية
        document.querySelectorAll('.module-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>
