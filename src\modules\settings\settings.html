<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - Black Horse ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .module-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .settings-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
        }
        .btn-gradient:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            color: white;
        }
        .nav-tabs .nav-link {
            border: none;
            color: #667eea;
            font-weight: 600;
        }
        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0;
        }
        .form-switch .form-check-input {
            width: 3em;
            height: 1.5em;
        }
        .form-switch .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }
        .backup-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #28a745;
        }
        .user-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="module-card p-4 mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-cog me-2"></i>إعدادات النظام</h2>
                    <p class="text-muted mb-0">إدارة وتخصيص إعدادات النظام</p>
                </div>
                <button class="btn btn-gradient" onclick="saveAllSettings()">
                    <i class="fas fa-save me-2"></i>حفظ جميع الإعدادات
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="module-card p-4">
            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs mb-4" id="settingsTabs">
                <li class="nav-item">
                    <a class="nav-link active" data-bs-toggle="tab" href="#generalTab">الإعدادات العامة</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#companyTab">بيانات الشركة</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#usersTab">المستخدمين</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#backupTab">النسخ الاحتياطي</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#securityTab">الأمان</a>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- General Settings Tab -->
                <div class="tab-pane fade show active" id="generalTab">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="settings-card">
                                <h5><i class="fas fa-globe me-2"></i>اللغة والمنطقة</h5>
                                <div class="mb-3">
                                    <label class="form-label">اللغة</label>
                                    <select class="form-select" id="systemLanguage">
                                        <option value="ar">العربية</option>
                                        <option value="en">English</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العملة</label>
                                    <select class="form-select" id="systemCurrency">
                                        <option value="EGP">جنيه مصري (EGP)</option>
                                        <option value="USD">دولار أمريكي (USD)</option>
                                        <option value="EUR">يورو (EUR)</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">المنطقة الزمنية</label>
                                    <select class="form-select" id="systemTimezone">
                                        <option value="Africa/Cairo">القاهرة</option>
                                        <option value="Asia/Riyadh">الرياض</option>
                                        <option value="Asia/Dubai">دبي</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="settings-card">
                                <h5><i class="fas fa-palette me-2"></i>المظهر</h5>
                                <div class="mb-3">
                                    <label class="form-label">السمة</label>
                                    <select class="form-select" id="systemTheme">
                                        <option value="light">فاتح</option>
                                        <option value="dark">داكن</option>
                                        <option value="auto">تلقائي</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">اللون الأساسي</label>
                                    <input type="color" class="form-control form-control-color" id="primaryColor" value="#667eea">
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="enableAnimations" checked>
                                    <label class="form-check-label" for="enableAnimations">
                                        تفعيل الحركات والانتقالات
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="settings-card">
                                <h5><i class="fas fa-bell me-2"></i>الإشعارات</h5>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="enableNotifications" checked>
                                    <label class="form-check-label" for="enableNotifications">
                                        تفعيل الإشعارات
                                    </label>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                                    <label class="form-check-label" for="emailNotifications">
                                        إشعارات البريد الإلكتروني
                                    </label>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="smsNotifications">
                                    <label class="form-check-label" for="smsNotifications">
                                        إشعارات الرسائل النصية
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="settings-card">
                                <h5><i class="fas fa-print me-2"></i>الطباعة</h5>
                                <div class="mb-3">
                                    <label class="form-label">طابعة الإيصالات</label>
                                    <select class="form-select" id="receiptPrinter">
                                        <option value="">اختر الطابعة</option>
                                        <option value="thermal">طابعة حرارية</option>
                                        <option value="inkjet">طابعة نافثة للحبر</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">حجم الورق</label>
                                    <select class="form-select" id="paperSize">
                                        <option value="80mm">80 مم</option>
                                        <option value="58mm">58 مم</option>
                                        <option value="A4">A4</option>
                                    </select>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="autoPrint">
                                    <label class="form-check-label" for="autoPrint">
                                        طباعة تلقائية للإيصالات
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Company Settings Tab -->
                <div class="tab-pane fade" id="companyTab">
                    <div class="settings-card">
                        <h5><i class="fas fa-building me-2"></i>معلومات الشركة</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم الشركة</label>
                                    <input type="text" class="form-control" id="companyName" value="Black Horse">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" id="companyAddress" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" id="companyPhone">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="companyEmail">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الرقم الضريبي</label>
                                    <input type="text" class="form-control" id="taxNumber">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">السجل التجاري</label>
                                    <input type="text" class="form-control" id="commercialRegister">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الموقع الإلكتروني</label>
                                    <input type="url" class="form-control" id="companyWebsite">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">شعار الشركة</label>
                                    <input type="file" class="form-control" id="companyLogo" accept="image/*">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Users Tab -->
                <div class="tab-pane fade" id="usersTab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>إدارة المستخدمين</h5>
                        <button class="btn btn-gradient" onclick="showAddUserModal()">
                            <i class="fas fa-plus me-2"></i>مستخدم جديد
                        </button>
                    </div>
                    <div id="usersList">
                        <!-- Users will be loaded here -->
                    </div>
                </div>

                <!-- Backup Tab -->
                <div class="tab-pane fade" id="backupTab">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="settings-card">
                                <h5><i class="fas fa-download me-2"></i>إنشاء نسخة احتياطية</h5>
                                <p class="text-muted">إنشاء نسخة احتياطية من جميع البيانات</p>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeProducts" checked>
                                        <label class="form-check-label" for="includeProducts">المنتجات</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeSales" checked>
                                        <label class="form-check-label" for="includeSales">المبيعات</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeCustomers" checked>
                                        <label class="form-check-label" for="includeCustomers">العملاء</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeSettings" checked>
                                        <label class="form-check-label" for="includeSettings">الإعدادات</label>
                                    </div>
                                </div>
                                <button class="btn btn-gradient w-100" onclick="createBackup()">
                                    <i class="fas fa-download me-2"></i>إنشاء نسخة احتياطية
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="settings-card">
                                <h5><i class="fas fa-upload me-2"></i>استعادة النسخة الاحتياطية</h5>
                                <p class="text-muted">استعادة البيانات من نسخة احتياطية</p>
                                <div class="mb-3">
                                    <input type="file" class="form-control" id="backupFile" accept=".json,.zip">
                                </div>
                                <button class="btn btn-warning w-100" onclick="restoreBackup()">
                                    <i class="fas fa-upload me-2"></i>استعادة النسخة الاحتياطية
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="settings-card">
                        <h5><i class="fas fa-history me-2"></i>النسخ الاحتياطية السابقة</h5>
                        <div id="backupHistory">
                            <!-- Backup history will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Security Tab -->
                <div class="tab-pane fade" id="securityTab">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="settings-card">
                                <h5><i class="fas fa-shield-alt me-2"></i>إعدادات الأمان</h5>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="requireLogin" checked>
                                    <label class="form-check-label" for="requireLogin">
                                        طلب تسجيل الدخول
                                    </label>
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="autoLogout">
                                    <label class="form-check-label" for="autoLogout">
                                        تسجيل خروج تلقائي
                                    </label>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">مدة الجلسة (بالدقائق)</label>
                                    <input type="number" class="form-control" id="sessionTimeout" value="30">
                                </div>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="enableAuditLog" checked>
                                    <label class="form-check-label" for="enableAuditLog">
                                        تفعيل سجل العمليات
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="settings-card">
                                <h5><i class="fas fa-key me-2"></i>تغيير كلمة المرور</h5>
                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور الحالية</label>
                                    <input type="password" class="form-control" id="currentPassword">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور الجديدة</label>
                                    <input type="password" class="form-control" id="newPassword">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">تأكيد كلمة المرور</label>
                                    <input type="password" class="form-control" id="confirmPassword">
                                </div>
                                <button class="btn btn-gradient w-100" onclick="changePassword()">
                                    <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">مستخدم جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm">
                        <div class="mb-3">
                            <label class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="userName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="userEmail" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="userPassword" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الدور</label>
                            <select class="form-select" id="userRole" required>
                                <option value="admin">مدير</option>
                                <option value="cashier">كاشير</option>
                                <option value="employee">موظف</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الصلاحيات</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="permSales" checked>
                                <label class="form-check-label" for="permSales">المبيعات</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="permInventory">
                                <label class="form-check-label" for="permInventory">المخزون</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="permReports">
                                <label class="form-check-label" for="permReports">التقارير</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="permSettings">
                                <label class="form-check-label" for="permSettings">الإعدادات</label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-gradient" onclick="saveUser()">حفظ المستخدم</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="settings.js"></script>
</body>
</html>
