/**
 * Black Horse POS - Application Initializer
 * مهيئ التطبيق الرئيسي
 * Developer: Augment Agent
 */

class AppInitializer {
    constructor() {
        this.isInitialized = false;
        this.initializationSteps = [];
        this.currentStep = 0;
        this.startTime = null;
        
        console.log('🚀 App Initializer created');
    }

    // بدء تهيئة التطبيق
    async initialize() {
        try {
            console.log('🔄 Starting Black Horse POS initialization...');
            this.startTime = Date.now();
            
            // تحديد خطوات التهيئة
            this.initializationSteps = [
                { name: 'تحميل الأدوات المساعدة', action: () => this.loadUtils() },
                { name: 'تهيئة نظام التخزين', action: () => this.initializeStorage() },
                { name: 'تشغيل ترحيل البيانات', action: () => this.runDataMigration() },
                { name: 'تحميل مدير الوحدات', action: () => this.initializeModuleManager() },
                { name: 'تحميل وحدات الأعمال', action: () => this.loadBusinessModules() },
                { name: 'تهيئة واجهة المستخدم', action: () => this.initializeUI() },
                { name: 'تحميل الإعدادات', action: () => this.loadSettings() },
                { name: 'إعداد معالجات الأحداث', action: () => this.setupEventHandlers() },
                { name: 'التحقق من النظام', action: () => this.systemCheck() }
            ];
            
            // تنفيذ خطوات التهيئة
            for (let i = 0; i < this.initializationSteps.length; i++) {
                this.currentStep = i;
                const step = this.initializationSteps[i];
                
                console.log(`📋 Step ${i + 1}/${this.initializationSteps.length}: ${step.name}`);
                this.updateProgressIndicator(i + 1, this.initializationSteps.length, step.name);
                
                try {
                    await step.action();
                    console.log(`✅ Completed: ${step.name}`);
                } catch (error) {
                    console.error(`❌ Failed: ${step.name}`, error);
                    // المتابعة مع الخطوات الأخرى حتى لو فشلت خطوة
                }
                
                // تأخير قصير لإظهار التقدم
                await this.delay(100);
            }
            
            this.isInitialized = true;
            const duration = Date.now() - this.startTime;
            
            console.log(`🎉 Black Horse POS initialized successfully in ${duration}ms`);
            this.hideProgressIndicator();
            this.showWelcomeMessage();
            
            return true;
            
        } catch (error) {
            console.error('❌ Critical error during initialization:', error);
            this.showErrorMessage('خطأ في تهيئة التطبيق: ' + error.message);
            return false;
        }
    }

    // تحميل الأدوات المساعدة
    async loadUtils() {
        // التحقق من تحميل Utils
        if (!window.Utils) {
            throw new Error('Utils not loaded');
        }
        
        // التحقق من تحميل Validators
        if (!window.Validators) {
            throw new Error('Validators not loaded');
        }
        
        console.log('✅ Utils and Validators loaded');
    }

    // تهيئة نظام التخزين
    async initializeStorage() {
        if (!window.UnifiedStorage) {
            throw new Error('UnifiedStorage not loaded');
        }
        
        await window.UnifiedStorage.init();
        console.log('✅ Storage system initialized');
    }

    // تشغيل ترحيل البيانات
    async runDataMigration() {
        if (window.DataMigration) {
            const migrationResult = await window.DataMigration.startMigration();
            if (migrationResult) {
                console.log('✅ Data migration completed successfully');
            } else {
                console.warn('⚠️ Data migration completed with warnings');
            }
        } else {
            console.warn('⚠️ DataMigration not available');
        }
    }

    // تهيئة مدير الوحدات
    async initializeModuleManager() {
        if (!window.ModuleManager) {
            throw new Error('ModuleManager not loaded');
        }
        
        await window.ModuleManager.init();
        console.log('✅ Module Manager initialized');
    }

    // تحميل وحدات الأعمال
    async loadBusinessModules() {
        const moduleManager = window.ModuleManager;
        if (!moduleManager) {
            throw new Error('ModuleManager not available');
        }
        
        // تحميل الوحدات الأساسية للأعمال
        const businessModules = ['ProductsManager', 'CustomersManager', 'SalesManager'];
        
        for (const moduleName of businessModules) {
            try {
                await moduleManager.loadModule(moduleName);
            } catch (error) {
                console.warn(`⚠️ Could not load ${moduleName}:`, error);
            }
        }
        
        console.log('✅ Business modules loaded');
    }

    // تهيئة واجهة المستخدم
    async initializeUI() {
        // إخفاء شاشة التحميل إذا وجدت
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.style.display = 'none';
        }
        
        // إظهار التطبيق الرئيسي
        const mainApp = document.getElementById('main-app') || document.body;
        if (mainApp) {
            mainApp.style.display = 'block';
        }
        
        // تهيئة مدير التنقل إذا كان متاحاً
        if (window.NavigationManager) {
            await window.NavigationManager.init();
        }
        
        // تهيئة مدير الواجهة إذا كان متاحاً
        if (window.UIManager) {
            await window.UIManager.init();
        }
        
        console.log('✅ UI initialized');
    }

    // تحميل الإعدادات
    async loadSettings() {
        try {
            const storage = window.UnifiedStorage;
            if (storage) {
                const settings = await storage.load('settings') || {};
                
                // تطبيق الإعدادات الافتراضية
                const defaultSettings = {
                    currency: 'ج.م',
                    language: 'ar',
                    theme: 'light',
                    autoSave: true,
                    printReceipts: true,
                    taxRate: 0.14
                };
                
                window.AppSettings = { ...defaultSettings, ...settings };
                
                // تطبيق الثيم
                this.applyTheme(window.AppSettings.theme);
                
                console.log('✅ Settings loaded');
            }
        } catch (error) {
            console.warn('⚠️ Could not load settings:', error);
            // إعدادات افتراضية
            window.AppSettings = {
                currency: 'ج.م',
                language: 'ar',
                theme: 'light',
                autoSave: true,
                printReceipts: true,
                taxRate: 0.14
            };
        }
    }

    // إعداد معالجات الأحداث
    async setupEventHandlers() {
        // معالج إغلاق التطبيق
        window.addEventListener('beforeunload', (e) => {
            if (window.AppSettings && window.AppSettings.autoSave) {
                // حفظ البيانات المؤقتة
                this.autoSave();
            }
        });
        
        // معالج الأخطاء العامة
        window.addEventListener('error', (e) => {
            console.error('❌ Global error:', e.error);
            if (window.Utils) {
                Utils.showMessage('حدث خطأ غير متوقع', 'error');
            }
        });
        
        // معالج الأخطاء غير المعالجة
        window.addEventListener('unhandledrejection', (e) => {
            console.error('❌ Unhandled promise rejection:', e.reason);
            if (window.Utils) {
                Utils.showMessage('حدث خطأ في النظام', 'error');
            }
        });
        
        console.log('✅ Event handlers setup');
    }

    // التحقق من النظام
    async systemCheck() {
        const checks = [];
        
        // التحقق من المتطلبات الأساسية
        checks.push({
            name: 'localStorage',
            status: !!window.localStorage,
            required: true
        });
        
        checks.push({
            name: 'IndexedDB',
            status: !!window.indexedDB,
            required: false
        });
        
        checks.push({
            name: 'UnifiedStorage',
            status: !!window.UnifiedStorage,
            required: true
        });
        
        checks.push({
            name: 'Utils',
            status: !!window.Utils,
            required: true
        });
        
        checks.push({
            name: 'Validators',
            status: !!window.Validators,
            required: true
        });
        
        // فحص النتائج
        const failedRequired = checks.filter(check => check.required && !check.status);
        const failedOptional = checks.filter(check => !check.required && !check.status);
        
        if (failedRequired.length > 0) {
            const failedNames = failedRequired.map(check => check.name).join(', ');
            throw new Error(`Required components failed: ${failedNames}`);
        }
        
        if (failedOptional.length > 0) {
            const failedNames = failedOptional.map(check => check.name).join(', ');
            console.warn(`⚠️ Optional components not available: ${failedNames}`);
        }
        
        console.log('✅ System check completed');
    }

    // تطبيق الثيم
    applyTheme(theme) {
        try {
            document.body.className = document.body.className.replace(/theme-\w+/g, '');
            document.body.classList.add(`theme-${theme}`);
        } catch (error) {
            console.warn('⚠️ Could not apply theme:', error);
        }
    }

    // حفظ تلقائي
    autoSave() {
        try {
            console.log('💾 Auto-saving...');
            // يمكن إضافة منطق الحفظ التلقائي هنا
        } catch (error) {
            console.error('❌ Auto-save failed:', error);
        }
    }

    // عرض مؤشر التقدم
    updateProgressIndicator(current, total, stepName) {
        try {
            let progressContainer = document.getElementById('init-progress');
            
            if (!progressContainer) {
                progressContainer = document.createElement('div');
                progressContainer.id = 'init-progress';
                progressContainer.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: white;
                    padding: 30px;
                    border-radius: 10px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                    z-index: 10000;
                    text-align: center;
                    min-width: 300px;
                    direction: rtl;
                `;
                document.body.appendChild(progressContainer);
            }
            
            const percentage = Math.round((current / total) * 100);
            
            progressContainer.innerHTML = `
                <h3 style="margin: 0 0 20px 0; color: #333;">🐎 Black Horse POS</h3>
                <div style="background: #f0f0f0; border-radius: 10px; overflow: hidden; margin: 15px 0;">
                    <div style="background: #007bff; height: 20px; width: ${percentage}%; transition: width 0.3s;"></div>
                </div>
                <p style="margin: 10px 0 0 0; color: #666;">${stepName}</p>
                <small style="color: #999;">${current} من ${total} (${percentage}%)</small>
            `;
        } catch (error) {
            console.warn('⚠️ Could not update progress indicator:', error);
        }
    }

    // إخفاء مؤشر التقدم
    hideProgressIndicator() {
        try {
            const progressContainer = document.getElementById('init-progress');
            if (progressContainer) {
                progressContainer.remove();
            }
        } catch (error) {
            console.warn('⚠️ Could not hide progress indicator:', error);
        }
    }

    // عرض رسالة الترحيب
    showWelcomeMessage() {
        try {
            if (window.Utils) {
                Utils.showMessage('🎉 مرحباً بك في Black Horse POS', 'success', 3000);
            }
        } catch (error) {
            console.warn('⚠️ Could not show welcome message:', error);
        }
    }

    // عرض رسالة خطأ
    showErrorMessage(message) {
        try {
            if (window.Utils) {
                Utils.showMessage(message, 'error', 5000);
            } else {
                alert(message);
            }
        } catch (error) {
            console.error('❌ Could not show error message:', error);
        }
    }

    // تأخير
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // الحصول على حالة التهيئة
    getInitializationStatus() {
        return {
            isInitialized: this.isInitialized,
            currentStep: this.currentStep,
            totalSteps: this.initializationSteps.length,
            duration: this.startTime ? Date.now() - this.startTime : 0
        };
    }
}

// إنشاء instance عام
window.AppInitializer = new AppInitializer();

// تصدير للاستخدام في الوحدات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AppInitializer;
}

// بدء التهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    console.log('📄 DOM loaded, starting app initialization...');
    window.AppInitializer.initialize();
});

console.log('✅ App Initializer loaded successfully');
