/**
 * Black Horse POS - UI Optimizer
 * محسن واجهة المستخدم
 * Developer: Augment Agent
 */

class UIOptimizer {
    constructor() {
        this.virtualScrollEnabled = true;
        this.lazyImageLoading = true;
        this.animationOptimization = true;
        this.debounceDelay = 300;
        this.throttleDelay = 100;
        this.intersectionObserver = null;
        this.resizeObserver = null;
        this.mutationObserver = null;
        
        console.log('🎨 UI Optimizer initialized');
        this.init();
    }

    // تهيئة محسن واجهة المستخدم
    init() {
        try {
            // إعداد Virtual Scrolling
            if (this.virtualScrollEnabled) {
                this.setupVirtualScrolling();
            }
            
            // إعداد Lazy Loading للصور
            if (this.lazyImageLoading) {
                this.setupLazyImageLoading();
            }
            
            // تحسين الرسوم المتحركة
            if (this.animationOptimization) {
                this.setupAnimationOptimization();
            }
            
            // إعداد Debouncing و Throttling
            this.setupPerformanceHelpers();
            
            // إعداد مراقبة التغييرات
            this.setupObservers();
            
            // تحسين الأحداث
            this.optimizeEventListeners();
            
            console.log('✅ UI Optimizer ready');
            
        } catch (error) {
            console.error('❌ Error initializing UI Optimizer:', error);
        }
    }

    // إعداد Virtual Scrolling
    setupVirtualScrolling() {
        try {
            console.log('📜 Setting up virtual scrolling...');
            
            // إنشاء Virtual Scroll للجداول الكبيرة
            this.virtualScrollConfig = {
                itemHeight: 50, // ارتفاع كل عنصر
                containerHeight: 400, // ارتفاع الحاوية
                buffer: 5 // عدد العناصر الإضافية للتحميل
            };
            
            console.log('✅ Virtual scrolling configured');
            
        } catch (error) {
            console.error('❌ Error setting up virtual scrolling:', error);
        }
    }

    // تطبيق Virtual Scrolling على جدول
    applyVirtualScrolling(tableId, data, renderFunction) {
        try {
            const container = document.getElementById(tableId);
            if (!container) return;
            
            const config = this.virtualScrollConfig;
            const totalItems = data.length;
            const visibleItems = Math.ceil(config.containerHeight / config.itemHeight);
            const totalHeight = totalItems * config.itemHeight;
            
            // إنشاء الحاوية الافتراضية
            container.innerHTML = `
                <div class="virtual-scroll-container" style="height: ${config.containerHeight}px; overflow-y: auto;">
                    <div class="virtual-scroll-spacer" style="height: ${totalHeight}px; position: relative;">
                        <div class="virtual-scroll-content" style="position: absolute; top: 0; width: 100%;"></div>
                    </div>
                </div>
            `;
            
            const scrollContainer = container.querySelector('.virtual-scroll-container');
            const content = container.querySelector('.virtual-scroll-content');
            
            // دالة تحديث المحتوى المرئي
            const updateVisibleItems = () => {
                const scrollTop = scrollContainer.scrollTop;
                const startIndex = Math.floor(scrollTop / config.itemHeight);
                const endIndex = Math.min(startIndex + visibleItems + config.buffer, totalItems);
                
                // تحديث موقع المحتوى
                content.style.top = `${startIndex * config.itemHeight}px`;
                
                // رسم العناصر المرئية
                const visibleData = data.slice(startIndex, endIndex);
                content.innerHTML = visibleData.map((item, index) => 
                    renderFunction(item, startIndex + index)
                ).join('');
            };
            
            // ربط حدث التمرير
            scrollContainer.addEventListener('scroll', this.throttle(updateVisibleItems, this.throttleDelay));
            
            // التحديث الأولي
            updateVisibleItems();
            
            console.log(`✅ Virtual scrolling applied to ${tableId}`);
            
        } catch (error) {
            console.error('❌ Error applying virtual scrolling:', error);
        }
    }

    // إعداد Lazy Loading للصور
    setupLazyImageLoading() {
        try {
            console.log('🖼️ Setting up lazy image loading...');
            
            // إنشاء Intersection Observer للصور
            this.intersectionObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        const src = img.dataset.src;
                        
                        if (src) {
                            img.src = src;
                            img.removeAttribute('data-src');
                            this.intersectionObserver.unobserve(img);
                            
                            // إضافة تأثير التحميل
                            img.style.opacity = '0';
                            img.onload = () => {
                                img.style.transition = 'opacity 0.3s';
                                img.style.opacity = '1';
                            };
                        }
                    }
                });
            }, {
                rootMargin: '50px'
            });
            
            // مراقبة الصور الموجودة
            this.observeImages();
            
            console.log('✅ Lazy image loading ready');
            
        } catch (error) {
            console.error('❌ Error setting up lazy image loading:', error);
        }
    }

    // مراقبة الصور للتحميل المتأخر
    observeImages() {
        try {
            const lazyImages = document.querySelectorAll('img[data-src]');
            lazyImages.forEach(img => {
                this.intersectionObserver.observe(img);
            });
            
        } catch (error) {
            console.error('❌ Error observing images:', error);
        }
    }

    // تحسين الرسوم المتحركة
    setupAnimationOptimization() {
        try {
            console.log('🎬 Setting up animation optimization...');
            
            // تحسين CSS animations
            this.optimizeCSS();
            
            // إعداد requestAnimationFrame helper
            this.setupRAFHelper();
            
            console.log('✅ Animation optimization ready');
            
        } catch (error) {
            console.error('❌ Error setting up animation optimization:', error);
        }
    }

    // تحسين CSS
    optimizeCSS() {
        try {
            // إضافة قواعد CSS محسنة
            const style = document.createElement('style');
            style.textContent = `
                /* تحسين الأداء للرسوم المتحركة */
                .optimized-animation {
                    will-change: transform, opacity;
                    transform: translateZ(0);
                    backface-visibility: hidden;
                }
                
                /* تحسين التمرير */
                .smooth-scroll {
                    scroll-behavior: smooth;
                    -webkit-overflow-scrolling: touch;
                }
                
                /* تحسين الجداول */
                .optimized-table {
                    table-layout: fixed;
                    border-collapse: separate;
                    border-spacing: 0;
                }
                
                /* تحسين النصوص */
                .optimized-text {
                    text-rendering: optimizeSpeed;
                    font-display: swap;
                }
                
                /* تحسين الصور */
                .optimized-image {
                    image-rendering: -webkit-optimize-contrast;
                    image-rendering: crisp-edges;
                }
                
                /* تحسين الظلال */
                .optimized-shadow {
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
                }
            `;
            
            document.head.appendChild(style);
            
        } catch (error) {
            console.error('❌ Error optimizing CSS:', error);
        }
    }

    // إعداد requestAnimationFrame helper
    setupRAFHelper() {
        try {
            // دالة محسنة للرسوم المتحركة
            this.animateWithRAF = (callback, duration = 300) => {
                const startTime = performance.now();
                
                const animate = (currentTime) => {
                    const elapsed = currentTime - startTime;
                    const progress = Math.min(elapsed / duration, 1);
                    
                    callback(progress);
                    
                    if (progress < 1) {
                        requestAnimationFrame(animate);
                    }
                };
                
                requestAnimationFrame(animate);
            };
            
        } catch (error) {
            console.error('❌ Error setting up RAF helper:', error);
        }
    }

    // إعداد Debouncing و Throttling
    setupPerformanceHelpers() {
        try {
            console.log('⚡ Setting up performance helpers...');
            
            // Debounce function
            this.debounce = (func, delay = this.debounceDelay) => {
                let timeoutId;
                return function (...args) {
                    clearTimeout(timeoutId);
                    timeoutId = setTimeout(() => func.apply(this, args), delay);
                };
            };
            
            // Throttle function
            this.throttle = (func, delay = this.throttleDelay) => {
                let lastCall = 0;
                return function (...args) {
                    const now = Date.now();
                    if (now - lastCall >= delay) {
                        lastCall = now;
                        return func.apply(this, args);
                    }
                };
            };
            
            console.log('✅ Performance helpers ready');
            
        } catch (error) {
            console.error('❌ Error setting up performance helpers:', error);
        }
    }

    // إعداد مراقبة التغييرات
    setupObservers() {
        try {
            console.log('👁️ Setting up observers...');
            
            // Resize Observer لتحسين التخطيط
            if (window.ResizeObserver) {
                this.resizeObserver = new ResizeObserver(this.throttle((entries) => {
                    entries.forEach(entry => {
                        // تحديث التخطيط عند تغيير الحجم
                        this.handleResize(entry.target);
                    });
                }));
            }
            
            // Mutation Observer لمراقبة تغييرات DOM
            this.mutationObserver = new MutationObserver(this.throttle((mutations) => {
                mutations.forEach(mutation => {
                    if (mutation.type === 'childList') {
                        // مراقبة الصور الجديدة للتحميل المتأخر
                        mutation.addedNodes.forEach(node => {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                const lazyImages = node.querySelectorAll('img[data-src]');
                                lazyImages.forEach(img => {
                                    this.intersectionObserver.observe(img);
                                });
                            }
                        });
                    }
                });
            }));
            
            // بدء مراقبة التغييرات
            this.mutationObserver.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            console.log('✅ Observers ready');
            
        } catch (error) {
            console.error('❌ Error setting up observers:', error);
        }
    }

    // معالجة تغيير الحجم
    handleResize(element) {
        try {
            // إعادة حساب Virtual Scrolling إذا لزم الأمر
            if (element.classList.contains('virtual-scroll-container')) {
                // سيتم تطوير هذه الوظيفة لاحقاً
                console.log('ℹ️ Resize handling for virtual scroll (to be implemented)');
            }
            
        } catch (error) {
            console.error('❌ Error handling resize:', error);
        }
    }

    // تحسين مستمعي الأحداث
    optimizeEventListeners() {
        try {
            console.log('🎧 Optimizing event listeners...');
            
            // تحسين أحداث التمرير
            const scrollElements = document.querySelectorAll('.scrollable');
            scrollElements.forEach(element => {
                element.addEventListener('scroll', this.throttle(() => {
                    // معالجة محسنة للتمرير
                }, this.throttleDelay), { passive: true });
            });
            
            // تحسين أحداث البحث
            const searchInputs = document.querySelectorAll('input[type="search"], .search-input');
            searchInputs.forEach(input => {
                input.addEventListener('input', this.debounce((event) => {
                    // معالجة محسنة للبحث
                    this.handleSearch(event.target.value);
                }, this.debounceDelay));
            });
            
            console.log('✅ Event listeners optimized');
            
        } catch (error) {
            console.error('❌ Error optimizing event listeners:', error);
        }
    }

    // معالجة البحث المحسنة
    handleSearch(query) {
        try {
            // إلغاء البحث السابق إذا كان قيد التنفيذ
            if (this.searchController) {
                this.searchController.abort();
            }
            
            // إنشاء controller جديد للبحث
            this.searchController = new AbortController();
            
            // تنفيذ البحث
            console.log(`🔍 Optimized search: ${query}`);
            
        } catch (error) {
            console.error('❌ Error in optimized search:', error);
        }
    }

    // تطبيق تحسينات على عنصر
    applyOptimizations(element) {
        try {
            // إضافة classes محسنة
            if (element.tagName === 'TABLE') {
                element.classList.add('optimized-table');
            }
            
            if (element.tagName === 'IMG') {
                element.classList.add('optimized-image');
            }
            
            // إضافة تحسينات النصوص
            const textElements = element.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6');
            textElements.forEach(el => {
                el.classList.add('optimized-text');
            });
            
            // مراقبة العنصر للتغييرات
            if (this.resizeObserver) {
                this.resizeObserver.observe(element);
            }
            
            console.log('✅ Optimizations applied to element');
            
        } catch (error) {
            console.error('❌ Error applying optimizations:', error);
        }
    }

    // تحسين جدول كبير
    optimizeTable(tableId, data, columns) {
        try {
            console.log(`📊 Optimizing table: ${tableId}`);
            
            // تطبيق Virtual Scrolling
            this.applyVirtualScrolling(tableId, data, (item, index) => {
                return `
                    <tr>
                        ${columns.map(col => `<td>${item[col.key] || '-'}</td>`).join('')}
                    </tr>
                `;
            });
            
            console.log('✅ Table optimized');
            
        } catch (error) {
            console.error('❌ Error optimizing table:', error);
        }
    }

    // الحصول على إحصائيات الأداء
    getPerformanceStats() {
        try {
            return {
                virtualScrollEnabled: this.virtualScrollEnabled,
                lazyImageLoading: this.lazyImageLoading,
                animationOptimization: this.animationOptimization,
                observersActive: {
                    intersection: !!this.intersectionObserver,
                    resize: !!this.resizeObserver,
                    mutation: !!this.mutationObserver
                },
                timing: {
                    debounceDelay: this.debounceDelay,
                    throttleDelay: this.throttleDelay
                }
            };
            
        } catch (error) {
            console.error('❌ Error getting performance stats:', error);
            return null;
        }
    }

    // تنظيف الموارد
    cleanup() {
        try {
            if (this.intersectionObserver) {
                this.intersectionObserver.disconnect();
            }
            
            if (this.resizeObserver) {
                this.resizeObserver.disconnect();
            }
            
            if (this.mutationObserver) {
                this.mutationObserver.disconnect();
            }
            
            console.log('✅ UI Optimizer cleaned up');
            
        } catch (error) {
            console.error('❌ Error cleaning up UI Optimizer:', error);
        }
    }
}

// إنشاء instance عام
window.UIOptimizer = new UIOptimizer();

// تصدير للاستخدام في الوحدات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UIOptimizer;
}

console.log('✅ UI Optimizer loaded successfully');
