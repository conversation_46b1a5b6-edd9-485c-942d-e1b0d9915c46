<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Black Horse ERP - نظام إدارة الأعمال</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .startup-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .startup-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 500px;
            width: 100%;
        }
        .logo-section {
            text-align: center;
            padding: 2rem 0;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 20px 20px 0 0;
            color: white;
        }
        .logo-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        .startup-content {
            padding: 2rem;
        }
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .feature-item:hover {
            background: rgba(102, 126, 234, 0.1);
        }
        .feature-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-left: 1rem;
        }
        .btn-start {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 50px;
            padding: 1rem 2rem;
            font-size: 1.2rem;
            font-weight: bold;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        .btn-start:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
            color: white;
        }
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 2rem;
        }
        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .system-check {
            margin-top: 1rem;
            padding: 1rem;
            background: rgba(40, 167, 69, 0.1);
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        .check-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        .check-icon {
            color: #28a745;
            margin-left: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="startup-container">
        <div class="startup-card">
            <!-- Logo Section -->
            <div class="logo-section">
                <div class="logo-icon">
                    <i class="fas fa-horse-head"></i>
                </div>
                <h1 class="mb-0">Black Horse ERP</h1>
                <p class="mb-0">نظام إدارة الأعمال المتكامل</p>
            </div>

            <!-- Content Section -->
            <div class="startup-content">
                <div id="welcomeSection">
                    <h3 class="text-center mb-4">مرحباً بك في نظام Black Horse</h3>
                    
                    <!-- Features List -->
                    <div class="features-list mb-4">
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-boxes"></i>
                            </div>
                            <div>
                                <strong>إدارة المخزون</strong>
                                <div class="small text-muted">تتبع المنتجات والمخزون</div>
                            </div>
                        </div>
                        
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div>
                                <strong>نظام المبيعات</strong>
                                <div class="small text-muted">إدارة المبيعات والفواتير</div>
                            </div>
                        </div>
                        
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-cash-register"></i>
                            </div>
                            <div>
                                <strong>نقاط البيع</strong>
                                <div class="small text-muted">نظام POS متقدم</div>
                            </div>
                        </div>
                        
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div>
                                <strong>الموارد البشرية</strong>
                                <div class="small text-muted">إدارة الموظفين والرواتب</div>
                            </div>
                        </div>
                        
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div>
                                <strong>التقارير والتحليلات</strong>
                                <div class="small text-muted">تقارير مفصلة وإحصائيات</div>
                            </div>
                        </div>
                    </div>

                    <!-- Start Button -->
                    <div class="text-center">
                        <button class="btn btn-start w-100" onclick="startSystem()">
                            <i class="fas fa-rocket me-2"></i>
                            بدء تشغيل النظام
                        </button>
                    </div>
                </div>

                <!-- Loading Section -->
                <div id="loadingSection" class="loading-spinner">
                    <div class="spinner"></div>
                    <h4>جاري تحميل النظام...</h4>
                    <p class="text-muted">يرجى الانتظار بينما نقوم بتهيئة جميع الوحدات</p>
                    
                    <div class="system-check">
                        <div class="check-item">
                            <i class="fas fa-check check-icon"></i>
                            <span>تحميل الملفات الأساسية</span>
                        </div>
                        <div class="check-item">
                            <i class="fas fa-check check-icon"></i>
                            <span>تهيئة قاعدة البيانات</span>
                        </div>
                        <div class="check-item">
                            <i class="fas fa-check check-icon"></i>
                            <span>تحميل الوحدات</span>
                        </div>
                        <div class="check-item">
                            <i class="fas fa-check check-icon"></i>
                            <span>إعداد واجهة المستخدم</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="position-fixed bottom-0 start-0 p-3">
        <div class="btn-group-vertical">
            <button class="btn btn-outline-light btn-sm mb-2" onclick="openHealthCheck()" title="فحص صحة النظام">
                <i class="fas fa-heartbeat"></i>
            </button>
            <button class="btn btn-outline-light btn-sm mb-2" onclick="openFixTool()" title="أداة الإصلاح">
                <i class="fas fa-tools"></i>
            </button>
            <button class="btn btn-outline-light btn-sm" onclick="openDocumentation()" title="الوثائق">
                <i class="fas fa-book"></i>
            </button>
        </div>
    </div>

    <!-- Version Info -->
    <div class="position-fixed bottom-0 end-0 p-3">
        <small class="text-white-50">
            الإصدار 1.0.0 | © 2024 Black Horse ERP
        </small>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function startSystem() {
            // Hide welcome section
            document.getElementById('welcomeSection').style.display = 'none';
            
            // Show loading section
            document.getElementById('loadingSection').style.display = 'block';
            
            // Simulate system loading
            setTimeout(() => {
                // Redirect to main application
                window.location.href = 'index.html';
            }, 3000);
        }

        function openHealthCheck() {
            window.open('system-health-check.html', '_blank');
        }

        function openFixTool() {
            window.open('fix-system.html', '_blank');
        }

        function openDocumentation() {
            window.open('README.md', '_blank');
        }

        // Auto-check system health on load
        document.addEventListener('DOMContentLoaded', function() {
            // Perform basic system checks
            checkSystemHealth();
        });

        async function checkSystemHealth() {
            try {
                // Check if main files exist
                const files = [
                    'index.html',
                    'src/core/database.js',
                    'src/core/utils.js',
                    'src/core/app.js'
                ];

                let allFilesExist = true;
                for (const file of files) {
                    try {
                        const response = await fetch(file);
                        if (!response.ok) {
                            allFilesExist = false;
                            break;
                        }
                    } catch (error) {
                        allFilesExist = false;
                        break;
                    }
                }

                if (!allFilesExist) {
                    showSystemWarning();
                }
            } catch (error) {
                console.warn('System health check failed:', error);
            }
        }

        function showSystemWarning() {
            const warningDiv = document.createElement('div');
            warningDiv.className = 'alert alert-warning position-fixed top-0 start-50 translate-middle-x mt-3';
            warningDiv.style.zIndex = '9999';
            warningDiv.innerHTML = `
                <i class="fas fa-exclamation-triangle me-2"></i>
                تم اكتشاف مشاكل في النظام. يُنصح بتشغيل فحص صحة النظام.
                <button class="btn btn-sm btn-warning ms-2" onclick="openHealthCheck()">
                    فحص النظام
                </button>
            `;
            document.body.appendChild(warningDiv);

            // Auto remove after 10 seconds
            setTimeout(() => {
                if (warningDiv.parentNode) {
                    warningDiv.remove();
                }
            }, 10000);
        }
    </script>
</body>
</html>
