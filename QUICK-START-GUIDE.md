# 🚀 دليل البدء السريع - Black Horse ERP

## ⚡ التشغيل السريع (3 خطوات)

### 1️⃣ تشغيل الخادم
```bash
# على Windows - انقر نقراً مزدوجاً
start.bat

# على Linux/macOS
./start.sh

# أو باستخدام Python مباشرة
python start-server.py
```

### 2️⃣ فتح النظام
سيتم فتح المتصفح تلقائياً على: `http://localhost:8080`

### 3️⃣ فحص صحة النظام
افتح: `http://localhost:8080/system-health-check.html`

## 🔧 حل المشاكل الشائعة

### ❌ مشكلة "Failed to fetch"
**السبب**: فتح الملفات مباشرة في المتصفح بدلاً من استخدام خادم

**الحل**:
```bash
# استخدم الخادم المحلي
python start-server.py
# ثم افتح: http://localhost:8080
```

### ❌ مشكلة "Python غير موجود"
**الحل**:
1. حمل Python من: https://www.python.org/downloads/
2. تأكد من تحديد "Add Python to PATH" أثناء التثبيت
3. أعد تشغيل الكمبيوتر
4. جرب مرة أخرى

### ❌ مشكلة "المنفذ 8080 مستخدم"
**الحل**:
```bash
# استخدم منفذ آخر
python start-server.py -p 3000
```

## 🎯 الوصول السريع للأدوات

| الأداة | الرابط | الوصف |
|--------|---------|--------|
| 🏠 النظام الرئيسي | `http://localhost:8080` | الواجهة الرئيسية |
| 🔍 فحص الصحة | `/system-health-check.html` | تشخيص شامل |
| ⚙️ معالج الإعداد | `/setup-wizard.html` | إعداد أولي |
| 🛠️ إصلاح النظام | `/system-repair.html` | إصلاح تلقائي |
| 🧪 اختبار النظام | `/test-system.html` | اختبار الوحدات |

## 📊 حالة النظام الحالية

بعد حل جميع المشاكل الحرجة:

✅ **الملفات الأساسية**: جميعها موجودة وتعمل  
✅ **وحدات النظام**: 18+ وحدة مكتملة  
✅ **واجهات HTML**: جميعها متوفرة  
✅ **ملفات JavaScript**: جميعها متوفرة  
✅ **التصميم**: Bootstrap 5 RTL مع تدرجات احترافية  

## 🎨 المميزات الجديدة

- 🌐 **خادم تطوير محلي**: تشغيل آمن وسريع
- 🔍 **فحص صحة متقدم**: تشخيص شامل للنظام
- 🛠️ **إصلاح تلقائي**: حل المشاكل بنقرة واحدة
- 📱 **تصميم متجاوب**: يعمل على جميع الأجهزة
- 🎯 **واجهة عربية**: RTL كامل مع تصميم احترافي

## 🚨 تنبيهات مهمة

1. **لا تفتح الملفات مباشرة** - استخدم الخادم المحلي دائماً
2. **تأكد من Python** - مطلوب لتشغيل الخادم
3. **استخدم فحص الصحة** - للتأكد من سلامة النظام
4. **احفظ البيانات** - النظام يستخدم LocalStorage

## 📞 الدعم السريع

إذا واجهت أي مشكلة:

1. 🔍 **افحص صحة النظام** أولاً
2. 🛠️ **استخدم أداة الإصلاح** التلقائي
3. 📋 **راجع هذا الدليل** للحلول الشائعة
4. 🆘 **اطلب المساعدة** إذا لم تحل المشكلة

---

<div align="center">
  <strong>🐎 Black Horse ERP</strong><br>
  <em>نظام إدارة الأعمال المتكامل</em>
</div>
