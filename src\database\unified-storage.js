/**
 * Black Horse POS - Unified Storage System
 * نظام التخزين الموحد
 * Developer: Augment Agent
 */

class UnifiedStorage {
    constructor() {
        this.dbName = 'BlackHorsePOS';
        this.dbVersion = 2;
        this.db = null;
        this.isInitialized = false;
        this.useIndexedDB = true;
        this.fallbackToLocalStorage = true;
        
        // جداول قاعدة البيانات
        this.tables = {
            products: 'products',
            customers: 'customers', 
            sales: 'sales',
            expenses: 'expenses',
            users: 'users',
            suppliers: 'suppliers',
            inventory: 'inventory',
            settings: 'settings',
            backups: 'backups'
        };
        
        console.log('🗄️ Unified Storage System initialized');
    }

    // تهيئة النظام
    async init() {
        try {
            console.log('🚀 Initializing Unified Storage...');
            
            if (this.useIndexedDB && window.indexedDB) {
                await this.initIndexedDB();
            } else {
                console.warn('⚠️ IndexedDB not available, using localStorage only');
                this.useIndexedDB = false;
            }
            
            this.isInitialized = true;
            console.log('✅ Unified Storage initialized successfully');
            return true;
            
        } catch (error) {
            console.error('❌ Error initializing Unified Storage:', error);
            this.useIndexedDB = false;
            this.isInitialized = true;
            return false;
        }
    }

    // تهيئة IndexedDB
    async initIndexedDB() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);
            
            request.onerror = () => {
                console.error('❌ IndexedDB error:', request.error);
                reject(request.error);
            };
            
            request.onsuccess = () => {
                this.db = request.result;
                console.log('✅ IndexedDB connected successfully');
                resolve(this.db);
            };
            
            request.onupgradeneeded = (event) => {
                this.db = event.target.result;
                this.createTables();
            };
        });
    }

    // إنشاء الجداول
    createTables() {
        try {
            console.log('📋 Creating database tables...');
            
            Object.values(this.tables).forEach(tableName => {
                if (!this.db.objectStoreNames.contains(tableName)) {
                    const store = this.db.createObjectStore(tableName, { keyPath: 'id' });
                    
                    // إضافة فهارس حسب نوع الجدول
                    switch(tableName) {
                        case 'products':
                            store.createIndex('name', 'name', { unique: false });
                            store.createIndex('barcode', 'barcode', { unique: true });
                            store.createIndex('category', 'category', { unique: false });
                            break;
                        case 'customers':
                            store.createIndex('name', 'name', { unique: false });
                            store.createIndex('phone', 'phone', { unique: true });
                            break;
                        case 'sales':
                            store.createIndex('date', 'date', { unique: false });
                            store.createIndex('invoiceNumber', 'invoiceNumber', { unique: true });
                            break;
                    }
                    
                    console.log(`✅ Table created: ${tableName}`);
                }
            });
            
        } catch (error) {
            console.error('❌ Error creating tables:', error);
        }
    }

    // حفظ البيانات
    async save(table, data) {
        try {
            if (!this.isInitialized) {
                await this.init();
            }
            
            // التأكد من وجود ID
            if (!data.id) {
                data.id = this.generateId();
            }
            
            // إضافة timestamps
            data.updatedAt = new Date().toISOString();
            if (!data.createdAt) {
                data.createdAt = data.updatedAt;
            }
            
            // محاولة الحفظ في IndexedDB أولاً
            if (this.useIndexedDB && this.db) {
                try {
                    await this.saveToIndexedDB(table, data);
                    console.log(`💾 Data saved to IndexedDB: ${table}`);
                    return data;
                } catch (error) {
                    console.warn('⚠️ IndexedDB save failed, falling back to localStorage');
                }
            }
            
            // الحفظ في localStorage كبديل
            if (this.fallbackToLocalStorage) {
                this.saveToLocalStorage(table, data);
                console.log(`💾 Data saved to localStorage: ${table}`);
                return data;
            }
            
            throw new Error('No storage method available');
            
        } catch (error) {
            console.error(`❌ Error saving data to ${table}:`, error);
            throw error;
        }
    }

    // حفظ في IndexedDB
    async saveToIndexedDB(table, data) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([table], 'readwrite');
            const store = transaction.objectStore(table);
            const request = store.put(data);
            
            request.onsuccess = () => resolve(data);
            request.onerror = () => reject(request.error);
        });
    }

    // حفظ في localStorage
    saveToLocalStorage(table, data) {
        try {
            const existingData = JSON.parse(localStorage.getItem(table) || '[]');
            const index = existingData.findIndex(item => item.id === data.id);
            
            if (index >= 0) {
                existingData[index] = data;
            } else {
                existingData.push(data);
            }
            
            localStorage.setItem(table, JSON.stringify(existingData));
            return data;
        } catch (error) {
            console.error('❌ Error saving to localStorage:', error);
            throw error;
        }
    }

    // تحميل البيانات
    async load(table, id = null) {
        try {
            if (!this.isInitialized) {
                await this.init();
            }
            
            // محاولة التحميل من IndexedDB أولاً
            if (this.useIndexedDB && this.db) {
                try {
                    const data = await this.loadFromIndexedDB(table, id);
                    console.log(`📖 Data loaded from IndexedDB: ${table}`);
                    return data;
                } catch (error) {
                    console.warn('⚠️ IndexedDB load failed, falling back to localStorage');
                }
            }
            
            // التحميل من localStorage كبديل
            if (this.fallbackToLocalStorage) {
                const data = this.loadFromLocalStorage(table, id);
                console.log(`📖 Data loaded from localStorage: ${table}`);
                return data;
            }
            
            return id ? null : [];
            
        } catch (error) {
            console.error(`❌ Error loading data from ${table}:`, error);
            return id ? null : [];
        }
    }

    // تحميل من IndexedDB
    async loadFromIndexedDB(table, id = null) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([table], 'readonly');
            const store = transaction.objectStore(table);
            
            if (id) {
                const request = store.get(id);
                request.onsuccess = () => resolve(request.result || null);
                request.onerror = () => reject(request.error);
            } else {
                const request = store.getAll();
                request.onsuccess = () => resolve(request.result || []);
                request.onerror = () => reject(request.error);
            }
        });
    }

    // تحميل من localStorage
    loadFromLocalStorage(table, id = null) {
        try {
            const data = JSON.parse(localStorage.getItem(table) || '[]');
            
            if (id) {
                return data.find(item => item.id === id) || null;
            }
            
            return data;
        } catch (error) {
            console.error('❌ Error loading from localStorage:', error);
            return id ? null : [];
        }
    }

    // حذف البيانات
    async delete(table, id) {
        try {
            if (!this.isInitialized) {
                await this.init();
            }
            
            // حذف من IndexedDB
            if (this.useIndexedDB && this.db) {
                try {
                    await this.deleteFromIndexedDB(table, id);
                } catch (error) {
                    console.warn('⚠️ IndexedDB delete failed');
                }
            }
            
            // حذف من localStorage
            if (this.fallbackToLocalStorage) {
                this.deleteFromLocalStorage(table, id);
            }
            
            console.log(`🗑️ Data deleted from ${table}: ${id}`);
            return true;
            
        } catch (error) {
            console.error(`❌ Error deleting data from ${table}:`, error);
            return false;
        }
    }

    // حذف من IndexedDB
    async deleteFromIndexedDB(table, id) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([table], 'readwrite');
            const store = transaction.objectStore(table);
            const request = store.delete(id);
            
            request.onsuccess = () => resolve(true);
            request.onerror = () => reject(request.error);
        });
    }

    // حذف من localStorage
    deleteFromLocalStorage(table, id) {
        try {
            const data = JSON.parse(localStorage.getItem(table) || '[]');
            const filteredData = data.filter(item => item.id !== id);
            localStorage.setItem(table, JSON.stringify(filteredData));
            return true;
        } catch (error) {
            console.error('❌ Error deleting from localStorage:', error);
            return false;
        }
    }

    // توليد ID فريد
    generateId() {
        return 'id_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // إحصائيات النظام
    async getStats() {
        const stats = {};
        
        for (const table of Object.values(this.tables)) {
            try {
                const data = await this.load(table);
                stats[table] = {
                    count: Array.isArray(data) ? data.length : 0,
                    storage: this.useIndexedDB ? 'IndexedDB' : 'localStorage'
                };
            } catch (error) {
                stats[table] = { count: 0, error: error.message };
            }
        }
        
        return stats;
    }
}

// إنشاء instance عام
window.UnifiedStorage = new UnifiedStorage();

// تصدير للاستخدام في الوحدات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UnifiedStorage;
}

console.log('✅ Unified Storage System loaded successfully');
