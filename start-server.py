#!/usr/bin/env python3
"""
خادم محلي بسيط لتشغيل نظام Black Horse ERP
Simple local server for running Black Horse ERP system
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

# تحديد المنفذ
PORT = 8080

# تحديد مجلد العمل
DIRECTORY = Path(__file__).parent

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """معالج مخصص للطلبات HTTP"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=DIRECTORY, **kwargs)
    
    def end_headers(self):
        # إضافة headers للسماح بـ CORS
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_GET(self):
        # إذا كان الطلب للجذر، إعادة توجيه لـ index.html
        if self.path == '/':
            self.path = '/index.html'
        return super().do_GET()
    
    def log_message(self, format, *args):
        # تخصيص رسائل السجل
        print(f"[{self.address_string()}] {format % args}")

def main():
    """تشغيل الخادم المحلي"""
    
    print("=" * 60)
    print("🐎 Black Horse ERP - خادم التطوير المحلي")
    print("=" * 60)
    
    # التحقق من وجود الملفات الأساسية
    required_files = [
        'index.html',
        'src/core/app.js',
        'src/core/database.js',
        'src/core/utils.js'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not (DIRECTORY / file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("⚠️  تحذير: الملفات التالية مفقودة:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        print()
    
    # البحث عن منفذ متاح
    original_port = PORT
    port = PORT
    max_attempts = 10
    
    for attempt in range(max_attempts):
        try:
            with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
                print(f"🚀 تم بدء الخادم على المنفذ {port}")
                print(f"🌐 رابط النظام: http://localhost:{port}")
                print(f"🔧 رابط فحص الصحة: http://localhost:{port}/system-health-check.html")
                print(f"⚙️  رابط الإعدادات: http://localhost:{port}/setup-wizard.html")
                print()
                print("💡 نصائح:")
                print("   - استخدم Ctrl+C لإيقاف الخادم")
                print("   - تأكد من أن جميع الملفات في نفس المجلد")
                print("   - استخدم فحص الصحة للتأكد من سلامة النظام")
                print()
                print("=" * 60)
                
                # فتح المتصفح تلقائياً
                try:
                    webbrowser.open(f'http://localhost:{port}')
                    print("✅ تم فتح المتصفح تلقائياً")
                except:
                    print("⚠️  لم يتم فتح المتصفح تلقائياً - افتحه يدوياً")
                
                print("🔄 الخادم يعمل... اضغط Ctrl+C للإيقاف")
                print("=" * 60)
                
                # تشغيل الخادم
                httpd.serve_forever()
                
        except OSError as e:
            if e.errno == 98 or "Address already in use" in str(e):
                port += 1
                if attempt < max_attempts - 1:
                    print(f"⚠️  المنفذ {port-1} مستخدم، جاري المحاولة على المنفذ {port}")
                    continue
                else:
                    print(f"❌ فشل في العثور على منفذ متاح بعد {max_attempts} محاولات")
                    sys.exit(1)
            else:
                print(f"❌ خطأ في بدء الخادم: {e}")
                sys.exit(1)
        except KeyboardInterrupt:
            print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
            print("👋 شكراً لاستخدام Black Horse ERP!")
            sys.exit(0)
        except Exception as e:
            print(f"❌ خطأ غير متوقع: {e}")
            sys.exit(1)

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 6):
        print("❌ يتطلب Python 3.6 أو أحدث")
        print(f"   الإصدار الحالي: {sys.version}")
        sys.exit(1)

def show_help():
    """عرض المساعدة"""
    print("استخدام: python start-server.py [خيارات]")
    print()
    print("خيارات:")
    print("  -h, --help     عرض هذه المساعدة")
    print("  -p, --port     تحديد المنفذ (افتراضي: 8080)")
    print()
    print("أمثلة:")
    print("  python start-server.py")
    print("  python start-server.py -p 3000")

if __name__ == "__main__":
    # التحقق من المعاملات
    if len(sys.argv) > 1:
        if sys.argv[1] in ['-h', '--help']:
            show_help()
            sys.exit(0)
        elif sys.argv[1] in ['-p', '--port'] and len(sys.argv) > 2:
            try:
                PORT = int(sys.argv[2])
                if PORT < 1 or PORT > 65535:
                    raise ValueError("المنفذ يجب أن يكون بين 1 و 65535")
            except ValueError as e:
                print(f"❌ خطأ في المنفذ: {e}")
                sys.exit(1)
    
    # التحقق من إصدار Python
    check_python_version()
    
    # تشغيل الخادم
    main()
