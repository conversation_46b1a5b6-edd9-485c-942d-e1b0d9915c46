/**
 * نظام إدارة الموردين المتقدم
 * Advanced Suppliers Management System
 */

class AdvancedSuppliersManager {
    constructor() {
        this.suppliers = new Map();
        this.supplierCategories = new Map();
        this.supplierContracts = new Map();
        this.supplierEvaluations = new Map();
        this.supplierPayments = new Map();
        this.supplierOrders = new Map();
        this.supplierPerformance = new Map();
        this.supplierRisks = new Map();
        this.supplierCommunications = new Map();
        this.supplierDocuments = new Map();
        
        this.settings = {
            defaultCurrency: 'SAR',
            evaluationPeriod: 90, // أيام
            paymentTerms: ['نقدي', 'آجل 30 يوم', 'آجل 60 يوم', 'آجل 90 يوم'],
            riskLevels: ['منخفض', 'متوسط', 'عالي', 'حرج'],
            performanceThresholds: {
                excellent: 90,
                good: 75,
                average: 60,
                poor: 40
            },
            autoEvaluation: true,
            notificationEnabled: true
        };
        
        this.loadData();
        this.initializeDefaultCategories();
    }

    // إنشاء مورد جديد
    createSupplier(supplierData) {
        const supplier = {
            id: this.generateSupplierId(),
            code: supplierData.code || this.generateSupplierCode(),
            name: supplierData.name,
            nameEn: supplierData.nameEn || '',
            type: supplierData.type || 'individual', // individual, company, government
            category: supplierData.category,
            
            // معلومات الاتصال
            contact: {
                phone: supplierData.phone || '',
                mobile: supplierData.mobile || '',
                email: supplierData.email || '',
                website: supplierData.website || '',
                fax: supplierData.fax || ''
            },
            
            // العنوان
            address: {
                country: supplierData.country || 'السعودية',
                city: supplierData.city || '',
                district: supplierData.district || '',
                street: supplierData.street || '',
                building: supplierData.building || '',
                postalCode: supplierData.postalCode || '',
                poBox: supplierData.poBox || ''
            },
            
            // المعلومات المالية
            financial: {
                currency: supplierData.currency || 'SAR',
                creditLimit: supplierData.creditLimit || 0,
                currentBalance: 0,
                totalPurchases: 0,
                averageOrderValue: 0,
                paymentTerms: supplierData.paymentTerms || 'نقدي',
                taxNumber: supplierData.taxNumber || '',
                commercialRegister: supplierData.commercialRegister || ''
            },
            
            // معلومات الأداء
            performance: {
                rating: 0,
                deliveryScore: 0,
                qualityScore: 0,
                serviceScore: 0,
                priceScore: 0,
                totalOrders: 0,
                onTimeDeliveries: 0,
                qualityIssues: 0,
                lastEvaluationDate: null
            },
            
            // إدارة المخاطر
            risk: {
                level: 'منخفض',
                factors: [],
                mitigation: [],
                lastAssessment: new Date().toISOString(),
                score: 0
            },
            
            // الحالة والإعدادات
            status: supplierData.status || 'active', // active, inactive, suspended, blacklisted
            isPreferred: supplierData.isPreferred || false,
            isApproved: supplierData.isApproved || false,
            
            // التواريخ
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            createdBy: supplierData.createdBy || 'system',
            
            // معلومات إضافية
            notes: supplierData.notes || '',
            tags: supplierData.tags || [],
            attachments: [],
            
            // إعدادات الإشعارات
            notifications: {
                orderUpdates: true,
                paymentReminders: true,
                evaluationAlerts: true,
                contractExpiry: true
            }
        };
        
        this.suppliers.set(supplier.id, supplier);
        this.saveData();
        
        // إنشاء سجل أداء أولي
        this.initializeSupplierPerformance(supplier.id);
        
        console.log(`✅ تم إنشاء المورد: ${supplier.name}`);
        return supplier;
    }

    // تحديث بيانات المورد
    updateSupplier(supplierId, updateData) {
        const supplier = this.suppliers.get(supplierId);
        if (!supplier) {
            throw new Error('المورد غير موجود');
        }
        
        // تحديث البيانات
        Object.keys(updateData).forEach(key => {
            if (typeof updateData[key] === 'object' && updateData[key] !== null) {
                supplier[key] = { ...supplier[key], ...updateData[key] };
            } else {
                supplier[key] = updateData[key];
            }
        });
        
        supplier.updatedAt = new Date().toISOString();
        this.saveData();
        
        console.log(`✅ تم تحديث المورد: ${supplier.name}`);
        return supplier;
    }

    // إنشاء عقد مع مورد
    createSupplierContract(contractData) {
        const contract = {
            id: this.generateContractId(),
            supplierId: contractData.supplierId,
            title: contractData.title,
            type: contractData.type || 'purchase', // purchase, service, maintenance
            
            // تفاصيل العقد
            details: {
                description: contractData.description || '',
                scope: contractData.scope || '',
                deliverables: contractData.deliverables || [],
                terms: contractData.terms || [],
                conditions: contractData.conditions || []
            },
            
            // المعلومات المالية
            financial: {
                totalValue: contractData.totalValue || 0,
                currency: contractData.currency || 'SAR',
                paymentSchedule: contractData.paymentSchedule || [],
                penalties: contractData.penalties || [],
                bonuses: contractData.bonuses || []
            },
            
            // التواريخ
            startDate: contractData.startDate,
            endDate: contractData.endDate,
            signedDate: contractData.signedDate || new Date().toISOString(),
            
            // الحالة
            status: contractData.status || 'draft', // draft, active, expired, terminated, renewed
            
            // الموافقات
            approvals: {
                legal: false,
                financial: false,
                management: false,
                supplier: false
            },
            
            // المرفقات
            attachments: contractData.attachments || [],
            
            // التجديد
            renewal: {
                autoRenewal: contractData.autoRenewal || false,
                renewalPeriod: contractData.renewalPeriod || 365,
                renewalNotice: contractData.renewalNotice || 30
            },
            
            createdAt: new Date().toISOString(),
            createdBy: contractData.createdBy || 'system'
        };
        
        this.supplierContracts.set(contract.id, contract);
        this.saveData();
        
        console.log(`✅ تم إنشاء العقد: ${contract.title}`);
        return contract;
    }

    // تقييم أداء المورد
    evaluateSupplier(evaluationData) {
        const evaluation = {
            id: this.generateEvaluationId(),
            supplierId: evaluationData.supplierId,
            period: evaluationData.period,
            
            // معايير التقييم
            criteria: {
                delivery: {
                    score: evaluationData.deliveryScore || 0,
                    weight: 25,
                    comments: evaluationData.deliveryComments || ''
                },
                quality: {
                    score: evaluationData.qualityScore || 0,
                    weight: 30,
                    comments: evaluationData.qualityComments || ''
                },
                service: {
                    score: evaluationData.serviceScore || 0,
                    weight: 20,
                    comments: evaluationData.serviceComments || ''
                },
                price: {
                    score: evaluationData.priceScore || 0,
                    weight: 15,
                    comments: evaluationData.priceComments || ''
                },
                communication: {
                    score: evaluationData.communicationScore || 0,
                    weight: 10,
                    comments: evaluationData.communicationComments || ''
                }
            },
            
            // النتيجة الإجمالية
            totalScore: 0,
            grade: '',
            
            // التوصيات
            recommendations: evaluationData.recommendations || [],
            actionItems: evaluationData.actionItems || [],
            
            // المقيم
            evaluatedBy: evaluationData.evaluatedBy || 'system',
            evaluatedAt: new Date().toISOString(),
            
            // الحالة
            status: 'completed',
            
            // المرفقات
            attachments: evaluationData.attachments || []
        };
        
        // حساب النتيجة الإجمالية
        evaluation.totalScore = this.calculateEvaluationScore(evaluation.criteria);
        evaluation.grade = this.getPerformanceGrade(evaluation.totalScore);
        
        this.supplierEvaluations.set(evaluation.id, evaluation);
        
        // تحديث أداء المورد
        this.updateSupplierPerformance(evaluationData.supplierId, evaluation);
        
        this.saveData();
        
        console.log(`✅ تم تقييم المورد بنتيجة: ${evaluation.totalScore}%`);
        return evaluation;
    }

    // إدارة مدفوعات المورد
    createSupplierPayment(paymentData) {
        const payment = {
            id: this.generatePaymentId(),
            supplierId: paymentData.supplierId,
            orderId: paymentData.orderId || null,
            contractId: paymentData.contractId || null,
            
            // تفاصيل الدفع
            amount: paymentData.amount,
            currency: paymentData.currency || 'SAR',
            method: paymentData.method || 'bank_transfer', // cash, bank_transfer, check, credit_card
            reference: paymentData.reference || '',
            
            // التواريخ
            dueDate: paymentData.dueDate,
            paidDate: paymentData.paidDate || null,
            
            // الحالة
            status: paymentData.status || 'pending', // pending, paid, overdue, cancelled
            
            // معلومات إضافية
            description: paymentData.description || '',
            notes: paymentData.notes || '',
            
            // المرفقات
            attachments: paymentData.attachments || [],
            
            createdAt: new Date().toISOString(),
            createdBy: paymentData.createdBy || 'system'
        };
        
        this.supplierPayments.set(payment.id, payment);
        
        // تحديث رصيد المورد
        if (payment.status === 'paid') {
            this.updateSupplierBalance(payment.supplierId, payment.amount);
        }
        
        this.saveData();
        
        console.log(`✅ تم إنشاء دفعة بقيمة: ${payment.amount} ${payment.currency}`);
        return payment;
    }

    // تحليل أداء المورد
    analyzeSupplierPerformance(supplierId) {
        const supplier = this.suppliers.get(supplierId);
        if (!supplier) {
            throw new Error('المورد غير موجود');
        }
        
        const evaluations = Array.from(this.supplierEvaluations.values())
            .filter(eval => eval.supplierId === supplierId);
        
        const payments = Array.from(this.supplierPayments.values())
            .filter(payment => payment.supplierId === supplierId);
        
        const orders = Array.from(this.supplierOrders.values())
            .filter(order => order.supplierId === supplierId);
        
        const analysis = {
            supplierId: supplierId,
            period: {
                from: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
                to: new Date().toISOString()
            },
            
            // تحليل الأداء
            performance: {
                averageRating: this.calculateAverageRating(evaluations),
                trendDirection: this.calculatePerformanceTrend(evaluations),
                consistencyScore: this.calculateConsistencyScore(evaluations),
                improvementAreas: this.identifyImprovementAreas(evaluations)
            },
            
            // تحليل مالي
            financial: {
                totalSpent: payments.reduce((sum, p) => sum + (p.status === 'paid' ? p.amount : 0), 0),
                averageOrderValue: orders.length > 0 ? orders.reduce((sum, o) => sum + o.total, 0) / orders.length : 0,
                paymentReliability: this.calculatePaymentReliability(payments),
                costEfficiency: this.calculateCostEfficiency(supplier, orders)
            },
            
            // تحليل التسليم
            delivery: {
                onTimeRate: this.calculateOnTimeDeliveryRate(orders),
                averageDeliveryTime: this.calculateAverageDeliveryTime(orders),
                reliabilityScore: this.calculateDeliveryReliability(orders)
            },
            
            // تحليل المخاطر
            risk: {
                currentLevel: supplier.risk.level,
                riskFactors: supplier.risk.factors,
                riskScore: this.calculateRiskScore(supplier),
                recommendations: this.generateRiskRecommendations(supplier)
            },
            
            // التوصيات
            recommendations: this.generateSupplierRecommendations(supplier, evaluations, orders),
            
            generatedAt: new Date().toISOString()
        };
        
        this.supplierPerformance.set(supplierId, analysis);
        this.saveData();
        
        return analysis;
    }

    // البحث في الموردين
    searchSuppliers(query, filters = {}) {
        let results = Array.from(this.suppliers.values());
        
        // البحث النصي
        if (query && query.trim()) {
            const searchTerm = query.toLowerCase();
            results = results.filter(supplier => 
                supplier.name.toLowerCase().includes(searchTerm) ||
                supplier.nameEn.toLowerCase().includes(searchTerm) ||
                supplier.code.toLowerCase().includes(searchTerm) ||
                supplier.contact.email.toLowerCase().includes(searchTerm) ||
                supplier.contact.phone.includes(searchTerm)
            );
        }
        
        // تطبيق الفلاتر
        if (filters.category) {
            results = results.filter(supplier => supplier.category === filters.category);
        }
        
        if (filters.status) {
            results = results.filter(supplier => supplier.status === filters.status);
        }
        
        if (filters.type) {
            results = results.filter(supplier => supplier.type === filters.type);
        }
        
        if (filters.riskLevel) {
            results = results.filter(supplier => supplier.risk.level === filters.riskLevel);
        }
        
        if (filters.isPreferred !== undefined) {
            results = results.filter(supplier => supplier.isPreferred === filters.isPreferred);
        }
        
        if (filters.minRating) {
            results = results.filter(supplier => supplier.performance.rating >= filters.minRating);
        }
        
        return results;
    }

    // الحصول على تقارير الموردين
    getSuppliersReports() {
        const suppliers = Array.from(this.suppliers.values());
        const contracts = Array.from(this.supplierContracts.values());
        const evaluations = Array.from(this.supplierEvaluations.values());
        const payments = Array.from(this.supplierPayments.values());
        
        return {
            summary: {
                totalSuppliers: suppliers.length,
                activeSuppliers: suppliers.filter(s => s.status === 'active').length,
                preferredSuppliers: suppliers.filter(s => s.isPreferred).length,
                averageRating: suppliers.reduce((sum, s) => sum + s.performance.rating, 0) / suppliers.length || 0
            },
            
            performance: {
                topPerformers: suppliers
                    .sort((a, b) => b.performance.rating - a.performance.rating)
                    .slice(0, 10),
                lowPerformers: suppliers
                    .filter(s => s.performance.rating < this.settings.performanceThresholds.average)
                    .sort((a, b) => a.performance.rating - b.performance.rating)
            },
            
            financial: {
                totalSpent: payments.reduce((sum, p) => sum + (p.status === 'paid' ? p.amount : 0), 0),
                pendingPayments: payments.filter(p => p.status === 'pending').length,
                overduePayments: payments.filter(p => p.status === 'overdue').length
            },
            
            contracts: {
                totalContracts: contracts.length,
                activeContracts: contracts.filter(c => c.status === 'active').length,
                expiringContracts: contracts.filter(c => {
                    const endDate = new Date(c.endDate);
                    const thirtyDaysFromNow = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
                    return endDate <= thirtyDaysFromNow && c.status === 'active';
                }).length
            },
            
            risk: {
                highRiskSuppliers: suppliers.filter(s => s.risk.level === 'عالي' || s.risk.level === 'حرج').length,
                riskDistribution: this.getRiskDistribution(suppliers)
            }
        };
    }

    // دوال مساعدة
    generateSupplierId() {
        return 'SUP_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    generateSupplierCode() {
        const count = this.suppliers.size + 1;
        return 'SUP' + count.toString().padStart(6, '0');
    }

    generateContractId() {
        return 'CON_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    generateEvaluationId() {
        return 'EVAL_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    generatePaymentId() {
        return 'PAY_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // دوال مساعدة إضافية
    initializeDefaultCategories() {
        if (this.supplierCategories.size === 0) {
            const defaultCategories = [
                { id: 'raw_materials', name: 'المواد الخام', description: 'موردو المواد الخام والأساسية' },
                { id: 'finished_goods', name: 'البضائع الجاهزة', description: 'موردو المنتجات النهائية' },
                { id: 'services', name: 'الخدمات', description: 'مقدمو الخدمات المختلفة' },
                { id: 'equipment', name: 'المعدات', description: 'موردو المعدات والآلات' },
                { id: 'technology', name: 'التكنولوجيا', description: 'موردو الحلول التقنية' },
                { id: 'logistics', name: 'اللوجستيات', description: 'شركات الشحن والنقل' }
            ];

            defaultCategories.forEach(category => {
                this.supplierCategories.set(category.id, category);
            });
        }
    }

    initializeSupplierPerformance(supplierId) {
        const performance = {
            supplierId: supplierId,
            metrics: {
                deliveryPerformance: [],
                qualityMetrics: [],
                serviceRatings: [],
                priceCompetitiveness: []
            },
            trends: {
                delivery: 'stable',
                quality: 'stable',
                service: 'stable',
                price: 'stable'
            },
            lastUpdated: new Date().toISOString()
        };

        this.supplierPerformance.set(supplierId, performance);
    }

    calculateEvaluationScore(criteria) {
        let totalScore = 0;
        let totalWeight = 0;

        Object.values(criteria).forEach(criterion => {
            totalScore += criterion.score * criterion.weight;
            totalWeight += criterion.weight;
        });

        return totalWeight > 0 ? totalScore / totalWeight : 0;
    }

    getPerformanceGrade(score) {
        if (score >= this.settings.performanceThresholds.excellent) return 'ممتاز';
        if (score >= this.settings.performanceThresholds.good) return 'جيد';
        if (score >= this.settings.performanceThresholds.average) return 'متوسط';
        if (score >= this.settings.performanceThresholds.poor) return 'ضعيف';
        return 'سيء جداً';
    }

    updateSupplierPerformance(supplierId, evaluation) {
        const supplier = this.suppliers.get(supplierId);
        if (supplier) {
            supplier.performance.rating = evaluation.totalScore;
            supplier.performance.deliveryScore = evaluation.criteria.delivery.score;
            supplier.performance.qualityScore = evaluation.criteria.quality.score;
            supplier.performance.serviceScore = evaluation.criteria.service.score;
            supplier.performance.priceScore = evaluation.criteria.price.score;
            supplier.performance.lastEvaluationDate = evaluation.evaluatedAt;
            supplier.updatedAt = new Date().toISOString();
        }
    }

    updateSupplierBalance(supplierId, amount) {
        const supplier = this.suppliers.get(supplierId);
        if (supplier) {
            supplier.financial.currentBalance += amount;
            supplier.financial.totalPurchases += amount;
            supplier.updatedAt = new Date().toISOString();
        }
    }

    calculateAverageRating(evaluations) {
        if (evaluations.length === 0) return 0;
        return evaluations.reduce((sum, eval) => sum + eval.totalScore, 0) / evaluations.length;
    }

    calculatePerformanceTrend(evaluations) {
        if (evaluations.length < 2) return 'stable';

        const sortedEvals = evaluations.sort((a, b) => new Date(a.evaluatedAt) - new Date(b.evaluatedAt));
        const recent = sortedEvals.slice(-3);
        const older = sortedEvals.slice(-6, -3);

        if (recent.length === 0 || older.length === 0) return 'stable';

        const recentAvg = recent.reduce((sum, eval) => sum + eval.totalScore, 0) / recent.length;
        const olderAvg = older.reduce((sum, eval) => sum + eval.totalScore, 0) / older.length;

        const difference = recentAvg - olderAvg;

        if (difference > 5) return 'improving';
        if (difference < -5) return 'declining';
        return 'stable';
    }

    calculateConsistencyScore(evaluations) {
        if (evaluations.length < 2) return 100;

        const scores = evaluations.map(eval => eval.totalScore);
        const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
        const standardDeviation = Math.sqrt(variance);

        // تحويل الانحراف المعياري إلى نقاط اتساق (كلما قل الانحراف، زاد الاتساق)
        return Math.max(0, 100 - (standardDeviation * 2));
    }

    identifyImprovementAreas(evaluations) {
        if (evaluations.length === 0) return [];

        const latestEval = evaluations[evaluations.length - 1];
        const areas = [];

        Object.entries(latestEval.criteria).forEach(([key, criterion]) => {
            if (criterion.score < this.settings.performanceThresholds.good) {
                areas.push({
                    area: key,
                    score: criterion.score,
                    priority: criterion.score < this.settings.performanceThresholds.poor ? 'high' : 'medium'
                });
            }
        });

        return areas.sort((a, b) => a.score - b.score);
    }

    calculatePaymentReliability(payments) {
        if (payments.length === 0) return 100;

        const paidOnTime = payments.filter(payment => {
            if (payment.status !== 'paid' || !payment.paidDate) return false;
            return new Date(payment.paidDate) <= new Date(payment.dueDate);
        }).length;

        return (paidOnTime / payments.length) * 100;
    }

    calculateCostEfficiency(supplier, orders) {
        if (orders.length === 0) return 0;

        // حساب متوسط التكلفة مقارنة بالسوق (افتراضي)
        const averageOrderValue = orders.reduce((sum, order) => sum + order.total, 0) / orders.length;
        const marketAverage = averageOrderValue * 1.1; // افتراض أن متوسط السوق أعلى بـ 10%

        return ((marketAverage - averageOrderValue) / marketAverage) * 100;
    }

    calculateOnTimeDeliveryRate(orders) {
        if (orders.length === 0) return 100;

        const onTimeDeliveries = orders.filter(order => {
            if (!order.deliveredDate || !order.expectedDeliveryDate) return false;
            return new Date(order.deliveredDate) <= new Date(order.expectedDeliveryDate);
        }).length;

        return (onTimeDeliveries / orders.length) * 100;
    }

    calculateAverageDeliveryTime(orders) {
        const deliveredOrders = orders.filter(order => order.deliveredDate && order.orderDate);

        if (deliveredOrders.length === 0) return 0;

        const totalDays = deliveredOrders.reduce((sum, order) => {
            const orderDate = new Date(order.orderDate);
            const deliveryDate = new Date(order.deliveredDate);
            const days = (deliveryDate - orderDate) / (1000 * 60 * 60 * 24);
            return sum + days;
        }, 0);

        return totalDays / deliveredOrders.length;
    }

    calculateDeliveryReliability(orders) {
        if (orders.length === 0) return 100;

        const reliableDeliveries = orders.filter(order => {
            if (!order.deliveredDate) return false;
            // اعتبار التسليم موثوق إذا كان في نطاق ±2 أيام من التاريخ المتوقع
            const expectedDate = new Date(order.expectedDeliveryDate);
            const actualDate = new Date(order.deliveredDate);
            const daysDifference = Math.abs((actualDate - expectedDate) / (1000 * 60 * 60 * 24));
            return daysDifference <= 2;
        }).length;

        return (reliableDeliveries / orders.length) * 100;
    }

    calculateRiskScore(supplier) {
        let riskScore = 0;

        // عوامل المخاطر
        const riskFactors = {
            'financial_instability': 30,
            'quality_issues': 25,
            'delivery_delays': 20,
            'communication_problems': 15,
            'regulatory_compliance': 10
        };

        supplier.risk.factors.forEach(factor => {
            riskScore += riskFactors[factor] || 5;
        });

        // تقليل المخاطر بناءً على الأداء
        const performanceReduction = (supplier.performance.rating / 100) * 20;
        riskScore = Math.max(0, riskScore - performanceReduction);

        return Math.min(100, riskScore);
    }

    generateRiskRecommendations(supplier) {
        const recommendations = [];

        if (supplier.risk.level === 'عالي' || supplier.risk.level === 'حرج') {
            recommendations.push('إجراء تقييم شامل للمورد');
            recommendations.push('وضع خطة طوارئ للموردين البديلين');
            recommendations.push('زيادة تكرار المراقبة والتقييم');
        }

        if (supplier.performance.rating < this.settings.performanceThresholds.average) {
            recommendations.push('وضع خطة تحسين الأداء');
            recommendations.push('تقديم التدريب والدعم الفني');
        }

        if (supplier.financial.currentBalance < 0) {
            recommendations.push('مراجعة شروط الدفع');
            recommendations.push('تقييم الوضع المالي للمورد');
        }

        return recommendations;
    }

    generateSupplierRecommendations(supplier, evaluations, orders) {
        const recommendations = [];

        // توصيات بناءً على الأداء
        if (supplier.performance.rating >= this.settings.performanceThresholds.excellent) {
            recommendations.push('ترقية المورد إلى قائمة الموردين المفضلين');
            recommendations.push('زيادة حجم الطلبات');
        }

        // توصيات بناءً على التقييمات
        const latestEval = evaluations[evaluations.length - 1];
        if (latestEval) {
            if (latestEval.criteria.delivery.score < this.settings.performanceThresholds.good) {
                recommendations.push('مناقشة تحسين أوقات التسليم');
            }
            if (latestEval.criteria.quality.score < this.settings.performanceThresholds.good) {
                recommendations.push('وضع معايير جودة أكثر صرامة');
            }
        }

        // توصيات بناءً على الطلبات
        if (orders.length > 0) {
            const avgDeliveryTime = this.calculateAverageDeliveryTime(orders);
            if (avgDeliveryTime > 14) {
                recommendations.push('مراجعة جداول التسليم');
            }
        }

        return recommendations;
    }

    getRiskDistribution(suppliers) {
        const distribution = {
            'منخفض': 0,
            'متوسط': 0,
            'عالي': 0,
            'حرج': 0
        };

        suppliers.forEach(supplier => {
            distribution[supplier.risk.level]++;
        });

        return distribution;
    }

    // حفظ وتحميل البيانات
    saveData() {
        try {
            localStorage.setItem('advancedSuppliersData', JSON.stringify({
                suppliers: Array.from(this.suppliers.entries()),
                categories: Array.from(this.supplierCategories.entries()),
                contracts: Array.from(this.supplierContracts.entries()),
                evaluations: Array.from(this.supplierEvaluations.entries()),
                payments: Array.from(this.supplierPayments.entries()),
                performance: Array.from(this.supplierPerformance.entries()),
                settings: this.settings
            }));
        } catch (error) {
            console.error('خطأ في حفظ بيانات الموردين:', error);
        }
    }

    loadData() {
        try {
            const data = localStorage.getItem('advancedSuppliersData');
            if (data) {
                const parsed = JSON.parse(data);
                this.suppliers = new Map(parsed.suppliers || []);
                this.supplierCategories = new Map(parsed.categories || []);
                this.supplierContracts = new Map(parsed.contracts || []);
                this.supplierEvaluations = new Map(parsed.evaluations || []);
                this.supplierPayments = new Map(parsed.payments || []);
                this.supplierPerformance = new Map(parsed.performance || []);
                if (parsed.settings) {
                    this.settings = { ...this.settings, ...parsed.settings };
                }
            }
        } catch (error) {
            console.error('خطأ في تحميل بيانات الموردين:', error);
        }
    }
}

// تصدير الفئة
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdvancedSuppliersManager;
}
