/**
 * <PERSON><PERSON><PERSON> and Auto-Fix Module
 * معالج الأخطاء والإصلاح التلقائي
 */

class ErrorHandler {
    constructor() {
        this.errors = [];
        this.fixes = new Map();
        this.autoFixEnabled = true;
        
        this.init();
    }
    
    /**
     * Initialize error handler
     */
    init() {
        console.log('🛠️ Initializing Error Handler...');
        
        // Setup global error handling
        this.setupGlobalErrorHandling();
        
        // Setup common fixes
        this.setupCommonFixes();
        
        // Setup auto-recovery
        this.setupAutoRecovery();
        
        console.log('✅ Error Handler initialized');
    }
    
    /**
     * Setup global error handling
     */
    setupGlobalErrorHandling() {
        // Handle JavaScript errors
        window.addEventListener('error', (event) => {
            this.handleError({
                type: 'javascript',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error,
                timestamp: new Date().toISOString()
            });
        });
        
        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError({
                type: 'promise',
                message: event.reason?.message || 'Unhandled promise rejection',
                reason: event.reason,
                timestamp: new Date().toISOString()
            });
        });
        
        // Handle resource loading errors
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.handleError({
                    type: 'resource',
                    message: `Failed to load resource: ${event.target.src || event.target.href}`,
                    element: event.target.tagName,
                    src: event.target.src || event.target.href,
                    timestamp: new Date().toISOString()
                });
            }
        }, true);
    }
    
    /**
     * Handle error
     */
    handleError(error) {
        console.error('🚨 Error detected:', error);
        
        // Store error
        this.errors.push(error);
        
        // Try to auto-fix
        if (this.autoFixEnabled) {
            this.attemptAutoFix(error);
        }
        
        // Show user-friendly message
        this.showUserFriendlyError(error);
        
        // Log for debugging
        this.logError(error);
    }
    
    /**
     * Setup common fixes
     */
    setupCommonFixes() {
        // Fix missing loadScript function
        this.fixes.set('loadScript_undefined', () => {
            if (typeof loadScript === 'undefined') {
                window.loadScript = function(src) {
                    return new Promise((resolve, reject) => {
                        const existingScript = document.querySelector(`script[src="${src}"]`);
                        if (existingScript) {
                            resolve();
                            return;
                        }
                        const script = document.createElement('script');
                        script.src = src;
                        script.onload = () => resolve();
                        script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
                        document.head.appendChild(script);
                    });
                };
                console.log('✅ Fixed: loadScript function added');
                return true;
            }
            return false;
        });
        
        // Fix missing Utils
        this.fixes.set('Utils_undefined', () => {
            if (typeof Utils === 'undefined') {
                window.Utils = {
                    showAlert: function(message, type = 'info') {
                        const alertDiv = document.createElement('div');
                        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
                        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
                        alertDiv.innerHTML = `
                            ${message}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        `;
                        document.body.appendChild(alertDiv);
                        setTimeout(() => alertDiv.remove(), 5000);
                    },
                    formatCurrency: function(amount) {
                        return new Intl.NumberFormat('ar-EG', {
                            style: 'currency',
                            currency: 'EGP'
                        }).format(amount || 0);
                    }
                };
                console.log('✅ Fixed: Utils object created');
                return true;
            }
            return false;
        });
        
        // Fix missing Database
        this.fixes.set('Database_undefined', () => {
            if (typeof Database === 'undefined') {
                window.Database = {
                    save: async function(table, data) {
                        try {
                            localStorage.setItem(`db_${table}`, JSON.stringify(data));
                            return true;
                        } catch (error) {
                            console.error('Database save error:', error);
                            return false;
                        }
                    },
                    get: async function(table) {
                        try {
                            const data = localStorage.getItem(`db_${table}`);
                            return data ? JSON.parse(data) : null;
                        } catch (error) {
                            console.error('Database get error:', error);
                            return null;
                        }
                    },
                    getAll: async function(table) {
                        try {
                            const data = localStorage.getItem(`db_${table}`);
                            return data ? JSON.parse(data) : [];
                        } catch (error) {
                            console.error('Database getAll error:', error);
                            return [];
                        }
                    }
                };
                console.log('✅ Fixed: Database object created');
                return true;
            }
            return false;
        });
        
        // Fix Bootstrap not loaded
        this.fixes.set('bootstrap_undefined', () => {
            if (typeof bootstrap === 'undefined') {
                const link = document.createElement('link');
                link.href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css';
                link.rel = 'stylesheet';
                document.head.appendChild(link);
                
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js';
                document.head.appendChild(script);
                
                console.log('✅ Fixed: Bootstrap loaded from CDN');
                return true;
            }
            return false;
        });
        
        // Fix missing CSS
        this.fixes.set('css_missing', () => {
            const customCSS = document.querySelector('link[href*="custom.css"]');
            if (!customCSS) {
                const link = document.createElement('link');
                link.href = 'src/assets/css/custom.css';
                link.rel = 'stylesheet';
                document.head.appendChild(link);
                console.log('✅ Fixed: Custom CSS loaded');
                return true;
            }
            return false;
        });
    }
    
    /**
     * Attempt auto-fix
     */
    attemptAutoFix(error) {
        let fixed = false;
        
        // Check for specific error patterns
        if (error.message.includes('loadScript is not defined')) {
            fixed = this.fixes.get('loadScript_undefined')();
        } else if (error.message.includes('Utils is not defined')) {
            fixed = this.fixes.get('Utils_undefined')();
        } else if (error.message.includes('Database is not defined')) {
            fixed = this.fixes.get('Database_undefined')();
        } else if (error.message.includes('bootstrap is not defined')) {
            fixed = this.fixes.get('bootstrap_undefined')();
        } else if (error.type === 'resource' && error.src?.includes('.css')) {
            fixed = this.fixes.get('css_missing')();
        }
        
        // Try common fixes
        if (!fixed) {
            for (const [key, fix] of this.fixes.entries()) {
                try {
                    if (fix()) {
                        console.log(`✅ Auto-fixed using: ${key}`);
                        fixed = true;
                        break;
                    }
                } catch (fixError) {
                    console.warn(`❌ Fix failed for ${key}:`, fixError);
                }
            }
        }
        
        return fixed;
    }
    
    /**
     * Setup auto-recovery
     */
    setupAutoRecovery() {
        // Auto-reload if too many errors
        setInterval(() => {
            const recentErrors = this.errors.filter(error => {
                const errorTime = new Date(error.timestamp);
                const now = new Date();
                return (now - errorTime) < 60000; // Last minute
            });
            
            if (recentErrors.length > 10) {
                console.warn('🔄 Too many errors detected, attempting recovery...');
                this.performRecovery();
            }
        }, 30000); // Check every 30 seconds
    }
    
    /**
     * Perform recovery
     */
    performRecovery() {
        // Clear errors
        this.errors = [];
        
        // Clear caches
        if (window.performanceOptimizer) {
            window.performanceOptimizer.cleanup();
        }
        
        // Reload critical resources
        this.reloadCriticalResources();
        
        // Show recovery message
        if (typeof Utils !== 'undefined') {
            Utils.showAlert('تم إجراء استرداد تلقائي للنظام', 'info');
        }
    }
    
    /**
     * Reload critical resources
     */
    reloadCriticalResources() {
        const criticalScripts = [
            'src/core/database.js',
            'src/core/utils.js',
            'src/assets/js/app.js'
        ];
        
        criticalScripts.forEach(src => {
            const existingScript = document.querySelector(`script[src="${src}"]`);
            if (!existingScript) {
                const script = document.createElement('script');
                script.src = src;
                document.head.appendChild(script);
            }
        });
    }
    
    /**
     * Show user-friendly error
     */
    showUserFriendlyError(error) {
        let message = 'حدث خطأ في النظام';
        
        switch (error.type) {
            case 'javascript':
                message = 'حدث خطأ في تشغيل النظام';
                break;
            case 'promise':
                message = 'حدث خطأ في معالجة البيانات';
                break;
            case 'resource':
                message = 'فشل في تحميل أحد الملفات';
                break;
        }
        
        // Only show critical errors to user
        if (this.isCriticalError(error)) {
            if (typeof Utils !== 'undefined' && Utils.showAlert) {
                Utils.showAlert(message, 'warning');
            } else {
                // Fallback alert
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-warning position-fixed';
                alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
                alertDiv.textContent = message;
                document.body.appendChild(alertDiv);
                setTimeout(() => alertDiv.remove(), 5000);
            }
        }
    }
    
    /**
     * Check if error is critical
     */
    isCriticalError(error) {
        const criticalPatterns = [
            'Database is not defined',
            'Utils is not defined',
            'loadScript is not defined',
            'bootstrap is not defined'
        ];
        
        return criticalPatterns.some(pattern => 
            error.message.includes(pattern)
        );
    }
    
    /**
     * Log error for debugging
     */
    logError(error) {
        try {
            const errorLog = JSON.parse(localStorage.getItem('error_log') || '[]');
            errorLog.push(error);
            
            // Keep only last 50 errors
            if (errorLog.length > 50) {
                errorLog.splice(0, errorLog.length - 50);
            }
            
            localStorage.setItem('error_log', JSON.stringify(errorLog));
        } catch (logError) {
            console.warn('Could not log error:', logError);
        }
    }
    
    /**
     * Get error log
     */
    getErrorLog() {
        try {
            return JSON.parse(localStorage.getItem('error_log') || '[]');
        } catch (error) {
            return [];
        }
    }
    
    /**
     * Clear error log
     */
    clearErrorLog() {
        localStorage.removeItem('error_log');
        this.errors = [];
        console.log('🧹 Error log cleared');
    }
    
    /**
     * Get error statistics
     */
    getErrorStats() {
        const errors = this.getErrorLog();
        const stats = {
            total: errors.length,
            byType: {},
            recent: 0,
            critical: 0
        };
        
        const now = new Date();
        errors.forEach(error => {
            // Count by type
            stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
            
            // Count recent (last hour)
            const errorTime = new Date(error.timestamp);
            if ((now - errorTime) < 3600000) {
                stats.recent++;
            }
            
            // Count critical
            if (this.isCriticalError(error)) {
                stats.critical++;
            }
        });
        
        return stats;
    }
    
    /**
     * Enable/disable auto-fix
     */
    setAutoFix(enabled) {
        this.autoFixEnabled = enabled;
        console.log(`🔧 Auto-fix ${enabled ? 'enabled' : 'disabled'}`);
    }
    
    /**
     * Add custom fix
     */
    addCustomFix(name, fixFunction) {
        this.fixes.set(name, fixFunction);
        console.log(`🔧 Custom fix added: ${name}`);
    }
    
    /**
     * Test all fixes
     */
    testFixes() {
        console.log('🧪 Testing all fixes...');
        
        for (const [name, fix] of this.fixes.entries()) {
            try {
                const result = fix();
                console.log(`${result ? '✅' : '⏭️'} ${name}: ${result ? 'Applied' : 'Not needed'}`);
            } catch (error) {
                console.error(`❌ ${name}: Failed -`, error);
            }
        }
    }
}

// Initialize error handler
const errorHandler = new ErrorHandler();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ErrorHandler;
} else {
    window.ErrorHandler = ErrorHandler;
    window.errorHandler = errorHandler;
}
