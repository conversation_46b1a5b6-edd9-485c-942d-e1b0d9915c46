/**
 * Point of Sale (POS) Module
 * Fast-selling interface with barcode scanning, thermal printing, and cash management
 */

class POSModule {
    constructor() {
        this.cart = [];
        this.currentCustomer = null;
        this.paymentMethods = [];
        this.cashDrawerBalance = 0;
        this.products = new Map();
        this.customers = new Map();
        this.barcodeBuffer = '';
        this.lastKeyTime = 0;
        this.currentInvoice = null;
    }

    /**
     * Initialize POS module
     */
    async initialize() {
        console.log('Initializing POS Module...');
        await this.loadProducts();
        await this.loadCustomers();
        await this.loadCashDrawerBalance();
        this.setupBarcodeScanner();
        this.setupKeyboardShortcuts();
    }

    /**
     * Show POS module
     */
    async show(params = {}) {
        await this.render();
        this.setupEventListeners();
        await this.loadFeaturedProducts();
    }

    /**
     * Render POS interface
     */
    async render() {
        const container = document.getElementById('moduleContainer');
        container.innerHTML = `
            <div class="pos-module">
                <!-- POS Header -->
                <div class="pos-header bg-primary text-white p-3 mb-3">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <h4 class="mb-0">
                                <i class="fas fa-cash-register me-2"></i>نقطة البيع
                            </h4>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="cash-drawer-info">
                                <small>رصيد الصندوق</small>
                                <h5 class="mb-0" id="cashDrawerDisplay">${Utils.formatCurrency(this.cashDrawerBalance)}</h5>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="btn-group">
                                <button class="btn btn-light btn-sm" onclick="POS.openCashDrawer()" title="فتح الصندوق">
                                    <i class="fas fa-cash-register"></i>
                                </button>
                                <button class="btn btn-light btn-sm" onclick="POS.dailyClosing()" title="إقفال يومي">
                                    <i class="fas fa-calendar-check"></i>
                                </button>
                                <button class="btn btn-light btn-sm" onclick="POS.showSettings()" title="الإعدادات">
                                    <i class="fas fa-cog"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Products Section -->
                    <div class="col-lg-8">
                        <div class="products-section">
                            <!-- Search and Barcode -->
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="row g-2">
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i class="fas fa-search"></i>
                                                </span>
                                                <input type="text" class="form-control" 
                                                       placeholder="البحث عن منتج أو مسح الباركود..." 
                                                       id="productSearch" autofocus>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-outline-primary w-100" onclick="POS.showBarcodeScanner()">
                                                <i class="fas fa-barcode me-1"></i>مسح باركود
                                            </button>
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-outline-success w-100" onclick="POS.showProductCategories()">
                                                <i class="fas fa-th-large me-1"></i>الفئات
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Featured Products Grid -->
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">المنتجات المميزة</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row" id="featuredProductsGrid">
                                        <!-- Products will be loaded here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Cart and Payment Section -->
                    <div class="col-lg-4">
                        <div class="cart-section">
                            <!-- Customer Selection -->
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">العميل</h6>
                                        <button class="btn btn-sm btn-outline-primary" onclick="POS.showAddCustomerModal()">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                    <select class="form-select" id="customerSelect">
                                        <option value="">عميل نقدي</option>
                                        ${Array.from(this.customers.values()).map(customer => 
                                            `<option value="${customer.id}">${customer.name}</option>`
                                        ).join('')}
                                    </select>
                                </div>
                            </div>

                            <!-- Cart Items -->
                            <div class="card mb-3">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">سلة المشتريات</h6>
                                    <button class="btn btn-sm btn-outline-danger" onclick="POS.clearCart()">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                                <div class="card-body p-0">
                                    <div class="cart-items" id="cartItems" style="max-height: 300px; overflow-y: auto;">
                                        <!-- Cart items will be displayed here -->
                                    </div>
                                </div>
                            </div>

                            <!-- Cart Summary -->
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="cart-summary">
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>المجموع الفرعي:</span>
                                            <span id="cartSubtotal">0.00 ج.م</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>الخصم:</span>
                                            <div class="input-group input-group-sm" style="width: 100px;">
                                                <input type="number" class="form-control" id="discountAmount" 
                                                       value="0" min="0" onchange="POS.updateCartSummary()">
                                                <span class="input-group-text">ج.م</span>
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>الضريبة (14%):</span>
                                            <span id="cartTax">0.00 ج.م</span>
                                        </div>
                                        <hr>
                                        <div class="d-flex justify-content-between fw-bold">
                                            <span>الإجمالي:</span>
                                            <span id="cartTotal">0.00 ج.م</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Methods -->
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h6 class="mb-0">طريقة الدفع</h6>
                                </div>
                                <div class="card-body">
                                    <div class="payment-methods">
                                        <div class="row g-2">
                                            <div class="col-6">
                                                <button class="btn btn-outline-success w-100 payment-method-btn" 
                                                        data-method="cash" onclick="POS.selectPaymentMethod('cash')">
                                                    <i class="fas fa-money-bill-wave d-block mb-1"></i>
                                                    <small>نقدي</small>
                                                </button>
                                            </div>
                                            <div class="col-6">
                                                <button class="btn btn-outline-info w-100 payment-method-btn" 
                                                        data-method="credit" onclick="POS.selectPaymentMethod('credit')">
                                                    <i class="fas fa-credit-card d-block mb-1"></i>
                                                    <small>آجل</small>
                                                </button>
                                            </div>
                                            <div class="col-6">
                                                <button class="btn btn-outline-danger w-100 payment-method-btn" 
                                                        data-method="vodafone_cash" onclick="POS.selectPaymentMethod('vodafone_cash')">
                                                    <i class="fas fa-mobile-alt d-block mb-1"></i>
                                                    <small>فودافون كاش</small>
                                                </button>
                                            </div>
                                            <div class="col-6">
                                                <button class="btn btn-outline-warning w-100 payment-method-btn" 
                                                        data-method="instapay" onclick="POS.selectPaymentMethod('instapay')">
                                                    <i class="fas fa-university d-block mb-1"></i>
                                                    <small>انستاباي</small>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="selected-payment mt-2" id="selectedPayment" style="display: none;">
                                            <div class="alert alert-info mb-0">
                                                <small>طريقة الدفع المحددة: <span id="selectedPaymentText"></span></small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-primary btn-lg" onclick="POS.processPayment()" 
                                                id="processPaymentBtn" disabled>
                                            <i class="fas fa-credit-card me-2"></i>إتمام البيع
                                        </button>
                                        <div class="row g-2">
                                            <div class="col-6">
                                                <button class="btn btn-outline-secondary w-100" onclick="POS.holdTransaction()">
                                                    <i class="fas fa-pause me-1"></i>تعليق
                                                </button>
                                            </div>
                                            <div class="col-6">
                                                <button class="btn btn-outline-info w-100" onclick="POS.showReturns()">
                                                    <i class="fas fa-undo me-1"></i>مرتجع
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modals -->
            ${this.renderModals()}
        `;
    }

    /**
     * Render modals
     */
    renderModals() {
        return `
            <!-- Payment Processing Modal -->
            <div class="modal fade" id="paymentModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إتمام عملية الدفع</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <!-- Payment processing content -->
                            <div class="text-center py-4">
                                <i class="fas fa-credit-card fa-3x text-primary mb-3"></i>
                                <h5>معالجة الدفع</h5>
                                <p class="text-muted">قريباً - نموذج معالجة الدفع قيد التطوير</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add Customer Modal -->
            <div class="modal fade" id="addCustomerModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إضافة عميل جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <!-- Add customer form -->
                            <div class="text-center py-4">
                                <i class="fas fa-user-plus fa-3x text-success mb-3"></i>
                                <h5>إضافة عميل جديد</h5>
                                <p class="text-muted">قريباً - نموذج إضافة العميل قيد التطوير</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Product search
        const searchInput = document.getElementById('productSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.searchProducts(e.target.value);
                }, 300);
            });
        }

        // Customer selection
        const customerSelect = document.getElementById('customerSelect');
        if (customerSelect) {
            customerSelect.addEventListener('change', (e) => {
                this.selectCustomer(e.target.value);
            });
        }

        // Discount input
        const discountInput = document.getElementById('discountAmount');
        if (discountInput) {
            discountInput.addEventListener('change', () => {
                this.updateCartSummary();
            });
        }
    }

    /**
     * Setup barcode scanner
     */
    setupBarcodeScanner() {
        document.addEventListener('keypress', (e) => {
            const currentTime = Date.now();

            // If more than 100ms since last key, reset buffer
            if (currentTime - this.lastKeyTime > 100) {
                this.barcodeBuffer = '';
            }

            this.lastKeyTime = currentTime;

            // Add character to buffer
            if (e.key !== 'Enter') {
                this.barcodeBuffer += e.key;
            } else {
                // Enter pressed - process barcode
                if (this.barcodeBuffer.length > 5) {
                    this.processBarcode(this.barcodeBuffer);
                    this.barcodeBuffer = '';
                }
            }
        });
    }

    /**
     * Setup keyboard shortcuts
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Only process if not in input field
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;

            switch (e.key) {
                case 'F1':
                    e.preventDefault();
                    this.processPayment();
                    break;
                case 'F2':
                    e.preventDefault();
                    this.clearCart();
                    break;
                case 'F3':
                    e.preventDefault();
                    this.holdTransaction();
                    break;
                case 'F4':
                    e.preventDefault();
                    this.showReturns();
                    break;
                case 'Escape':
                    e.preventDefault();
                    this.clearCart();
                    break;
            }
        });
    }

    /**
     * Load products
     */
    async loadProducts() {
        try {
            const products = await Database.getAll('products');
            this.products.clear();
            products.forEach(product => {
                this.products.set(product.id, product);
            });
        } catch (error) {
            console.error('Error loading products:', error);
        }
    }

    /**
     * Load customers
     */
    async loadCustomers() {
        try {
            const customers = await Database.getAll('customers');
            this.customers.clear();
            customers.forEach(customer => {
                this.customers.set(customer.id, customer);
            });
        } catch (error) {
            console.error('Error loading customers:', error);
        }
    }

    /**
     * Load cash drawer balance
     */
    async loadCashDrawerBalance() {
        try {
            // Get today's cash transactions
            const today = new Date().toISOString().split('T')[0];
            const transactions = await Database.getAll('cash_transactions');
            const todayTransactions = transactions.filter(t => t.date === today);

            this.cashDrawerBalance = todayTransactions.reduce((sum, t) => {
                return sum + (t.type === 'in' ? t.amount : -t.amount);
            }, 0);

            // Update display
            const display = document.getElementById('cashDrawerDisplay');
            if (display) {
                display.textContent = Utils.formatCurrency(this.cashDrawerBalance);
            }
        } catch (error) {
            console.error('Error loading cash drawer balance:', error);
        }
    }

    /**
     * Load featured products
     */
    async loadFeaturedProducts() {
        try {
            const products = Array.from(this.products.values())
                .filter(p => p.is_active && p.stock_quantity > 0)
                .slice(0, 12); // Show first 12 products

            this.renderFeaturedProducts(products);
        } catch (error) {
            console.error('Error loading featured products:', error);
        }
    }

    /**
     * Render featured products grid
     */
    renderFeaturedProducts(products) {
        const grid = document.getElementById('featuredProductsGrid');
        if (!grid) return;

        grid.innerHTML = products.map(product => `
            <div class="col-md-4 col-sm-6 mb-3">
                <div class="card product-card h-100" onclick="POS.addToCart('${product.id}')">
                    <div class="card-body text-center p-2">
                        <div class="product-image mb-2">
                            ${product.image_url ?
                                `<img src="${product.image_url}" alt="${product.name}" class="img-fluid" style="max-height: 60px;">` :
                                `<i class="fas fa-box fa-2x text-muted"></i>`
                            }
                        </div>
                        <h6 class="card-title small mb-1">${product.name}</h6>
                        <p class="card-text small text-muted mb-1">${Utils.formatCurrency(product.selling_price)}</p>
                        <small class="text-success">متوفر: ${product.stock_quantity}</small>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * Search products
     */
    async searchProducts(query) {
        if (!query.trim()) {
            await this.loadFeaturedProducts();
            return;
        }

        const filteredProducts = Array.from(this.products.values())
            .filter(product => {
                const searchTerm = query.toLowerCase();
                return product.name.toLowerCase().includes(searchTerm) ||
                       product.barcode?.toLowerCase().includes(searchTerm) ||
                       product.sku?.toLowerCase().includes(searchTerm);
            })
            .slice(0, 12);

        this.renderFeaturedProducts(filteredProducts);
    }

    /**
     * Process barcode
     */
    async processBarcode(barcode) {
        const product = Array.from(this.products.values())
            .find(p => p.barcode === barcode);

        if (product) {
            await this.addToCart(product.id);
            Utils.showToast('تم الإضافة', `تم إضافة ${product.name} إلى السلة`, 'success');
        } else {
            Utils.showToast('غير موجود', 'المنتج غير موجود', 'warning');
        }
    }

    /**
     * Add product to cart
     */
    async addToCart(productId, quantity = 1) {
        const product = this.products.get(productId);
        if (!product) return;

        // Check stock
        if (product.stock_quantity < quantity) {
            Utils.showToast('مخزون غير كافي', `المتوفر: ${product.stock_quantity}`, 'warning');
            return;
        }

        // Check if product already in cart
        const existingItem = this.cart.find(item => item.product_id === productId);

        if (existingItem) {
            existingItem.quantity += quantity;
            existingItem.total_price = existingItem.quantity * existingItem.unit_price;
        } else {
            this.cart.push({
                product_id: productId,
                product_name: product.name,
                quantity: quantity,
                unit_price: product.selling_price,
                total_price: quantity * product.selling_price,
                discount: 0
            });
        }

        this.renderCart();
        this.updateCartSummary();
    }

    /**
     * Remove item from cart
     */
    removeFromCart(productId) {
        this.cart = this.cart.filter(item => item.product_id !== productId);
        this.renderCart();
        this.updateCartSummary();
    }

    /**
     * Update cart item quantity
     */
    updateCartItemQuantity(productId, quantity) {
        const item = this.cart.find(item => item.product_id === productId);
        if (item) {
            const product = this.products.get(productId);
            if (product && quantity <= product.stock_quantity) {
                item.quantity = quantity;
                item.total_price = quantity * item.unit_price;
                this.renderCart();
                this.updateCartSummary();
            } else {
                Utils.showToast('مخزون غير كافي', `المتوفر: ${product.stock_quantity}`, 'warning');
            }
        }
    }

    /**
     * Render cart
     */
    renderCart() {
        const cartContainer = document.getElementById('cartItems');
        if (!cartContainer) return;

        if (this.cart.length === 0) {
            cartContainer.innerHTML = `
                <div class="text-center py-4 text-muted">
                    <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                    <p>السلة فارغة</p>
                </div>
            `;
            return;
        }

        cartContainer.innerHTML = this.cart.map(item => `
            <div class="cart-item border-bottom p-2">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${item.product_name}</h6>
                        <small class="text-muted">${Utils.formatCurrency(item.unit_price)} × ${item.quantity}</small>
                    </div>
                    <div class="text-end">
                        <div class="fw-bold">${Utils.formatCurrency(item.total_price)}</div>
                        <button class="btn btn-sm btn-outline-danger" onclick="POS.removeFromCart('${item.product_id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="mt-2">
                    <div class="input-group input-group-sm">
                        <button class="btn btn-outline-secondary" onclick="POS.updateCartItemQuantity('${item.product_id}', ${item.quantity - 1})">
                            <i class="fas fa-minus"></i>
                        </button>
                        <input type="number" class="form-control text-center" value="${item.quantity}"
                               min="1" onchange="POS.updateCartItemQuantity('${item.product_id}', parseInt(this.value))">
                        <button class="btn btn-outline-secondary" onclick="POS.updateCartItemQuantity('${item.product_id}', ${item.quantity + 1})">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * Update cart summary
     */
    updateCartSummary() {
        const subtotal = this.cart.reduce((sum, item) => sum + item.total_price, 0);
        const discountAmount = parseFloat(document.getElementById('discountAmount')?.value || 0);
        const tax = Math.round((subtotal - discountAmount) * 0.14);
        const total = subtotal - discountAmount + tax;

        // Update display
        const subtotalEl = document.getElementById('cartSubtotal');
        const taxEl = document.getElementById('cartTax');
        const totalEl = document.getElementById('cartTotal');
        const processBtn = document.getElementById('processPaymentBtn');

        if (subtotalEl) subtotalEl.textContent = Utils.formatCurrency(subtotal);
        if (taxEl) taxEl.textContent = Utils.formatCurrency(tax);
        if (totalEl) totalEl.textContent = Utils.formatCurrency(total);
        if (processBtn) processBtn.disabled = this.cart.length === 0;
    }

    /**
     * Select customer
     */
    selectCustomer(customerId) {
        this.currentCustomer = customerId ? this.customers.get(customerId) : null;
    }

    /**
     * Select payment method
     */
    selectPaymentMethod(method) {
        // Remove active class from all buttons
        document.querySelectorAll('.payment-method-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // Add active class to selected button
        const selectedBtn = document.querySelector(`[data-method="${method}"]`);
        if (selectedBtn) {
            selectedBtn.classList.add('active');
        }

        // Show selected payment method
        const selectedPayment = document.getElementById('selectedPayment');
        const selectedPaymentText = document.getElementById('selectedPaymentText');

        if (selectedPayment && selectedPaymentText) {
            const methodNames = {
                'cash': 'نقدي',
                'credit': 'آجل',
                'vodafone_cash': 'فودافون كاش',
                'instapay': 'انستاباي'
            };

            selectedPaymentText.textContent = methodNames[method];
            selectedPayment.style.display = 'block';
        }

        this.selectedPaymentMethod = method;
    }

    /**
     * Clear cart
     */
    clearCart() {
        this.cart = [];
        this.currentCustomer = null;
        this.selectedPaymentMethod = null;

        // Reset UI
        const customerSelect = document.getElementById('customerSelect');
        if (customerSelect) customerSelect.value = '';

        const discountInput = document.getElementById('discountAmount');
        if (discountInput) discountInput.value = '0';

        const selectedPayment = document.getElementById('selectedPayment');
        if (selectedPayment) selectedPayment.style.display = 'none';

        document.querySelectorAll('.payment-method-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        this.renderCart();
        this.updateCartSummary();
    }

    // Placeholder methods for UI actions
    async processPayment() {
        if (this.cart.length === 0) {
            Utils.showToast('خطأ', 'السلة فارغة', 'error');
            return;
        }

        if (!this.selectedPaymentMethod) {
            Utils.showToast('خطأ', 'يرجى اختيار طريقة الدفع', 'error');
            return;
        }

        const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
        modal.show();
    }

    async holdTransaction() {
        if (this.cart.length === 0) {
            Utils.showToast('خطأ', 'السلة فارغة', 'error');
            return;
        }
        Utils.showToast('قريباً', 'ميزة تعليق المعاملة قيد التطوير', 'info');
    }

    async showReturns() {
        Utils.showToast('قريباً', 'ميزة المرتجعات قيد التطوير', 'info');
    }

    async showBarcodeScanner() {
        Utils.showToast('قريباً', 'ميزة ماسح الباركود قيد التطوير', 'info');
    }

    async showProductCategories() {
        Utils.showToast('قريباً', 'ميزة فئات المنتجات قيد التطوير', 'info');
    }

    async showAddCustomerModal() {
        const modal = new bootstrap.Modal(document.getElementById('addCustomerModal'));
        modal.show();
    }

    async openCashDrawer() {
        Utils.showToast('قريباً', 'ميزة فتح الصندوق قيد التطوير', 'info');
    }

    async dailyClosing() {
        Utils.showToast('قريباً', 'ميزة الإقفال اليومي قيد التطوير', 'info');
    }

    async showSettings() {
        Utils.showToast('قريباً', 'ميزة إعدادات نقطة البيع قيد التطوير', 'info');
    }
}

// Create global instance
window.POS = new POSModule();
