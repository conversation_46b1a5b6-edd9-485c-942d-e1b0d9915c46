const { contextBridge, ipcRenderer } = require('electron');

// تعريض APIs آمنة للواجهة الأمامية
contextBridge.exposeInMainWorld('electronAPI', {
    // معلومات التطبيق
    getAppVersion: () => ipcRenderer.invoke('get-app-version'),
    getAppPath: () => ipcRenderer.invoke('get-app-path'),
    
    // نوافذ الحوار
    showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
    showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
    showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
    
    // إدارة الملفات
    readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),
    writeFile: (filePath, data) => ipcRenderer.invoke('write-file', filePath, data),
    deleteFile: (filePath) => ipcRenderer.invoke('delete-file', filePath),
    fileExists: (filePath) => ipcRenderer.invoke('file-exists', filePath),
    
    // إدارة المجلدات
    createDirectory: (dirPath) => ipcRenderer.invoke('create-directory', dirPath),
    readDirectory: (dirPath) => ipcRenderer.invoke('read-directory', dirPath),
    
    // قاعدة البيانات المحلية
    setStoreValue: (key, value) => ipcRenderer.invoke('set-store-value', key, value),
    getStoreValue: (key) => ipcRenderer.invoke('get-store-value', key),
    deleteStoreValue: (key) => ipcRenderer.invoke('delete-store-value', key),
    clearStore: () => ipcRenderer.invoke('clear-store'),
    
    // الطباعة
    print: (options) => ipcRenderer.invoke('print', options),
    printToPDF: (options) => ipcRenderer.invoke('print-to-pdf', options),
    
    // النظام
    getSystemInfo: () => ipcRenderer.invoke('get-system-info'),
    openExternal: (url) => ipcRenderer.invoke('open-external', url),
    
    // الإشعارات
    showNotification: (title, body, options) => ipcRenderer.invoke('show-notification', title, body, options),
    
    // الأحداث
    on: (channel, callback) => {
        const validChannels = [
            'open-settings',
            'module-loaded',
            'data-updated',
            'print-ready',
            'notification-clicked'
        ];
        if (validChannels.includes(channel)) {
            ipcRenderer.on(channel, callback);
        }
    },
    
    removeListener: (channel, callback) => {
        ipcRenderer.removeListener(channel, callback);
    },
    
    // إرسال الأحداث
    send: (channel, data) => {
        const validChannels = [
            'settings-updated',
            'module-action',
            'data-changed',
            'print-request',
            'window-minimize',
            'window-maximize',
            'window-close',
            'window-restore',
            'toggle-fullscreen',
            'load-module',
            'open-module-window',
            'create-backup'
        ];
        if (validChannels.includes(channel)) {
            ipcRenderer.send(channel, data);
        }
    },

    // إدارة النوافذ
    window: {
        minimize: () => ipcRenderer.send('window-minimize'),
        maximize: () => ipcRenderer.send('window-maximize'),
        restore: () => ipcRenderer.send('window-restore'),
        close: () => ipcRenderer.send('window-close'),
        toggleFullscreen: () => ipcRenderer.send('toggle-fullscreen'),
        isMaximized: () => ipcRenderer.invoke('window-is-maximized'),
        isMinimized: () => ipcRenderer.invoke('window-is-minimized'),
        isFullscreen: () => ipcRenderer.invoke('window-is-fullscreen')
    },

    // إدارة المديولات
    modules: {
        load: (moduleName) => ipcRenderer.send('load-module', moduleName),
        openWindow: (moduleName) => ipcRenderer.send('open-module-window', moduleName),
        getAvailable: () => ipcRenderer.invoke('get-available-modules'),
        isLoaded: (moduleName) => ipcRenderer.invoke('is-module-loaded', moduleName)
    }
});

// APIs خاصة بنظام ERP
contextBridge.exposeInMainWorld('erpAPI', {
    // إدارة المخزون
    inventory: {
        getProducts: () => ipcRenderer.invoke('inventory-get-products'),
        addProduct: (product) => ipcRenderer.invoke('inventory-add-product', product),
        updateProduct: (id, product) => ipcRenderer.invoke('inventory-update-product', id, product),
        deleteProduct: (id) => ipcRenderer.invoke('inventory-delete-product', id),
        searchProducts: (query) => ipcRenderer.invoke('inventory-search-products', query),
        getCategories: () => ipcRenderer.invoke('inventory-get-categories'),
        updateStock: (productId, quantity, type) => ipcRenderer.invoke('inventory-update-stock', productId, quantity, type)
    },
    
    // إدارة المبيعات
    sales: {
        getSales: () => ipcRenderer.invoke('sales-get-sales'),
        addSale: (sale) => ipcRenderer.invoke('sales-add-sale', sale),
        updateSale: (id, sale) => ipcRenderer.invoke('sales-update-sale', id, sale),
        deleteSale: (id) => ipcRenderer.invoke('sales-delete-sale', id),
        getCustomers: () => ipcRenderer.invoke('sales-get-customers'),
        addCustomer: (customer) => ipcRenderer.invoke('sales-add-customer', customer),
        generateInvoice: (saleId) => ipcRenderer.invoke('sales-generate-invoice', saleId)
    },
    
    // نقاط البيع
    pos: {
        processTransaction: (transaction) => ipcRenderer.invoke('pos-process-transaction', transaction),
        printReceipt: (receiptData) => ipcRenderer.invoke('pos-print-receipt', receiptData),
        openCashDrawer: () => ipcRenderer.invoke('pos-open-cash-drawer'),
        getPaymentMethods: () => ipcRenderer.invoke('pos-get-payment-methods'),
        validateBarcode: (barcode) => ipcRenderer.invoke('pos-validate-barcode', barcode)
    },
    
    // إدارة المشتريات
    purchases: {
        getPurchaseOrders: () => ipcRenderer.invoke('purchases-get-orders'),
        addPurchaseOrder: (order) => ipcRenderer.invoke('purchases-add-order', order),
        updatePurchaseOrder: (id, order) => ipcRenderer.invoke('purchases-update-order', id, order),
        getSuppliers: () => ipcRenderer.invoke('purchases-get-suppliers'),
        addSupplier: (supplier) => ipcRenderer.invoke('purchases-add-supplier', supplier),
        receiveGoods: (orderId, items) => ipcRenderer.invoke('purchases-receive-goods', orderId, items)
    },
    
    // إدارة الموظفين
    hr: {
        getEmployees: () => ipcRenderer.invoke('hr-get-employees'),
        addEmployee: (employee) => ipcRenderer.invoke('hr-add-employee', employee),
        updateEmployee: (id, employee) => ipcRenderer.invoke('hr-update-employee', id, employee),
        deleteEmployee: (id) => ipcRenderer.invoke('hr-delete-employee', id),
        markAttendance: (employeeId, type) => ipcRenderer.invoke('hr-mark-attendance', employeeId, type),
        getAttendance: (employeeId, date) => ipcRenderer.invoke('hr-get-attendance', employeeId, date),
        processPayroll: (month, year) => ipcRenderer.invoke('hr-process-payroll', month, year),
        addLeaveRequest: (request) => ipcRenderer.invoke('hr-add-leave-request', request)
    },
    
    // إدارة الصرافين
    cashier: {
        getCashiers: () => ipcRenderer.invoke('cashier-get-cashiers'),
        addCashier: (cashier) => ipcRenderer.invoke('cashier-add-cashier', cashier),
        updateCashier: (id, cashier) => ipcRenderer.invoke('cashier-update-cashier', id, cashier),
        getShifts: () => ipcRenderer.invoke('cashier-get-shifts'),
        startShift: (cashierId, openingAmount) => ipcRenderer.invoke('cashier-start-shift', cashierId, openingAmount),
        endShift: (shiftId, closingAmount) => ipcRenderer.invoke('cashier-end-shift', shiftId, closingAmount)
    },
    
    // التقارير
    reports: {
        generateSalesReport: (params) => ipcRenderer.invoke('reports-generate-sales', params),
        generateInventoryReport: (params) => ipcRenderer.invoke('reports-generate-inventory', params),
        generateFinancialReport: (params) => ipcRenderer.invoke('reports-generate-financial', params),
        generateHRReport: (params) => ipcRenderer.invoke('reports-generate-hr', params),
        exportReport: (reportData, format) => ipcRenderer.invoke('reports-export', reportData, format)
    },
    
    // الإعدادات
    settings: {
        getSettings: () => ipcRenderer.invoke('settings-get-all'),
        updateSetting: (key, value) => ipcRenderer.invoke('settings-update', key, value),
        resetSettings: () => ipcRenderer.invoke('settings-reset'),
        exportSettings: () => ipcRenderer.invoke('settings-export'),
        importSettings: (settingsData) => ipcRenderer.invoke('settings-import', settingsData),
        getUsers: () => ipcRenderer.invoke('settings-get-users'),
        addUser: (user) => ipcRenderer.invoke('settings-add-user', user),
        updateUser: (id, user) => ipcRenderer.invoke('settings-update-user', id, user),
        deleteUser: (id) => ipcRenderer.invoke('settings-delete-user', id)
    },
    
    // النسخ الاحتياطي
    backup: {
        createBackup: () => ipcRenderer.invoke('backup-create'),
        restoreBackup: (backupPath) => ipcRenderer.invoke('backup-restore', backupPath),
        getBackupList: () => ipcRenderer.invoke('backup-get-list'),
        deleteBackup: (backupId) => ipcRenderer.invoke('backup-delete', backupId),
        scheduleBackup: (schedule) => ipcRenderer.invoke('backup-schedule', schedule)
    }
});

// إضافة وظائف مساعدة عامة
contextBridge.exposeInMainWorld('utils', {
    // تنسيق التاريخ والوقت
    formatDate: (date, format = 'YYYY-MM-DD') => {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        
        switch (format) {
            case 'DD/MM/YYYY':
                return `${day}/${month}/${year}`;
            case 'MM/DD/YYYY':
                return `${month}/${day}/${year}`;
            case 'YYYY-MM-DD':
            default:
                return `${year}-${month}-${day}`;
        }
    },
    
    formatTime: (date) => {
        const d = new Date(date);
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        return `${hours}:${minutes}`;
    },
    
    formatDateTime: (date) => {
        return `${utils.formatDate(date)} ${utils.formatTime(date)}`;
    },
    
    // تنسيق العملة
    formatCurrency: (amount, currency = 'EGP') => {
        const formatter = new Intl.NumberFormat('ar-EG', {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 2
        });
        return formatter.format(amount);
    },
    
    // تنسيق الأرقام
    formatNumber: (number, decimals = 2) => {
        return new Intl.NumberFormat('ar-EG', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(number);
    },
    
    // التحقق من صحة البيانات
    validateEmail: (email) => {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },
    
    validatePhone: (phone) => {
        const re = /^[\+]?[1-9][\d]{0,15}$/;
        return re.test(phone);
    },
    
    validateBarcode: (barcode) => {
        return barcode && barcode.length >= 8 && /^\d+$/.test(barcode);
    },
    
    // إنشاء معرفات فريدة
    generateId: () => {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },
    
    generateBarcode: () => {
        return Math.floor(Math.random() * 9000000000000) + 1000000000000;
    },
    
    // تشفير وفك تشفير بسيط
    encodeBase64: (str) => {
        return btoa(unescape(encodeURIComponent(str)));
    },
    
    decodeBase64: (str) => {
        return decodeURIComponent(escape(atob(str)));
    },
    
    // حفظ واستعادة البيانات المحلية
    saveToLocalStorage: (key, data) => {
        localStorage.setItem(key, JSON.stringify(data));
    },
    
    loadFromLocalStorage: (key) => {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    },
    
    removeFromLocalStorage: (key) => {
        localStorage.removeItem(key);
    },
    
    // إدارة الجلسة
    setSessionData: (key, data) => {
        sessionStorage.setItem(key, JSON.stringify(data));
    },
    
    getSessionData: (key) => {
        const data = sessionStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    },
    
    clearSession: () => {
        sessionStorage.clear();
    }
});

// إضافة معلومات النظام
contextBridge.exposeInMainWorld('systemInfo', {
    platform: process.platform,
    arch: process.arch,
    version: process.version,
    isWindows: process.platform === 'win32',
    isMac: process.platform === 'darwin',
    isLinux: process.platform === 'linux'
});

// إضافة دعم للطباعة الحرارية
contextBridge.exposeInMainWorld('thermalPrinter', {
    isAvailable: () => ipcRenderer.invoke('thermal-printer-available'),
    print: (data) => ipcRenderer.invoke('thermal-printer-print', data),
    getStatus: () => ipcRenderer.invoke('thermal-printer-status'),
    configure: (settings) => ipcRenderer.invoke('thermal-printer-configure', settings)
});

console.log('Preload script loaded successfully - Black Horse ERP');
