<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Black Horse ERP - نظام إدارة موارد المؤسسات</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', 'Tahoma', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1e3c72 100%);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
            user-select: none;
            -webkit-app-region: no-drag;
        }
        
        .app-container {
            display: flex;
            height: 100vh;
            position: relative;
        }
        
        /* الشريط الجانبي */
        .sidebar {
            width: 300px;
            background: rgba(30, 60, 114, 0.95);
            backdrop-filter: blur(15px);
            border-left: 2px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 5px 0 20px rgba(0, 0, 0, 0.3);
        }
        
        .sidebar.collapsed {
            width: 80px;
        }
        
        .logo-section {
            padding: 25px;
            text-align: center;
            border-bottom: 2px solid rgba(255, 255, 255, 0.1);
            background: linear-gradient(135deg, rgba(42, 82, 152, 0.8), rgba(30, 60, 114, 0.8));
            position: relative;
            overflow: hidden;
        }
        
        .logo-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: logoGlow 4s ease-in-out infinite;
        }
        
        .logo {
            font-size: 3rem;
            margin-bottom: 15px;
            animation: horseBounce 3s ease-in-out infinite;
            position: relative;
            z-index: 2;
        }
        
        .logo-text {
            font-size: 1.4rem;
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 8px;
            position: relative;
            z-index: 2;
        }
        
        .logo-subtitle {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.8);
            position: relative;
            z-index: 2;
        }
        
        .nav-menu {
            flex: 1;
            padding: 25px 0;
            overflow-y: auto;
        }
        
        .nav-item {
            display: flex;
            align-items: center;
            padding: 18px 25px;
            margin: 8px 15px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        
        .nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            right: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: right 0.6s ease;
        }
        
        .nav-item:hover::before {
            right: 100%;
        }
        
        .nav-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateX(-8px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
        
        .nav-item.active {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            box-shadow: 0 10px 30px rgba(76, 175, 80, 0.4);
            transform: translateX(-5px);
        }
        
        .nav-icon {
            font-size: 1.4rem;
            margin-left: 18px;
            min-width: 25px;
            transition: all 0.3s ease;
        }
        
        .nav-item:hover .nav-icon {
            transform: scale(1.2) rotate(5deg);
        }
        
        .nav-text {
            font-size: 1.1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .sidebar.collapsed .nav-text {
            opacity: 0;
            transform: translateX(20px);
        }
        
        /* المحتوى الرئيسي */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
        }
        
        .header {
            height: 80px;
            background: rgba(30, 60, 114, 0.9);
            backdrop-filter: blur(15px);
            border-bottom: 2px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 35px;
            -webkit-app-region: drag;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.2);
        }
        
        .header-left {
            display: flex;
            align-items: center;
            -webkit-app-region: no-drag;
        }
        
        .menu-toggle {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: #ffffff;
            font-size: 1.3rem;
            cursor: pointer;
            padding: 12px;
            border-radius: 12px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-left: 20px;
        }
        
        .menu-toggle:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1) rotate(90deg);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .page-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ffffff;
            margin-right: 25px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
            -webkit-app-region: no-drag;
        }
        
        .header-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: #ffffff;
            padding: 10px 18px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 0.95rem;
            font-weight: 500;
        }
        
        .header-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }
        
        .header-btn.close:hover {
            background: #e74c3c;
            border-color: #c0392b;
        }
        
        .header-btn.minimize:hover {
            background: #f39c12;
            border-color: #e67e22;
        }
        
        .header-btn.maximize:hover {
            background: #27ae60;
            border-color: #229954;
        }
        
        .content-area {
            flex: 1;
            padding: 35px;
            overflow-y: auto;
            background: rgba(255, 255, 255, 0.02);
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 35px;
        }
        
        .dashboard-card {
            background: rgba(30, 60, 114, 0.6);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            border: 2px solid rgba(255, 255, 255, 0.1);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #4CAF50, #2196F3, #FF9800);
            transform: scaleX(0);
            transition: transform 0.4s ease;
        }
        
        .dashboard-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
            border-color: rgba(255, 255, 255, 0.3);
            background: rgba(30, 60, 114, 0.8);
        }
        
        .dashboard-card:hover::before {
            transform: scaleX(1);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }
        
        .card-icon {
            font-size: 2.5rem;
            margin-left: 20px;
            color: #4CAF50;
            transition: all 0.3s ease;
        }
        
        .dashboard-card:hover .card-icon {
            transform: scale(1.2) rotate(10deg);
            color: #ffffff;
        }
        
        .card-title {
            font-size: 1.4rem;
            font-weight: bold;
            color: #ffffff;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .card-description {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.8;
            margin-bottom: 25px;
            font-size: 1rem;
        }
        
        .card-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .stat-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #4CAF50;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .stat-label {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.7);
        }
        
        /* الإجراءات السريعة */
        .quick-actions {
            background: rgba(30, 60, 114, 0.6);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            border: 2px solid rgba(255, 255, 255, 0.1);
            margin-top: 20px;
        }
        
        .quick-actions h3 {
            color: #ffffff;
            margin-bottom: 25px;
            font-size: 1.5rem;
            text-align: center;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
        }
        
        .action-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: #ffffff;
            padding: 20px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            text-align: center;
            font-weight: 500;
            font-size: 1rem;
            position: relative;
            overflow: hidden;
        }
        
        .action-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent);
            transition: all 0.4s ease;
            border-radius: 50%;
            transform: translate(-50%, -50%);
        }
        
        .action-btn:hover::before {
            width: 300px;
            height: 300px;
        }
        
        .action-btn:hover {
            background: rgba(76, 175, 80, 0.3);
            border-color: #4CAF50;
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
        }
        
        /* شريط الحالة */
        .status-bar {
            height: 35px;
            background: rgba(30, 60, 114, 0.9);
            border-top: 2px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 25px;
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
        }
        
        /* الرسوم المتحركة */
        @keyframes logoGlow {
            0%, 100% { opacity: 0.3; transform: rotate(0deg); }
            50% { opacity: 0.6; transform: rotate(180deg); }
        }
        
        @keyframes horseBounce {
            0%, 100% { transform: translateY(0px) scale(1); }
            50% { transform: translateY(-10px) scale(1.1); }
        }
        
        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .slide-in {
            animation: slideInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .fade-in {
            animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .sidebar {
                width: 80px;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .actions-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .header {
                padding: 0 20px;
            }
            
            .content-area {
                padding: 20px;
            }
        }
        
        /* تأثيرات التمرير المحسنة */
        ::-webkit-scrollbar {
            width: 10px;
        }
        
        ::-webkit-scrollbar-track {
            background: rgba(30, 60, 114, 0.3);
            border-radius: 5px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #4CAF50, #2196F3);
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #45a049, #1976D2);
            transform: scale(1.1);
        }
    </style>

    <!-- نظام إدارة الصندوق -->
    <script src="src/js/cashbox-manager.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- الشريط الجانبي -->
        <div class="sidebar" id="sidebar">
            <div class="logo-section">
                <div class="logo">🐎</div>
                <div class="logo-text">Black Horse</div>
                <div class="logo-subtitle">نظام إدارة المؤسسات</div>
            </div>

            <div class="nav-menu">
                <div class="nav-item active" onclick="loadModule('dashboard')" data-module="dashboard">
                    <div class="nav-icon"><i class="fas fa-home"></i></div>
                    <div class="nav-text">الرئيسية</div>
                </div>
                <div class="nav-item" onclick="loadModule('inventory')" data-module="inventory">
                    <div class="nav-icon"><i class="fas fa-boxes"></i></div>
                    <div class="nav-text">المخزون</div>
                </div>
                <div class="nav-item" onclick="loadModule('sales')" data-module="sales">
                    <div class="nav-icon"><i class="fas fa-chart-line"></i></div>
                    <div class="nav-text">المبيعات</div>
                </div>
                <div class="nav-item" onclick="loadModule('pos')" data-module="pos">
                    <div class="nav-icon"><i class="fas fa-cash-register"></i></div>
                    <div class="nav-text">نقاط البيع</div>
                </div>
                <div class="nav-item" onclick="loadModule('purchases')" data-module="purchases">
                    <div class="nav-icon"><i class="fas fa-shopping-cart"></i></div>
                    <div class="nav-text">المشتريات</div>
                </div>
                <div class="nav-item" onclick="loadModule('hr')" data-module="hr">
                    <div class="nav-icon"><i class="fas fa-users"></i></div>
                    <div class="nav-text">الموارد البشرية</div>
                </div>
                <div class="nav-item" onclick="loadModule('cashier')" data-module="cashier">
                    <div class="nav-icon"><i class="fas fa-user-tie"></i></div>
                    <div class="nav-text">الصرافين</div>
                </div>
                <div class="nav-item" onclick="loadModule('reports')" data-module="reports">
                    <div class="nav-icon"><i class="fas fa-chart-bar"></i></div>
                    <div class="nav-text">التقارير</div>
                </div>
                <div class="nav-item" onclick="loadModule('settings')" data-module="settings">
                    <div class="nav-icon"><i class="fas fa-cog"></i></div>
                    <div class="nav-text">الإعدادات</div>
                </div>
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="main-content">
            <div class="header">
                <div class="header-left">
                    <button class="menu-toggle" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="page-title" id="pageTitle">لوحة التحكم الرئيسية</div>
                </div>
                <div class="header-right">
                    <div class="cashbox-status" onclick="showCashboxStatus()" style="background: linear-gradient(135deg, #4CAF50, #45a049); color: white; padding: 8px 15px; border-radius: 20px; margin-left: 10px; cursor: pointer; display: inline-flex; align-items: center; gap: 8px; font-size: 0.9em; transition: all 0.3s ease;">
                        <i class="fas fa-wallet"></i>
                        <span id="totalCashboxBalance">0.00 ج.م</span>
                    </div>
                    <button class="header-btn" onclick="showNotifications()">
                        <i class="fas fa-bell"></i> الإشعارات
                    </button>
                    <button class="header-btn" onclick="showProfile()">
                        <i class="fas fa-user"></i> الملف الشخصي
                    </button>
                    <button class="header-btn minimize" onclick="minimizeApp()">
                        <i class="fas fa-window-minimize"></i>
                    </button>
                    <button class="header-btn maximize" onclick="maximizeApp()">
                        <i class="fas fa-window-maximize"></i>
                    </button>
                    <button class="header-btn close" onclick="closeApp()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <div class="content-area" id="contentArea">
                <!-- محتوى لوحة التحكم -->
                <div class="dashboard-grid">
                    <div class="dashboard-card fade-in" onclick="loadModule('inventory')" style="animation-delay: 0.1s">
                        <div class="card-header">
                            <div class="card-icon"><i class="fas fa-boxes"></i></div>
                            <div class="card-title">إدارة المخزون</div>
                        </div>
                        <div class="card-description">
                            إدارة شاملة للمنتجات والمخزون مع تتبع الكميات والتنبيهات الذكية
                        </div>
                        <div class="card-stats">
                            <div>
                                <div class="stat-value">1,234</div>
                                <div class="stat-label">منتج</div>
                            </div>
                            <div>
                                <div class="stat-value">89%</div>
                                <div class="stat-label">متوفر</div>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card fade-in" onclick="loadModule('sales')" style="animation-delay: 0.2s">
                        <div class="card-header">
                            <div class="card-icon"><i class="fas fa-chart-line"></i></div>
                            <div class="card-title">إدارة المبيعات</div>
                        </div>
                        <div class="card-description">
                            تتبع المبيعات والفواتير وإدارة العملاء والحسابات بطريقة احترافية
                        </div>
                        <div class="card-stats">
                            <div>
                                <div class="stat-value">45,678</div>
                                <div class="stat-label">ج.م</div>
                            </div>
                            <div>
                                <div class="stat-value">156</div>
                                <div class="stat-label">فاتورة</div>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card fade-in" onclick="loadModule('pos')" style="animation-delay: 0.3s">
                        <div class="card-header">
                            <div class="card-icon"><i class="fas fa-cash-register"></i></div>
                            <div class="card-title">نقاط البيع</div>
                        </div>
                        <div class="card-description">
                            نظام نقاط بيع سريع مع دعم الباركود والطباعة الحرارية المتقدمة
                        </div>
                        <div class="card-stats">
                            <div>
                                <div class="stat-value">23</div>
                                <div class="stat-label">معاملة اليوم</div>
                            </div>
                            <div>
                                <div class="stat-value">3</div>
                                <div class="stat-label">نقطة بيع</div>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card fade-in" onclick="loadModule('purchases')" style="animation-delay: 0.4s">
                        <div class="card-header">
                            <div class="card-icon"><i class="fas fa-shopping-cart"></i></div>
                            <div class="card-title">إدارة المشتريات</div>
                        </div>
                        <div class="card-description">
                            إدارة أوامر الشراء والموردين واستلام البضائع وتتبع التكاليف
                        </div>
                        <div class="card-stats">
                            <div>
                                <div class="stat-value">12</div>
                                <div class="stat-label">أمر شراء</div>
                            </div>
                            <div>
                                <div class="stat-value">8</div>
                                <div class="stat-label">مورد</div>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card fade-in" onclick="loadModule('hr')" style="animation-delay: 0.5s">
                        <div class="card-header">
                            <div class="card-icon"><i class="fas fa-users"></i></div>
                            <div class="card-title">الموارد البشرية</div>
                        </div>
                        <div class="card-description">
                            إدارة الموظفين والحضور والرواتب والإجازات بنظام متكامل
                        </div>
                        <div class="card-stats">
                            <div>
                                <div class="stat-value">24</div>
                                <div class="stat-label">موظف</div>
                            </div>
                            <div>
                                <div class="stat-value">92%</div>
                                <div class="stat-label">حضور</div>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card fade-in" onclick="loadModule('reports')" style="animation-delay: 0.6s">
                        <div class="card-header">
                            <div class="card-icon"><i class="fas fa-chart-bar"></i></div>
                            <div class="card-title">التقارير والتحليلات</div>
                        </div>
                        <div class="card-description">
                            تقارير مالية شاملة وتحليلات ذكية لاتخاذ القرارات الصحيحة
                        </div>
                        <div class="card-stats">
                            <div>
                                <div class="stat-value">15</div>
                                <div class="stat-label">تقرير</div>
                            </div>
                            <div>
                                <div class="stat-value">5</div>
                                <div class="stat-label">تحليل</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="quick-actions fade-in" style="animation-delay: 0.7s">
                    <h3><i class="fas fa-bolt"></i> الإجراءات السريعة</h3>
                    <div class="actions-grid">
                        <div class="action-btn" onclick="quickAction('new-sale')">
                            <i class="fas fa-plus-circle"></i><br>
                            بيع جديد
                        </div>
                        <div class="action-btn" onclick="quickAction('add-product')">
                            <i class="fas fa-box"></i><br>
                            إضافة منتج
                        </div>
                        <div class="action-btn" onclick="quickAction('new-customer')">
                            <i class="fas fa-user-plus"></i><br>
                            عميل جديد
                        </div>
                        <div class="action-btn" onclick="quickAction('daily-report')">
                            <i class="fas fa-file-alt"></i><br>
                            تقرير يومي
                        </div>
                        <div class="action-btn" onclick="quickAction('backup')">
                            <i class="fas fa-database"></i><br>
                            نسخة احتياطية
                        </div>
                        <div class="action-btn" onclick="quickAction('settings')">
                            <i class="fas fa-tools"></i><br>
                            الإعدادات
                        </div>
                    </div>
                </div>
            </div>

            <div class="status-bar">
                <div><i class="fas fa-horse"></i> Black Horse ERP v1.0.0 - جاهز للعمل</div>
                <div id="statusTime"><i class="fas fa-clock"></i> <span id="currentTime"></span></div>
            </div>
        </div>
    </div>

    <script>
        // متغيرات عامة
        let currentModule = 'dashboard';
        let sidebarCollapsed = false;

        // تحديث الوقت في شريط الحالة
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-EG', {
                weekday: 'short',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
        }

        // تحديث عرض حالة الصندوق في الشريط العلوي
        function updateCashboxDisplay() {
            if (window.cashboxManager) {
                const totalBalance = window.cashboxManager.getTotalBalance();
                const balanceElement = document.getElementById('totalCashboxBalance');
                if (balanceElement) {
                    balanceElement.textContent = `${totalBalance.toFixed(2)} ج.م`;

                    // تغيير اللون حسب الرصيد
                    const statusElement = balanceElement.parentElement;
                    if (totalBalance > 0) {
                        statusElement.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
                    } else if (totalBalance < 0) {
                        statusElement.style.background = 'linear-gradient(135deg, #f44336, #d32f2f)';
                    } else {
                        statusElement.style.background = 'linear-gradient(135deg, #607D8B, #455A64)';
                    }
                }
            }
        }

        // تبديل الشريط الجانبي
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebarCollapsed = !sidebarCollapsed;
            sidebar.classList.toggle('collapsed', sidebarCollapsed);

            // تأثير صوتي (اختياري)
            playClickSound();
        }

        // تحميل المديولات
        function loadModule(moduleName) {
            if (currentModule === moduleName) return;

            // إزالة الحالة النشطة من جميع العناصر
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // إضافة الحالة النشطة للعنصر المحدد
            const activeItem = document.querySelector(`[data-module="${moduleName}"]`);
            if (activeItem) {
                activeItem.classList.add('active');
            }

            // تحديث عنوان الصفحة
            const titles = {
                'dashboard': 'لوحة التحكم الرئيسية',
                'inventory': 'إدارة المخزون',
                'sales': 'إدارة المبيعات',
                'pos': 'نقاط البيع',
                'purchases': 'إدارة المشتريات',
                'hr': 'الموارد البشرية',
                'cashier': 'إدارة الصرافين',
                'reports': 'التقارير والتحليلات',
                'settings': 'إعدادات النظام'
            };

            const pageTitle = document.getElementById('pageTitle');
            pageTitle.textContent = titles[moduleName] || 'Black Horse ERP';

            // تأثير انتقال
            pageTitle.style.transform = 'scale(0.8)';
            pageTitle.style.opacity = '0.5';
            setTimeout(() => {
                pageTitle.style.transform = 'scale(1)';
                pageTitle.style.opacity = '1';
            }, 200);

            currentModule = moduleName;

            // تحميل محتوى المديول
            if (moduleName !== 'dashboard') {
                loadModuleContent(moduleName);
            } else {
                showDashboard();
            }

            // تأثير صوتي
            playClickSound();
        }

        // تحميل محتوى المديول داخل التطبيق
        async function loadModuleContent(moduleName) {
            const contentArea = document.getElementById('contentArea');

            // تأثير انتقال
            contentArea.style.opacity = '0.3';
            contentArea.style.transform = 'translateY(20px)';

            try {
                // محاولة تحميل الملف من المجلد المحلي
                const modulePath = `src/modules/${moduleName}/${moduleName}.html`;

                // تحميل المحتوى
                const response = await fetch(modulePath);
                if (response.ok) {
                    const content = await response.text();

                    // استخراج محتوى body من الملف
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(content, 'text/html');
                    const bodyContent = doc.body ? doc.body.innerHTML : content;

                    // تحديث المحتوى
                    contentArea.innerHTML = `
                        <div class="module-container" style="padding: 20px; height: 100%; overflow-y: auto;">
                            <div class="module-header" style="margin-bottom: 20px; padding-bottom: 15px; border-bottom: 2px solid #e0e0e0;">
                                <button onclick="showDashboard()" class="back-btn" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; margin-bottom: 10px;">
                                    <i class="fas fa-arrow-right"></i> العودة للرئيسية
                                </button>
                            </div>
                            <div class="module-content">
                                ${bodyContent}
                            </div>
                        </div>
                    `;

                    // تحميل موارد المديول
                    await loadModuleAssets(moduleName);

                    showNotification(`تم تحميل ${getModuleTitle(moduleName)} بنجاح`, 'success');
                } else {
                    // إنشاء واجهة افتراضية
                    contentArea.innerHTML = createDefaultModuleInterface(moduleName);
                    showNotification(`تم إنشاء واجهة ${getModuleTitle(moduleName)}`, 'info');
                }
            } catch (error) {
                console.error('خطأ في تحميل المديول:', error);
                contentArea.innerHTML = createErrorInterface(moduleName);
                showNotification(`خطأ في تحميل ${getModuleTitle(moduleName)}`, 'error');
            }

            // إعادة تعيين التأثير
            setTimeout(() => {
                contentArea.style.opacity = '1';
                contentArea.style.transform = 'translateY(0)';
            }, 300);
        }

        // تحميل موارد المديول (CSS & JS)
        async function loadModuleAssets(moduleName) {
            // تحميل CSS
            const cssPath = `src/modules/${moduleName}/${moduleName}.css`;
            if (!document.querySelector(`link[href="${cssPath}"]`)) {
                try {
                    const cssResponse = await fetch(cssPath);
                    if (cssResponse.ok) {
                        const link = document.createElement('link');
                        link.rel = 'stylesheet';
                        link.href = cssPath;
                        document.head.appendChild(link);
                    }
                } catch (error) {
                    console.log('ملف CSS غير موجود:', cssPath);
                }
            }

            // تحميل JavaScript
            const jsPath = `src/modules/${moduleName}/${moduleName}.js`;
            if (!document.querySelector(`script[src="${jsPath}"]`)) {
                try {
                    const jsResponse = await fetch(jsPath);
                    if (jsResponse.ok) {
                        const script = document.createElement('script');
                        script.src = jsPath;
                        document.head.appendChild(script);
                    }
                } catch (error) {
                    console.log('ملف JS غير موجود:', jsPath);
                }
            }
        }

        // إنشاء واجهة افتراضية للمديول
        function createDefaultModuleInterface(moduleName) {
            const moduleTitle = getModuleTitle(moduleName);
            return `
                <div class="module-container" style="padding: 20px; height: 100%; overflow-y: auto;">
                    <div class="module-header" style="margin-bottom: 20px; padding-bottom: 15px; border-bottom: 2px solid #e0e0e0;">
                        <button onclick="showDashboard()" class="back-btn" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; margin-bottom: 10px;">
                            <i class="fas fa-arrow-right"></i> العودة للرئيسية
                        </button>
                        <h2 style="color: #333; margin: 0;"><i class="fas fa-${getModuleIcon(moduleName)}"></i> ${moduleTitle}</h2>
                    </div>
                    <div class="module-content" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
                        <div style="text-align: center; padding: 50px;">
                            <i class="fas fa-${getModuleIcon(moduleName)}" style="font-size: 4em; color: #667eea; margin-bottom: 20px;"></i>
                            <h3 style="color: #333; margin-bottom: 15px;">${moduleTitle}</h3>
                            <p style="color: #666; margin-bottom: 30px;">هذا المديول قيد التطوير حالياً</p>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; max-width: 600px; margin: 0 auto;">
                                ${getModuleQuickActions(moduleName)}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // إنشاء واجهة خطأ
        function createErrorInterface(moduleName) {
            return `
                <div class="module-container" style="padding: 20px; height: 100%; overflow-y: auto;">
                    <div class="module-header" style="margin-bottom: 20px; padding-bottom: 15px; border-bottom: 2px solid #e0e0e0;">
                        <button onclick="showDashboard()" class="back-btn" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; margin-bottom: 10px;">
                            <i class="fas fa-arrow-right"></i> العودة للرئيسية
                        </button>
                    </div>
                    <div class="error-container" style="text-align: center; padding: 50px; background: white; border-radius: 15px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
                        <i class="fas fa-exclamation-triangle" style="font-size: 4em; color: #f44336; margin-bottom: 20px;"></i>
                        <h3 style="color: #333; margin-bottom: 15px;">خطأ في تحميل المديول</h3>
                        <p style="color: #666; margin-bottom: 30px;">المديول "${getModuleTitle(moduleName)}" غير متاح حالياً</p>
                        <button onclick="showDashboard()" style="background: linear-gradient(135deg, #4CAF50, #45a049); color: white; border: none; padding: 12px 25px; border-radius: 8px; cursor: pointer;">
                            <i class="fas fa-home"></i> العودة للرئيسية
                        </button>
                    </div>
                </div>
            `;
        }

        // عرض لوحة التحكم
        function showDashboard() {
            const contentArea = document.getElementById('contentArea');

            // تأثير انتقال
            contentArea.style.opacity = '0.3';
            contentArea.style.transform = 'translateY(20px)';

            setTimeout(() => {
                // إعادة تحميل محتوى لوحة التحكم
                contentArea.innerHTML = `
                    ${document.querySelector('.dashboard-grid').outerHTML}
                    ${document.querySelector('.quick-actions').outerHTML}
                `;

                // تحديث العنوان والحالة النشطة
                document.getElementById('pageTitle').textContent = 'لوحة التحكم الرئيسية';
                document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
                document.querySelector('[data-module="dashboard"]').classList.add('active');
                currentModule = 'dashboard';

                // إعادة تعيين التأثير
                contentArea.style.opacity = '1';
                contentArea.style.transform = 'translateY(0)';
            }, 300);
        }

        // الإجراءات السريعة
        function quickAction(action) {
            console.log('تنفيذ الإجراء:', action);

            // تأثير بصري
            const button = event.target.closest('.action-btn');
            if (button) {
                button.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    button.style.transform = 'scale(1.05)';
                    setTimeout(() => {
                        button.style.transform = '';
                    }, 150);
                }, 100);
            }

            // تنفيذ الإجراءات
            switch(action) {
                case 'new-sale':
                    loadModule('pos');
                    showNotification('تم فتح نقطة البيع', 'success');
                    break;
                case 'add-product':
                    loadModule('inventory');
                    showNotification('تم فتح إدارة المخزون', 'success');
                    break;
                case 'new-customer':
                    loadModule('sales');
                    showNotification('تم فتح إدارة المبيعات', 'success');
                    break;
                case 'daily-report':
                    loadModule('reports');
                    showNotification('تم فتح التقارير', 'success');
                    break;
                case 'backup':
                    createBackup();
                    break;
                case 'settings':
                    loadModule('settings');
                    showNotification('تم فتح الإعدادات', 'success');
                    break;
            }

            playClickSound();
        }

        // إنشاء نسخة احتياطية
        function createBackup() {
            if (window.electronAPI && window.electronAPI.send) {
                window.electronAPI.send('create-backup');
                showNotification('جاري إنشاء النسخة الاحتياطية...', 'info');
            } else {
                showNotification('ميزة النسخ الاحتياطي غير متاحة حالياً', 'warning');
            }
        }

        // وظائف النافذة
        function minimizeApp() {
            if (window.electronAPI && window.electronAPI.window) {
                window.electronAPI.window.minimize();
            } else if (window.electronAPI && window.electronAPI.send) {
                window.electronAPI.send('window-minimize');
            }
            playClickSound();
        }

        function maximizeApp() {
            if (window.electronAPI && window.electronAPI.window) {
                window.electronAPI.window.maximize();
            } else if (window.electronAPI && window.electronAPI.send) {
                window.electronAPI.send('window-maximize');
            }
            playClickSound();
        }

        function closeApp() {
            if (confirm('هل تريد إغلاق التطبيق؟')) {
                if (window.electronAPI && window.electronAPI.window) {
                    window.electronAPI.window.close();
                } else if (window.electronAPI && window.electronAPI.send) {
                    window.electronAPI.send('window-close');
                } else {
                    window.close();
                }
            }
        }

        // عرض الإشعارات
        function showNotifications() {
            showNotification('لا توجد إشعارات جديدة', 'info');
            playClickSound();
        }

        // عرض الملف الشخصي
        function showProfile() {
            showNotification('الملف الشخصي - قريباً', 'info');
            playClickSound();
        }

        // نظام الإشعارات المحسن
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas fa-${getNotificationIcon(type)}"></i>
                    <span>${message}</span>
                </div>
            `;

            // إضافة الأنماط
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${getNotificationColor(type)};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                z-index: 10000;
                transform: translateX(400px);
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                backdrop-filter: blur(10px);
                border: 2px solid rgba(255,255,255,0.2);
            `;

            document.body.appendChild(notification);

            // تأثير الظهور
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // إزالة الإشعار
            setTimeout(() => {
                notification.style.transform = 'translateX(400px)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 400);
            }, 3000);
        }

        function getNotificationIcon(type) {
            const icons = {
                'success': 'check-circle',
                'error': 'exclamation-circle',
                'warning': 'exclamation-triangle',
                'info': 'info-circle'
            };
            return icons[type] || 'info-circle';
        }

        function getNotificationColor(type) {
            const colors = {
                'success': 'linear-gradient(135deg, #4CAF50, #45a049)',
                'error': 'linear-gradient(135deg, #f44336, #d32f2f)',
                'warning': 'linear-gradient(135deg, #ff9800, #f57c00)',
                'info': 'linear-gradient(135deg, #2196F3, #1976D2)'
            };
            return colors[type] || colors.info;
        }

        // تأثير صوتي (اختياري)
        function playClickSound() {
            // يمكن إضافة ملف صوتي هنا
            // const audio = new Audio('assets/sounds/click.mp3');
            // audio.play().catch(() => {});
        }

        // معالجة أحداث لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            // Ctrl+M لتبديل الشريط الجانبي
            if (e.ctrlKey && e.key === 'm') {
                e.preventDefault();
                toggleSidebar();
            }

            // F11 للشاشة الكاملة
            if (e.key === 'F11') {
                e.preventDefault();
                if (window.electronAPI && window.electronAPI.send) {
                    window.electronAPI.send('toggle-fullscreen');
                }
            }

            // Ctrl+Q للخروج
            if (e.ctrlKey && e.key === 'q') {
                e.preventDefault();
                closeApp();
            }

            // Ctrl+N لبيع جديد
            if (e.ctrlKey && e.key === 'n') {
                e.preventDefault();
                quickAction('new-sale');
            }

            // Ctrl+B للنسخة الاحتياطية
            if (e.ctrlKey && e.key === 'b') {
                e.preventDefault();
                quickAction('backup');
            }
        });

        // تهيئة التطبيق
        document.addEventListener('DOMContentLoaded', function() {
            // تحديث الوقت
            updateTime();
            setInterval(updateTime, 1000);

            // تحديث حالة الصندوق
            setTimeout(() => {
                updateCashboxDisplay();
                // تحديث حالة الصندوق كل 30 ثانية
                setInterval(updateCashboxDisplay, 30000);
            }, 500);

            // تأثيرات الحركة المتدرجة
            const cards = document.querySelectorAll('.dashboard-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
            });

            // رسالة ترحيب
            setTimeout(() => {
                showNotification('مرحباً بك في نظام Black Horse ERP', 'success');
            }, 1000);

            // تحميل البيانات الأولية
            loadInitialData();
        });

        // تحميل البيانات الأولية
        function loadInitialData() {
            // يمكن إضافة تحميل البيانات من قاعدة البيانات هنا
            console.log('تحميل البيانات الأولية...');
        }

        // معالجة تغيير حجم النافذة
        window.addEventListener('resize', function() {
            // تحديث التخطيط عند تغيير حجم النافذة
            if (window.innerWidth < 768 && !sidebarCollapsed) {
                toggleSidebar();
            }
        });

        // منع القائمة السياقية (اختياري)
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });

        // الحصول على عنوان المديول
        function getModuleTitle(moduleName) {
            const titles = {
                'inventory': 'إدارة المخزون',
                'sales': 'إدارة المبيعات',
                'pos': 'نقاط البيع',
                'purchases': 'إدارة المشتريات',
                'hr': 'الموارد البشرية',
                'cashier': 'إدارة الصرافين',
                'reports': 'التقارير والتحليلات',
                'settings': 'إعدادات النظام'
            };
            return titles[moduleName] || moduleName;
        }

        // الحصول على أيقونة المديول
        function getModuleIcon(moduleName) {
            const icons = {
                'inventory': 'boxes',
                'sales': 'chart-line',
                'pos': 'cash-register',
                'purchases': 'shopping-cart',
                'hr': 'users',
                'cashier': 'user-tie',
                'reports': 'chart-bar',
                'settings': 'cogs'
            };
            return icons[moduleName] || 'cube';
        }

        // الحصول على الإجراءات السريعة للمديول
        function getModuleQuickActions(moduleName) {
            const actions = {
                'inventory': `
                    <button class="quick-action-btn" onclick="showNotification('إضافة منتج جديد', 'info')" style="background: linear-gradient(135deg, #4CAF50, #45a049); color: white; border: none; padding: 15px; border-radius: 10px; cursor: pointer;">
                        <i class="fas fa-plus"></i><br>إضافة منتج
                    </button>
                    <button class="quick-action-btn" onclick="showNotification('عرض المخزون', 'info')" style="background: linear-gradient(135deg, #2196F3, #1976D2); color: white; border: none; padding: 15px; border-radius: 10px; cursor: pointer;">
                        <i class="fas fa-list"></i><br>عرض المخزون
                    </button>
                `,
                'sales': `
                    <button class="quick-action-btn" onclick="showNotification('فاتورة جديدة', 'info')" style="background: linear-gradient(135deg, #4CAF50, #45a049); color: white; border: none; padding: 15px; border-radius: 10px; cursor: pointer;">
                        <i class="fas fa-plus"></i><br>فاتورة جديدة
                    </button>
                    <button class="quick-action-btn" onclick="showNotification('عرض المبيعات', 'info')" style="background: linear-gradient(135deg, #FF9800, #F57C00); color: white; border: none; padding: 15px; border-radius: 10px; cursor: pointer;">
                        <i class="fas fa-chart-line"></i><br>تقرير المبيعات
                    </button>
                `,
                'pos': `
                    <button class="quick-action-btn" onclick="showNotification('بيع جديد', 'info')" style="background: linear-gradient(135deg, #4CAF50, #45a049); color: white; border: none; padding: 15px; border-radius: 10px; cursor: pointer;">
                        <i class="fas fa-cash-register"></i><br>بيع جديد
                    </button>
                    <button class="quick-action-btn" onclick="showCashboxStatus()" style="background: linear-gradient(135deg, #9C27B0, #7B1FA2); color: white; border: none; padding: 15px; border-radius: 10px; cursor: pointer;">
                        <i class="fas fa-wallet"></i><br>حالة الصندوق
                    </button>
                `
            };
            return actions[moduleName] || `
                <button class="quick-action-btn" onclick="showNotification('قريباً...', 'info')" style="background: linear-gradient(135deg, #607D8B, #455A64); color: white; border: none; padding: 15px; border-radius: 10px; cursor: pointer;">
                    <i class="fas fa-tools"></i><br>قيد التطوير
                </button>
            `;
        }

        // منع السحب والإفلات (اختياري)
        document.addEventListener('dragover', function(e) {
            e.preventDefault();
        });

        document.addEventListener('drop', function(e) {
            e.preventDefault();
        });
    </script>
</body>
</html>
