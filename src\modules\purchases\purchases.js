/**
 * Purchases Management Module
 * إدارة المشتريات والموردين
 */

class PurchasesModule {
    constructor() {
        this.currentView = 'orders';
        this.selectedOrders = new Set();
        this.filters = {
            supplier: '',
            status: 'all',
            payment_status: 'all',
            date_from: '',
            date_to: '',
            search: ''
        };
        this.pagination = {
            page: 1,
            limit: 20,
            total: 0
        };
        this.currentOrder = null;
        this.orderItems = [];
        this.suppliers = new Map();
        this.products = new Map();
    }

    /**
     * Initialize purchases module
     */
    async initialize() {
        console.log('Initializing Purchases Module...');
        await this.loadSuppliers();
        await this.loadProducts();
        await this.createSampleData();
    }

    /**
     * Show purchases module
     */
    async show(params = {}) {
        this.currentView = params.view || 'orders';
        await this.render();
        await this.loadData();
        this.setupEventListeners();
    }

    /**
     * Render purchases module HTML
     */
    async render() {
        const container = document.getElementById('moduleContainer');
        container.innerHTML = `
            <div class="purchases-module">
                <!-- Header -->
                <div class="module-header d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="fas fa-shopping-bag me-2"></i>إدارة المشتريات</h2>
                        <p class="text-muted">إدارة أوامر الشراء والموردين والمدفوعات</p>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="Purchases.showNewOrderModal()">
                            <i class="fas fa-plus me-1"></i>أمر شراء جديد
                        </button>
                        <button class="btn btn-success" onclick="Purchases.showQuickPurchase()">
                            <i class="fas fa-bolt me-1"></i>شراء سريع
                        </button>
                        <button class="btn btn-info" onclick="Purchases.exportPurchases()">
                            <i class="fas fa-download me-1"></i>تصدير
                        </button>
                    </div>
                </div>

                <!-- Navigation Tabs -->
                <ul class="nav nav-tabs mb-4" id="purchasesTabs">
                    <li class="nav-item">
                        <a class="nav-link ${this.currentView === 'orders' ? 'active' : ''}" 
                           href="#" onclick="Purchases.switchView('orders')">
                            <i class="fas fa-file-invoice me-1"></i>أوامر الشراء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link ${this.currentView === 'suppliers' ? 'active' : ''}" 
                           href="#" onclick="Purchases.switchView('suppliers')">
                            <i class="fas fa-truck me-1"></i>الموردين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link ${this.currentView === 'payments' ? 'active' : ''}" 
                           href="#" onclick="Purchases.switchView('payments')">
                            <i class="fas fa-credit-card me-1"></i>المدفوعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link ${this.currentView === 'reports' ? 'active' : ''}" 
                           href="#" onclick="Purchases.switchView('reports')">
                            <i class="fas fa-chart-bar me-1"></i>التقارير
                        </a>
                    </li>
                </ul>

                <!-- Content Area -->
                <div id="purchasesContent">
                    ${await this.renderCurrentView()}
                </div>
            </div>

            <!-- Modals -->
            ${this.renderModals()}
        `;
    }

    /**
     * Render current view content
     */
    async renderCurrentView() {
        switch (this.currentView) {
            case 'orders':
                return await this.renderOrdersView();
            case 'suppliers':
                return await this.renderSuppliersView();
            case 'payments':
                return await this.renderPaymentsView();
            case 'reports':
                return await this.renderReportsView();
            default:
                return await this.renderOrdersView();
        }
    }

    /**
     * Render orders view
     */
    async renderOrdersView() {
        return `
            <div class="orders-view">
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">إجمالي الأوامر</h6>
                                        <h4 class="mb-0" id="totalOrdersCount">0</h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-file-invoice fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">أوامر مكتملة</h6>
                                        <h4 class="mb-0" id="completedOrdersCount">0</h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">أوامر معلقة</h6>
                                        <h4 class="mb-0" id="pendingOrdersCount">0</h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-clock fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">إجمالي المبلغ</h6>
                                        <h4 class="mb-0" id="totalPurchasesAmount">0 ج.م</h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-money-bill fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">البحث</label>
                                <input type="text" class="form-control" id="searchInput" 
                                       placeholder="رقم الأمر، المورد...">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">المورد</label>
                                <select class="form-select" id="supplierFilter">
                                    <option value="">جميع الموردين</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">الحالة</label>
                                <select class="form-select" id="statusFilter">
                                    <option value="all">جميع الحالات</option>
                                    <option value="pending">معلق</option>
                                    <option value="confirmed">مؤكد</option>
                                    <option value="received">مستلم</option>
                                    <option value="cancelled">ملغي</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">حالة الدفع</label>
                                <select class="form-select" id="paymentStatusFilter">
                                    <option value="all">جميع الحالات</option>
                                    <option value="unpaid">غير مدفوع</option>
                                    <option value="partial">مدفوع جزئياً</option>
                                    <option value="paid">مدفوع</option>
                                </select>
                            </div>
                            <div class="col-md-1.5">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="dateFromFilter">
                            </div>
                            <div class="col-md-1.5">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="dateToFilter">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button class="btn btn-primary me-2" onclick="Purchases.applyFilters()">
                                    <i class="fas fa-search me-1"></i>بحث
                                </button>
                                <button class="btn btn-secondary" onclick="Purchases.clearFilters()">
                                    <i class="fas fa-times me-1"></i>مسح
                                </button>
                                <div class="btn-group ms-3">
                                    <button class="btn btn-outline-danger" onclick="Purchases.deleteSelectedOrders()" 
                                            id="deleteSelectedBtn" disabled>
                                        <i class="fas fa-trash me-1"></i>حذف المحدد
                                    </button>
                                    <button class="btn btn-outline-primary" onclick="Purchases.printSelectedOrders()" 
                                            id="printSelectedBtn" disabled>
                                        <i class="fas fa-print me-1"></i>طباعة المحدد
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Orders Table -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" class="form-check-input" 
                                                   onchange="Purchases.toggleSelectAll(this)">
                                        </th>
                                        <th>رقم الأمر</th>
                                        <th>التاريخ</th>
                                        <th>المورد</th>
                                        <th>المبلغ الإجمالي</th>
                                        <th>المدفوع</th>
                                        <th>المتبقي</th>
                                        <th>الحالة</th>
                                        <th>حالة الدفع</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="ordersTableBody">
                                    <!-- Orders will be loaded here -->
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <nav aria-label="صفحات الأوامر" class="mt-3">
                            <ul class="pagination justify-content-center" id="ordersPagination">
                                <!-- Pagination will be generated here -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        `;
    }
}

    /**
     * Render suppliers view
     */
    async renderSuppliersView() {
        return `
            <div class="suppliers-view">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4>إدارة الموردين</h4>
                    <button class="btn btn-primary" onclick="Purchases.showAddSupplierModal()">
                        <i class="fas fa-plus me-1"></i>مورد جديد
                    </button>
                </div>

                <!-- Suppliers Grid -->
                <div class="row" id="suppliersGrid">
                    <!-- Suppliers will be loaded here -->
                </div>
            </div>
        `;
    }

    /**
     * Render payments view
     */
    async renderPaymentsView() {
        return `
            <div class="payments-view">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4>مدفوعات المشتريات</h4>
                    <button class="btn btn-primary" onclick="Purchases.showAddPaymentModal()">
                        <i class="fas fa-plus me-1"></i>دفعة جديدة
                    </button>
                </div>

                <!-- Payments content will be implemented -->
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                        <h5>مدفوعات المشتريات</h5>
                        <p class="text-muted">قريباً - ميزة مدفوعات المشتريات قيد التطوير</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render reports view
     */
    async renderReportsView() {
        return `
            <div class="reports-view">
                <h4 class="mb-4">تقارير المشتريات</h4>

                <!-- Reports content will be implemented -->
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                        <h5>تقارير المشتريات</h5>
                        <p class="text-muted">قريباً - ميزة التقارير قيد التطوير</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render modals
     */
    renderModals() {
        return `
            <!-- New Purchase Order Modal -->
            <div class="modal fade" id="newOrderModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">أمر شراء جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <!-- Order form will be implemented -->
                            <div class="text-center py-5">
                                <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                                <h5>أمر شراء جديد</h5>
                                <p class="text-muted">قريباً - نموذج أمر الشراء قيد التطوير</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add Supplier Modal -->
            <div class="modal fade" id="addSupplierModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إضافة مورد جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="addSupplierForm">
                                <div class="mb-3">
                                    <label class="form-label">اسم المورد *</label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" name="phone">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" name="email">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="address" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الرصيد الافتتاحي</label>
                                    <input type="number" class="form-control" name="opening_balance" value="0" step="0.01">
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="Purchases.saveSupplier()">حفظ</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Switch between views
     */
    async switchView(view) {
        this.currentView = view;
        await this.show({ view });
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.filters.search = e.target.value;
                    this.loadData();
                }, 300);
            });
        }

        // Filter change listeners
        ['supplierFilter', 'statusFilter', 'paymentStatusFilter', 'dateFromFilter', 'dateToFilter'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => this.applyFilters());
            }
        });
    }

    /**
     * Load data for current view
     */
    async loadData() {
        switch (this.currentView) {
            case 'orders':
                await this.loadOrders();
                await this.loadOrderStatistics();
                break;
            case 'suppliers':
                await this.loadSuppliersData();
                break;
            case 'payments':
                await this.loadPaymentsData();
                break;
            case 'reports':
                await this.loadReportsData();
                break;
        }
    }

    /**
     * Load purchase orders
     */
    async loadOrders() {
        try {
            let orders = await Database.getAll('purchase_orders');

            // Apply filters
            orders = this.applyOrderFilters(orders);

            // Apply pagination
            const startIndex = (this.pagination.page - 1) * this.pagination.limit;
            const endIndex = startIndex + this.pagination.limit;
            const paginatedOrders = orders.slice(startIndex, endIndex);

            this.pagination.total = orders.length;

            // Render orders table
            this.renderOrdersTable(paginatedOrders);
            this.renderPagination();

        } catch (error) {
            console.error('Error loading orders:', error);
            Utils.showToast('خطأ', 'فشل في تحميل أوامر الشراء', 'error');
        }
    }

    /**
     * Apply order filters
     */
    applyOrderFilters(orders) {
        return orders.filter(order => {
            // Search filter
            if (this.filters.search) {
                const searchTerm = this.filters.search.toLowerCase();
                const matchesSearch =
                    order.order_number.toLowerCase().includes(searchTerm) ||
                    order.supplier_name?.toLowerCase().includes(searchTerm);
                if (!matchesSearch) return false;
            }

            // Supplier filter
            if (this.filters.supplier && order.supplier_id !== this.filters.supplier) {
                return false;
            }

            // Status filter
            if (this.filters.status !== 'all' && order.status !== this.filters.status) {
                return false;
            }

            // Payment status filter
            if (this.filters.payment_status !== 'all' && order.payment_status !== this.filters.payment_status) {
                return false;
            }

            // Date range filter
            if (this.filters.date_from && order.order_date < this.filters.date_from) {
                return false;
            }
            if (this.filters.date_to && order.order_date > this.filters.date_to) {
                return false;
            }

            return true;
        });
    }

    /**
     * Render orders table
     */
    renderOrdersTable(orders) {
        const tbody = document.getElementById('ordersTableBody');
        if (!tbody) return;

        if (orders.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="10" class="text-center py-4">
                        <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                        <p class="text-muted">لا توجد أوامر شراء</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = orders.map(order => `
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input order-checkbox"
                           value="${order.id}" onchange="Purchases.toggleOrderSelection(this)">
                </td>
                <td>
                    <strong>${order.order_number}</strong>
                </td>
                <td>${Utils.formatDate(order.order_date)}</td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="avatar-sm bg-primary text-white rounded-circle me-2 d-flex align-items-center justify-content-center">
                            ${order.supplier_name?.charAt(0) || 'M'}
                        </div>
                        <div>
                            <div class="fw-medium">${order.supplier_name || 'غير محدد'}</div>
                            <small class="text-muted">${order.supplier_phone || ''}</small>
                        </div>
                    </div>
                </td>
                <td><strong>${Utils.formatCurrency(order.total_amount)}</strong></td>
                <td>${Utils.formatCurrency(order.paid_amount)}</td>
                <td>${Utils.formatCurrency(order.total_amount - order.paid_amount)}</td>
                <td>
                    <span class="badge ${this.getStatusBadgeClass(order.status)}">
                        ${this.getStatusText(order.status)}
                    </span>
                </td>
                <td>
                    <span class="badge ${this.getPaymentStatusBadgeClass(order.payment_status)}">
                        ${this.getPaymentStatusText(order.payment_status)}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="Purchases.viewOrder('${order.id}')"
                                title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="Purchases.printOrder('${order.id}')"
                                title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="Purchases.editOrder('${order.id}')"
                                title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="Purchases.deleteOrder('${order.id}')"
                                title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * Get status badge class
     */
    getStatusBadgeClass(status) {
        const classes = {
            'pending': 'bg-warning',
            'confirmed': 'bg-info',
            'received': 'bg-success',
            'cancelled': 'bg-danger'
        };
        return classes[status] || 'bg-secondary';
    }

    /**
     * Get status text
     */
    getStatusText(status) {
        const texts = {
            'pending': 'معلق',
            'confirmed': 'مؤكد',
            'received': 'مستلم',
            'cancelled': 'ملغي'
        };
        return texts[status] || status;
    }

    /**
     * Get payment status badge class
     */
    getPaymentStatusBadgeClass(status) {
        const classes = {
            'unpaid': 'bg-danger',
            'partial': 'bg-warning',
            'paid': 'bg-success'
        };
        return classes[status] || 'bg-secondary';
    }

    /**
     * Get payment status text
     */
    getPaymentStatusText(status) {
        const texts = {
            'unpaid': 'غير مدفوع',
            'partial': 'مدفوع جزئياً',
            'paid': 'مدفوع'
        };
        return texts[status] || status;
    }

    /**
     * Load order statistics
     */
    async loadOrderStatistics() {
        try {
            const orders = await Database.getAll('purchase_orders');

            const stats = {
                total: orders.length,
                completed: orders.filter(o => o.status === 'received').length,
                pending: orders.filter(o => o.status === 'pending').length,
                totalAmount: orders.reduce((sum, o) => sum + o.total_amount, 0)
            };

            // Update statistics cards
            document.getElementById('totalOrdersCount').textContent = stats.total;
            document.getElementById('completedOrdersCount').textContent = stats.completed;
            document.getElementById('pendingOrdersCount').textContent = stats.pending;
            document.getElementById('totalPurchasesAmount').textContent = Utils.formatCurrency(stats.totalAmount);

        } catch (error) {
            console.error('Error loading statistics:', error);
        }
    }

    /**
     * Load suppliers data
     */
    async loadSuppliersData() {
        try {
            const suppliers = await Database.getAll('suppliers');
            this.renderSuppliersGrid(suppliers);
            this.populateSupplierFilter(suppliers);
        } catch (error) {
            console.error('Error loading suppliers:', error);
        }
    }

    /**
     * Render suppliers grid
     */
    renderSuppliersGrid(suppliers) {
        const grid = document.getElementById('suppliersGrid');
        if (!grid) return;

        if (suppliers.length === 0) {
            grid.innerHTML = `
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                            <h5>لا يوجد موردين</h5>
                            <p class="text-muted">ابدأ بإضافة مورد جديد</p>
                            <button class="btn btn-primary" onclick="Purchases.showAddSupplierModal()">
                                <i class="fas fa-plus me-1"></i>إضافة مورد
                            </button>
                        </div>
                    </div>
                </div>
            `;
            return;
        }

        grid.innerHTML = suppliers.map(supplier => `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="avatar-lg bg-primary text-white rounded-circle me-3 d-flex align-items-center justify-content-center">
                                <i class="fas fa-truck"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">${supplier.name}</h6>
                                <small class="text-muted">${supplier.phone || 'لا يوجد هاتف'}</small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <small class="text-muted d-block">البريد الإلكتروني:</small>
                            <span>${supplier.email || 'غير محدد'}</span>
                        </div>

                        <div class="mb-3">
                            <small class="text-muted d-block">العنوان:</small>
                            <span>${supplier.address || 'غير محدد'}</span>
                        </div>

                        <div class="mb-3">
                            <small class="text-muted d-block">الرصيد:</small>
                            <span class="fw-bold ${supplier.balance >= 0 ? 'text-success' : 'text-danger'}">
                                ${Utils.formatCurrency(supplier.balance || 0)}
                            </span>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="btn-group w-100">
                            <button class="btn btn-outline-primary btn-sm" onclick="Purchases.editSupplier('${supplier.id}')">
                                <i class="fas fa-edit"></i> تعديل
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="Purchases.viewSupplierOrders('${supplier.id}')">
                                <i class="fas fa-file-invoice"></i> الأوامر
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="Purchases.deleteSupplier('${supplier.id}')">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * Populate supplier filter
     */
    populateSupplierFilter(suppliers) {
        const filter = document.getElementById('supplierFilter');
        if (!filter) return;

        filter.innerHTML = '<option value="">جميع الموردين</option>' +
            suppliers.map(supplier =>
                `<option value="${supplier.id}">${supplier.name}</option>`
            ).join('');
    }

    /**
     * Load suppliers and products
     */
    async loadSuppliers() {
        try {
            const suppliers = await Database.getAll('suppliers');
            suppliers.forEach(supplier => {
                this.suppliers.set(supplier.id, supplier);
            });
        } catch (error) {
            console.error('Error loading suppliers:', error);
        }
    }

    async loadProducts() {
        try {
            const products = await Database.getAll('products');
            products.forEach(product => {
                this.products.set(product.id, product);
            });
        } catch (error) {
            console.error('Error loading products:', error);
        }
    }

    /**
     * Create sample data
     */
    async createSampleData() {
        try {
            // Check if sample data already exists
            const existingSuppliers = await Database.getAll('suppliers');
            const existingOrders = await Database.getAll('purchase_orders');

            if (existingSuppliers.length > 0 && existingOrders.length > 0) {
                return; // Sample data already exists
            }

            // Create sample suppliers
            const sampleSuppliers = [
                {
                    id: Utils.generateId(),
                    name: 'شركة الأهرام للتوريدات',
                    phone: '01234567890',
                    email: '<EMAIL>',
                    address: 'القاهرة، مصر الجديدة',
                    balance: 0,
                    created_at: new Date().toISOString()
                },
                {
                    id: Utils.generateId(),
                    name: 'مؤسسة النيل التجارية',
                    phone: '01098765432',
                    email: '<EMAIL>',
                    address: 'الجيزة، المهندسين',
                    balance: -1500,
                    created_at: new Date().toISOString()
                },
                {
                    id: Utils.generateId(),
                    name: 'شركة دلتا للاستيراد',
                    phone: '01555666777',
                    email: '<EMAIL>',
                    address: 'الإسكندرية، سموحة',
                    balance: 2500,
                    created_at: new Date().toISOString()
                }
            ];

            // Save suppliers
            for (const supplier of sampleSuppliers) {
                await Database.add('suppliers', supplier);
                this.suppliers.set(supplier.id, supplier);
            }

            // Create sample purchase orders
            const sampleOrders = [];
            for (let i = 1; i <= 15; i++) {
                const supplier = sampleSuppliers[Math.floor(Math.random() * sampleSuppliers.length)];
                const orderDate = new Date();
                orderDate.setDate(orderDate.getDate() - Math.floor(Math.random() * 30));

                const totalAmount = Math.floor(Math.random() * 10000) + 1000;
                const paidAmount = Math.random() > 0.3 ? Math.floor(totalAmount * (0.3 + Math.random() * 0.7)) : 0;

                const order = {
                    id: Utils.generateId(),
                    order_number: `PO-${String(i).padStart(4, '0')}`,
                    supplier_id: supplier.id,
                    supplier_name: supplier.name,
                    supplier_phone: supplier.phone,
                    order_date: orderDate.toISOString().split('T')[0],
                    total_amount: totalAmount,
                    paid_amount: paidAmount,
                    status: ['pending', 'confirmed', 'received', 'cancelled'][Math.floor(Math.random() * 4)],
                    payment_status: paidAmount === 0 ? 'unpaid' : (paidAmount >= totalAmount ? 'paid' : 'partial'),
                    notes: `ملاحظات أمر الشراء رقم ${i}`,
                    created_at: orderDate.toISOString()
                };

                sampleOrders.push(order);
            }

            // Save orders
            for (const order of sampleOrders) {
                await Database.add('purchase_orders', order);
            }

            console.log('Sample purchase data created successfully');

        } catch (error) {
            console.error('Error creating sample data:', error);
        }
    }

    /**
     * Apply filters
     */
    applyFilters() {
        this.filters.supplier = document.getElementById('supplierFilter')?.value || '';
        this.filters.status = document.getElementById('statusFilter')?.value || 'all';
        this.filters.payment_status = document.getElementById('paymentStatusFilter')?.value || 'all';
        this.filters.date_from = document.getElementById('dateFromFilter')?.value || '';
        this.filters.date_to = document.getElementById('dateToFilter')?.value || '';
        this.filters.search = document.getElementById('searchInput')?.value || '';

        this.pagination.page = 1;
        this.loadData();
    }

    /**
     * Clear filters
     */
    clearFilters() {
        this.filters = {
            supplier: '',
            status: 'all',
            payment_status: 'all',
            date_from: '',
            date_to: '',
            search: ''
        };

        // Reset form elements
        document.getElementById('searchInput').value = '';
        document.getElementById('supplierFilter').value = '';
        document.getElementById('statusFilter').value = 'all';
        document.getElementById('paymentStatusFilter').value = 'all';
        document.getElementById('dateFromFilter').value = '';
        document.getElementById('dateToFilter').value = '';

        this.pagination.page = 1;
        this.loadData();
    }

    /**
     * Toggle order selection
     */
    toggleOrderSelection(checkbox) {
        const orderId = checkbox.value;
        if (checkbox.checked) {
            this.selectedOrders.add(orderId);
        } else {
            this.selectedOrders.delete(orderId);
        }

        this.updateBulkActionButtons();
    }

    /**
     * Toggle select all orders
     */
    toggleSelectAll(checkbox) {
        const orderCheckboxes = document.querySelectorAll('.order-checkbox');
        orderCheckboxes.forEach(cb => {
            cb.checked = checkbox.checked;
            this.toggleOrderSelection(cb);
        });
    }

    /**
     * Update bulk action buttons
     */
    updateBulkActionButtons() {
        const deleteBtn = document.getElementById('deleteSelectedBtn');
        const printBtn = document.getElementById('printSelectedBtn');
        const hasSelection = this.selectedOrders.size > 0;

        if (deleteBtn) deleteBtn.disabled = !hasSelection;
        if (printBtn) printBtn.disabled = !hasSelection;
    }

    /**
     * Show new order modal
     */
    showNewOrderModal() {
        const modal = new bootstrap.Modal(document.getElementById('newOrderModal'));
        modal.show();
    }

    /**
     * Show add supplier modal
     */
    showAddSupplierModal() {
        const modal = new bootstrap.Modal(document.getElementById('addSupplierModal'));
        modal.show();
    }

    /**
     * Save supplier
     */
    async saveSupplier() {
        try {
            const form = document.getElementById('addSupplierForm');
            const formData = new FormData(form);

            const supplier = {
                id: Utils.generateId(),
                name: formData.get('name'),
                phone: formData.get('phone'),
                email: formData.get('email'),
                address: formData.get('address'),
                balance: parseFloat(formData.get('opening_balance')) || 0,
                created_at: new Date().toISOString()
            };

            await Database.add('suppliers', supplier);
            this.suppliers.set(supplier.id, supplier);

            // Close modal and refresh data
            bootstrap.Modal.getInstance(document.getElementById('addSupplierModal')).hide();
            form.reset();

            if (this.currentView === 'suppliers') {
                await this.loadSuppliersData();
            }

            Utils.showToast('نجح', 'تم إضافة المورد بنجاح', 'success');

        } catch (error) {
            console.error('Error saving supplier:', error);
            Utils.showToast('خطأ', 'فشل في إضافة المورد', 'error');
        }
    }

    /**
     * View order details
     */
    viewOrder(orderId) {
        Utils.showToast('معلومات', 'ميزة عرض تفاصيل الأمر قيد التطوير', 'info');
    }

    /**
     * Edit order
     */
    editOrder(orderId) {
        Utils.showToast('معلومات', 'ميزة تعديل الأمر قيد التطوير', 'info');
    }

    /**
     * Delete order
     */
    async deleteOrder(orderId) {
        if (!confirm('هل أنت متأكد من حذف هذا الأمر؟')) return;

        try {
            await Database.delete('purchase_orders', orderId);
            await this.loadData();
            Utils.showToast('نجح', 'تم حذف الأمر بنجاح', 'success');
        } catch (error) {
            console.error('Error deleting order:', error);
            Utils.showToast('خطأ', 'فشل في حذف الأمر', 'error');
        }
    }

    /**
     * Print order
     */
    printOrder(orderId) {
        Utils.showToast('معلومات', 'ميزة طباعة الأمر قيد التطوير', 'info');
    }

    /**
     * Delete selected orders
     */
    async deleteSelectedOrders() {
        if (this.selectedOrders.size === 0) return;

        if (!confirm(`هل أنت متأكد من حذف ${this.selectedOrders.size} أمر؟`)) return;

        try {
            for (const orderId of this.selectedOrders) {
                await Database.delete('purchase_orders', orderId);
            }

            this.selectedOrders.clear();
            await this.loadData();
            Utils.showToast('نجح', 'تم حذف الأوامر المحددة بنجاح', 'success');
        } catch (error) {
            console.error('Error deleting orders:', error);
            Utils.showToast('خطأ', 'فشل في حذف الأوامر', 'error');
        }
    }

    /**
     * Print selected orders
     */
    printSelectedOrders() {
        Utils.showToast('معلومات', 'ميزة طباعة الأوامر المحددة قيد التطوير', 'info');
    }

    /**
     * Show quick purchase
     */
    showQuickPurchase() {
        Utils.showToast('معلومات', 'ميزة الشراء السريع قيد التطوير', 'info');
    }

    /**
     * Export purchases
     */
    exportPurchases() {
        Utils.showToast('معلومات', 'ميزة تصدير المشتريات قيد التطوير', 'info');
    }

    /**
     * Edit supplier
     */
    editSupplier(supplierId) {
        Utils.showToast('معلومات', 'ميزة تعديل المورد قيد التطوير', 'info');
    }

    /**
     * View supplier orders
     */
    viewSupplierOrders(supplierId) {
        this.filters.supplier = supplierId;
        this.switchView('orders');
    }

    /**
     * Delete supplier
     */
    async deleteSupplier(supplierId) {
        if (!confirm('هل أنت متأكد من حذف هذا المورد؟')) return;

        try {
            await Database.delete('suppliers', supplierId);
            this.suppliers.delete(supplierId);
            await this.loadSuppliersData();
            Utils.showToast('نجح', 'تم حذف المورد بنجاح', 'success');
        } catch (error) {
            console.error('Error deleting supplier:', error);
            Utils.showToast('خطأ', 'فشل في حذف المورد', 'error');
        }
    }

    /**
     * Render pagination
     */
    renderPagination() {
        const pagination = document.getElementById('ordersPagination');
        if (!pagination) return;

        const totalPages = Math.ceil(this.pagination.total / this.pagination.limit);
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let paginationHTML = '';

        // Previous button
        paginationHTML += `
            <li class="page-item ${this.pagination.page === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="Purchases.goToPage(${this.pagination.page - 1})">السابق</a>
            </li>
        `;

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.pagination.page - 2 && i <= this.pagination.page + 2)) {
                paginationHTML += `
                    <li class="page-item ${i === this.pagination.page ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="Purchases.goToPage(${i})">${i}</a>
                    </li>
                `;
            } else if (i === this.pagination.page - 3 || i === this.pagination.page + 3) {
                paginationHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }

        // Next button
        paginationHTML += `
            <li class="page-item ${this.pagination.page === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="Purchases.goToPage(${this.pagination.page + 1})">التالي</a>
            </li>
        `;

        pagination.innerHTML = paginationHTML;
    }

    /**
     * Go to specific page
     */
    goToPage(page) {
        const totalPages = Math.ceil(this.pagination.total / this.pagination.limit);
        if (page < 1 || page > totalPages) return;

        this.pagination.page = page;
        this.loadData();
    }

    /**
     * Load payments data (placeholder)
     */
    async loadPaymentsData() {
        // Placeholder for payments functionality
    }

    /**
     * Load reports data (placeholder)
     */
    async loadReportsData() {
        // Placeholder for reports functionality
    }
}

// Create global instance
window.Purchases = new PurchasesModule();
