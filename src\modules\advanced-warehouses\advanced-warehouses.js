/**
 * نظام إدارة المخازن المتقدم
 * Advanced Warehouses Management System
 */

class AdvancedWarehousesManager {
    constructor() {
        this.warehouses = new Map();
        this.warehouseZones = new Map();
        this.warehouseLocations = new Map();
        this.inventoryMovements = new Map();
        this.stockTransfers = new Map();
        this.warehouseStaff = new Map();
        this.warehouseEquipment = new Map();
        this.warehouseReports = new Map();
        this.warehouseAlerts = new Map();
        this.warehouseSettings = new Map();
        
        this.settings = {
            defaultWarehouse: null,
            autoLocationAssignment: true,
            stockAlertThreshold: 10,
            transferApprovalRequired: true,
            barcodeEnabled: true,
            rfidEnabled: false,
            temperatureMonitoring: false,
            humidityMonitoring: false,
            securityLevel: 'medium', // low, medium, high
            auditTrailEnabled: true,
            realTimeTracking: true
        };
        
        this.loadData();
        this.initializeDefaultWarehouses();
    }

    // إنشاء مخزن جديد
    createWarehouse(warehouseData) {
        const warehouse = {
            id: this.generateWarehouseId(),
            code: warehouseData.code || this.generateWarehouseCode(),
            name: warehouseData.name,
            nameEn: warehouseData.nameEn || '',
            type: warehouseData.type || 'main', // main, branch, temporary, external
            
            // الموقع والعنوان
            location: {
                country: warehouseData.country || 'السعودية',
                city: warehouseData.city || '',
                district: warehouseData.district || '',
                street: warehouseData.street || '',
                building: warehouseData.building || '',
                floor: warehouseData.floor || '',
                coordinates: {
                    latitude: warehouseData.latitude || null,
                    longitude: warehouseData.longitude || null
                },
                postalCode: warehouseData.postalCode || '',
                poBox: warehouseData.poBox || ''
            },
            
            // المواصفات الفيزيائية
            specifications: {
                totalArea: warehouseData.totalArea || 0, // متر مربع
                storageArea: warehouseData.storageArea || 0,
                officeArea: warehouseData.officeArea || 0,
                loadingArea: warehouseData.loadingArea || 0,
                height: warehouseData.height || 0, // متر
                capacity: warehouseData.capacity || 0, // طن أو متر مكعب
                temperatureRange: {
                    min: warehouseData.minTemp || null,
                    max: warehouseData.maxTemp || null
                },
                humidityRange: {
                    min: warehouseData.minHumidity || null,
                    max: warehouseData.maxHumidity || null
                }
            },
            
            // المرافق والخدمات
            facilities: {
                airConditioning: warehouseData.airConditioning || false,
                heating: warehouseData.heating || false,
                ventilation: warehouseData.ventilation || false,
                fireProtection: warehouseData.fireProtection || false,
                securitySystem: warehouseData.securitySystem || false,
                cctv: warehouseData.cctv || false,
                accessControl: warehouseData.accessControl || false,
                loadingDocks: warehouseData.loadingDocks || 0,
                parkingSpaces: warehouseData.parkingSpaces || 0,
                powerBackup: warehouseData.powerBackup || false,
                internetConnection: warehouseData.internetConnection || false
            },
            
            // المعدات
            equipment: {
                forklifts: warehouseData.forklifts || 0,
                conveyors: warehouseData.conveyors || 0,
                scanners: warehouseData.scanners || 0,
                printers: warehouseData.printers || 0,
                scales: warehouseData.scales || 0,
                computers: warehouseData.computers || 0,
                tablets: warehouseData.tablets || 0,
                rfidReaders: warehouseData.rfidReaders || 0
            },
            
            // الموظفون
            staff: {
                manager: warehouseData.manager || null,
                supervisors: warehouseData.supervisors || [],
                workers: warehouseData.workers || [],
                guards: warehouseData.guards || [],
                totalStaff: 0
            },
            
            // ساعات العمل
            workingHours: {
                monday: warehouseData.mondayHours || { start: '08:00', end: '17:00', closed: false },
                tuesday: warehouseData.tuesdayHours || { start: '08:00', end: '17:00', closed: false },
                wednesday: warehouseData.wednesdayHours || { start: '08:00', end: '17:00', closed: false },
                thursday: warehouseData.thursdayHours || { start: '08:00', end: '17:00', closed: false },
                friday: warehouseData.fridayHours || { start: '08:00', end: '12:00', closed: false },
                saturday: warehouseData.saturdayHours || { start: '08:00', end: '17:00', closed: false },
                sunday: warehouseData.sundayHours || { start: '08:00', end: '17:00', closed: true }
            },
            
            // الحالة والإعدادات
            status: warehouseData.status || 'active', // active, inactive, maintenance, closed
            isDefault: warehouseData.isDefault || false,
            priority: warehouseData.priority || 1, // 1 = highest
            
            // الإحصائيات
            statistics: {
                totalProducts: 0,
                totalQuantity: 0,
                totalValue: 0,
                utilizationRate: 0,
                turnoverRate: 0,
                accuracyRate: 100,
                lastInventoryDate: null,
                lastMovementDate: null
            },
            
            // التكاليف
            costs: {
                rent: warehouseData.rent || 0,
                utilities: warehouseData.utilities || 0,
                maintenance: warehouseData.maintenance || 0,
                insurance: warehouseData.insurance || 0,
                security: warehouseData.security || 0,
                staff: warehouseData.staffCost || 0,
                equipment: warehouseData.equipmentCost || 0,
                other: warehouseData.otherCosts || 0
            },
            
            // التواريخ
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            createdBy: warehouseData.createdBy || 'system',
            
            // معلومات إضافية
            description: warehouseData.description || '',
            notes: warehouseData.notes || '',
            tags: warehouseData.tags || [],
            attachments: warehouseData.attachments || [],
            
            // إعدادات الإشعارات
            notifications: {
                stockAlerts: true,
                temperatureAlerts: true,
                securityAlerts: true,
                maintenanceAlerts: true,
                transferAlerts: true
            }
        };
        
        // حساب إجمالي الموظفين
        warehouse.staff.totalStaff = 
            (warehouse.staff.manager ? 1 : 0) +
            warehouse.staff.supervisors.length +
            warehouse.staff.workers.length +
            warehouse.staff.guards.length;
        
        this.warehouses.set(warehouse.id, warehouse);
        this.saveData();
        
        // إنشاء المناطق الافتراضية
        this.createDefaultZones(warehouse.id);
        
        console.log(`✅ تم إنشاء المخزن: ${warehouse.name}`);
        return warehouse;
    }

    // إنشاء منطقة في المخزن
    createWarehouseZone(zoneData) {
        const zone = {
            id: this.generateZoneId(),
            warehouseId: zoneData.warehouseId,
            code: zoneData.code || this.generateZoneCode(),
            name: zoneData.name,
            type: zoneData.type || 'storage', // storage, receiving, shipping, staging, quarantine
            
            // المواصفات
            specifications: {
                area: zoneData.area || 0,
                height: zoneData.height || 0,
                capacity: zoneData.capacity || 0,
                maxWeight: zoneData.maxWeight || 0,
                temperatureControlled: zoneData.temperatureControlled || false,
                humidityControlled: zoneData.humidityControlled || false,
                securityLevel: zoneData.securityLevel || 'medium'
            },
            
            // الموقع داخل المخزن
            position: {
                x: zoneData.x || 0,
                y: zoneData.y || 0,
                floor: zoneData.floor || 1,
                section: zoneData.section || 'A'
            },
            
            // القيود والشروط
            restrictions: {
                productTypes: zoneData.allowedProductTypes || [], // أنواع المنتجات المسموحة
                hazardousMaterials: zoneData.hazardousMaterials || false,
                fragileItems: zoneData.fragileItems || false,
                heavyItems: zoneData.heavyItems || false,
                accessLevel: zoneData.accessLevel || 'general' // general, restricted, high_security
            },
            
            // الحالة
            status: zoneData.status || 'active', // active, inactive, maintenance, full
            occupancyRate: 0,
            
            // الإحصائيات
            statistics: {
                totalLocations: 0,
                occupiedLocations: 0,
                totalProducts: 0,
                totalQuantity: 0,
                lastActivity: null
            },
            
            createdAt: new Date().toISOString(),
            createdBy: zoneData.createdBy || 'system'
        };
        
        this.warehouseZones.set(zone.id, zone);
        this.saveData();
        
        console.log(`✅ تم إنشاء المنطقة: ${zone.name}`);
        return zone;
    }

    // إنشاء موقع تخزين
    createStorageLocation(locationData) {
        const location = {
            id: this.generateLocationId(),
            warehouseId: locationData.warehouseId,
            zoneId: locationData.zoneId,
            code: locationData.code || this.generateLocationCode(),
            
            // الموقع الفيزيائي
            position: {
                aisle: locationData.aisle || 'A',
                rack: locationData.rack || '01',
                shelf: locationData.shelf || '01',
                bin: locationData.bin || '01',
                level: locationData.level || 1,
                coordinates: {
                    x: locationData.x || 0,
                    y: locationData.y || 0,
                    z: locationData.z || 0
                }
            },
            
            // المواصفات
            specifications: {
                width: locationData.width || 0,
                depth: locationData.depth || 0,
                height: locationData.height || 0,
                volume: 0, // سيتم حسابه
                maxWeight: locationData.maxWeight || 0,
                type: locationData.type || 'shelf' // shelf, floor, rack, bin, pallet
            },
            
            // الحالة والمحتوى
            status: locationData.status || 'empty', // empty, occupied, reserved, blocked
            currentProduct: null,
            currentQuantity: 0,
            reservedQuantity: 0,
            
            // القيود
            restrictions: {
                productTypes: locationData.allowedProductTypes || [],
                singleProduct: locationData.singleProduct || false, // منتج واحد فقط
                fifo: locationData.fifo || false, // أول داخل أول خارج
                lifo: locationData.lifo || false // آخر داخل أول خارج
            },
            
            // التاريخ
            createdAt: new Date().toISOString(),
            lastUpdated: new Date().toISOString(),
            lastInventoryCheck: null,
            
            // معلومات إضافية
            barcode: locationData.barcode || this.generateLocationBarcode(),
            rfidTag: locationData.rfidTag || null,
            notes: locationData.notes || ''
        };
        
        // حساب الحجم
        location.specifications.volume = 
            location.specifications.width * 
            location.specifications.depth * 
            location.specifications.height;
        
        this.warehouseLocations.set(location.id, location);
        this.saveData();
        
        // تحديث إحصائيات المنطقة
        this.updateZoneStatistics(locationData.zoneId);
        
        console.log(`✅ تم إنشاء الموقع: ${location.code}`);
        return location;
    }

    // تسجيل حركة مخزون
    recordInventoryMovement(movementData) {
        const movement = {
            id: this.generateMovementId(),
            type: movementData.type, // in, out, transfer, adjustment, cycle_count
            
            // معلومات المنتج
            productId: movementData.productId,
            productCode: movementData.productCode,
            productName: movementData.productName,
            
            // معلومات الكمية
            quantity: movementData.quantity,
            unitOfMeasure: movementData.unitOfMeasure || 'قطعة',
            unitCost: movementData.unitCost || 0,
            totalValue: movementData.quantity * (movementData.unitCost || 0),
            
            // معلومات المخزن والموقع
            warehouseId: movementData.warehouseId,
            warehouseName: movementData.warehouseName,
            fromLocationId: movementData.fromLocationId || null,
            toLocationId: movementData.toLocationId || null,
            
            // معلومات الدفعة والانتهاء
            batchNumber: movementData.batchNumber || null,
            serialNumber: movementData.serialNumber || null,
            expiryDate: movementData.expiryDate || null,
            manufacturingDate: movementData.manufacturingDate || null,
            
            // المرجع والسبب
            referenceType: movementData.referenceType || null, // purchase, sale, transfer, adjustment
            referenceId: movementData.referenceId || null,
            reason: movementData.reason || '',
            
            // الحالة
            status: movementData.status || 'completed', // pending, completed, cancelled
            
            // المستخدم والتاريخ
            userId: movementData.userId || 'system',
            userName: movementData.userName || 'النظام',
            timestamp: new Date().toISOString(),
            
            // معلومات إضافية
            notes: movementData.notes || '',
            attachments: movementData.attachments || []
        };
        
        this.inventoryMovements.set(movement.id, movement);
        
        // تحديث مواقع التخزين
        this.updateLocationInventory(movement);
        
        // تحديث إحصائيات المخزن
        this.updateWarehouseStatistics(movementData.warehouseId);
        
        this.saveData();
        
        console.log(`✅ تم تسجيل حركة المخزون: ${movement.type} - ${movement.quantity} ${movement.unitOfMeasure}`);
        return movement;
    }

    // نقل المخزون بين المواقع
    transferStock(transferData) {
        const transfer = {
            id: this.generateTransferId(),
            
            // معلومات النقل
            fromWarehouseId: transferData.fromWarehouseId,
            toWarehouseId: transferData.toWarehouseId,
            fromLocationId: transferData.fromLocationId,
            toLocationId: transferData.toLocationId,
            
            // معلومات المنتج
            productId: transferData.productId,
            quantity: transferData.quantity,
            unitOfMeasure: transferData.unitOfMeasure || 'قطعة',
            
            // الحالة والموافقة
            status: transferData.status || 'pending', // pending, approved, in_transit, completed, cancelled
            approvalRequired: this.settings.transferApprovalRequired,
            approvedBy: null,
            approvedAt: null,
            
            // التواريخ
            requestedAt: new Date().toISOString(),
            scheduledDate: transferData.scheduledDate || new Date().toISOString(),
            startedAt: null,
            completedAt: null,
            
            // المستخدم والسبب
            requestedBy: transferData.requestedBy || 'system',
            reason: transferData.reason || '',
            priority: transferData.priority || 'normal', // low, normal, high, urgent
            
            // التكلفة والنقل
            transportMethod: transferData.transportMethod || 'internal',
            transportCost: transferData.transportCost || 0,
            carrier: transferData.carrier || null,
            trackingNumber: transferData.trackingNumber || null,
            
            // معلومات إضافية
            notes: transferData.notes || '',
            attachments: transferData.attachments || []
        };
        
        this.stockTransfers.set(transfer.id, transfer);
        
        // إذا كان النقل لا يحتاج موافقة، قم بتنفيذه مباشرة
        if (!transfer.approvalRequired) {
            this.executeTransfer(transfer.id);
        }
        
        this.saveData();
        
        console.log(`✅ تم إنشاء طلب النقل: ${transfer.id}`);
        return transfer;
    }

    // تنفيذ النقل
    executeTransfer(transferId) {
        const transfer = this.stockTransfers.get(transferId);
        if (!transfer || transfer.status !== 'pending') {
            throw new Error('طلب النقل غير صالح أو تم تنفيذه مسبقاً');
        }
        
        try {
            // تسجيل حركة خروج من المخزن المصدر
            this.recordInventoryMovement({
                type: 'out',
                productId: transfer.productId,
                quantity: transfer.quantity,
                warehouseId: transfer.fromWarehouseId,
                fromLocationId: transfer.fromLocationId,
                referenceType: 'transfer',
                referenceId: transfer.id,
                reason: `نقل إلى مخزن آخر - ${transfer.reason}`,
                userId: transfer.requestedBy
            });
            
            // تسجيل حركة دخول للمخزن المستهدف
            this.recordInventoryMovement({
                type: 'in',
                productId: transfer.productId,
                quantity: transfer.quantity,
                warehouseId: transfer.toWarehouseId,
                toLocationId: transfer.toLocationId,
                referenceType: 'transfer',
                referenceId: transfer.id,
                reason: `نقل من مخزن آخر - ${transfer.reason}`,
                userId: transfer.requestedBy
            });
            
            // تحديث حالة النقل
            transfer.status = 'completed';
            transfer.completedAt = new Date().toISOString();
            
            this.saveData();
            
            console.log(`✅ تم تنفيذ النقل بنجاح: ${transferId}`);
            
        } catch (error) {
            transfer.status = 'failed';
            transfer.notes += `\nخطأ في التنفيذ: ${error.message}`;
            this.saveData();
            throw error;
        }
    }

    // البحث في المخازن
    searchWarehouses(query, filters = {}) {
        let results = Array.from(this.warehouses.values());
        
        // البحث النصي
        if (query && query.trim()) {
            const searchTerm = query.toLowerCase();
            results = results.filter(warehouse => 
                warehouse.name.toLowerCase().includes(searchTerm) ||
                warehouse.nameEn.toLowerCase().includes(searchTerm) ||
                warehouse.code.toLowerCase().includes(searchTerm) ||
                warehouse.location.city.toLowerCase().includes(searchTerm)
            );
        }
        
        // تطبيق الفلاتر
        if (filters.type) {
            results = results.filter(warehouse => warehouse.type === filters.type);
        }
        
        if (filters.status) {
            results = results.filter(warehouse => warehouse.status === filters.status);
        }
        
        if (filters.city) {
            results = results.filter(warehouse => warehouse.location.city === filters.city);
        }
        
        return results;
    }

    // الحصول على تقارير المخازن
    getWarehousesReports() {
        const warehouses = Array.from(this.warehouses.values());
        const movements = Array.from(this.inventoryMovements.values());
        const transfers = Array.from(this.stockTransfers.values());
        
        return {
            summary: {
                totalWarehouses: warehouses.length,
                activeWarehouses: warehouses.filter(w => w.status === 'active').length,
                totalArea: warehouses.reduce((sum, w) => sum + w.specifications.totalArea, 0),
                totalCapacity: warehouses.reduce((sum, w) => sum + w.specifications.capacity, 0),
                averageUtilization: warehouses.reduce((sum, w) => sum + w.statistics.utilizationRate, 0) / warehouses.length || 0
            },
            
            movements: {
                totalMovements: movements.length,
                todayMovements: movements.filter(m => {
                    const today = new Date().toDateString();
                    return new Date(m.timestamp).toDateString() === today;
                }).length,
                inboundMovements: movements.filter(m => m.type === 'in').length,
                outboundMovements: movements.filter(m => m.type === 'out').length
            },
            
            transfers: {
                totalTransfers: transfers.length,
                pendingTransfers: transfers.filter(t => t.status === 'pending').length,
                completedTransfers: transfers.filter(t => t.status === 'completed').length,
                failedTransfers: transfers.filter(t => t.status === 'failed').length
            },
            
            performance: {
                topPerformingWarehouses: warehouses
                    .sort((a, b) => b.statistics.turnoverRate - a.statistics.turnoverRate)
                    .slice(0, 5),
                utilizationRates: warehouses.map(w => ({
                    warehouseId: w.id,
                    name: w.name,
                    utilizationRate: w.statistics.utilizationRate
                }))
            }
        };
    }

    // دوال مساعدة
    generateWarehouseId() {
        return 'WH_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    generateWarehouseCode() {
        const count = this.warehouses.size + 1;
        return 'WH' + count.toString().padStart(4, '0');
    }

    generateZoneId() {
        return 'ZONE_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    generateZoneCode() {
        return 'Z' + (this.warehouseZones.size + 1).toString().padStart(3, '0');
    }

    generateLocationId() {
        return 'LOC_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    generateLocationCode() {
        return 'L' + (this.warehouseLocations.size + 1).toString().padStart(6, '0');
    }

    generateLocationBarcode() {
        return 'BC' + Date.now().toString().substr(-8);
    }

    generateMovementId() {
        return 'MOV_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    generateTransferId() {
        return 'TRF_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // تحديث إحصائيات المنطقة
    updateZoneStatistics(zoneId) {
        const zone = this.warehouseZones.get(zoneId);
        if (!zone) return;

        const locations = Array.from(this.warehouseLocations.values())
            .filter(loc => loc.zoneId === zoneId);

        zone.statistics.totalLocations = locations.length;
        zone.statistics.occupiedLocations = locations.filter(loc => loc.status === 'occupied').length;
        zone.occupancyRate = zone.statistics.totalLocations > 0 ?
            (zone.statistics.occupiedLocations / zone.statistics.totalLocations) * 100 : 0;

        this.warehouseZones.set(zoneId, zone);
    }

    // تحديث إحصائيات المخزن
    updateWarehouseStatistics(warehouseId) {
        const warehouse = this.warehouses.get(warehouseId);
        if (!warehouse) return;

        const movements = Array.from(this.inventoryMovements.values())
            .filter(mov => mov.warehouseId === warehouseId);

        const zones = Array.from(this.warehouseZones.values())
            .filter(zone => zone.warehouseId === warehouseId);

        // حساب معدل الاستخدام
        const totalCapacity = warehouse.specifications.capacity;
        const currentStock = movements
            .filter(mov => mov.type === 'in')
            .reduce((sum, mov) => sum + mov.quantity, 0) -
            movements
            .filter(mov => mov.type === 'out')
            .reduce((sum, mov) => sum + mov.quantity, 0);

        warehouse.statistics.utilizationRate = totalCapacity > 0 ?
            (currentStock / totalCapacity) * 100 : 0;

        warehouse.statistics.lastMovementDate = movements.length > 0 ?
            Math.max(...movements.map(mov => new Date(mov.timestamp).getTime())) : null;

        this.warehouses.set(warehouseId, warehouse);
    }

    // تحديث مخزون الموقع
    updateLocationInventory(movement) {
        if (movement.toLocationId) {
            const location = this.warehouseLocations.get(movement.toLocationId);
            if (location) {
                if (movement.type === 'in') {
                    location.currentQuantity += movement.quantity;
                    location.currentProduct = movement.productId;
                    location.status = 'occupied';
                } else if (movement.type === 'out') {
                    location.currentQuantity -= movement.quantity;
                    if (location.currentQuantity <= 0) {
                        location.currentQuantity = 0;
                        location.currentProduct = null;
                        location.status = 'empty';
                    }
                }
                location.lastUpdated = new Date().toISOString();
                this.warehouseLocations.set(movement.toLocationId, location);
            }
        }

        if (movement.fromLocationId) {
            const location = this.warehouseLocations.get(movement.fromLocationId);
            if (location) {
                if (movement.type === 'out') {
                    location.currentQuantity -= movement.quantity;
                    if (location.currentQuantity <= 0) {
                        location.currentQuantity = 0;
                        location.currentProduct = null;
                        location.status = 'empty';
                    }
                }
                location.lastUpdated = new Date().toISOString();
                this.warehouseLocations.set(movement.fromLocationId, location);
            }
        }
    }

    // إنشاء المناطق الافتراضية
    createDefaultZones(warehouseId) {
        const defaultZones = [
            { name: 'منطقة الاستلام', type: 'receiving', section: 'A' },
            { name: 'منطقة التخزين الرئيسية', type: 'storage', section: 'B' },
            { name: 'منطقة الشحن', type: 'shipping', section: 'C' },
            { name: 'منطقة التجهيز', type: 'staging', section: 'D' },
            { name: 'منطقة الحجر الصحي', type: 'quarantine', section: 'Q' }
        ];

        defaultZones.forEach(zoneData => {
            this.createWarehouseZone({
                ...zoneData,
                warehouseId: warehouseId,
                area: 100, // متر مربع افتراضي
                capacity: 1000 // كيلو افتراضي
            });
        });
    }

    // تهيئة المخازن الافتراضية
    initializeDefaultWarehouses() {
        if (this.warehouses.size === 0) {
            // إنشاء المخزن الرئيسي
            this.createWarehouse({
                name: 'المخزن الرئيسي',
                nameEn: 'Main Warehouse',
                type: 'main',
                city: 'الرياض',
                district: 'العليا',
                totalArea: 1000,
                storageArea: 800,
                capacity: 10000,
                isDefault: true,
                status: 'active'
            });

            console.log('✅ تم إنشاء المخازن الافتراضية');
        }
    }

    // الحصول على المخزن الافتراضي
    getDefaultWarehouse() {
        return Array.from(this.warehouses.values()).find(w => w.isDefault) ||
               Array.from(this.warehouses.values())[0];
    }

    // تحديث مخزن
    updateWarehouse(warehouseId, updateData) {
        const warehouse = this.warehouses.get(warehouseId);
        if (!warehouse) {
            throw new Error('المخزن غير موجود');
        }

        // تحديث البيانات
        Object.keys(updateData).forEach(key => {
            if (updateData[key] !== undefined) {
                if (typeof warehouse[key] === 'object' && warehouse[key] !== null) {
                    warehouse[key] = { ...warehouse[key], ...updateData[key] };
                } else {
                    warehouse[key] = updateData[key];
                }
            }
        });

        warehouse.updatedAt = new Date().toISOString();
        this.warehouses.set(warehouseId, warehouse);
        this.saveData();

        return warehouse;
    }

    // حذف مخزن
    deleteWarehouse(warehouseId) {
        const warehouse = this.warehouses.get(warehouseId);
        if (!warehouse) {
            throw new Error('المخزن غير موجود');
        }

        // التحقق من وجود مخزون
        const hasInventory = Array.from(this.inventoryMovements.values())
            .some(mov => mov.warehouseId === warehouseId && mov.type === 'in');

        if (hasInventory) {
            throw new Error('لا يمكن حذف المخزن لوجود مخزون به');
        }

        // حذف المناطق والمواقع المرتبطة
        const zones = Array.from(this.warehouseZones.values())
            .filter(zone => zone.warehouseId === warehouseId);

        zones.forEach(zone => {
            const locations = Array.from(this.warehouseLocations.values())
                .filter(loc => loc.zoneId === zone.id);

            locations.forEach(loc => this.warehouseLocations.delete(loc.id));
            this.warehouseZones.delete(zone.id);
        });

        this.warehouses.delete(warehouseId);
        this.saveData();

        console.log(`✅ تم حذف المخزن: ${warehouse.name}`);
    }

    // الحصول على مخزون المنتج في مخزن معين
    getProductStock(productId, warehouseId = null) {
        let movements = Array.from(this.inventoryMovements.values())
            .filter(mov => mov.productId === productId);

        if (warehouseId) {
            movements = movements.filter(mov => mov.warehouseId === warehouseId);
        }

        const inbound = movements
            .filter(mov => mov.type === 'in')
            .reduce((sum, mov) => sum + mov.quantity, 0);

        const outbound = movements
            .filter(mov => mov.type === 'out')
            .reduce((sum, mov) => sum + mov.quantity, 0);

        return inbound - outbound;
    }

    // الحصول على تفاصيل المخزون حسب الموقع
    getInventoryByLocation(warehouseId = null) {
        let locations = Array.from(this.warehouseLocations.values());

        if (warehouseId) {
            locations = locations.filter(loc => loc.warehouseId === warehouseId);
        }

        return locations
            .filter(loc => loc.currentProduct && loc.currentQuantity > 0)
            .map(loc => ({
                locationId: loc.id,
                locationCode: loc.code,
                warehouseId: loc.warehouseId,
                zoneId: loc.zoneId,
                productId: loc.currentProduct,
                quantity: loc.currentQuantity,
                lastUpdated: loc.lastUpdated
            }));
    }

    // حفظ وتحميل البيانات
    saveData() {
        try {
            localStorage.setItem('advancedWarehousesData', JSON.stringify({
                warehouses: Array.from(this.warehouses.entries()),
                zones: Array.from(this.warehouseZones.entries()),
                locations: Array.from(this.warehouseLocations.entries()),
                movements: Array.from(this.inventoryMovements.entries()),
                transfers: Array.from(this.stockTransfers.entries()),
                settings: this.settings
            }));
        } catch (error) {
            console.error('خطأ في حفظ بيانات المخازن:', error);
        }
    }

    loadData() {
        try {
            const data = localStorage.getItem('advancedWarehousesData');
            if (data) {
                const parsed = JSON.parse(data);
                this.warehouses = new Map(parsed.warehouses || []);
                this.warehouseZones = new Map(parsed.zones || []);
                this.warehouseLocations = new Map(parsed.locations || []);
                this.inventoryMovements = new Map(parsed.movements || []);
                this.stockTransfers = new Map(parsed.transfers || []);
                if (parsed.settings) {
                    this.settings = { ...this.settings, ...parsed.settings };
                }
            }
        } catch (error) {
            console.error('خطأ في تحميل بيانات المخازن:', error);
        }
    }
}

// تصدير الفئة
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdvancedWarehousesManager;
}
