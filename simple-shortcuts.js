/**
 * Black Horse POS - Simple Keyboard Shortcuts
 * أوامر سريعة بسيطة ومفيدة
 * Developer: Augment Agent
 */

console.log('⌨️ Loading Simple Shortcuts...');

// ===== SIMPLE SHORTCUTS SYSTEM =====
const SimpleShortcuts = {
    isInitialized: false,
    
    // تهيئة النظام
    init: function() {
        try {
            console.log('⌨️ Initializing Simple Shortcuts...');
            
            // إعداد معالجات لوحة المفاتيح
            this.setupKeyboardHandlers();
            
            // إضافة مؤشر المساعدة
            this.addHelpIndicator();
            
            this.isInitialized = true;
            console.log('✅ Simple Shortcuts initialized successfully');
        } catch (error) {
            console.error('❌ Error initializing Simple Shortcuts:', error);
        }
    },
    
    // إعداد معالجات لوحة المفاتيح
    setupKeyboardHandlers: function() {
        try {
            console.log('🎯 Setting up keyboard handlers...');
            
            document.addEventListener('keydown', (e) => {
                // تجاهل الاختصارات إذا كان المستخدم يكتب في حقل نص
                if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                    return;
                }
                
                // التحقق من الاختصارات
                const key = e.key.toLowerCase();
                const ctrl = e.ctrlKey;
                const alt = e.altKey;
                
                // اختصارات التنقل السريع
                if (ctrl && !alt) {
                    switch (key) {
                        case '1':
                            e.preventDefault();
                            this.navigateToPage('dashboard');
                            break;
                        case '2':
                            e.preventDefault();
                            this.navigateToPage('pos');
                            break;
                        case '3':
                            e.preventDefault();
                            this.navigateToPage('products');
                            break;
                        case '4':
                            e.preventDefault();
                            this.navigateToPage('customers');
                            break;
                        case '5':
                            e.preventDefault();
                            this.navigateToPage('sales');
                            break;
                        case '6':
                            e.preventDefault();
                            this.navigateToPage('reports');
                            break;
                        case '7':
                            e.preventDefault();
                            this.navigateToPage('settings');
                            break;
                        case 'n':
                            e.preventDefault();
                            this.quickAction('new-sale');
                            break;
                        case 'p':
                            e.preventDefault();
                            this.quickAction('new-product');
                            break;
                        case 'c':
                            e.preventDefault();
                            this.quickAction('new-customer');
                            break;
                        case 'r':
                            e.preventDefault();
                            this.quickAction('daily-report');
                            break;
                        case 's':
                            e.preventDefault();
                            this.quickAction('save');
                            break;
                        case 'f':
                            e.preventDefault();
                            this.quickAction('search');
                            break;
                    }
                }
                
                // اختصارات أخرى
                switch (key) {
                    case 'f1':
                        e.preventDefault();
                        this.showHelp();
                        break;
                    case 'f5':
                        e.preventDefault();
                        this.refreshPage();
                        break;
                    case 'escape':
                        this.closeAllModals();
                        break;
                }
            });
            
            console.log('✅ Keyboard handlers set up');
        } catch (error) {
            console.error('❌ Error setting up keyboard handlers:', error);
        }
    },
    
    // التنقل إلى صفحة
    navigateToPage: function(page) {
        try {
            console.log(`🔄 Navigating to: ${page}`);
            
            const pageMap = {
                'dashboard': '#dashboard',
                'pos': '#pos',
                'products': '#products',
                'customers': '#customers',
                'sales': '#sales',
                'reports': '#reports',
                'settings': '#settings'
            };
            
            const targetPage = pageMap[page];
            if (targetPage) {
                // البحث عن الرابط المناسب
                const link = document.querySelector(`a[href="${targetPage}"]`);
                if (link) {
                    link.click();
                    this.showNotification(`تم الانتقال إلى ${this.getPageNameInArabic(page)}`);
                } else {
                    console.warn(`⚠️ Page link not found: ${targetPage}`);
                }
            }
        } catch (error) {
            console.error('❌ Error navigating to page:', error);
        }
    },
    
    // إجراء سريع
    quickAction: function(action) {
        try {
            console.log(`⚡ Quick action: ${action}`);
            
            switch (action) {
                case 'new-sale':
                    this.triggerNewSale();
                    break;
                case 'new-product':
                    this.triggerNewProduct();
                    break;
                case 'new-customer':
                    this.triggerNewCustomer();
                    break;
                case 'daily-report':
                    this.triggerDailyReport();
                    break;
                case 'save':
                    this.triggerSave();
                    break;
                case 'search':
                    this.triggerSearch();
                    break;
                default:
                    console.warn(`⚠️ Unknown action: ${action}`);
            }
        } catch (error) {
            console.error('❌ Error executing quick action:', error);
        }
    },
    
    // تشغيل بيع جديد
    triggerNewSale: function() {
        const newSaleBtn = document.querySelector('#new-sale-btn, .new-sale-btn, [onclick*="newSale"]');
        if (newSaleBtn) {
            newSaleBtn.click();
            this.showNotification('تم فتح بيع جديد');
        } else {
            this.showNotification('لم يتم العثور على زر البيع الجديد', 'warning');
        }
    },
    
    // تشغيل منتج جديد
    triggerNewProduct: function() {
        const newProductBtn = document.querySelector('#new-product-btn, .new-product-btn, [onclick*="newProduct"]');
        if (newProductBtn) {
            newProductBtn.click();
            this.showNotification('تم فتح منتج جديد');
        } else {
            this.showNotification('لم يتم العثور على زر المنتج الجديد', 'warning');
        }
    },
    
    // تشغيل عميل جديد
    triggerNewCustomer: function() {
        const newCustomerBtn = document.querySelector('#new-customer-btn, .new-customer-btn, [onclick*="newCustomer"]');
        if (newCustomerBtn) {
            newCustomerBtn.click();
            this.showNotification('تم فتح عميل جديد');
        } else {
            this.showNotification('لم يتم العثور على زر العميل الجديد', 'warning');
        }
    },
    
    // تشغيل التقرير اليومي
    triggerDailyReport: function() {
        const reportBtn = document.querySelector('#daily-report-btn, .daily-report-btn, [onclick*="dailyReport"]');
        if (reportBtn) {
            reportBtn.click();
            this.showNotification('تم فتح التقرير اليومي');
        } else {
            this.showNotification('لم يتم العثور على زر التقرير اليومي', 'warning');
        }
    },
    
    // تشغيل الحفظ
    triggerSave: function() {
        const saveBtn = document.querySelector('#save-btn, .save-btn, [onclick*="save"]');
        if (saveBtn) {
            saveBtn.click();
            this.showNotification('تم الحفظ');
        } else {
            this.showNotification('لم يتم العثور على زر الحفظ', 'warning');
        }
    },
    
    // تشغيل البحث
    triggerSearch: function() {
        const searchInput = document.querySelector('#search-input, .search-input, input[type="search"]');
        if (searchInput) {
            searchInput.focus();
            this.showNotification('تم تفعيل البحث');
        } else {
            this.showNotification('لم يتم العثور على حقل البحث', 'warning');
        }
    },
    
    // عرض المساعدة
    showHelp: function() {
        const helpText = `
الأوامر السريعة المتاحة:

التنقل السريع:
• Ctrl + 1 → الصفحة الرئيسية
• Ctrl + 2 → نقطة البيع
• Ctrl + 3 → المنتجات
• Ctrl + 4 → العملاء
• Ctrl + 5 → المبيعات
• Ctrl + 6 → التقارير
• Ctrl + 7 → الإعدادات

الإجراءات السريعة:
• Ctrl + N → بيع جديد
• Ctrl + P → منتج جديد
• Ctrl + C → عميل جديد
• Ctrl + R → تقرير يومي
• Ctrl + S → حفظ
• Ctrl + F → بحث

أوامر أخرى:
• F1 → عرض هذه المساعدة
• F5 → تحديث الصفحة
• Escape → إغلاق النوافذ المنبثقة
        `;
        
        alert(helpText);
    },
    
    // تحديث الصفحة
    refreshPage: function() {
        this.showNotification('جاري تحديث الصفحة...');
        setTimeout(() => {
            location.reload();
        }, 1000);
    },
    
    // إغلاق جميع النوافذ المنبثقة
    closeAllModals: function() {
        // إغلاق القوائم المنسدلة
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            menu.style.display = 'none';
        });
        
        // إغلاق النوافذ المنبثقة
        document.querySelectorAll('.modal.show').forEach(modal => {
            modal.classList.remove('show');
        });
        
        this.showNotification('تم إغلاق جميع النوافذ');
    },
    
    // عرض إشعار
    showNotification: function(message, type = 'success') {
        try {
            // إنشاء عنصر الإشعار
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#28a745' : '#ffc107'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-size: 14px;
                max-width: 300px;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
            `;
            
            document.body.appendChild(notification);
            
            // إظهار الإشعار
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            // إخفاء الإشعار
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
            
        } catch (error) {
            console.error('❌ Error showing notification:', error);
        }
    },
    
    // الحصول على اسم الصفحة بالعربية
    getPageNameInArabic: function(page) {
        const names = {
            'dashboard': 'الصفحة الرئيسية',
            'pos': 'نقطة البيع',
            'products': 'المنتجات',
            'customers': 'العملاء',
            'sales': 'المبيعات',
            'reports': 'التقارير',
            'settings': 'الإعدادات'
        };
        return names[page] || page;
    },
    
    // إضافة مؤشر المساعدة
    addHelpIndicator: function() {
        try {
            const helpIndicator = document.createElement('div');
            helpIndicator.id = 'shortcuts-help-indicator';
            helpIndicator.innerHTML = '<i class="fas fa-keyboard"></i> F1';
            helpIndicator.style.cssText = `
                position: fixed;
                bottom: 20px;
                left: 20px;
                background: #007bff;
                color: white;
                padding: 8px 12px;
                border-radius: 20px;
                font-size: 12px;
                cursor: pointer;
                z-index: 1000;
                opacity: 0.7;
                transition: opacity 0.3s ease;
            `;
            
            helpIndicator.addEventListener('click', () => {
                this.showHelp();
            });
            
            helpIndicator.addEventListener('mouseenter', () => {
                helpIndicator.style.opacity = '1';
            });
            
            helpIndicator.addEventListener('mouseleave', () => {
                helpIndicator.style.opacity = '0.7';
            });
            
            document.body.appendChild(helpIndicator);
        } catch (error) {
            console.error('❌ Error adding help indicator:', error);
        }
    }
};

// تهيئة تلقائية عند تحميل الملف
if (typeof window !== 'undefined') {
    window.SimpleShortcuts = SimpleShortcuts;
    
    // تهيئة عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            SimpleShortcuts.init();
        }, 1000);
    });
}

console.log('⌨️ Simple Shortcuts loaded');
