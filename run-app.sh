#!/bin/bash

# Black Horse ERP - تشغيل التطبيق
# نظام إدارة موارد المؤسسات

# تعيين الترميز
export LANG=en_US.UTF-8

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دالة طباعة الرسائل الملونة
print_message() {
    echo -e "${2}${1}${NC}"
}

# دالة التحقق من الأخطاء
check_error() {
    if [ $? -ne 0 ]; then
        print_message "❌ خطأ: $1" $RED
        exit 1
    fi
}

# عرض الترحيب
clear
echo "========================================"
echo "    🐎 Black Horse ERP System"
echo "    نظام إدارة موارد المؤسسات"
echo "========================================"
echo ""

print_message "جاري تشغيل التطبيق..." $BLUE
echo ""

# التحقق من وجود Node.js
if ! command -v node &> /dev/null; then
    print_message "❌ خطأ: Node.js غير مثبت على النظام" $RED
    print_message "يرجى تثبيت Node.js من: https://nodejs.org" $YELLOW
    exit 1
fi

# التحقق من وجود npm
if ! command -v npm &> /dev/null; then
    print_message "❌ خطأ: npm غير مثبت على النظام" $RED
    exit 1
fi

# التحقق من وجود ملفات التطبيق
if [ ! -f "package.json" ]; then
    print_message "❌ خطأ: ملفات التطبيق غير موجودة" $RED
    print_message "تأكد من وجود جميع ملفات النظام" $YELLOW
    exit 1
fi

# التحقق من تثبيت التبعيات
if [ ! -d "node_modules" ]; then
    print_message "📦 جاري تثبيت التبعيات المطلوبة..." $YELLOW
    npm install
    check_error "فشل في تثبيت التبعيات"
fi

print_message "✅ جاري تشغيل Black Horse ERP..." $GREEN
echo ""
print_message "💡 نصائح:" $BLUE
print_message "   - استخدم Ctrl+C لإيقاف التطبيق" $BLUE
print_message "   - لا تغلق هذه النافذة أثناء تشغيل التطبيق" $BLUE
echo ""

# تشغيل التطبيق
npm start

echo ""
print_message "تم إغلاق التطبيق" $YELLOW
read -p "اضغط Enter للمتابعة..."
