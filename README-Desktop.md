# Black Horse ERP - نظام إدارة موارد المؤسسات (إصدار سطح المكتب)

## 🐎 نظرة عامة
Black Horse ERP هو نظام شامل لإدارة موارد المؤسسات مطور خصيصاً لسطح المكتب باستخدام تقنية Electron. يوفر النظام واجهة مستخدم سلسة ومحسنة لسطح المكتب مع دعم كامل للغة العربية.

## ✨ المميزات الجديدة - الإصدار المكتبي

### 🖥️ واجهة سطح المكتب الأصلية
- **تصميم محسن لسطح المكتب**: واجهة مستخدم مصممة خصيصاً لتطبيقات سطح المكتب
- **أدوات تحكم النافذة الأصلية**: أزرار تصغير، تكبير، وإغلاق مدمجة في الواجهة
- **شريط جانبي قابل للطي**: تنقل سهل بين المديولات مع إمكانية إخفاء/إظهار الشريط
- **لوحة تحكم تفاعلية**: عرض إحصائيات المديولات مع تأثيرات بصرية متقدمة

### ⌨️ اختصارات لوحة المفاتيح
- `Ctrl + M`: تبديل الشريط الجانبي
- `F11`: تبديل وضع الشاشة الكاملة
- `Ctrl + Q`: إغلاق التطبيق
- `Ctrl + N`: فتح نقطة بيع جديدة
- `Ctrl + B`: إنشاء نسخة احتياطية

### 🎨 تحسينات بصرية
- **تأثيرات انتقال سلسة**: رسوم متحركة محسنة للتنقل بين الصفحات
- **نظام إشعارات متقدم**: إشعارات منبثقة مع تأثيرات بصرية
- **تدرجات لونية احترافية**: ألوان محسنة لتجربة مستخدم أفضل
- **أيقونات Font Awesome**: مكتبة أيقونات شاملة ومحدثة

## 🚀 كيفية التشغيل

### الطريقة الأولى: ملف التشغيل العربي (Windows)
```bash
# انقر نقراً مزدوجاً على الملف
تشغيل-التطبيق.bat
```

### الطريقة الثانية: ملف التشغيل (Linux/macOS)
```bash
# في الطرفية
./run-app.sh
```

### الطريقة الثالثة: npm (جميع الأنظمة)
```bash
npm start
```

## 📁 هيكل المشروع المحدث

```
Black Horse ERP/
├── 📄 desktop-app.html          # الواجهة الرئيسية المحسنة لسطح المكتب
├── 📄 main.js                   # العملية الرئيسية لـ Electron (محدث)
├── 📄 preload.js                # APIs سطح المكتب (محدث)
├── 📄 تشغيل-التطبيق.bat         # ملف تشغيل Windows
├── 📄 run-app.sh                # ملف تشغيل Linux/macOS
├── 📄 package.json              # إعدادات المشروع
├── 📁 src/                      # مجلد المصادر
│   ├── 📁 modules/              # مديولات النظام
│   │   ├── 📁 inventory/        # إدارة المخزون
│   │   ├── 📁 sales/            # إدارة المبيعات
│   │   ├── 📁 pos/              # نقاط البيع
│   │   ├── 📁 purchases/        # إدارة المشتريات
│   │   ├── 📁 hr/               # الموارد البشرية
│   │   ├── 📁 cashier/          # إدارة الصرافين
│   │   ├── 📁 reports/          # التقارير
│   │   └── 📁 settings/         # الإعدادات
│   ├── 📁 css/                  # ملفات التنسيق
│   ├── 📁 js/                   # ملفات JavaScript
│   └── 📁 assets/               # الموارد (صور، أيقونات)
├── 📁 data/                     # قاعدة البيانات
└── 📁 node_modules/             # حزم Node.js
```

## 🔧 المديولات المتاحة

### 1. 📦 إدارة المخزون
- إضافة وتعديل المنتجات
- تتبع الكميات والتنبيهات
- إدارة الفئات والوحدات
- دعم الباركود

### 2. 💰 إدارة المبيعات
- إنشاء فواتير البيع
- إدارة العملاء
- تتبع المدفوعات
- تقارير المبيعات

### 3. 🏪 نقاط البيع (POS)
- واجهة بيع سريعة
- دعم الباركود
- طرق دفع متعددة
- طباعة حرارية

### 4. 🛒 إدارة المشتريات
- أوامر الشراء
- إدارة الموردين
- استلام البضائع
- تتبع التكاليف

### 5. 👥 الموارد البشرية
- إدارة الموظفين
- نظام الحضور والانصراف
- حساب الرواتب
- إدارة الإجازات

### 6. 💳 إدارة الصرافين
- حسابات الصرافين
- تتبع المعاملات
- تقارير الصندوق
- إدارة الورديات

### 7. 📊 التقارير والتحليلات
- تقارير مالية شاملة
- تحليلات المبيعات
- تقارير المخزون
- لوحات معلومات تفاعلية

### 8. ⚙️ إعدادات النظام
- إعدادات الشركة
- إعدادات الطباعة
- إدارة المستخدمين
- النسخ الاحتياطي

## 🎯 الإجراءات السريعة

تتيح لوحة التحكم الرئيسية الوصول السريع إلى:
- **بيع جديد**: فتح نقطة البيع مباشرة
- **إضافة منتج**: إضافة منتج جديد للمخزون
- **عميل جديد**: إضافة عميل جديد
- **تقرير يومي**: عرض تقرير اليوم
- **نسخة احتياطية**: إنشاء نسخة احتياطية فورية
- **الإعدادات**: الوصول لإعدادات النظام

## 🔒 الأمان والنسخ الاحتياطي

### النسخ الاحتياطي التلقائي
- إنشاء نسخ احتياطية بضغطة زر
- حفظ جميع البيانات والإعدادات
- استعادة سهلة للبيانات

### أمان البيانات
- تشفير قاعدة البيانات المحلية
- حماية من فقدان البيانات
- نظام صلاحيات متقدم

## 🛠️ المتطلبات التقنية

### الحد الأدنى:
- **نظام التشغيل**: Windows 10, macOS 10.13, Ubuntu 18.04
- **الذاكرة**: 4 GB RAM
- **المساحة**: 500 MB مساحة فارغة
- **الشاشة**: 1366x768 أو أعلى

### المستحسن:
- **نظام التشغيل**: Windows 11, macOS 12+, Ubuntu 20.04+
- **الذاكرة**: 8 GB RAM أو أكثر
- **المساحة**: 2 GB مساحة فارغة
- **الشاشة**: 1920x1080 أو أعلى

## 🆘 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

1. **التطبيق لا يبدأ**:
   ```bash
   npm install
   npm start
   ```

2. **أخطاء GPU في وحدة التحكم**:
   - هذه أخطاء عادية في Electron ولا تؤثر على الوظائف

3. **المديولات لا تفتح**:
   - تأكد من وجود ملفات المديولات في مجلد `src/modules/`

4. **مشاكل الخطوط العربية**:
   - تأكد من تثبيت خطوط عربية على النظام

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- 📧 البريد الإلكتروني: <EMAIL>
- 📱 الهاتف: +20-XXX-XXX-XXXX
- 🌐 الموقع: www.blackhorse-erp.com

## 📝 سجل التحديثات

### الإصدار 1.0.0 - إصدار سطح المكتب
- ✅ تحويل كامل لتطبيق سطح مكتب أصلي
- ✅ واجهة مستخدم محسنة ومتجاوبة
- ✅ نظام تنقل متقدم مع شريط جانبي
- ✅ اختصارات لوحة مفاتيح شاملة
- ✅ نظام إشعارات متطور
- ✅ تحسينات الأداء والاستقرار
- ✅ دعم كامل للغة العربية RTL
- ✅ نظام نسخ احتياطي متقدم

---

**© 2024 Black Horse ERP - جميع الحقوق محفوظة**
