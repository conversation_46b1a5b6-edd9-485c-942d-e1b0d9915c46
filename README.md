# Black Horse ERP - نظام إدارة الأعمال الشامل

## 🚀 كيفية تشغيل النظام

### الطريقة الأولى: تشغيل مباشر
1. افتح ملف `index.html` في المتصفح مباشرة
2. سيتم تحميل النظام تلقائياً

### الطريقة الثانية: خادم محلي (مُوصى بها)
```bash
# إذا كان لديك Python مثبت
python -m http.server 8000

# أو إذا كان لديك Node.js مثبت
npx http-server

# ثم افتح المتصفح على
http://localhost:8000
```

### الطريقة الثالثة: Live Server (VS Code)
1. ثبت إضافة Live Server في VS Code
2. انقر بزر الماوس الأيمن على `index.html`
3. اختر "Open with Live Server"

## 📁 هيكل المشروع

```
Black Horse ERP/
├── index.html              # الملف الرئيسي لتشغيل النظام
├── test-hr.html           # صفحة اختبار وحدة الموارد البشرية
├── README.md              # دليل الاستخدام
├── src/
│   ├── core/
│   │   ├── database.js    # نظام قاعدة البيانات
│   │   └── utils.js       # الوظائف المساعدة
│   └── modules/
│       ├── inventory/
│       │   └── inventory.js    # وحدة إدارة المخزون
│       ├── sales/
│       │   └── sales.js        # وحدة المبيعات
│       ├── pos/
│       │   └── pos.js          # وحدة نقطة البيع
│       ├── purchases/
│       │   └── purchases.js    # وحدة المشتريات
│       ├── cashier/
│       │   └── cashier.js      # وحدة إدارة الصندوق
│       └── hr/
│           └── hr.js           # وحدة الموارد البشرية
```

## 🎯 الوحدات المكتملة

### ✅ وحدة إدارة المخزون
- إدارة المنتجات والفئات
- تتبع المخزون والكميات
- تنبيهات المخزون المنخفض
- إدارة الباركود

### ✅ وحدة المبيعات
- إنشاء وإدارة الفواتير
- إدارة العملاء
- تتبع المدفوعات
- تقارير المبيعات

### ✅ وحدة نقطة البيع (POS)
- واجهة بيع سريعة
- دعم الباركود
- طرق دفع متعددة
- طباعة الفواتير

### ✅ وحدة المشتريات
- إدارة أوامر الشراء
- إدارة الموردين
- تتبع المدفوعات للموردين
- تقارير المشتريات

### ✅ وحدة إدارة الصندوق
- إدارة متعددة الصناديق
- تتبع المعاملات النقدية
- تقارير الصندوق
- إدارة الكاشيرز

### ✅ وحدة الموارد البشرية
- إدارة الموظفين
- نظام الحضور والانصراف
- إدارة الرواتب
- تقييم الأداء

## 🔄 الوحدات قيد التطوير

### 🚧 نظام التقارير الشامل
- تقارير الأرباح والخسائر
- تحليلات المبيعات المتقدمة
- تقارير المشتريات
- ذكاء الأعمال

### 🚧 إعدادات النظام
- إعدادات طرق الدفع
- تكاملات API
- النسخ الاحتياطي
- تخصيص الواجهة

## 🛠️ التقنيات المستخدمة

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **UI Framework**: Bootstrap 5 RTL
- **Icons**: Font Awesome 6
- **Database**: IndexedDB (قاعدة بيانات محلية)
- **Architecture**: Modular JavaScript

## 📱 الميزات

### 🌐 دعم اللغة العربية
- واجهة مستخدم باللغة العربية
- دعم RTL (من اليمين لليسار)
- تنسيق التواريخ والأرقام العربية

### 💾 قاعدة بيانات محلية
- تخزين البيانات محلياً باستخدام IndexedDB
- لا يتطلب خادم قاعدة بيانات
- سرعة في الوصول للبيانات

### 📊 تقارير وإحصائيات
- لوحة تحكم تفاعلية
- إحصائيات في الوقت الفعلي
- تصدير البيانات

### 🔒 أمان البيانات
- تشفير البيانات الحساسة
- نسخ احتياطية
- صلاحيات المستخدمين

## 🎮 كيفية الاستخدام

### البدء السريع
1. افتح `index.html` في المتصفح
2. ستظهر لوحة التحكم الرئيسية
3. استخدم القائمة الجانبية للتنقل بين الوحدات
4. ابدأ بإضافة البيانات الأساسية (المنتجات، العملاء، الموظفين)

### إضافة منتج جديد
1. اذهب إلى وحدة "إدارة المخزون"
2. انقر على "إضافة منتج"
3. املأ البيانات المطلوبة
4. احفظ المنتج

### إنشاء فاتورة مبيعات
1. اذهب إلى وحدة "المبيعات"
2. انقر على "فاتورة جديدة"
3. اختر العميل والمنتجات
4. احفظ الفاتورة

### استخدام نقطة البيع
1. اذهب إلى وحدة "نقطة البيع"
2. امسح الباركود أو ابحث عن المنتج
3. أضف المنتجات للسلة
4. اختر طريقة الدفع واكمل البيع

## 🧪 اختبار النظام

### اختبار وحدة الموارد البشرية
افتح `test-hr.html` لاختبار وحدة الموارد البشرية بشكل منفصل

### بيانات تجريبية
النظام يقوم بإنشاء بيانات تجريبية تلقائياً عند التشغيل الأول

## 🐛 استكشاف الأخطاء

### المشاكل الشائعة
1. **النظام لا يعمل**: تأكد من تشغيله على خادم محلي وليس فتح الملف مباشرة
2. **البيانات لا تحفظ**: تأكد من أن المتصفح يدعم IndexedDB
3. **الواجهة لا تظهر بشكل صحيح**: تأكد من اتصال الإنترنت لتحميل Bootstrap و Font Awesome

### متطلبات النظام
- متصفح حديث يدعم ES6+ و IndexedDB
- اتصال إنترنت لتحميل المكتبات الخارجية
- دقة شاشة 1024x768 أو أعلى

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع هذا الدليل أولاً
- تحقق من وحدة التحكم في المتصفح للأخطاء
- تأكد من تحديث المتصفح لآخر إصدار

## 🔄 التحديثات المستقبلية

- إضافة المزيد من التقارير
- تحسين الأداء
- إضافة ميزات جديدة
- دعم التصدير والاستيراد
- تطبيق الهاتف المحمول

---

**Black Horse ERP** - نظام إدارة الأعمال الشامل باللغة العربية 🐎
