# Black Horse ERP System 🐎

نظام إدارة الأعمال الشامل - Black Horse ERP

[![Version](https://img.shields.io/badge/version-2.0.0-blue.svg)](https://github.com/blackhorse/erp)
[![License](https://img.shields.io/badge/license-Proprietary-red.svg)](LICENSE)
[![Arabic](https://img.shields.io/badge/language-Arabic%20RTL-green.svg)](README.md)

## 📋 الوصف

نظام إدارة أعمال شامل ومتطور يتضمن 8 وحدات رئيسية لإدارة جميع جوانب الأعمال التجارية. مصمم خصيصاً للشركات العربية مع دعم كامل للغة العربية واتجاه النص من اليمين إلى اليسار (RTL).

## 🚀 كيفية تشغيل النظام

### 🆕 للمستخدمين الجدد: معالج الإعداد الأولي
1. افتح ملف `setup-wizard.html` في المتصفح
2. اتبع خطوات معالج الإعداد (5 خطوات سهلة):
   - معلومات الشركة
   - اختيار الوحدات
   - البيانات الأولية
   - إعدادات النظام
   - إكمال الإعداد
3. سيتم توجيهك تلقائياً للنظام الرئيسي

### الطريقة الأولى: تشغيل مباشر ⚡
1. افتح ملف `index.html` في المتصفح مباشرة
2. سيتم تحميل النظام وتهيئة قاعدة البيانات تلقائياً
3. ابدأ في استخدام النظام فوراً

### الطريقة الثانية: خادم محلي (مُوصى بها) 🌐
```bash
# إذا كان لديك Python مثبت
python -m http.server 8000

# أو إذا كان لديك Node.js مثبت
npx http-server

# ثم افتح المتصفح على
http://localhost:8000
```

### الطريقة الثالثة: Live Server (VS Code) 💻
1. ثبت إضافة Live Server في VS Code
2. انقر بزر الماوس الأيمن على `index.html`
3. اختر "Open with Live Server"

### 🔧 أدوات التشخيص والصيانة
- **البدء السريع**: افتح `start.html` لواجهة بدء احترافية مع تحميل تدريجي
- **تشخيص النظام**: افتح `system-health-check.html` لفحص حالة النظام الشامل
- **الإصلاح التلقائي**: افتح `system-repair.html` لإصلاح المشاكل تلقائياً
- **اختبار الوحدات**: افتح `test-system.html` لاختبار جميع الوحدات
- **معالج الإعداد**: افتح `setup-wizard.html` لإعادة إعداد النظام
- **الإصلاح اليدوي**: افتح `fix-system.html` للإصلاح المتقدم

## 📁 هيكل المشروع

```
Black Horse ERP/
├── 📄 index.html                    # الملف الرئيسي لتشغيل النظام
├── 📄 setup-wizard.html            # معالج الإعداد الأولي
├── 📄 system-diagnostics.html      # أداة تشخيص النظام الشامل
├── 📄 test-system.html             # نظام الاختبار الشامل
├── 📄 README.md                    # دليل الاستخدام (هذا الملف)
├── 📁 src/                         # مجلد الكود المصدري
│   ├── 📁 core/                    # الملفات الأساسية
│   │   ├── 📄 database.js          # نظام قاعدة البيانات IndexedDB
│   │   └── 📄 utils.js             # الوظائف المساعدة والأدوات
│   ├── 📁 modules/                 # وحدات النظام الرئيسية
│   │   ├── 📁 inventory/           # 🏪 وحدة إدارة المخزون
│   │   │   └── 📄 inventory.js
│   │   ├── 📁 sales/               # 💰 وحدة إدارة المبيعات
│   │   │   └── 📄 sales.js
│   │   ├── 📁 pos/                 # 🛒 وحدة نقطة البيع
│   │   │   └── 📄 pos.js
│   │   ├── 📁 purchases/           # 📦 وحدة إدارة المشتريات
│   │   │   └── 📄 purchases.js
│   │   ├── 📁 cashier/             # 👥 وحدة إدارة الصرافين
│   │   │   └── 📄 cashier.js
│   │   ├── 📁 hr/                  # 👨‍💼 وحدة الموارد البشرية
│   │   │   └── 📄 hr.js
│   │   ├── 📁 reports/             # 📊 وحدة التقارير
│   │   │   └── 📄 reports.js
│   │   └── 📁 settings/            # ⚙️ وحدة الإعدادات
│   │       └── 📄 settings.js
│   └── 📁 assets/                  # الملفات الثابتة
│       ├── 📁 css/                 # ملفات التنسيق
│       │   └── 📄 custom.css       # تنسيقات مخصصة متطورة
│       └── 📁 js/                  # ملفات JavaScript إضافية
│           ├── 📄 app.js           # وظائف التطبيق الرئيسية
│           ├── 📄 performance.js   # تحسين الأداء والسرعة
│           └── 📄 error-handler.js # معالج الأخطاء والإصلاح التلقائي
```

## 🛠️ أدوات النظام المتقدمة

### 🧙‍♂️ معالج الإعداد الأولي
- **الملف**: `setup-wizard.html`
- **الوصف**: معالج تفاعلي لإعداد النظام لأول مرة
- **الميزات**:
  - إعداد معلومات الشركة
  - اختيار الوحدات المطلوبة
  - إنشاء حساب المدير
  - تحميل بيانات تجريبية
  - واجهة سهلة ومتدرجة

### 🔍 أداة التشخيص الشامل
- **الملف**: `system-diagnostics.html`
- **الوصف**: فحص شامل لحالة النظام وأدائه
- **الميزات**:
  - فحص دعم المتصفح
  - فحص قاعدة البيانات
  - فحص الملفات الأساسية
  - فحص وحدات النظام
  - مراقبة الأداء والذاكرة
  - تقارير مفصلة عن الأخطاء

### ⚡ تحسين الأداء التلقائي
- **الملف**: `src/assets/js/performance.js`
- **الوصف**: نظام تحسين الأداء والسرعة
- **الميزات**:
  - تحميل كسول للوحدات والصور
  - إدارة ذكية للذاكرة
  - تحسين طلبات الشبكة
  - مراقبة الأداء المستمر
  - تنظيف تلقائي للكاش

### 🛡️ معالج الأخطاء التلقائي
- **الملف**: `src/assets/js/error-handler.js`
- **الوصف**: نظام معالجة وإصلاح الأخطاء تلقائياً
- **الميزات**:
  - اكتشاف الأخطاء تلقائياً
  - إصلاح المشاكل الشائعة
  - استرداد النظام عند الأخطاء الحرجة
  - تسجيل مفصل للأخطاء
  - رسائل خطأ واضحة للمستخدم

## 🎯 الوحدات المكتملة

### ✅ وحدة إدارة المخزون
- إدارة المنتجات والفئات
- تتبع المخزون والكميات
- تنبيهات المخزون المنخفض
- إدارة الباركود

### ✅ وحدة المبيعات
- إنشاء وإدارة الفواتير
- إدارة العملاء
- تتبع المدفوعات
- تقارير المبيعات

### ✅ وحدة نقطة البيع (POS)
- واجهة بيع سريعة
- دعم الباركود
- طرق دفع متعددة
- طباعة الفواتير

### ✅ وحدة المشتريات
- إدارة أوامر الشراء
- إدارة الموردين
- تتبع المدفوعات للموردين
- تقارير المشتريات

### ✅ وحدة إدارة الصندوق
- إدارة متعددة الصناديق
- تتبع المعاملات النقدية
- تقارير الصندوق
- إدارة الكاشيرز

### ✅ وحدة الموارد البشرية
- إدارة الموظفين
- نظام الحضور والانصراف
- إدارة الرواتب
- تقييم الأداء

## 🔄 الوحدات قيد التطوير

### 🚧 نظام التقارير الشامل
- تقارير الأرباح والخسائر
- تحليلات المبيعات المتقدمة
- تقارير المشتريات
- ذكاء الأعمال

### 🚧 إعدادات النظام
- إعدادات طرق الدفع
- تكاملات API
- النسخ الاحتياطي
- تخصيص الواجهة

## 🛠️ التقنيات المستخدمة

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **UI Framework**: Bootstrap 5 RTL
- **Icons**: Font Awesome 6
- **Database**: IndexedDB (قاعدة بيانات محلية)
- **Architecture**: Modular JavaScript

## 📱 الميزات

### 🌐 دعم اللغة العربية
- واجهة مستخدم باللغة العربية
- دعم RTL (من اليمين لليسار)
- تنسيق التواريخ والأرقام العربية

### 💾 قاعدة بيانات محلية
- تخزين البيانات محلياً باستخدام IndexedDB
- لا يتطلب خادم قاعدة بيانات
- سرعة في الوصول للبيانات

### 📊 تقارير وإحصائيات
- لوحة تحكم تفاعلية
- إحصائيات في الوقت الفعلي
- تصدير البيانات

### 🔒 أمان البيانات
- تشفير البيانات الحساسة
- نسخ احتياطية
- صلاحيات المستخدمين

## 🎮 كيفية الاستخدام

### البدء السريع
1. افتح `index.html` في المتصفح
2. ستظهر لوحة التحكم الرئيسية
3. استخدم القائمة الجانبية للتنقل بين الوحدات
4. ابدأ بإضافة البيانات الأساسية (المنتجات، العملاء، الموظفين)

### إضافة منتج جديد
1. اذهب إلى وحدة "إدارة المخزون"
2. انقر على "إضافة منتج"
3. املأ البيانات المطلوبة
4. احفظ المنتج

### إنشاء فاتورة مبيعات
1. اذهب إلى وحدة "المبيعات"
2. انقر على "فاتورة جديدة"
3. اختر العميل والمنتجات
4. احفظ الفاتورة

### استخدام نقطة البيع
1. اذهب إلى وحدة "نقطة البيع"
2. امسح الباركود أو ابحث عن المنتج
3. أضف المنتجات للسلة
4. اختر طريقة الدفع واكمل البيع

## 🧪 اختبار النظام

### اختبار وحدة الموارد البشرية
افتح `test-hr.html` لاختبار وحدة الموارد البشرية بشكل منفصل

### بيانات تجريبية
النظام يقوم بإنشاء بيانات تجريبية تلقائياً عند التشغيل الأول

## 🔧 استكشاف الأخطاء وإصلاحها

### أدوات التشخيص التلقائي 🛠️

#### 🔍 أداة التشخيص الشامل
- افتح `system-diagnostics.html` لفحص شامل للنظام
- يتضمن 8 اختبارات مختلفة لحالة النظام
- تقارير مفصلة عن الأخطاء والمشاكل
- اقتراحات للحلول التلقائية

#### ⚡ نظام الإصلاح التلقائي
- النظام يحتوي على معالج أخطاء ذكي
- إصلاح تلقائي للمشاكل الشائعة
- استرداد النظام عند الأخطاء الحرجة
- تسجيل مفصل لجميع الأخطاء

### المشاكل الشائعة وحلولها
1. **النظام لا يعمل**: تأكد من تشغيله على خادم محلي وليس فتح الملف مباشرة
   - **التشخيص**: استخدم `system-diagnostics.html` للفحص
2. **البيانات لا تحفظ**: تأكد من أن المتصفح يدعم IndexedDB
   - **الإصلاح التلقائي**: النظام يتحول لـ localStorage تلقائياً
3. **الواجهة لا تظهر بشكل صحيح**: تأكد من اتصال الإنترنت لتحميل Bootstrap و Font Awesome
   - **المراقبة**: أداة التشخيص تفحص الموارد الخارجية
4. **بطء في الأداء**: نظام تحسين الأداء يعمل تلقائياً
   - **المراقبة**: مراقبة الأداء المستمر مدمجة في النظام

### متطلبات النظام
- متصفح حديث يدعم ES6+ و IndexedDB
- اتصال إنترنت لتحميل المكتبات الخارجية
- دقة شاشة 1024x768 أو أعلى
- ذاكرة RAM: 4GB أو أكثر (موصى به)

## 📞 الدعم والمساعدة

### الدعم التقني الذاتي 🔧
- **أداة التشخيص**: `system-diagnostics.html` - فحص شامل للنظام
- **معالج الإعداد**: `setup-wizard.html` - إعادة إعداد النظام
- **نظام الاختبار**: `test-system.html` - اختبار جميع الوحدات
- **الإصلاح التلقائي**: معالج الأخطاء المدمج يصلح المشاكل تلقائياً

### للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع هذا الدليل أولاً
- استخدم أدوات التشخيص المدمجة
- تحقق من وحدة التحكم في المتصفح للأخطاء
- تأكد من تحديث المتصفح لآخر إصدار

### معلومات الاتصال
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +20 123 456 7890
- **ساعات العمل**: من السبت إلى الخميس، 9 صباحاً - 6 مساءً
- **الدعم التقني**: متاح 24/7 عبر أدوات التشخيص المدمجة

## 🔄 التحديثات المستقبلية

- إضافة المزيد من التقارير
- تحسين الأداء
- إضافة ميزات جديدة
- دعم التصدير والاستيراد
- تطبيق الهاتف المحمول

---

**Black Horse ERP** - نظام إدارة الأعمال الشامل باللغة العربية 🐎
