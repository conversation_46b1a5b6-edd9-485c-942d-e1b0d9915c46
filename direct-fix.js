// Direct Fix - إصلاح مباشر
console.log('🔧 Loading Direct Fix...');

// Wait for everything to load
window.addEventListener('load', function() {
    setTimeout(function() {
        console.log('🚀 Starting Direct Fix...');
        
        // DIRECT FIX 1: Create working addProduct function
        window.addProduct = function() {
            console.log('🔄 Direct addProduct called...');
            
            try {
                // Get form elements
                const nameField = document.getElementById('productName');
                const priceField = document.getElementById('productPrice');
                const stockField = document.getElementById('productStock');
                const barcodeField = document.getElementById('productBarcode');
                
                // Check if elements exist
                if (!nameField || !priceField || !stockField) {
                    alert('عناصر النموذج غير موجودة');
                    return;
                }
                
                // Get values
                const name = nameField.value.trim();
                const price = parseFloat(priceField.value);
                const stock = parseInt(stockField.value);
                const barcode = barcodeField ? barcodeField.value.trim() : '';
                
                // Validate
                if (!name) {
                    alert('يرجى إدخال اسم المنتج');
                    nameField.focus();
                    return;
                }
                
                if (!price || price <= 0) {
                    alert('يرجى إدخال سعر صحيح');
                    priceField.focus();
                    return;
                }
                
                if (!stock || stock < 0) {
                    alert('يرجى إدخال كمية صحيحة');
                    stockField.focus();
                    return;
                }
                
                // Generate barcode if empty
                let finalBarcode = barcode;
                if (!finalBarcode) {
                    finalBarcode = '2' + Date.now().toString().slice(-11);
                    if (barcodeField) {
                        barcodeField.value = finalBarcode;
                    }
                }
                
                // Create product object
                const product = {
                    id: Date.now().toString(),
                    name: name,
                    price: price,
                    stock: stock,
                    barcode: finalBarcode,
                    dateAdded: new Date().toISOString()
                };
                
                // Get existing products
                let products = [];
                try {
                    const stored = localStorage.getItem('products');
                    if (stored) {
                        products = JSON.parse(stored);
                    }
                } catch (e) {
                    console.log('No existing products found');
                }
                
                // Check for duplicate barcode
                const existingProduct = products.find(p => p.barcode === finalBarcode);
                if (existingProduct) {
                    alert('الباركود موجود مسبقاً');
                    if (barcodeField) barcodeField.focus();
                    return;
                }
                
                // Add product
                products.push(product);
                
                // Save to localStorage
                localStorage.setItem('products', JSON.stringify(products));
                
                // Show success message
                alert('تم إضافة المنتج بنجاح');
                
                // Reset form
                nameField.value = '';
                priceField.value = '';
                stockField.value = '';
                if (barcodeField) barcodeField.value = '';
                
                // Close modal
                const modal = document.getElementById('addProductModal');
                if (modal) {
                    modal.style.display = 'none';
                }
                
                // Refresh display if function exists
                if (typeof displayProducts === 'function') {
                    displayProducts();
                }
                
                console.log('✅ Product added successfully:', product);
                
            } catch (error) {
                console.error('❌ Error in addProduct:', error);
                alert('حدث خطأ في إضافة المنتج: ' + error.message);
            }
        };
        
        // DIRECT FIX 2: Create working generateBarcode function
        window.generateBarcode = function() {
            console.log('🔄 Direct generateBarcode called...');
            
            try {
                const barcodeField = document.getElementById('productBarcode');
                if (!barcodeField) {
                    alert('حقل الباركود غير موجود');
                    return;
                }
                
                // Generate unique barcode
                const timestamp = Date.now().toString();
                const barcode = '2' + timestamp.slice(-11);
                
                barcodeField.value = barcode;
                barcodeField.focus();
                
                alert('تم توليد الباركود: ' + barcode);
                console.log('✅ Barcode generated:', barcode);
                
            } catch (error) {
                console.error('❌ Error in generateBarcode:', error);
                alert('حدث خطأ في توليد الباركود: ' + error.message);
            }
        };
        
        // DIRECT FIX 3: Create working showModal function
        window.showModal = function(modalId) {
            console.log('🔄 Direct showModal called for:', modalId);
            
            try {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.style.display = 'block';
                    console.log('✅ Modal shown:', modalId);
                } else {
                    console.error('❌ Modal not found:', modalId);
                }
            } catch (error) {
                console.error('❌ Error in showModal:', error);
            }
        };
        
        // DIRECT FIX 4: Create working closeModal function
        window.closeModal = function(modalId) {
            console.log('🔄 Direct closeModal called for:', modalId);
            
            try {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.style.display = 'none';
                    console.log('✅ Modal closed:', modalId);
                } else {
                    console.error('❌ Modal not found:', modalId);
                }
            } catch (error) {
                console.error('❌ Error in closeModal:', error);
            }
        };
        
        // DIRECT FIX 5: Attach event listeners directly
        function attachDirectEventListeners() {
            console.log('🔗 Attaching direct event listeners...');
            
            // Add Product Button
            const addProductBtn = document.querySelector('[onclick*="addProduct"]');
            if (addProductBtn) {
                addProductBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    window.addProduct();
                });
                console.log('✅ addProduct button listener attached');
            }
            
            // Generate Barcode Button
            const generateBarcodeBtn = document.querySelector('[onclick*="generateBarcode"]');
            if (generateBarcodeBtn) {
                generateBarcodeBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    window.generateBarcode();
                });
                console.log('✅ generateBarcode button listener attached');
            }
            
            // Close Modal Buttons
            const closeButtons = document.querySelectorAll('[onclick*="closeModal"]');
            closeButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    const onclick = button.getAttribute('onclick');
                    const modalMatch = onclick.match(/closeModal\(['"]([^'"]+)['"]\)/);
                    if (modalMatch) {
                        window.closeModal(modalMatch[1]);
                    }
                });
            });
            console.log('✅ closeModal buttons listeners attached');
        }
        
        // Attach listeners
        attachDirectEventListeners();
        
        // Test functions
        console.log('🧪 Testing functions...');
        console.log('addProduct available:', typeof window.addProduct === 'function');
        console.log('generateBarcode available:', typeof window.generateBarcode === 'function');
        console.log('showModal available:', typeof window.showModal === 'function');
        console.log('closeModal available:', typeof window.closeModal === 'function');
        
        console.log('✅ Direct Fix completed successfully!');
        alert('تم تحميل الإصلاحات المباشرة - جرب الآن إضافة منتج');
        
    }, 1000);
});

console.log('✅ Direct Fix loaded');
