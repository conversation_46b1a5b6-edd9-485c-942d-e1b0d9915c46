<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حقيقي - Black Horse POS</title>
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- الأيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .test-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            max-width: 1200px;
        }
        
        .console-output {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 8px;
        }
        
        .status-ok { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .status-loading { 
            background: #17a2b8; 
            animation: pulse 1s infinite; 
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .btn-test {
            margin: 5px;
            min-width: 180px;
        }
        
        .test-result {
            padding: 8px;
            border-radius: 5px;
            margin: 5px 0;
            font-size: 14px;
        }
        
        .test-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .test-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .test-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container-fluid p-4">
        <div class="test-container p-4">
            <div class="text-center mb-4">
                <h1 class="text-primary">
                    <i class="fas fa-vials me-2"></i>
                    اختبار حقيقي للنظام
                </h1>
                <p class="text-muted">اختبار مباشر مع تحميل الوحدات الفعلية</p>
            </div>
            
            <!-- حالة تحميل الوحدات -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <span class="status-indicator status-loading" id="utilsStatus"></span>
                            <h6 id="utilsText">جاري التحميل...</h6>
                            <small class="text-muted">Utils</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <span class="status-indicator status-loading" id="storageStatus"></span>
                            <h6 id="storageText">جاري التحميل...</h6>
                            <small class="text-muted">Storage</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <span class="status-indicator status-loading" id="productsStatus"></span>
                            <h6 id="productsText">جاري التحميل...</h6>
                            <small class="text-muted">Products</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <span class="status-indicator status-loading" id="systemStatus"></span>
                            <h6 id="systemText">جاري التحميل...</h6>
                            <small class="text-muted">System</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- أزرار الاختبار -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <h5>اختبار الوظائف الأساسية:</h5>
                    <button class="btn btn-primary btn-test" onclick="testUtils()" disabled id="testUtilsBtn">
                        <i class="fas fa-tools me-2"></i>اختبار Utils
                    </button>
                    <button class="btn btn-success btn-test" onclick="testStorage()" disabled id="testStorageBtn">
                        <i class="fas fa-database me-2"></i>اختبار التخزين
                    </button>
                    <button class="btn btn-info btn-test" onclick="testProducts()" disabled id="testProductsBtn">
                        <i class="fas fa-box me-2"></i>اختبار المنتجات
                    </button>
                </div>
                <div class="col-md-6">
                    <h5>اختبار النماذج:</h5>
                    <button class="btn btn-warning btn-test" onclick="testShowMessage()" disabled id="testMessageBtn">
                        <i class="fas fa-comment me-2"></i>اختبار الرسائل
                    </button>
                    <button class="btn btn-secondary btn-test" onclick="testDataOperations()" disabled id="testDataBtn">
                        <i class="fas fa-exchange-alt me-2"></i>اختبار البيانات
                    </button>
                    <button class="btn btn-dark btn-test" onclick="runAllTests()" disabled id="runAllBtn">
                        <i class="fas fa-play me-2"></i>تشغيل جميع الاختبارات
                    </button>
                </div>
            </div>
            
            <!-- نتائج الاختبار -->
            <div class="mb-4">
                <h5>نتائج الاختبار:</h5>
                <div id="testResults" style="max-height: 300px; overflow-y: auto;"></div>
            </div>
            
            <!-- سجل العمليات -->
            <div class="mb-4">
                <h5>سجل العمليات:</h5>
                <div class="console-output" id="consoleOutput">
بدء تحميل النظام...
                </div>
                <div class="mt-3">
                    <button class="btn btn-outline-secondary btn-sm" onclick="clearConsole()">
                        <i class="fas fa-trash me-2"></i>مسح السجل
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="window.open('src/index.html', '_blank')">
                        <i class="fas fa-external-link-alt me-2"></i>فتح النظام الرئيسي
                    </button>
                </div>
            </div>
            
            <!-- إحصائيات -->
            <div class="row text-center">
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <h3 id="successCount">0</h3>
                            <p>اختبارات ناجحة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <h3 id="errorCount">0</h3>
                            <p>أخطاء</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <h3 id="totalTests">0</h3>
                            <p>إجمالي الاختبارات</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- تحميل الوحدات الفعلية -->
    <script src="src/utils/helpers.js"></script>
    <script src="src/database/unified-storage.js"></script>
    <script src="src/modules/products/products-manager.js"></script>
    
    <script>
        let successCount = 0;
        let errorCount = 0;
        let totalTests = 0;
        let modulesLoaded = {};
        
        function logToConsole(message) {
            const console = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            console.textContent += `\n[${timestamp}] ${message}`;
            console.scrollTop = console.scrollHeight;
        }
        
        function updateStatus(elementId, status, text) {
            const indicator = document.getElementById(elementId);
            const textElement = document.getElementById(elementId.replace('Status', 'Text'));
            
            indicator.className = `status-indicator status-${status}`;
            textElement.textContent = text;
        }
        
        function addTestResult(test, status, message) {
            totalTests++;
            
            if (status === 'success') successCount++;
            else if (status === 'error') errorCount++;
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result test-${status}`;
            resultDiv.innerHTML = `
                <i class="fas fa-${status === 'success' ? 'check' : status === 'error' ? 'times' : 'exclamation-triangle'} me-2"></i>
                <strong>${test}:</strong> ${message}
            `;
            
            document.getElementById('testResults').appendChild(resultDiv);
            updateCounters();
            logToConsole(`${status === 'success' ? '✅' : status === 'error' ? '❌' : '⚠️'} ${test}: ${message}`);
        }
        
        function updateCounters() {
            document.getElementById('successCount').textContent = successCount;
            document.getElementById('errorCount').textContent = errorCount;
            document.getElementById('totalTests').textContent = totalTests;
        }
        
        function enableButton(buttonId) {
            const button = document.getElementById(buttonId);
            if (button) {
                button.disabled = false;
                button.classList.remove('btn-secondary');
                button.classList.add('btn-primary');
            }
        }
        
        // فحص تحميل الوحدات
        function checkModules() {
            logToConsole('🔍 فحص الوحدات المحملة...');
            
            // فحص Utils
            if (typeof Utils !== 'undefined') {
                updateStatus('utilsStatus', 'ok', 'محمل');
                modulesLoaded.utils = true;
                enableButton('testUtilsBtn');
                enableButton('testMessageBtn');
                addTestResult('تحميل Utils', 'success', 'تم تحميل وحدة Utils بنجاح');
            } else {
                updateStatus('utilsStatus', 'error', 'خطأ');
                addTestResult('تحميل Utils', 'error', 'فشل في تحميل وحدة Utils');
            }
            
            // فحص UnifiedStorage
            if (typeof UnifiedStorage !== 'undefined') {
                updateStatus('storageStatus', 'ok', 'محمل');
                modulesLoaded.storage = true;
                enableButton('testStorageBtn');
                addTestResult('تحميل Storage', 'success', 'تم تحميل نظام التخزين بنجاح');
            } else {
                updateStatus('storageStatus', 'error', 'خطأ');
                addTestResult('تحميل Storage', 'error', 'فشل في تحميل نظام التخزين');
            }
            
            // فحص ProductsManager
            if (typeof ProductsManager !== 'undefined') {
                updateStatus('productsStatus', 'ok', 'محمل');
                modulesLoaded.products = true;
                enableButton('testProductsBtn');
                addTestResult('تحميل Products', 'success', 'تم تحميل مدير المنتجات بنجاح');
            } else {
                updateStatus('productsStatus', 'error', 'خطأ');
                addTestResult('تحميل Products', 'error', 'فشل في تحميل مدير المنتجات');
            }
            
            // فحص النظام العام
            const loadedCount = Object.values(modulesLoaded).filter(Boolean).length;
            if (loadedCount >= 2) {
                updateStatus('systemStatus', 'ok', 'جاهز');
                enableButton('testDataBtn');
                enableButton('runAllBtn');
                addTestResult('حالة النظام', 'success', `النظام جاهز - تم تحميل ${loadedCount} وحدة`);
            } else {
                updateStatus('systemStatus', 'warning', 'جزئي');
                addTestResult('حالة النظام', 'warning', `تحميل جزئي - ${loadedCount} وحدة فقط`);
            }
        }
        
        // دوال الاختبار
        function testUtils() {
            try {
                if (typeof Utils !== 'undefined' && Utils.formatDate) {
                    const testDate = Utils.formatDate(new Date(), 'DD/MM/YYYY');
                    addTestResult('اختبار Utils', 'success', `تنسيق التاريخ يعمل: ${testDate}`);
                } else {
                    addTestResult('اختبار Utils', 'error', 'دالة formatDate غير متاحة');
                }
            } catch (error) {
                addTestResult('اختبار Utils', 'error', error.message);
            }
        }
        
        function testStorage() {
            try {
                if (typeof UnifiedStorage !== 'undefined') {
                    const storage = new UnifiedStorage();
                    addTestResult('اختبار التخزين', 'success', 'تم إنشاء كائن التخزين بنجاح');
                } else {
                    addTestResult('اختبار التخزين', 'error', 'فئة UnifiedStorage غير متاحة');
                }
            } catch (error) {
                addTestResult('اختبار التخزين', 'error', error.message);
            }
        }
        
        function testProducts() {
            try {
                if (typeof ProductsManager !== 'undefined') {
                    const products = new ProductsManager();
                    addTestResult('اختبار المنتجات', 'success', 'تم إنشاء مدير المنتجات بنجاح');
                } else {
                    addTestResult('اختبار المنتجات', 'error', 'فئة ProductsManager غير متاحة');
                }
            } catch (error) {
                addTestResult('اختبار المنتجات', 'error', error.message);
            }
        }
        
        function testShowMessage() {
            try {
                if (typeof Utils !== 'undefined' && Utils.showMessage) {
                    Utils.showMessage('هذه رسالة اختبار', 'success');
                    addTestResult('اختبار الرسائل', 'success', 'تم عرض الرسالة بنجاح');
                } else {
                    addTestResult('اختبار الرسائل', 'error', 'دالة showMessage غير متاحة');
                }
            } catch (error) {
                addTestResult('اختبار الرسائل', 'error', error.message);
            }
        }
        
        function testDataOperations() {
            try {
                // اختبار localStorage
                localStorage.setItem('test', 'test-value');
                const value = localStorage.getItem('test');
                localStorage.removeItem('test');
                
                if (value === 'test-value') {
                    addTestResult('اختبار البيانات', 'success', 'عمليات التخزين تعمل بشكل صحيح');
                } else {
                    addTestResult('اختبار البيانات', 'error', 'مشكلة في عمليات التخزين');
                }
            } catch (error) {
                addTestResult('اختبار البيانات', 'error', error.message);
            }
        }
        
        function runAllTests() {
            logToConsole('🚀 بدء تشغيل جميع الاختبارات...');
            
            setTimeout(() => testUtils(), 500);
            setTimeout(() => testStorage(), 1000);
            setTimeout(() => testProducts(), 1500);
            setTimeout(() => testShowMessage(), 2000);
            setTimeout(() => testDataOperations(), 2500);
            
            setTimeout(() => {
                logToConsole('✅ اكتملت جميع الاختبارات');
            }, 3000);
        }
        
        function clearConsole() {
            document.getElementById('consoleOutput').textContent = 'تم مسح السجل...\n';
        }
        
        // بدء النظام
        document.addEventListener('DOMContentLoaded', function() {
            logToConsole('🎉 مرحباً بك في الاختبار الحقيقي');
            
            // انتظار تحميل الوحدات
            setTimeout(() => {
                checkModules();
            }, 2000);
        });
    </script>
</body>
</html>
