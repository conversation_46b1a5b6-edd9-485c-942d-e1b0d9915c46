/**
 * Black Horse POS - Validation System
 * نظام التحقق من البيانات
 * Developer: Augment Agent
 */

const Validators = {
    // ===== قواعد التحقق الأساسية =====
    
    // التحقق من وجود القيمة
    required: function(value, fieldName = 'الحقل') {
        if (value === null || value === undefined || value === '') {
            return `${fieldName} مطلوب`;
        }
        return null;
    },

    // التحقق من الحد الأدنى للطول
    minLength: function(value, minLen, fieldName = 'الحقل') {
        if (value && value.toString().length < minLen) {
            return `${fieldName} يجب أن يكون ${minLen} أحرف على الأقل`;
        }
        return null;
    },

    // التحقق من الحد الأقصى للطول
    maxLength: function(value, maxLen, fieldName = 'الحقل') {
        if (value && value.toString().length > maxLen) {
            return `${fieldName} يجب أن يكون ${maxLen} أحرف كحد أقصى`;
        }
        return null;
    },

    // التحقق من كون القيمة رقم
    isNumber: function(value, fieldName = 'الحقل') {
        if (value !== null && value !== undefined && value !== '' && isNaN(value)) {
            return `${fieldName} يجب أن يكون رقماً`;
        }
        return null;
    },

    // التحقق من كون الرقم موجب
    isPositive: function(value, fieldName = 'الحقل') {
        const numError = this.isNumber(value, fieldName);
        if (numError) return numError;
        
        if (value !== null && value !== undefined && value !== '' && parseFloat(value) < 0) {
            return `${fieldName} يجب أن يكون رقماً موجباً`;
        }
        return null;
    },

    // التحقق من البريد الإلكتروني
    isEmail: function(value, fieldName = 'البريد الإلكتروني') {
        if (value && !Utils.isValidEmail(value)) {
            return `${fieldName} غير صحيح`;
        }
        return null;
    },

    // التحقق من رقم الهاتف
    isPhone: function(value, fieldName = 'رقم الهاتف') {
        if (value && !Utils.isValidPhone(value)) {
            return `${fieldName} غير صحيح`;
        }
        return null;
    },

    // ===== التحقق من بيانات المنتجات =====
    
    validateProduct: function(product) {
        const errors = [];
        
        // اسم المنتج
        const nameError = this.required(product.name, 'اسم المنتج');
        if (nameError) errors.push(nameError);
        
        const nameLengthError = this.minLength(product.name, 2, 'اسم المنتج');
        if (nameLengthError) errors.push(nameLengthError);
        
        // السعر
        const priceError = this.required(product.price, 'السعر');
        if (priceError) errors.push(priceError);
        
        const priceNumberError = this.isPositive(product.price, 'السعر');
        if (priceNumberError) errors.push(priceNumberError);
        
        // سعر التكلفة
        if (product.costPrice !== null && product.costPrice !== undefined && product.costPrice !== '') {
            const costPriceError = this.isPositive(product.costPrice, 'سعر التكلفة');
            if (costPriceError) errors.push(costPriceError);
            
            // التحقق من أن سعر التكلفة أقل من سعر البيع
            if (parseFloat(product.costPrice) > parseFloat(product.price)) {
                errors.push('سعر التكلفة لا يمكن أن يكون أكبر من سعر البيع');
            }
        }
        
        // الكمية
        if (product.stock !== null && product.stock !== undefined && product.stock !== '') {
            const stockError = this.isNumber(product.stock, 'الكمية');
            if (stockError) errors.push(stockError);
            
            if (parseFloat(product.stock) < 0) {
                errors.push('الكمية لا يمكن أن تكون سالبة');
            }
        }
        
        // الكود الشريطي
        if (product.barcode && !Utils.isValidBarcode(product.barcode)) {
            errors.push('الكود الشريطي غير صحيح');
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    },

    // ===== التحقق من بيانات العملاء =====
    
    validateCustomer: function(customer) {
        const errors = [];
        
        // اسم العميل
        const nameError = this.required(customer.name, 'اسم العميل');
        if (nameError) errors.push(nameError);
        
        const nameLengthError = this.minLength(customer.name, 2, 'اسم العميل');
        if (nameLengthError) errors.push(nameLengthError);
        
        // رقم الهاتف
        if (customer.phone) {
            const phoneError = this.isPhone(customer.phone, 'رقم الهاتف');
            if (phoneError) errors.push(phoneError);
        }
        
        // البريد الإلكتروني
        if (customer.email) {
            const emailError = this.isEmail(customer.email, 'البريد الإلكتروني');
            if (emailError) errors.push(emailError);
        }
        
        // الدين
        if (customer.debt !== null && customer.debt !== undefined && customer.debt !== '') {
            const debtError = this.isNumber(customer.debt, 'الدين');
            if (debtError) errors.push(debtError);
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    },

    // ===== التحقق من بيانات المبيعات =====
    
    validateSale: function(sale) {
        const errors = [];
        
        // العناصر
        if (!sale.items || !Array.isArray(sale.items) || sale.items.length === 0) {
            errors.push('يجب إضافة عنصر واحد على الأقل للبيع');
        } else {
            // التحقق من كل عنصر
            sale.items.forEach((item, index) => {
                if (!item.productId) {
                    errors.push(`العنصر ${index + 1}: معرف المنتج مطلوب`);
                }
                
                const qtyError = this.isPositive(item.quantity, `العنصر ${index + 1}: الكمية`);
                if (qtyError) errors.push(qtyError);
                
                const priceError = this.isPositive(item.price, `العنصر ${index + 1}: السعر`);
                if (priceError) errors.push(priceError);
            });
        }
        
        // المجموع
        const totalError = this.isPositive(sale.total, 'المجموع الكلي');
        if (totalError) errors.push(totalError);
        
        // طريقة الدفع
        const paymentError = this.required(sale.paymentMethod, 'طريقة الدفع');
        if (paymentError) errors.push(paymentError);
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    },

    // ===== التحقق من بيانات المصروفات =====
    
    validateExpense: function(expense) {
        const errors = [];
        
        // الوصف
        const descError = this.required(expense.description, 'وصف المصروف');
        if (descError) errors.push(descError);
        
        // المبلغ
        const amountError = this.required(expense.amount, 'المبلغ');
        if (amountError) errors.push(amountError);
        
        const amountPositiveError = this.isPositive(expense.amount, 'المبلغ');
        if (amountPositiveError) errors.push(amountPositiveError);
        
        // التاريخ
        if (expense.date && !Utils.isValidDate(new Date(expense.date))) {
            errors.push('التاريخ غير صحيح');
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    },

    // ===== التحقق من بيانات المستخدمين =====
    
    validateUser: function(user) {
        const errors = [];
        
        // اسم المستخدم
        const usernameError = this.required(user.username, 'اسم المستخدم');
        if (usernameError) errors.push(usernameError);
        
        const usernameLengthError = this.minLength(user.username, 3, 'اسم المستخدم');
        if (usernameLengthError) errors.push(usernameLengthError);
        
        // كلمة المرور
        const passwordError = this.required(user.password, 'كلمة المرور');
        if (passwordError) errors.push(passwordError);
        
        const passwordLengthError = this.minLength(user.password, 6, 'كلمة المرور');
        if (passwordLengthError) errors.push(passwordLengthError);
        
        // البريد الإلكتروني
        if (user.email) {
            const emailError = this.isEmail(user.email, 'البريد الإلكتروني');
            if (emailError) errors.push(emailError);
        }
        
        // الدور
        const roleError = this.required(user.role, 'دور المستخدم');
        if (roleError) errors.push(roleError);
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    },

    // ===== التحقق من النماذج =====
    
    validateForm: function(formElement) {
        const errors = [];
        const formData = new FormData(formElement);
        const data = {};
        
        // تحويل FormData إلى object
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        // التحقق من الحقول المطلوبة
        const requiredFields = formElement.querySelectorAll('[required]');
        requiredFields.forEach(field => {
            const fieldName = field.getAttribute('data-label') || field.name || field.id;
            const error = this.required(data[field.name], fieldName);
            if (error) errors.push(error);
        });
        
        // التحقق من أنواع البيانات
        const numberFields = formElement.querySelectorAll('input[type="number"]');
        numberFields.forEach(field => {
            const fieldName = field.getAttribute('data-label') || field.name || field.id;
            const error = this.isNumber(data[field.name], fieldName);
            if (error) errors.push(error);
        });
        
        // التحقق من البريد الإلكتروني
        const emailFields = formElement.querySelectorAll('input[type="email"]');
        emailFields.forEach(field => {
            const fieldName = field.getAttribute('data-label') || field.name || field.id;
            const error = this.isEmail(data[field.name], fieldName);
            if (error) errors.push(error);
        });
        
        return {
            isValid: errors.length === 0,
            errors: errors,
            data: data
        };
    },

    // ===== عرض الأخطاء =====
    
    displayErrors: function(errors, containerId = null) {
        if (!errors || errors.length === 0) return;
        
        const errorMessage = errors.join('\n');
        
        if (containerId) {
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <ul style="margin: 0; padding-right: 20px;">
                            ${errors.map(error => `<li>${error}</li>`).join('')}
                        </ul>
                    </div>
                `;
                return;
            }
        }
        
        // fallback إلى Utils.showMessage
        if (window.Utils) {
            Utils.showMessage(errorMessage, 'error', 5000);
        } else {
            alert(errorMessage);
        }
    },

    // مسح الأخطاء
    clearErrors: function(containerId) {
        if (containerId) {
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = '';
            }
        }
    },

    // ===== التحقق المباشر =====
    
    setupLiveValidation: function(formElement) {
        if (!formElement) return;
        
        const fields = formElement.querySelectorAll('input, select, textarea');
        
        fields.forEach(field => {
            field.addEventListener('blur', () => {
                this.validateField(field);
            });
            
            field.addEventListener('input', () => {
                // إزالة رسائل الخطأ عند الكتابة
                this.clearFieldError(field);
            });
        });
    },

    // التحقق من حقل واحد
    validateField: function(field) {
        const value = field.value;
        const fieldName = field.getAttribute('data-label') || field.name || field.id;
        let error = null;
        
        // التحقق من الحقول المطلوبة
        if (field.hasAttribute('required')) {
            error = this.required(value, fieldName);
        }
        
        // التحقق من النوع
        if (!error && value) {
            switch (field.type) {
                case 'number':
                    error = this.isNumber(value, fieldName);
                    break;
                case 'email':
                    error = this.isEmail(value, fieldName);
                    break;
            }
        }
        
        // التحقق من الحد الأدنى والأقصى
        if (!error && value) {
            const minLength = field.getAttribute('minlength');
            if (minLength) {
                error = this.minLength(value, parseInt(minLength), fieldName);
            }
            
            const maxLength = field.getAttribute('maxlength');
            if (maxLength && !error) {
                error = this.maxLength(value, parseInt(maxLength), fieldName);
            }
        }
        
        // عرض أو إخفاء الخطأ
        if (error) {
            this.showFieldError(field, error);
            return false;
        } else {
            this.clearFieldError(field);
            return true;
        }
    },

    // عرض خطأ الحقل
    showFieldError: function(field, error) {
        this.clearFieldError(field);
        
        field.classList.add('is-invalid');
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = error;
        errorDiv.setAttribute('data-field-error', field.name || field.id);
        
        field.parentNode.appendChild(errorDiv);
    },

    // مسح خطأ الحقل
    clearFieldError: function(field) {
        field.classList.remove('is-invalid');
        
        const errorDiv = field.parentNode.querySelector(`[data-field-error="${field.name || field.id}"]`);
        if (errorDiv) {
            errorDiv.remove();
        }
    }
};

// إتاحة Validators عالمياً
window.Validators = Validators;

// تصدير للاستخدام في الوحدات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Validators;
}

console.log('✅ Validators loaded successfully');
