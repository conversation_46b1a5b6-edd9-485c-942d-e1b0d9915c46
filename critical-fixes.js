// Critical Fixes for Black Horse POS - إصلاحات حرجة لنظام نقطة البيع
console.log('🔧 Loading Critical Fixes...');

// ===== FIX 1: BUTTON EVENT HANDLERS =====
// إصلاح معالجات أحداث الأزرار

function initializeButtonHandlers() {
    console.log('🔄 Initializing button handlers...');
    
    try {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', setupButtonHandlers);
        } else {
            setupButtonHandlers();
        }
    } catch (error) {
        console.error('❌ Error initializing button handlers:', error);
    }
}

function setupButtonHandlers() {
    console.log('🔧 Setting up button handlers...');
    
    try {
        // Remove existing event listeners to prevent duplicates
        document.removeEventListener('click', globalClickHandler);
        
        // Add global click handler
        document.addEventListener('click', globalClickHandler);
        
        // Setup specific button handlers
        setupProductButtons();
        setupModalButtons();
        setupNavigationButtons();
        
        console.log('✅ Button handlers setup completed');
    } catch (error) {
        console.error('❌ Error setting up button handlers:', error);
    }
}

function globalClickHandler(event) {
    try {
        const target = event.target;
        const button = target.closest('button') || target.closest('a[onclick]') || target.closest('[onclick]');
        
        if (!button) return;
        
        // Handle specific button types
        if (button.onclick && typeof button.onclick === 'function') {
            // Let the existing onclick handler work
            return;
        }
        
        // Handle data-action attributes
        const action = button.getAttribute('data-action');
        if (action) {
            event.preventDefault();
            handleDataAction(action, button);
            return;
        }
        
        // Handle specific button classes or IDs
        handleSpecificButtons(button, event);
        
    } catch (error) {
        console.error('❌ Error in global click handler:', error);
    }
}

function handleDataAction(action, button) {
    console.log('🎯 Handling data-action:', action);
    
    try {
        switch (action) {
            case 'add-product':
                if (typeof showAddProductModal === 'function') {
                    showAddProductModal();
                } else {
                    showModal('addProductModal');
                }
                break;
                
            case 'edit-product':
                const productId = button.getAttribute('data-product-id');
                if (productId && typeof editProduct === 'function') {
                    editProduct(productId);
                }
                break;
                
            case 'delete-product':
                const deleteProductId = button.getAttribute('data-product-id');
                if (deleteProductId && typeof deleteProduct === 'function') {
                    deleteProduct(deleteProductId);
                }
                break;
                
            case 'generate-barcode':
                if (typeof generateBarcode === 'function') {
                    generateBarcode();
                } else {
                    console.error('❌ generateBarcode function not found');
                }
                break;
                
            default:
                console.warn('⚠️ Unknown data-action:', action);
        }
    } catch (error) {
        console.error('❌ Error handling data-action:', error);
    }
}

function setupProductButtons() {
    console.log('🔧 Setting up product buttons...');
    
    try {
        // Add Product Button
        const addProductBtn = document.querySelector('[onclick*="showAddProductModal"]');
        if (addProductBtn && !addProductBtn.hasAttribute('data-fixed')) {
            addProductBtn.setAttribute('data-fixed', 'true');
            addProductBtn.addEventListener('click', function(e) {
                e.preventDefault();
                if (typeof showAddProductModal === 'function') {
                    showAddProductModal();
                } else {
                    showModal('addProductModal');
                }
            });
        }
        
        // Generate Barcode Button
        const generateBarcodeBtn = document.querySelector('[onclick*="generateBarcode"]');
        if (generateBarcodeBtn && !generateBarcodeBtn.hasAttribute('data-fixed')) {
            generateBarcodeBtn.setAttribute('data-fixed', 'true');
            generateBarcodeBtn.addEventListener('click', function(e) {
                e.preventDefault();
                if (typeof generateBarcode === 'function') {
                    generateBarcode();
                } else {
                    console.error('❌ generateBarcode function not found');
                }
            });
        }
        
        // Save Product Button
        const saveProductBtn = document.querySelector('[onclick*="addProduct"]');
        if (saveProductBtn && !saveProductBtn.hasAttribute('data-fixed')) {
            saveProductBtn.setAttribute('data-fixed', 'true');
            saveProductBtn.addEventListener('click', function(e) {
                e.preventDefault();
                if (typeof addProduct === 'function') {
                    addProduct();
                } else {
                    console.error('❌ addProduct function not found');
                }
            });
        }
        
        console.log('✅ Product buttons setup completed');
    } catch (error) {
        console.error('❌ Error setting up product buttons:', error);
    }
}

function setupModalButtons() {
    console.log('🔧 Setting up modal buttons...');
    
    try {
        // Close modal buttons
        const closeButtons = document.querySelectorAll('[data-dismiss="modal"], .modal-close, .btn-close');
        closeButtons.forEach(button => {
            if (!button.hasAttribute('data-fixed')) {
                button.setAttribute('data-fixed', 'true');
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const modal = button.closest('.modal');
                    if (modal) {
                        closeModal(modal.id);
                    }
                });
            }
        });
        
        console.log('✅ Modal buttons setup completed');
    } catch (error) {
        console.error('❌ Error setting up modal buttons:', error);
    }
}

function setupNavigationButtons() {
    console.log('🔧 Setting up navigation buttons...');
    
    try {
        // Page navigation buttons
        const navButtons = document.querySelectorAll('[onclick*="showPage"]');
        navButtons.forEach(button => {
            if (!button.hasAttribute('data-fixed')) {
                button.setAttribute('data-fixed', 'true');
                const onclickAttr = button.getAttribute('onclick');
                if (onclickAttr) {
                    const pageMatch = onclickAttr.match(/showPage\(['"]([^'"]+)['"]\)/);
                    if (pageMatch) {
                        const pageName = pageMatch[1];
                        button.addEventListener('click', function(e) {
                            e.preventDefault();
                            if (typeof showPage === 'function') {
                                showPage(pageName);
                            }
                        });
                    }
                }
            }
        });
        
        console.log('✅ Navigation buttons setup completed');
    } catch (error) {
        console.error('❌ Error setting up navigation buttons:', error);
    }
}

function handleSpecificButtons(button, event) {
    try {
        // Handle buttons by class or ID
        if (button.classList.contains('btn-add-product')) {
            event.preventDefault();
            if (typeof showAddProductModal === 'function') {
                showAddProductModal();
            }
        }
        
        if (button.classList.contains('btn-generate-barcode')) {
            event.preventDefault();
            if (typeof generateBarcode === 'function') {
                generateBarcode();
            }
        }
        
        if (button.classList.contains('btn-save-product')) {
            event.preventDefault();
            if (typeof addProduct === 'function') {
                addProduct();
            }
        }
        
    } catch (error) {
        console.error('❌ Error handling specific buttons:', error);
    }
}

// ===== FIX 2: ENSURE FUNCTIONS ARE AVAILABLE =====
// التأكد من توفر الوظائف

function ensureFunctionsAvailable() {
    console.log('🔄 Ensuring functions are available...');
    
    try {
        // Check and create missing functions
        if (typeof window.showAddProductModal !== 'function') {
            window.showAddProductModal = function() {
                console.log('📝 Opening add product modal...');
                showModal('addProductModal');
            };
        }
        
        if (typeof window.generateBarcode !== 'function') {
            window.generateBarcode = function() {
                console.log('📊 Generating barcode...');
                try {
                    const barcodeField = document.getElementById('productBarcode');
                    if (barcodeField) {
                        const timestamp = Date.now().toString();
                        const barcode = '2' + timestamp.slice(-11);
                        barcodeField.value = barcode;
                        if (typeof showNotification === 'function') {
                            showNotification('تم توليد الباركود: ' + barcode, 'success');
                        }
                    }
                } catch (error) {
                    console.error('❌ Error generating barcode:', error);
                }
            };
        }
        
        if (typeof window.addProduct !== 'function') {
            window.addProduct = function() {
                console.log('📦 Adding product...');
                if (typeof showNotification === 'function') {
                    showNotification('وظيفة إضافة المنتج غير متوفرة', 'error');
                }
            };
        }
        
        console.log('✅ Functions availability check completed');
    } catch (error) {
        console.error('❌ Error ensuring functions availability:', error);
    }
}

// ===== FIX 3: INITIALIZATION =====
// التهيئة

function initializeCriticalFixes() {
    console.log('🚀 Initializing critical fixes...');
    
    try {
        ensureFunctionsAvailable();
        initializeButtonHandlers();
        
        // Re-initialize every 5 seconds to catch dynamically added buttons
        setInterval(function() {
            setupButtonHandlers();
        }, 5000);
        
        console.log('✅ Critical fixes initialized successfully');
    } catch (error) {
        console.error('❌ Error initializing critical fixes:', error);
    }
}

// Start initialization
initializeCriticalFixes();

console.log('✅ Critical Fixes loaded successfully');
