/**
 * Black Horse POS - Sales Manager (Optimized)
 * مدير المبيعات المحسن
 * Developer: Augment Agent
 */

class SalesManager {
    constructor() {
        this.sales = [];
        this.currentSale = null;
        this.isInitialized = false;
        this.autoSaveEnabled = true;
        this.autoSaveInterval = 30000; // 30 ثانية
        this.autoSaveTimer = null;
        
        console.log('💰 Sales Manager initialized');
    }

    // تهيئة مدير المبيعات
    async init() {
        try {
            console.log('🚀 Initializing Sales Manager...');
            
            // تحميل المبيعات من التخزين المؤقت أولاً
            if (window.PerformanceOptimizer) {
                const cachedSales = window.PerformanceOptimizer.getCachedData('sales');
                if (cachedSales) {
                    this.sales = cachedSales;
                    console.log('📂 Sales loaded from cache');
                    this.isInitialized = true;
                    this.setupAutoSave();
                    return;
                }
            }
            
            // تحميل من قاعدة البيانات
            await this.loadSales();
            
            // إعداد الحفظ التلقائي
            this.setupAutoSave();
            
            this.isInitialized = true;
            console.log('✅ Sales Manager ready');
            
        } catch (error) {
            console.error('❌ Error initializing Sales Manager:', error);
            this.isInitialized = true; // للمتابعة حتى لو فشل التحميل
        }
    }

    // تحميل المبيعات
    async loadSales() {
        try {
            if (window.UnifiedStorage) {
                this.sales = await window.UnifiedStorage.load('sales', []);
            } else {
                // fallback إلى localStorage
                const savedSales = localStorage.getItem('sales');
                this.sales = savedSales ? JSON.parse(savedSales) : [];
            }
            
            // حفظ في التخزين المؤقت
            if (window.PerformanceOptimizer) {
                window.PerformanceOptimizer.cacheData('sales', this.sales);
            }
            
            console.log(`📊 Loaded ${this.sales.length} sales records`);
            
        } catch (error) {
            console.error('❌ Error loading sales:', error);
            this.sales = [];
        }
    }

    // حفظ المبيعات
    async saveSales() {
        try {
            if (window.UnifiedStorage) {
                await window.UnifiedStorage.save('sales', this.sales);
            } else {
                // fallback إلى localStorage
                localStorage.setItem('sales', JSON.stringify(this.sales));
            }
            
            // تحديث التخزين المؤقت
            if (window.PerformanceOptimizer) {
                window.PerformanceOptimizer.cacheData('sales', this.sales);
            }
            
            console.log('💾 Sales saved successfully');
            
        } catch (error) {
            console.error('❌ Error saving sales:', error);
            throw error;
        }
    }

    // إعداد الحفظ التلقائي
    setupAutoSave() {
        try {
            if (!this.autoSaveEnabled) return;
            
            // إلغاء المؤقت السابق إذا كان موجوداً
            if (this.autoSaveTimer) {
                clearInterval(this.autoSaveTimer);
            }
            
            // إعداد مؤقت جديد
            this.autoSaveTimer = setInterval(async () => {
                try {
                    await this.saveSales();
                    console.log('💾 Auto-save completed');
                } catch (error) {
                    console.error('❌ Auto-save failed:', error);
                }
            }, this.autoSaveInterval);
            
            console.log('⏰ Auto-save enabled');
            
        } catch (error) {
            console.error('❌ Error setting up auto-save:', error);
        }
    }

    // إنشاء مبيعة جديدة
    createNewSale() {
        try {
            this.currentSale = {
                id: this.generateSaleId(),
                date: new Date().toISOString(),
                items: [],
                subtotal: 0,
                tax: 0,
                discount: 0,
                total: 0,
                customerId: null,
                customerName: '',
                paymentMethod: 'cash',
                status: 'draft',
                notes: '',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            
            console.log(`🆕 New sale created: ${this.currentSale.id}`);
            return this.currentSale;
            
        } catch (error) {
            console.error('❌ Error creating new sale:', error);
            return null;
        }
    }

    // توليد معرف مبيعة
    generateSaleId() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substr(2, 5);
        return `SALE-${timestamp}-${random}`;
    }

    // إضافة منتج للمبيعة الحالية
    addItemToCurrentSale(product, quantity = 1) {
        try {
            if (!this.currentSale) {
                throw new Error('No active sale');
            }
            
            if (!product || !product.id) {
                throw new Error('Invalid product');
            }
            
            if (quantity <= 0) {
                throw new Error('Invalid quantity');
            }
            
            // البحث عن المنتج في المبيعة الحالية
            const existingItemIndex = this.currentSale.items.findIndex(item => item.productId === product.id);
            
            if (existingItemIndex >= 0) {
                // تحديث الكمية للمنتج الموجود
                this.currentSale.items[existingItemIndex].quantity += quantity;
                this.currentSale.items[existingItemIndex].total = 
                    this.currentSale.items[existingItemIndex].quantity * this.currentSale.items[existingItemIndex].price;
            } else {
                // إضافة منتج جديد
                const saleItem = {
                    productId: product.id,
                    productName: product.name,
                    price: product.price,
                    quantity: quantity,
                    total: product.price * quantity,
                    barcode: product.barcode || '',
                    category: product.category || ''
                };
                
                this.currentSale.items.push(saleItem);
            }
            
            // إعادة حساب المجاميع
            this.calculateSaleTotals();
            
            console.log(`➕ Item added to sale: ${product.name} x${quantity}`);
            return this.currentSale;
            
        } catch (error) {
            console.error('❌ Error adding item to sale:', error);
            throw error;
        }
    }

    // إزالة منتج من المبيعة الحالية
    removeItemFromCurrentSale(productId) {
        try {
            if (!this.currentSale) {
                throw new Error('No active sale');
            }
            
            const itemIndex = this.currentSale.items.findIndex(item => item.productId === productId);
            
            if (itemIndex >= 0) {
                const removedItem = this.currentSale.items.splice(itemIndex, 1)[0];
                this.calculateSaleTotals();
                
                console.log(`➖ Item removed from sale: ${removedItem.productName}`);
                return this.currentSale;
            } else {
                throw new Error('Item not found in sale');
            }
            
        } catch (error) {
            console.error('❌ Error removing item from sale:', error);
            throw error;
        }
    }

    // تحديث كمية منتج في المبيعة الحالية
    updateItemQuantity(productId, newQuantity) {
        try {
            if (!this.currentSale) {
                throw new Error('No active sale');
            }
            
            if (newQuantity <= 0) {
                return this.removeItemFromCurrentSale(productId);
            }
            
            const item = this.currentSale.items.find(item => item.productId === productId);
            
            if (item) {
                item.quantity = newQuantity;
                item.total = item.price * newQuantity;
                this.calculateSaleTotals();
                
                console.log(`🔄 Item quantity updated: ${item.productName} = ${newQuantity}`);
                return this.currentSale;
            } else {
                throw new Error('Item not found in sale');
            }
            
        } catch (error) {
            console.error('❌ Error updating item quantity:', error);
            throw error;
        }
    }

    // حساب مجاميع المبيعة
    calculateSaleTotals() {
        try {
            if (!this.currentSale) return;
            
            // حساب المجموع الفرعي
            this.currentSale.subtotal = this.currentSale.items.reduce((sum, item) => sum + item.total, 0);
            
            // حساب الضريبة (إذا كانت مفعلة)
            const taxRate = this.getTaxRate();
            this.currentSale.tax = this.currentSale.subtotal * (taxRate / 100);
            
            // حساب المجموع النهائي
            this.currentSale.total = this.currentSale.subtotal + this.currentSale.tax - this.currentSale.discount;
            
            // تحديث وقت التعديل
            this.currentSale.updatedAt = new Date().toISOString();
            
        } catch (error) {
            console.error('❌ Error calculating sale totals:', error);
        }
    }

    // الحصول على معدل الضريبة
    getTaxRate() {
        try {
            // يمكن تحميل هذا من الإعدادات
            return 0; // 0% افتراضياً
        } catch (error) {
            console.error('❌ Error getting tax rate:', error);
            return 0;
        }
    }

    // تطبيق خصم
    applyDiscount(discountAmount) {
        try {
            if (!this.currentSale) {
                throw new Error('No active sale');
            }
            
            if (discountAmount < 0) {
                throw new Error('Discount cannot be negative');
            }
            
            if (discountAmount > this.currentSale.subtotal) {
                throw new Error('Discount cannot exceed subtotal');
            }
            
            this.currentSale.discount = discountAmount;
            this.calculateSaleTotals();
            
            console.log(`💸 Discount applied: ${discountAmount}`);
            return this.currentSale;
            
        } catch (error) {
            console.error('❌ Error applying discount:', error);
            throw error;
        }
    }

    // تعيين عميل للمبيعة
    setCustomer(customer) {
        try {
            if (!this.currentSale) {
                throw new Error('No active sale');
            }
            
            if (customer) {
                this.currentSale.customerId = customer.id;
                this.currentSale.customerName = customer.name;
            } else {
                this.currentSale.customerId = null;
                this.currentSale.customerName = '';
            }
            
            this.currentSale.updatedAt = new Date().toISOString();
            
            console.log(`👤 Customer set: ${customer ? customer.name : 'None'}`);
            return this.currentSale;
            
        } catch (error) {
            console.error('❌ Error setting customer:', error);
            throw error;
        }
    }

    // إتمام المبيعة
    async completeSale(paymentMethod = 'cash') {
        try {
            if (!this.currentSale) {
                throw new Error('No active sale');
            }
            
            if (this.currentSale.items.length === 0) {
                throw new Error('Cannot complete sale with no items');
            }
            
            if (this.currentSale.total <= 0) {
                throw new Error('Sale total must be greater than zero');
            }
            
            // تحديث بيانات المبيعة
            this.currentSale.paymentMethod = paymentMethod;
            this.currentSale.status = 'completed';
            this.currentSale.completedAt = new Date().toISOString();
            this.currentSale.updatedAt = new Date().toISOString();
            
            // إضافة المبيعة للقائمة
            this.sales.unshift(this.currentSale); // إضافة في المقدمة للعرض الأحدث أولاً
            
            // حفظ المبيعات
            await this.saveSales();
            
            // تحديث المخزون (إذا كان متاحاً)
            await this.updateInventory();
            
            const completedSale = { ...this.currentSale };
            this.currentSale = null;
            
            console.log(`✅ Sale completed: ${completedSale.id} - Total: ${completedSale.total}`);
            return completedSale;
            
        } catch (error) {
            console.error('❌ Error completing sale:', error);
            throw error;
        }
    }

    // تحديث المخزون بعد المبيعة
    async updateInventory() {
        try {
            if (!this.currentSale || !window.ProductsManager) return;
            
            for (const item of this.currentSale.items) {
                try {
                    await window.ProductsManager.updateStock(item.productId, -item.quantity);
                } catch (error) {
                    console.warn(`⚠️ Could not update stock for product ${item.productId}:`, error);
                }
            }
            
            console.log('📦 Inventory updated');
            
        } catch (error) {
            console.error('❌ Error updating inventory:', error);
        }
    }

    // إلغاء المبيعة الحالية
    cancelCurrentSale() {
        try {
            if (this.currentSale) {
                console.log(`❌ Sale cancelled: ${this.currentSale.id}`);
                this.currentSale = null;
            }
            
        } catch (error) {
            console.error('❌ Error cancelling sale:', error);
        }
    }

    // البحث في المبيعات
    searchSales(query, filters = {}) {
        try {
            let results = [...this.sales];
            
            // البحث النصي
            if (query && query.trim()) {
                const searchTerm = query.toLowerCase().trim();
                results = results.filter(sale => 
                    sale.id.toLowerCase().includes(searchTerm) ||
                    sale.customerName.toLowerCase().includes(searchTerm) ||
                    sale.items.some(item => 
                        item.productName.toLowerCase().includes(searchTerm) ||
                        item.barcode.includes(searchTerm)
                    )
                );
            }
            
            // تطبيق المرشحات
            if (filters.dateFrom) {
                results = results.filter(sale => new Date(sale.date) >= new Date(filters.dateFrom));
            }
            
            if (filters.dateTo) {
                results = results.filter(sale => new Date(sale.date) <= new Date(filters.dateTo));
            }
            
            if (filters.status) {
                results = results.filter(sale => sale.status === filters.status);
            }
            
            if (filters.paymentMethod) {
                results = results.filter(sale => sale.paymentMethod === filters.paymentMethod);
            }
            
            if (filters.minTotal) {
                results = results.filter(sale => sale.total >= filters.minTotal);
            }
            
            if (filters.maxTotal) {
                results = results.filter(sale => sale.total <= filters.maxTotal);
            }
            
            console.log(`🔍 Sales search: ${results.length} results found`);
            return results;
            
        } catch (error) {
            console.error('❌ Error searching sales:', error);
            return [];
        }
    }

    // الحصول على إحصائيات المبيعات
    getSalesStats(period = 'today') {
        try {
            const now = new Date();
            let startDate;
            
            switch (period) {
                case 'today':
                    startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    break;
                case 'week':
                    startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    break;
                case 'month':
                    startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                    break;
                case 'year':
                    startDate = new Date(now.getFullYear(), 0, 1);
                    break;
                default:
                    startDate = new Date(0);
            }
            
            const periodSales = this.sales.filter(sale => 
                sale.status === 'completed' && new Date(sale.date) >= startDate
            );
            
            const stats = {
                period,
                totalSales: periodSales.length,
                totalRevenue: periodSales.reduce((sum, sale) => sum + sale.total, 0),
                averageSale: periodSales.length > 0 ? 
                    periodSales.reduce((sum, sale) => sum + sale.total, 0) / periodSales.length : 0,
                totalItems: periodSales.reduce((sum, sale) => 
                    sum + sale.items.reduce((itemSum, item) => itemSum + item.quantity, 0), 0
                ),
                paymentMethods: this.getPaymentMethodStats(periodSales),
                topProducts: this.getTopProducts(periodSales)
            };
            
            return stats;
            
        } catch (error) {
            console.error('❌ Error getting sales stats:', error);
            return null;
        }
    }

    // إحصائيات طرق الدفع
    getPaymentMethodStats(sales) {
        try {
            const methods = {};
            
            sales.forEach(sale => {
                if (!methods[sale.paymentMethod]) {
                    methods[sale.paymentMethod] = { count: 0, total: 0 };
                }
                methods[sale.paymentMethod].count++;
                methods[sale.paymentMethod].total += sale.total;
            });
            
            return methods;
            
        } catch (error) {
            console.error('❌ Error getting payment method stats:', error);
            return {};
        }
    }

    // أفضل المنتجات مبيعاً
    getTopProducts(sales, limit = 10) {
        try {
            const products = {};
            
            sales.forEach(sale => {
                sale.items.forEach(item => {
                    if (!products[item.productId]) {
                        products[item.productId] = {
                            id: item.productId,
                            name: item.productName,
                            quantity: 0,
                            revenue: 0
                        };
                    }
                    products[item.productId].quantity += item.quantity;
                    products[item.productId].revenue += item.total;
                });
            });
            
            return Object.values(products)
                .sort((a, b) => b.quantity - a.quantity)
                .slice(0, limit);
                
        } catch (error) {
            console.error('❌ Error getting top products:', error);
            return [];
        }
    }

    // الحصول على المبيعة الحالية
    getCurrentSale() {
        return this.currentSale;
    }

    // الحصول على جميع المبيعات
    getAllSales() {
        return this.sales;
    }

    // تنظيف الموارد
    cleanup() {
        try {
            if (this.autoSaveTimer) {
                clearInterval(this.autoSaveTimer);
                this.autoSaveTimer = null;
            }
            
            console.log('✅ Sales Manager cleaned up');
            
        } catch (error) {
            console.error('❌ Error cleaning up Sales Manager:', error);
        }
    }
}

// إنشاء instance عام
window.SalesManager = new SalesManager();

// تصدير للاستخدام في الوحدات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SalesManager;
}

console.log('✅ Sales Manager loaded successfully');
