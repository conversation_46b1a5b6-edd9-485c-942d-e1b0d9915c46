<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار وحدة المشتريات - Black Horse ERP</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .avatar-sm {
            width: 32px;
            height: 32px;
            font-size: 12px;
        }
        
        .avatar-lg {
            width: 48px;
            height: 48px;
            font-size: 18px;
        }
        
        .module-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-radius: 10px;
        }
        
        .btn {
            border-radius: 6px;
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
            color: #495057;
            background-color: #f8f9fa;
        }
        
        .nav-tabs .nav-link {
            border-radius: 10px 10px 0 0;
            border: none;
            color: #6c757d;
            font-weight: 500;
        }
        
        .nav-tabs .nav-link.active {
            background-color: #fff;
            color: #495057;
            border-bottom: 3px solid #007bff;
        }
        
        .toast-container {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 9999;
        }
    </style>
</head>
<body>
    <!-- Toast Container -->
    <div class="toast-container"></div>
    
    <!-- Main Container -->
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <!-- Test Header -->
                <div class="card mb-4">
                    <div class="card-body">
                        <h3 class="mb-3">
                            <i class="fas fa-vial me-2 text-primary"></i>
                            اختبار وحدة المشتريات
                        </h3>
                        <p class="text-muted mb-3">
                            هذه صفحة اختبار لوحدة إدارة المشتريات في نظام Black Horse ERP
                        </p>
                        <div class="btn-group">
                            <button class="btn btn-primary" onclick="initializePurchases()">
                                <i class="fas fa-play me-1"></i>تشغيل الوحدة
                            </button>
                            <button class="btn btn-success" onclick="testSampleData()">
                                <i class="fas fa-database me-1"></i>إنشاء بيانات تجريبية
                            </button>
                            <button class="btn btn-info" onclick="showModuleInfo()">
                                <i class="fas fa-info-circle me-1"></i>معلومات الوحدة
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Module Container -->
                <div id="moduleContainer">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-shopping-bag fa-3x text-muted mb-3"></i>
                            <h5>وحدة المشتريات</h5>
                            <p class="text-muted">اضغط على "تشغيل الوحدة" لبدء اختبار وحدة المشتريات</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Mock Database and Utils -->
    <script>
        // Mock Database
        window.Database = {
            stores: new Map(),
            
            async getAll(storeName) {
                if (!this.stores.has(storeName)) {
                    this.stores.set(storeName, []);
                }
                return [...this.stores.get(storeName)];
            },
            
            async add(storeName, data) {
                if (!this.stores.has(storeName)) {
                    this.stores.set(storeName, []);
                }
                this.stores.get(storeName).push(data);
                return data;
            },
            
            async delete(storeName, id) {
                if (!this.stores.has(storeName)) return;
                const store = this.stores.get(storeName);
                const index = store.findIndex(item => item.id === id);
                if (index > -1) {
                    store.splice(index, 1);
                }
            }
        };
        
        // Mock Utils
        window.Utils = {
            generateId() {
                return 'id_' + Math.random().toString(36).substr(2, 9);
            },
            
            formatCurrency(amount) {
                return new Intl.NumberFormat('ar-EG', {
                    style: 'currency',
                    currency: 'EGP'
                }).format(amount || 0);
            },
            
            formatDate(dateString) {
                if (!dateString) return '';
                const date = new Date(dateString);
                return date.toLocaleDateString('ar-EG');
            },
            
            showToast(title, message, type = 'info') {
                const toastContainer = document.querySelector('.toast-container');
                const toastId = 'toast_' + Date.now();
                
                const bgClass = {
                    'success': 'bg-success',
                    'error': 'bg-danger',
                    'warning': 'bg-warning',
                    'info': 'bg-info'
                }[type] || 'bg-info';
                
                const toastHTML = `
                    <div class="toast ${bgClass} text-white" id="${toastId}" role="alert">
                        <div class="toast-header ${bgClass} text-white border-0">
                            <strong class="me-auto">${title}</strong>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
                        </div>
                        <div class="toast-body">
                            ${message}
                        </div>
                    </div>
                `;
                
                toastContainer.insertAdjacentHTML('beforeend', toastHTML);
                const toastElement = document.getElementById(toastId);
                const toast = new bootstrap.Toast(toastElement);
                toast.show();
                
                // Remove toast after it's hidden
                toastElement.addEventListener('hidden.bs.toast', () => {
                    toastElement.remove();
                });
            }
        };
        
        // Test Functions
        async function initializePurchases() {
            try {
                Utils.showToast('جاري التحميل', 'تشغيل وحدة المشتريات...', 'info');
                
                await Purchases.initialize();
                await Purchases.show();
                
                Utils.showToast('نجح', 'تم تشغيل وحدة المشتريات بنجاح', 'success');
            } catch (error) {
                console.error('Error initializing purchases:', error);
                Utils.showToast('خطأ', 'فشل في تشغيل وحدة المشتريات', 'error');
            }
        }
        
        async function testSampleData() {
            try {
                Utils.showToast('جاري التحميل', 'إنشاء بيانات تجريبية...', 'info');
                
                await Purchases.createSampleData();
                
                if (Purchases.currentView) {
                    await Purchases.loadData();
                }
                
                Utils.showToast('نجح', 'تم إنشاء البيانات التجريبية بنجاح', 'success');
            } catch (error) {
                console.error('Error creating sample data:', error);
                Utils.showToast('خطأ', 'فشل في إنشاء البيانات التجريبية', 'error');
            }
        }
        
        function showModuleInfo() {
            const info = `
                <strong>وحدة إدارة المشتريات</strong><br>
                - إدارة أوامر الشراء<br>
                - إدارة الموردين<br>
                - تتبع المدفوعات<br>
                - التقارير والإحصائيات<br>
                - البحث والفلترة المتقدمة
            `;
            Utils.showToast('معلومات الوحدة', info, 'info');
        }
    </script>
    
    <!-- Load Purchases Module -->
    <script src="src/modules/purchases/purchases.js"></script>
</body>
</html>
