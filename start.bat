@echo off
chcp 65001 >nul
title Black Horse ERP - خادم التطوير المحلي

echo.
echo ================================================================
echo 🐎 Black Horse ERP - نظام إدارة الأعمال المتكامل
echo ================================================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت على النظام
    echo.
    echo 💡 يرجى تثبيت Python من الرابط التالي:
    echo    https://www.python.org/downloads/
    echo.
    echo 📋 خطوات التثبيت:
    echo    1. قم بتحميل Python من الرابط أعلاه
    echo    2. قم بتشغيل ملف التثبيت
    echo    3. تأكد من تحديد "Add Python to PATH"
    echo    4. أعد تشغيل هذا الملف
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
python --version

echo.
echo 🚀 جاري بدء خادم التطوير...
echo.

REM تشغيل الخادم
python start-server.py

REM في حالة حدوث خطأ
if %errorlevel% neq 0 (
    echo.
    echo ❌ حدث خطأ في تشغيل الخادم
    echo.
    echo 🔧 حلول مقترحة:
    echo    1. تأكد من وجود جميع الملفات في نفس المجلد
    echo    2. تأكد من عدم استخدام المنفذ 8080 من برنامج آخر
    echo    3. جرب تشغيل الأمر التالي يدوياً:
    echo       python start-server.py
    echo.
    pause
    exit /b 1
)

pause
