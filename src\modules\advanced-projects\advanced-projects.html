<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المشاريع المتقدمة - Black Horse ERP</title>
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }

        .main-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
            margin: 0 auto;
            max-width: 1400px;
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255,255,255,0.05) 10px,
                rgba(255,255,255,0.05) 20px
            );
            animation: slide 20s linear infinite;
        }

        @keyframes slide {
            0% { transform: translateX(-50px); }
            100% { transform: translateX(50px); }
        }

        .page-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .page-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .stats-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .stats-label {
            color: #6c757d;
            font-weight: 500;
        }

        .stats-change {
            margin-top: 10px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .stats-up {
            color: var(--success-color);
        }

        .stats-down {
            color: var(--danger-color);
        }

        .view-tabs {
            display: flex;
            background: #f8f9fa;
            padding: 0 30px;
            border-bottom: 1px solid #dee2e6;
            overflow-x: auto;
        }

        .view-tab {
            background: none;
            border: none;
            padding: 15px 25px;
            color: #6c757d;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
            white-space: nowrap;
        }

        .view-tab:hover {
            color: var(--primary-color);
            background: rgba(44, 62, 80, 0.05);
        }

        .view-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
            background: white;
        }

        .view-content {
            padding: 30px;
            min-height: 500px;
        }

        .filters-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .filters-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .filter-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: var(--dark-color);
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #dee2e6;
            padding: 10px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
        }

        .btn {
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(44, 62, 80, 0.3);
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
        }

        .project-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .project-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: start;
            margin-bottom: 20px;
        }

        .project-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .project-code {
            font-size: 0.9rem;
            color: #6c757d;
            font-family: monospace;
        }

        .project-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-active {
            background: rgba(39, 174, 96, 0.1);
            color: var(--success-color);
        }

        .status-planning {
            background: rgba(52, 152, 219, 0.1);
            color: var(--secondary-color);
        }

        .status-on-hold {
            background: rgba(243, 156, 18, 0.1);
            color: var(--warning-color);
        }

        .status-completed {
            background: rgba(39, 174, 96, 0.1);
            color: var(--success-color);
        }

        .status-cancelled {
            background: rgba(231, 76, 60, 0.1);
            color: var(--danger-color);
        }

        .project-progress {
            margin: 20px 0;
        }

        .progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .progress {
            height: 8px;
            border-radius: 10px;
            background: #e9ecef;
        }

        .progress-bar {
            border-radius: 10px;
            background: linear-gradient(90deg, var(--success-color), #2ecc71);
        }

        .project-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .stat-value {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--primary-color);
        }

        .stat-label {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 2px;
        }

        .project-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .action-btn {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            background: white;
            border-radius: 6px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h4 {
            margin-bottom: 10px;
        }

        .empty-state p {
            margin-bottom: 30px;
        }

        .quick-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .quick-action-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(44, 62, 80, 0.3);
        }

        .portfolio-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
        }

        .portfolio-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .portfolio-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .portfolio-stat {
            text-align: center;
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
        }

        .portfolio-stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .portfolio-stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        /* تبويبات العرض */
        .view-tabs {
            display: flex;
            background: white;
            border-radius: 15px;
            padding: 10px;
            margin: 30px 0;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            overflow-x: auto;
        }

        .view-tab {
            flex: 1;
            padding: 15px 20px;
            border: none;
            background: transparent;
            border-radius: 10px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            min-width: 120px;
        }

        .view-tab:hover {
            background: rgba(44, 62, 80, 0.1);
        }

        .view-tab.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 3px 10px rgba(44, 62, 80, 0.3);
        }

        .view-tab i {
            margin-left: 8px;
        }

        /* محتوى العرض */
        .view-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        /* لوحة الفلاتر */
        .filters-panel {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
        }

        .filters-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--primary-color);
        }

        .form-control, .form-select {
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
            outline: none;
        }

        /* شبكة المشاريع */
        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 25px;
        }

        .project-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .project-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .project-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .project-code {
            font-size: 0.9rem;
            color: #6c757d;
            font-family: 'Courier New', monospace;
        }

        .project-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-active {
            background: rgba(39, 174, 96, 0.1);
            color: var(--success-color);
        }

        .status-planning {
            background: rgba(52, 152, 219, 0.1);
            color: var(--secondary-color);
        }

        .status-on-hold {
            background: rgba(243, 156, 18, 0.1);
            color: var(--warning-color);
        }

        .status-completed {
            background: rgba(39, 174, 96, 0.1);
            color: var(--success-color);
        }

        .status-cancelled {
            background: rgba(231, 76, 60, 0.1);
            color: var(--danger-color);
        }

        @media (max-width: 768px) {
            .page-header h1 {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                padding: 20px;
            }

            .view-content {
                padding: 20px;
            }

            .projects-grid {
                grid-template-columns: 1fr;
            }

            .quick-actions {
                justify-content: center;
            }

            .view-tabs {
                padding: 5px;
            }

            .view-tab {
                padding: 10px 15px;
                font-size: 0.9rem;
            }

            .filters-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <h1><i class="fas fa-project-diagram"></i> إدارة المشاريع المتقدمة</h1>
            <p>نظام شامل لإدارة المشاريع والمحافظ والموارد</p>
        </div>

        <!-- الإحصائيات السريعة -->
        <div class="stats-grid">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-project-diagram"></i>
                </div>
                <div class="stats-number" id="totalProjects">0</div>
                <div class="stats-label">إجمالي المشاريع</div>
                <div class="stats-change stats-up" id="projectsChange">
                    <i class="fas fa-arrow-up"></i> +5.2%
                </div>
            </div>
            
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-tasks"></i>
                </div>
                <div class="stats-number" id="activeProjects">0</div>
                <div class="stats-label">المشاريع النشطة</div>
                <div class="stats-change stats-up" id="activeChange">
                    <i class="fas fa-arrow-up"></i> +3.1%
                </div>
            </div>
            
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stats-number" id="averageProgress">0%</div>
                <div class="stats-label">متوسط التقدم</div>
                <div class="stats-change stats-up" id="progressChange">
                    <i class="fas fa-arrow-up"></i> +2.8%
                </div>
            </div>
            
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stats-number" id="totalBudget">0</div>
                <div class="stats-label">إجمالي الميزانية</div>
                <div class="stats-change stats-down" id="budgetChange">
                    <i class="fas fa-arrow-down"></i> -1.2%
                </div>
            </div>
        </div>

        <!-- تبويبات العرض -->
        <div class="view-tabs">
            <button class="view-tab active" onclick="setView('projects')" id="projectsTab">
                <i class="fas fa-project-diagram"></i> المشاريع
            </button>
            <button class="view-tab" onclick="setView('portfolios')" id="portfoliosTab">
                <i class="fas fa-briefcase"></i> المحافظ
            </button>
            <button class="view-tab" onclick="setView('risks')" id="risksTab">
                <i class="fas fa-exclamation-triangle"></i> المخاطر
            </button>
            <button class="view-tab" onclick="setView('budgets')" id="budgetsTab">
                <i class="fas fa-chart-pie"></i> الميزانيات
            </button>
            <button class="view-tab" onclick="setView('timelines')" id="timelinesTab">
                <i class="fas fa-calendar-alt"></i> الجداول
            </button>
            <button class="view-tab" onclick="setView('reports')" id="reportsTab">
                <i class="fas fa-chart-bar"></i> التقارير
            </button>
        </div>

        <!-- محتوى العرض -->
        <div class="view-content">
            <!-- عرض المشاريع -->
            <div id="projectsView">
                <!-- لوحة الفلاتر -->
                <div class="filters-panel">
                    <div class="filters-row">
                        <div class="filter-group">
                            <label for="searchInput">البحث</label>
                            <input type="text" class="form-control" id="searchInput"
                                   placeholder="ابحث في المشاريع..." onkeyup="searchProjects()">
                        </div>
                        <div class="filter-group">
                            <label for="statusFilter">الحالة</label>
                            <select class="form-select" id="statusFilter" onchange="applyFilters()">
                                <option value="">جميع الحالات</option>
                                <option value="planning">التخطيط</option>
                                <option value="active">نشط</option>
                                <option value="on-hold">معلق</option>
                                <option value="completed">مكتمل</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="priorityFilter">الأولوية</label>
                            <select class="form-select" id="priorityFilter" onchange="applyFilters()">
                                <option value="">جميع الأولويات</option>
                                <option value="low">منخفضة</option>
                                <option value="medium">متوسطة</option>
                                <option value="high">عالية</option>
                                <option value="critical">حرجة</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="typeFilter">النوع</label>
                            <select class="form-select" id="typeFilter" onchange="applyFilters()">
                                <option value="">جميع الأنواع</option>
                                <option value="standard">قياسي</option>
                                <option value="agile">رشيق</option>
                                <option value="waterfall">تتابعي</option>
                                <option value="hybrid">مختلط</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <button class="btn btn-primary" onclick="refreshData()">
                                <i class="fas fa-sync-alt"></i> تحديث
                            </button>
                        </div>
                    </div>
                </div>

                <!-- الإجراءات السريعة -->
                <div class="quick-actions mb-4">
                    <button class="quick-action-btn" onclick="showCreateProjectModal()">
                        <i class="fas fa-plus"></i> مشروع جديد
                    </button>
                    <button class="quick-action-btn" onclick="showCreatePortfolioModal()">
                        <i class="fas fa-briefcase"></i> محفظة جديدة
                    </button>
                    <button class="quick-action-btn" onclick="showImportProjectsModal()">
                        <i class="fas fa-upload"></i> استيراد مشاريع
                    </button>
                    <button class="quick-action-btn" onclick="exportProjects()">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>

                <!-- شبكة المشاريع -->
                <div class="projects-grid" id="projectsGrid">
                    <!-- سيتم ملء المشاريع هنا -->
                </div>
            </div>

            <!-- عرض المحافظ -->
            <div id="portfoliosView" style="display: none;">
                <div class="quick-actions mb-4">
                    <button class="quick-action-btn" onclick="showCreatePortfolioModal()">
                        <i class="fas fa-plus"></i> محفظة جديدة
                    </button>
                    <button class="quick-action-btn" onclick="analyzePortfolios()">
                        <i class="fas fa-chart-line"></i> تحليل الأداء
                    </button>
                </div>

                <div id="portfoliosList">
                    <!-- سيتم ملء المحافظ هنا -->
                </div>
            </div>

            <!-- عرض المخاطر -->
            <div id="risksView" style="display: none;">
                <div class="quick-actions mb-4">
                    <button class="quick-action-btn" onclick="showCreateRiskModal()">
                        <i class="fas fa-plus"></i> مخاطرة جديدة
                    </button>
                    <button class="quick-action-btn" onclick="generateRiskReport()">
                        <i class="fas fa-file-alt"></i> تقرير المخاطر
                    </button>
                </div>

                <div id="risksList">
                    <!-- سيتم ملء المخاطر هنا -->
                </div>
            </div>

            <!-- عرض الميزانيات -->
            <div id="budgetsView" style="display: none;">
                <div class="quick-actions mb-4">
                    <button class="quick-action-btn" onclick="showBudgetAnalysis()">
                        <i class="fas fa-chart-pie"></i> تحليل الميزانية
                    </button>
                    <button class="quick-action-btn" onclick="generateBudgetReport()">
                        <i class="fas fa-file-alt"></i> تقرير الميزانية
                    </button>
                </div>

                <div id="budgetsList">
                    <!-- سيتم ملء الميزانيات هنا -->
                </div>
            </div>

            <!-- عرض الجداول -->
            <div id="timelinesView" style="display: none;">
                <div class="quick-actions mb-4">
                    <button class="quick-action-btn" onclick="showGanttChart()">
                        <i class="fas fa-chart-gantt"></i> مخطط جانت
                    </button>
                    <button class="quick-action-btn" onclick="showCalendarView()">
                        <i class="fas fa-calendar"></i> عرض التقويم
                    </button>
                </div>

                <div id="timelinesList">
                    <!-- سيتم ملء الجداول هنا -->
                </div>
            </div>

            <!-- عرض التقارير -->
            <div id="reportsView" style="display: none;">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-bar"></i> تقرير الأداء</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="performanceChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-pie"></i> توزيع الميزانية</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="budgetChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-exclamation-triangle"></i> تحليل المخاطر</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="riskChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-calendar-check"></i> الجدولة الزمنية</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="timelineChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../core/database.js"></script>
    <script src="../../core/utils.js"></script>
    <script src="advanced-projects.js"></script>

    <script>
        // متغيرات عامة
        let advancedProjectsManager;
        let currentView = 'projects';
        let currentFilters = {};
        let charts = {};

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeAdvancedProjects();
            loadData();
            updateStatistics();
            setupEventListeners();
        });

        // تهيئة نظام المشاريع المتقدم
        function initializeAdvancedProjects() {
            advancedProjectsManager = new AdvancedProjectsManager();
            console.log('✅ تم تهيئة واجهة إدارة المشاريع المتقدمة');
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // تحديث الإحصائيات كل دقيقة
            setInterval(updateStatistics, 60000);
        }

        // تحميل البيانات
        function loadData() {
            switch(currentView) {
                case 'projects':
                    loadProjects();
                    break;
                case 'portfolios':
                    loadPortfolios();
                    break;
                case 'risks':
                    loadRisks();
                    break;
                case 'budgets':
                    loadBudgets();
                    break;
                case 'timelines':
                    loadTimelines();
                    break;
                case 'reports':
                    loadReports();
                    break;
            }
        }

        // تحميل المشاريع
        function loadProjects() {
            const projects = Array.from(advancedProjectsManager.projects.values());
            displayProjects(projects);
        }

        // عرض المشاريع
        function displayProjects(projects) {
            const grid = document.getElementById('projectsGrid');

            if (projects.length === 0) {
                grid.innerHTML = `
                    <div class="col-12">
                        <div class="empty-state">
                            <i class="fas fa-project-diagram"></i>
                            <h4>لا توجد مشاريع</h4>
                            <p>ابدأ بإنشاء مشروع جديد لإدارة أعمالك</p>
                            <button class="btn btn-primary" onclick="showCreateProjectModal()">
                                <i class="fas fa-plus"></i> إنشاء مشروع جديد
                            </button>
                        </div>
                    </div>
                `;
                return;
            }

            grid.innerHTML = projects.map(project => createProjectCard(project)).join('');
        }

        // إنشاء بطاقة مشروع
        function createProjectCard(project) {
            const progressColor = project.progress.overall >= 75 ? 'success' :
                                 project.progress.overall >= 50 ? 'warning' : 'danger';

            return `
                <div class="project-card">
                    <div class="project-header">
                        <div>
                            <div class="project-title">${project.name}</div>
                            <div class="project-code">${project.code}</div>
                        </div>
                        <span class="project-status status-${project.status}">
                            ${getStatusLabel(project.status)}
                        </span>
                    </div>

                    <p class="text-muted mb-3">${project.description || 'لا يوجد وصف'}</p>

                    <div class="project-progress">
                        <div class="progress-label">
                            <span>التقدم الإجمالي</span>
                            <span>${project.progress.overall.toFixed(1)}%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-${progressColor}"
                                 style="width: ${project.progress.overall}%"></div>
                        </div>
                    </div>

                    <div class="project-stats">
                        <div class="stat-item">
                            <div class="stat-value">${formatCurrency(project.budget.planned)}</div>
                            <div class="stat-label">الميزانية</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${project.team.members.length}</div>
                            <div class="stat-label">أعضاء الفريق</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${project.risks.total}</div>
                            <div class="stat-label">المخاطر</div>
                        </div>
                    </div>

                    <div class="project-actions">
                        <button class="action-btn" onclick="viewProject('${project.id}')">
                            <i class="fas fa-eye"></i> عرض
                        </button>
                        <button class="action-btn" onclick="editProject('${project.id}')">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="action-btn" onclick="manageRisks('${project.id}')">
                            <i class="fas fa-exclamation-triangle"></i> المخاطر
                        </button>
                    </div>
                </div>
            `;
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            const reports = advancedProjectsManager.getAdvancedProjectsReports();

            document.getElementById('totalProjects').textContent = reports.projects.total;
            document.getElementById('activeProjects').textContent = reports.projects.active;
            document.getElementById('averageProgress').textContent = reports.projects.averageProgress.toFixed(1) + '%';
            document.getElementById('totalBudget').textContent = formatCurrency(reports.budget.totalPlanned);
        }

        // تعيين العرض
        function setView(view) {
            currentView = view;

            // إخفاء جميع العروض
            document.querySelectorAll('[id$="View"]').forEach(el => {
                el.style.display = 'none';
            });

            // إزالة الفئة النشطة من جميع التبويبات
            document.querySelectorAll('.view-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // عرض العرض المحدد
            document.getElementById(view + 'View').style.display = 'block';
            document.getElementById(view + 'Tab').classList.add('active');

            // تحميل البيانات
            loadData();
        }

        // البحث في المشاريع
        function searchProjects() {
            const query = document.getElementById('searchInput').value;
            if (!query.trim()) {
                loadProjects();
                return;
            }

            const results = advancedProjectsManager.searchAdvancedProjects(query, currentFilters);
            displayProjects(results);
        }

        // تطبيق الفلاتر
        function applyFilters() {
            currentFilters = {
                status: document.getElementById('statusFilter').value,
                priority: document.getElementById('priorityFilter').value,
                type: document.getElementById('typeFilter').value
            };

            // إزالة الفلاتر الفارغة
            Object.keys(currentFilters).forEach(key => {
                if (!currentFilters[key]) {
                    delete currentFilters[key];
                }
            });

            searchProjects();
        }

        // الحصول على تسمية الحالة
        function getStatusLabel(status) {
            const labels = {
                'planning': 'التخطيط',
                'active': 'نشط',
                'on-hold': 'معلق',
                'completed': 'مكتمل',
                'cancelled': 'ملغي'
            };
            return labels[status] || status;
        }

        // تنسيق العملة
        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: 'SAR'
            }).format(amount);
        }

        // عرض الإشعارات
        function showNotification(message, type = 'info') {
            const alertClass = {
                'success': 'alert-success',
                'error': 'alert-danger',
                'warning': 'alert-warning',
                'info': 'alert-info'
            }[type] || 'alert-info';

            const notification = document.createElement('div');
            notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // دوال النوافذ المنبثقة
        function showCreateProjectModal() {
            const projectData = {
                name: 'مشروع تجريبي ' + Date.now(),
                description: 'مشروع تجريبي لاختبار النظام',
                type: 'standard',
                methodology: 'traditional',
                priority: 'medium',
                plannedStart: new Date().toISOString().split('T')[0],
                plannedEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                plannedBudget: 100000,
                managerId: 'user1',
                teamMembers: ['user1', 'user2'],
                objectives: ['تحقيق الهدف الأول', 'تحقيق الهدف الثاني'],
                scope: 'نطاق المشروع التجريبي'
            };

            try {
                const project = advancedProjectsManager.createAdvancedProject(projectData);
                showNotification('تم إنشاء المشروع بنجاح', 'success');
                loadProjects();
                updateStatistics();
            } catch (error) {
                showNotification('خطأ في إنشاء المشروع: ' + error.message, 'error');
            }
        }

        function showCreatePortfolioModal() {
            const portfolioData = {
                name: 'محفظة تجريبية ' + Date.now(),
                description: 'محفظة تجريبية لاختبار النظام',
                strategicGoals: ['الهدف الاستراتيجي الأول', 'الهدف الاستراتيجي الثاني'],
                budget: 500000,
                manager: 'user1',
                startDate: new Date().toISOString().split('T')[0],
                endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
            };

            try {
                const portfolio = advancedProjectsManager.createProjectPortfolio(portfolioData);
                showNotification('تم إنشاء المحفظة بنجاح', 'success');
                updateStatistics();
            } catch (error) {
                showNotification('خطأ في إنشاء المحفظة: ' + error.message, 'error');
            }
        }

        function showCreateRiskModal() {
            const riskData = {
                projectId: Array.from(advancedProjectsManager.projects.keys())[0],
                name: 'مخاطرة تجريبية ' + Date.now(),
                description: 'مخاطرة تجريبية لاختبار النظام',
                category: 'technical',
                probability: 0.5,
                impact: 0.7,
                response: 'mitigate',
                owner: 'user1'
            };

            if (!riskData.projectId) {
                showNotification('يجب إنشاء مشروع أولاً', 'warning');
                return;
            }

            try {
                const risk = advancedProjectsManager.createProjectRisk(riskData);
                showNotification('تم إنشاء المخاطرة بنجاح', 'success');
                updateStatistics();
            } catch (error) {
                showNotification('خطأ في إنشاء المخاطرة: ' + error.message, 'error');
            }
        }

        function showImportProjectsModal() {
            showNotification('سيتم إضافة نافذة الاستيراد قريباً', 'info');
        }

        // دوال إضافية
        function viewProject(projectId) {
            const project = advancedProjectsManager.projects.get(projectId);
            if (project) {
                showNotification(`عرض تفاصيل المشروع: ${project.name}`, 'info');
            }
        }

        function editProject(projectId) {
            const project = advancedProjectsManager.projects.get(projectId);
            if (project) {
                showNotification(`تعديل المشروع: ${project.name}`, 'info');
            }
        }

        function manageRisks(projectId) {
            const project = advancedProjectsManager.projects.get(projectId);
            if (project) {
                showNotification(`إدارة مخاطر المشروع: ${project.name}`, 'info');
            }
        }

        function loadPortfolios() {
            const portfolios = Array.from(advancedProjectsManager.projectPortfolios.values());
            const container = document.getElementById('portfoliosList');

            if (portfolios.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-briefcase"></i>
                        <h4>لا توجد محافظ</h4>
                        <p>ابدأ بإنشاء محفظة جديدة لتنظيم مشاريعك</p>
                        <button class="btn btn-primary" onclick="showCreatePortfolioModal()">
                            <i class="fas fa-plus"></i> إنشاء محفظة جديدة
                        </button>
                    </div>
                `;
                return;
            }

            container.innerHTML = portfolios.map(portfolio => `
                <div class="portfolio-card">
                    <div class="portfolio-title">${portfolio.name}</div>
                    <p>${portfolio.description}</p>
                    <div class="portfolio-stats">
                        <div class="portfolio-stat">
                            <div class="portfolio-stat-value">${portfolio.projects.length}</div>
                            <div class="portfolio-stat-label">المشاريع</div>
                        </div>
                        <div class="portfolio-stat">
                            <div class="portfolio-stat-value">${formatCurrency(portfolio.totalBudget)}</div>
                            <div class="portfolio-stat-label">الميزانية</div>
                        </div>
                        <div class="portfolio-stat">
                            <div class="portfolio-stat-value">${(portfolio.performance?.onTimePercentage || 0).toFixed(1)}%</div>
                            <div class="portfolio-stat-label">في الوقت المحدد</div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function loadRisks() {
            const risks = Array.from(advancedProjectsManager.projectRisks.values());
            const container = document.getElementById('risksList');

            if (risks.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h4>لا توجد مخاطر</h4>
                        <p>ابدأ بتحديد المخاطر المحتملة لمشاريعك</p>
                        <button class="btn btn-primary" onclick="showCreateRiskModal()">
                            <i class="fas fa-plus"></i> إضافة مخاطرة جديدة
                        </button>
                    </div>
                `;
                return;
            }

            container.innerHTML = risks.map(risk => `
                <div class="card mb-3">
                    <div class="card-body">
                        <h5 class="card-title">${risk.name}</h5>
                        <p class="card-text">${risk.description}</p>
                        <div class="row">
                            <div class="col-md-3">
                                <strong>الاحتمالية:</strong> ${(risk.probability * 100).toFixed(0)}%
                            </div>
                            <div class="col-md-3">
                                <strong>التأثير:</strong> ${(risk.impact * 100).toFixed(0)}%
                            </div>
                            <div class="col-md-3">
                                <strong>المستوى:</strong>
                                <span class="badge bg-${risk.level === 'high' ? 'danger' : risk.level === 'medium' ? 'warning' : 'success'}">
                                    ${risk.level}
                                </span>
                            </div>
                            <div class="col-md-3">
                                <strong>النتيجة:</strong> ${(risk.score * 100).toFixed(0)}%
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function loadBudgets() {
            const budgets = Array.from(advancedProjectsManager.projectBudgets.values());
            const container = document.getElementById('budgetsList');

            if (budgets.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-chart-pie"></i>
                        <h4>لا توجد ميزانيات</h4>
                        <p>الميزانيات ستظهر هنا عند إنشاء المشاريع</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = budgets.map(budget => `
                <div class="card mb-3">
                    <div class="card-body">
                        <h5 class="card-title">ميزانية المشروع</h5>
                        <div class="row">
                            <div class="col-md-3">
                                <strong>المخطط:</strong> ${formatCurrency(budget.planned.total)}
                            </div>
                            <div class="col-md-3">
                                <strong>الفعلي:</strong> ${formatCurrency(budget.actual.total)}
                            </div>
                            <div class="col-md-3">
                                <strong>التباين:</strong> ${formatCurrency(budget.variance.total)}
                            </div>
                            <div class="col-md-3">
                                <strong>CPI:</strong> ${budget.indicators.cpi.toFixed(2)}
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function loadTimelines() {
            const timelines = Array.from(advancedProjectsManager.projectTimelines.values());
            const container = document.getElementById('timelinesList');

            if (timelines.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-calendar-alt"></i>
                        <h4>لا توجد جداول زمنية</h4>
                        <p>الجداول الزمنية ستظهر هنا عند إنشاء المشاريع</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = timelines.map(timeline => `
                <div class="card mb-3">
                    <div class="card-body">
                        <h5 class="card-title">الجدول الزمني للمشروع</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <strong>التقدم الإجمالي:</strong> ${timeline.progress.overall.toFixed(1)}%
                            </div>
                            <div class="col-md-4">
                                <strong>SPI:</strong> ${timeline.indicators.spi.toFixed(2)}
                            </div>
                            <div class="col-md-4">
                                <strong>المراحل:</strong> ${timeline.phases.length}
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function loadReports() {
            // تحميل التقارير والرسوم البيانية
            const reports = advancedProjectsManager.getAdvancedProjectsReports();

            // رسم بياني للأداء
            if (charts.performance) {
                charts.performance.destroy();
            }

            const performanceCtx = document.getElementById('performanceChart').getContext('2d');
            charts.performance = new Chart(performanceCtx, {
                type: 'bar',
                data: {
                    labels: ['إجمالي المشاريع', 'المشاريع النشطة', 'المشاريع المكتملة'],
                    datasets: [{
                        label: 'عدد المشاريع',
                        data: [reports.projects.total, reports.projects.active, reports.projects.completed],
                        backgroundColor: ['#3498db', '#27ae60', '#2ecc71']
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        function exportProjects() {
            const projects = Array.from(advancedProjectsManager.projects.values());
            const dataStr = JSON.stringify(projects, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = 'advanced_projects_export.json';
            link.click();

            showNotification('تم تصدير المشاريع بنجاح', 'success');
        }

        function refreshData() {
            loadData();
            updateStatistics();
            showNotification('تم تحديث البيانات بنجاح', 'success');
        }

        function analyzePortfolios() {
            const portfolios = Array.from(advancedProjectsManager.projectPortfolios.values());
            if (portfolios.length === 0) {
                showNotification('لا توجد محافظ للتحليل', 'warning');
                return;
            }

            portfolios.forEach(portfolio => {
                advancedProjectsManager.analyzePortfolioPerformance(portfolio.id);
            });

            showNotification('تم تحليل أداء المحافظ بنجاح', 'success');
            loadPortfolios();
        }

        function generateRiskReport() {
            const risks = Array.from(advancedProjectsManager.projectRisks.values());
            if (risks.length === 0) {
                showNotification('لا توجد مخاطر لإنشاء التقرير', 'warning');
                return;
            }

            showNotification('تم إنشاء تقرير المخاطر', 'success');
        }

        function showBudgetAnalysis() {
            showNotification('عرض تحليل الميزانية', 'info');
        }

        function generateBudgetReport() {
            showNotification('تم إنشاء تقرير الميزانية', 'success');
        }

        function showGanttChart() {
            showNotification('عرض مخطط جانت', 'info');
        }

        function showCalendarView() {
            showNotification('عرض التقويم', 'info');
        }
    </script>
</body>
</html>
