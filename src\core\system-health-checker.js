/**
 * نظام التحقق من صحة النظام الشامل
 * System Health Checker for Black Horse POS
 * 
 * يقوم بفحص جميع الوحدات والتحسينات للتأكد من عملها بشكل صحيح
 */

class SystemHealthChecker {
    constructor() {
        this.requiredModules = [
            'Utils', 'UnifiedStorage', 'DatabaseOptimizer', 'ModuleManager',
            'AssetOptimizer', 'PerformanceOptimizer', 'UIOptimizer',
            'ProductsManager', 'CustomersManager', 'SalesManager', 
            'ReportsManager', 'ExpensesManager', 'PerformanceDashboard',
            'PerformanceTests', 'AppInitializer'
        ];
        
        this.optionalModules = [
            'ConsolidatedFunctions', 'DataMigration', 'Helpers', 'Validators'
        ];
        
        this.healthStatus = {};
        this.errors = [];
        this.warnings = [];
        this.performanceMetrics = {};
    }

    /**
     * فحص صحة النظام الشامل
     */
    async checkSystemHealth() {
        console.log('🔍 بدء فحص صحة النظام الشامل...');
        const startTime = performance.now();
        
        try {
            // فحص الوحدات المطلوبة
            await this.checkRequiredModules();
            
            // فحص الوحدات الاختيارية
            await this.checkOptionalModules();
            
            // فحص قاعدة البيانات
            await this.checkDatabase();
            
            // فحص الأداء
            await this.checkPerformance();
            
            // فحص التخزين المؤقت
            await this.checkCaching();
            
            // فحص واجهة المستخدم
            await this.checkUI();
            
            // فحص الأمان
            await this.checkSecurity();
            
            // حساب المقاييس النهائية
            const endTime = performance.now();
            this.performanceMetrics.totalCheckTime = endTime - startTime;
            
            // إنشاء التقرير النهائي
            this.generateHealthReport();
            
            return this.getHealthSummary();
            
        } catch (error) {
            console.error('❌ خطأ في فحص صحة النظام:', error);
            this.errors.push(`خطأ عام في فحص النظام: ${error.message}`);
            return this.getHealthSummary();
        }
    }

    /**
     * فحص الوحدات المطلوبة
     */
    async checkRequiredModules() {
        console.log('📦 فحص الوحدات المطلوبة...');
        
        for (const moduleName of this.requiredModules) {
            const startTime = performance.now();
            
            try {
                const isAvailable = window[moduleName] !== undefined;
                const loadTime = performance.now() - startTime;
                
                this.healthStatus[moduleName] = {
                    status: isAvailable ? 'OK' : 'ERROR',
                    message: isAvailable ? 'متاح ويعمل بشكل صحيح' : 'غير متاح',
                    loadTime: loadTime,
                    type: 'required'
                };
                
                if (!isAvailable) {
                    this.errors.push(`الوحدة المطلوبة ${moduleName} غير متاحة`);
                } else {
                    // فحص إضافي للوحدات المهمة
                    await this.checkModuleFunctionality(moduleName);
                }
                
            } catch (error) {
                this.healthStatus[moduleName] = {
                    status: 'ERROR',
                    message: `خطأ في فحص الوحدة: ${error.message}`,
                    type: 'required'
                };
                this.errors.push(`خطأ في فحص الوحدة ${moduleName}: ${error.message}`);
            }
        }
    }

    /**
     * فحص الوحدات الاختيارية
     */
    async checkOptionalModules() {
        console.log('📋 فحص الوحدات الاختيارية...');
        
        for (const moduleName of this.optionalModules) {
            try {
                const isAvailable = window[moduleName] !== undefined;
                
                this.healthStatus[moduleName] = {
                    status: isAvailable ? 'OK' : 'WARNING',
                    message: isAvailable ? 'متاح' : 'غير متاح (اختياري)',
                    type: 'optional'
                };
                
                if (!isAvailable) {
                    this.warnings.push(`الوحدة الاختيارية ${moduleName} غير متاحة`);
                }
                
            } catch (error) {
                this.healthStatus[moduleName] = {
                    status: 'WARNING',
                    message: `خطأ في فحص الوحدة الاختيارية: ${error.message}`,
                    type: 'optional'
                };
                this.warnings.push(`خطأ في فحص الوحدة الاختيارية ${moduleName}: ${error.message}`);
            }
        }
    }

    /**
     * فحص وظائف الوحدة
     */
    async checkModuleFunctionality(moduleName) {
        const module = window[moduleName];
        
        if (!module) return;
        
        try {
            // فحص الوحدات الخاصة
            switch (moduleName) {
                case 'UnifiedStorage':
                    if (typeof module.init === 'function') {
                        await module.init();
                    }
                    break;
                    
                case 'PerformanceDashboard':
                    if (typeof module.initialize === 'function') {
                        // لا نقوم بالتهيئة الفعلية هنا لتجنب التداخل
                        this.healthStatus[moduleName].functionalityCheck = 'متاح للتهيئة';
                    }
                    break;
                    
                case 'AssetOptimizer':
                    if (typeof module.initialize === 'function') {
                        this.healthStatus[moduleName].functionalityCheck = 'جاهز للتحسين';
                    }
                    break;
                    
                default:
                    // فحص عام للوحدات الأخرى
                    if (typeof module === 'object' && module !== null) {
                        this.healthStatus[moduleName].functionalityCheck = 'كائن صالح';
                    }
                    break;
            }
        } catch (error) {
            this.warnings.push(`تحذير في فحص وظائف ${moduleName}: ${error.message}`);
        }
    }

    /**
     * فحص قاعدة البيانات
     */
    async checkDatabase() {
        console.log('🗄️ فحص قاعدة البيانات...');
        const startTime = performance.now();
        
        try {
            if (window.UnifiedStorage) {
                // فحص الاتصال
                await window.UnifiedStorage.init();
                
                // فحص العمليات الأساسية
                const testKey = 'health_check_test';
                const testData = { test: true, timestamp: Date.now() };
                
                await window.UnifiedStorage.save(testKey, testData);
                const retrievedData = await window.UnifiedStorage.load(testKey);
                
                if (retrievedData && retrievedData.test === true) {
                    const responseTime = performance.now() - startTime;
                    
                    this.healthStatus.Database = {
                        status: 'OK',
                        message: 'قاعدة البيانات تعمل بشكل صحيح',
                        responseTime: responseTime,
                        operations: ['save', 'load', 'init']
                    };
                    
                    this.performanceMetrics.databaseResponseTime = responseTime;
                    
                    // تنظيف بيانات الاختبار
                    await window.UnifiedStorage.remove(testKey);
                } else {
                    throw new Error('فشل في اختبار العمليات الأساسية');
                }
            } else {
                throw new Error('UnifiedStorage غير متاح');
            }
        } catch (error) {
            this.healthStatus.Database = {
                status: 'ERROR',
                message: `خطأ في قاعدة البيانات: ${error.message}`,
                responseTime: performance.now() - startTime
            };
            this.errors.push(`خطأ في قاعدة البيانات: ${error.message}`);
        }
    }

    /**
     * فحص الأداء
     */
    async checkPerformance() {
        console.log('⚡ فحص الأداء...');
        const startTime = performance.now();
        
        try {
            // فحص الذاكرة
            const memoryInfo = this.getMemoryInfo();
            
            // فحص وقت الاستجابة
            const responseTest = await this.testResponseTime();
            
            // فحص FPS (إذا كان متاحاً)
            const fpsTest = this.testFrameRate();
            
            const totalTime = performance.now() - startTime;
            
            this.healthStatus.Performance = {
                status: this.evaluatePerformanceStatus(memoryInfo, responseTest, totalTime),
                message: `فحص الأداء مكتمل في ${totalTime.toFixed(2)}ms`,
                memory: memoryInfo,
                responseTime: responseTest,
                fps: fpsTest,
                checkTime: totalTime
            };
            
            this.performanceMetrics = {
                ...this.performanceMetrics,
                memory: memoryInfo,
                responseTime: responseTest,
                fps: fpsTest
            };
            
        } catch (error) {
            this.healthStatus.Performance = {
                status: 'ERROR',
                message: `خطأ في فحص الأداء: ${error.message}`
            };
            this.errors.push(`خطأ في فحص الأداء: ${error.message}`);
        }
    }

    /**
     * الحصول على معلومات الذاكرة
     */
    getMemoryInfo() {
        try {
            if (performance.memory) {
                return {
                    used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                    total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                    limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
                };
            }
            return { message: 'معلومات الذاكرة غير متاحة' };
        } catch (error) {
            return { error: error.message };
        }
    }

    /**
     * اختبار وقت الاستجابة
     */
    async testResponseTime() {
        const startTime = performance.now();
        
        // محاكاة عملية
        await new Promise(resolve => setTimeout(resolve, 10));
        
        const endTime = performance.now();
        return endTime - startTime;
    }

    /**
     * اختبار معدل الإطارات
     */
    testFrameRate() {
        return new Promise((resolve) => {
            let frames = 0;
            const startTime = performance.now();
            
            function countFrames() {
                frames++;
                if (frames < 60) {
                    requestAnimationFrame(countFrames);
                } else {
                    const endTime = performance.now();
                    const fps = Math.round(1000 / ((endTime - startTime) / frames));
                    resolve(fps);
                }
            }
            
            requestAnimationFrame(countFrames);
        });
    }

    /**
     * تقييم حالة الأداء
     */
    evaluatePerformanceStatus(memoryInfo, responseTime, checkTime) {
        if (memoryInfo.used > 200) return 'WARNING'; // أكثر من 200 MB
        if (responseTime > 100) return 'WARNING'; // أكثر من 100ms
        if (checkTime > 1000) return 'WARNING'; // أكثر من ثانية واحدة
        return 'OK';
    }

    /**
     * فحص التخزين المؤقت
     */
    async checkCaching() {
        console.log('💾 فحص التخزين المؤقت...');
        
        try {
            const cacheStatus = {
                localStorage: this.checkLocalStorage(),
                sessionStorage: this.checkSessionStorage(),
                indexedDB: await this.checkIndexedDB()
            };
            
            this.healthStatus.Caching = {
                status: 'OK',
                message: 'أنظمة التخزين المؤقت تعمل بشكل صحيح',
                details: cacheStatus
            };
            
        } catch (error) {
            this.healthStatus.Caching = {
                status: 'ERROR',
                message: `خطأ في فحص التخزين المؤقت: ${error.message}`
            };
            this.errors.push(`خطأ في التخزين المؤقت: ${error.message}`);
        }
    }

    /**
     * فحص localStorage
     */
    checkLocalStorage() {
        try {
            const testKey = 'health_check_ls';
            localStorage.setItem(testKey, 'test');
            const value = localStorage.getItem(testKey);
            localStorage.removeItem(testKey);
            return value === 'test' ? 'متاح' : 'غير متاح';
        } catch (error) {
            return `خطأ: ${error.message}`;
        }
    }

    /**
     * فحص sessionStorage
     */
    checkSessionStorage() {
        try {
            const testKey = 'health_check_ss';
            sessionStorage.setItem(testKey, 'test');
            const value = sessionStorage.getItem(testKey);
            sessionStorage.removeItem(testKey);
            return value === 'test' ? 'متاح' : 'غير متاح';
        } catch (error) {
            return `خطأ: ${error.message}`;
        }
    }

    /**
     * فحص IndexedDB
     */
    async checkIndexedDB() {
        return new Promise((resolve) => {
            try {
                const request = indexedDB.open('health_check_db', 1);
                
                request.onsuccess = () => {
                    request.result.close();
                    indexedDB.deleteDatabase('health_check_db');
                    resolve('متاح');
                };
                
                request.onerror = () => {
                    resolve(`خطأ: ${request.error}`);
                };
                
                setTimeout(() => resolve('انتهت المهلة الزمنية'), 5000);
            } catch (error) {
                resolve(`خطأ: ${error.message}`);
            }
        });
    }

    /**
     * فحص واجهة المستخدم
     */
    async checkUI() {
        console.log('🎨 فحص واجهة المستخدم...');
        
        try {
            const uiChecks = {
                dom: document.readyState === 'complete',
                bootstrap: typeof window.bootstrap !== 'undefined',
                fontAwesome: this.checkFontAwesome(),
                responsiveness: this.checkResponsiveness()
            };
            
            const allPassed = Object.values(uiChecks).every(check => check === true);
            
            this.healthStatus.UI = {
                status: allPassed ? 'OK' : 'WARNING',
                message: allPassed ? 'واجهة المستخدم تعمل بشكل صحيح' : 'بعض مكونات واجهة المستخدم قد لا تعمل بشكل مثالي',
                details: uiChecks
            };
            
        } catch (error) {
            this.healthStatus.UI = {
                status: 'ERROR',
                message: `خطأ في فحص واجهة المستخدم: ${error.message}`
            };
            this.warnings.push(`خطأ في واجهة المستخدم: ${error.message}`);
        }
    }

    /**
     * فحص Font Awesome
     */
    checkFontAwesome() {
        const testElement = document.createElement('i');
        testElement.className = 'fas fa-home';
        testElement.style.position = 'absolute';
        testElement.style.left = '-9999px';
        document.body.appendChild(testElement);
        
        const computedStyle = window.getComputedStyle(testElement);
        const fontFamily = computedStyle.getPropertyValue('font-family');
        
        document.body.removeChild(testElement);
        
        return fontFamily.includes('Font Awesome');
    }

    /**
     * فحص الاستجابة
     */
    checkResponsiveness() {
        return window.innerWidth > 0 && window.innerHeight > 0;
    }

    /**
     * فحص الأمان
     */
    async checkSecurity() {
        console.log('🔒 فحص الأمان...');
        
        try {
            const securityChecks = {
                https: location.protocol === 'https:' || location.hostname === 'localhost',
                xss: this.checkXSSProtection(),
                csp: this.checkCSP(),
                localStorage: this.checkLocalStorageSecurity()
            };
            
            this.healthStatus.Security = {
                status: 'OK',
                message: 'فحص الأمان مكتمل',
                details: securityChecks
            };
            
        } catch (error) {
            this.healthStatus.Security = {
                status: 'WARNING',
                message: `تحذير في فحص الأمان: ${error.message}`
            };
            this.warnings.push(`تحذير أمني: ${error.message}`);
        }
    }

    /**
     * فحص حماية XSS
     */
    checkXSSProtection() {
        // فحص بسيط للحماية من XSS
        const testScript = '<script>alert("test")</script>';
        const div = document.createElement('div');
        div.innerHTML = testScript;
        return div.innerHTML !== testScript;
    }

    /**
     * فحص Content Security Policy
     */
    checkCSP() {
        const metaTags = document.querySelectorAll('meta[http-equiv="Content-Security-Policy"]');
        return metaTags.length > 0;
    }

    /**
     * فحص أمان localStorage
     */
    checkLocalStorageSecurity() {
        try {
            // فحص وجود بيانات حساسة في localStorage
            const sensitiveKeys = ['password', 'token', 'secret', 'key'];
            const localStorageKeys = Object.keys(localStorage);
            
            const hasSensitiveData = localStorageKeys.some(key => 
                sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))
            );
            
            return !hasSensitiveData;
        } catch (error) {
            return false;
        }
    }

    /**
     * إنشاء تقرير صحة النظام
     */
    generateHealthReport() {
        const totalChecks = Object.keys(this.healthStatus).length;
        const okChecks = Object.values(this.healthStatus).filter(status => status.status === 'OK').length;
        const warningChecks = Object.values(this.healthStatus).filter(status => status.status === 'WARNING').length;
        const errorChecks = Object.values(this.healthStatus).filter(status => status.status === 'ERROR').length;
        
        console.log(`📊 تقرير صحة النظام الشامل:`);
        console.log(`✅ فحوصات ناجحة: ${okChecks}/${totalChecks}`);
        console.log(`⚠️ تحذيرات: ${warningChecks}/${totalChecks}`);
        console.log(`❌ أخطاء: ${errorChecks}/${totalChecks}`);
        
        if (this.errors.length > 0) {
            console.log('🚨 الأخطاء المكتشفة:');
            this.errors.forEach(error => console.log(`  - ${error}`));
        }
        
        if (this.warnings.length > 0) {
            console.log('⚠️ التحذيرات:');
            this.warnings.forEach(warning => console.log(`  - ${warning}`));
        }
        
        if (this.performanceMetrics.totalCheckTime) {
            console.log(`⏱️ وقت الفحص الإجمالي: ${this.performanceMetrics.totalCheckTime.toFixed(2)}ms`);
        }
    }

    /**
     * الحصول على ملخص صحة النظام
     */
    getHealthSummary() {
        const totalChecks = Object.keys(this.healthStatus).length;
        const okChecks = Object.values(this.healthStatus).filter(status => status.status === 'OK').length;
        const warningChecks = Object.values(this.healthStatus).filter(status => status.status === 'WARNING').length;
        const errorChecks = Object.values(this.healthStatus).filter(status => status.status === 'ERROR').length;
        
        return {
            total: totalChecks,
            ok: okChecks,
            warnings: warningChecks,
            errors: errorChecks,
            healthPercentage: totalChecks > 0 ? Math.round((okChecks / totalChecks) * 100) : 0,
            status: this.healthStatus,
            errorList: this.errors,
            warningList: this.warnings,
            performanceMetrics: this.performanceMetrics,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * تصدير تقرير مفصل
     */
    exportDetailedReport() {
        const summary = this.getHealthSummary();
        
        const report = {
            title: 'Black Horse POS - تقرير صحة النظام الشامل',
            timestamp: summary.timestamp,
            summary: {
                totalChecks: summary.total,
                successfulChecks: summary.ok,
                warnings: summary.warnings,
                errors: summary.errors,
                healthPercentage: summary.healthPercentage
            },
            moduleStatus: summary.status,
            performanceMetrics: summary.performanceMetrics,
            issues: {
                errors: summary.errorList,
                warnings: summary.warningList
            },
            recommendations: this.generateRecommendations(summary)
        };
        
        return report;
    }

    /**
     * إنشاء توصيات بناءً على نتائج الفحص
     */
    generateRecommendations(summary) {
        const recommendations = [];
        
        if (summary.errors > 0) {
            recommendations.push('يوصى بحل الأخطاء المكتشفة قبل الاستخدام الإنتاجي');
        }
        
        if (summary.warnings > 0) {
            recommendations.push('راجع التحذيرات لتحسين أداء النظام');
        }
        
        if (summary.healthPercentage < 80) {
            recommendations.push('صحة النظام أقل من المستوى المطلوب، يوصى بمراجعة شاملة');
        }
        
        if (this.performanceMetrics.memory && this.performanceMetrics.memory.used > 150) {
            recommendations.push('استخدام الذاكرة مرتفع، يوصى بتحسين الأداء');
        }
        
        if (recommendations.length === 0) {
            recommendations.push('النظام يعمل بشكل ممتاز! 🎉');
        }
        
        return recommendations;
    }
}

// تصدير الكلاس للاستخدام العام
if (typeof window !== 'undefined') {
    window.SystemHealthChecker = SystemHealthChecker;
}

// تصدير للاستخدام في Node.js إذا لزم الأمر
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SystemHealthChecker;
}
