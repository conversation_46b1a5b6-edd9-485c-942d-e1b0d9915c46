<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الكاشير - Black Horse ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .module-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .cashier-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }
        .cashier-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
        }
        .btn-gradient:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            color: white;
        }
        .nav-tabs .nav-link {
            border: none;
            color: #667eea;
            font-weight: 600;
        }
        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0;
        }
        .shift-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-active { background: #d4edda; color: #155724; }
        .status-closed { background: #f8d7da; color: #721c24; }
        .status-pending { background: #fff3cd; color: #856404; }
        .cash-drawer {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        .transaction-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 3px solid #667eea;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="module-card p-4 mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-cash-register me-2"></i>إدارة الكاشير</h2>
                    <p class="text-muted mb-0">إدارة شاملة للكاشيرات والمناوبات والمعاملات</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-success" onclick="openShift()">
                        <i class="fas fa-play me-2"></i>بدء مناوبة
                    </button>
                    <button class="btn btn-gradient" onclick="showAddCashierModal()">
                        <i class="fas fa-user-plus me-2"></i>كاشير جديد
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h4 id="totalCashiers">0</h4>
                    <p>إجمالي الكاشيرات</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h4 id="activeShifts">0</h4>
                    <p>المناوبات النشطة</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                    <h4 id="todaySales">0</h4>
                    <p>مبيعات اليوم</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-receipt fa-2x mb-2"></i>
                    <h4 id="todayTransactions">0</h4>
                    <p>معاملات اليوم</p>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="module-card p-4">
            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs mb-4" id="cashierTabs">
                <li class="nav-item">
                    <a class="nav-link active" data-bs-toggle="tab" href="#cashiersTab">الكاشيرات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#shiftsTab">المناوبات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#transactionsTab">المعاملات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#drawerTab">الخزنة</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#reportsTab">التقارير</a>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Cashiers Tab -->
                <div class="tab-pane fade show active" id="cashiersTab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>قائمة الكاشيرات</h5>
                        <div class="d-flex gap-2">
                            <select class="form-select" id="statusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                                <option value="on-shift">في المناوبة</option>
                            </select>
                            <input type="text" class="form-control" placeholder="البحث..." id="cashierSearch">
                        </div>
                    </div>
                    <div class="row" id="cashiersGrid">
                        <!-- Cashiers will be loaded here -->
                    </div>
                </div>

                <!-- Shifts Tab -->
                <div class="tab-pane fade" id="shiftsTab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>إدارة المناوبات</h5>
                        <div class="d-flex gap-2">
                            <input type="date" class="form-control" id="shiftDate">
                            <select class="form-select" id="shiftStatusFilter">
                                <option value="">جميع المناوبات</option>
                                <option value="active">نشطة</option>
                                <option value="closed">مغلقة</option>
                                <option value="pending">معلقة</option>
                            </select>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الكاشير</th>
                                    <th>تاريخ البدء</th>
                                    <th>وقت البدء</th>
                                    <th>وقت الانتهاء</th>
                                    <th>المبلغ الافتتاحي</th>
                                    <th>المبلغ الختامي</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="shiftsTableBody">
                                <!-- Shifts data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Transactions Tab -->
                <div class="tab-pane fade" id="transactionsTab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>معاملات الكاشير</h5>
                        <div class="d-flex gap-2">
                            <input type="date" class="form-control" id="transactionDate">
                            <select class="form-select" id="transactionCashier">
                                <option value="">جميع الكاشيرات</option>
                            </select>
                            <select class="form-select" id="transactionType">
                                <option value="">جميع المعاملات</option>
                                <option value="sale">بيع</option>
                                <option value="return">مرتجع</option>
                                <option value="cash_in">إيداع نقدي</option>
                                <option value="cash_out">سحب نقدي</option>
                            </select>
                        </div>
                    </div>
                    <div id="transactionsList">
                        <!-- Transactions will be loaded here -->
                    </div>
                </div>

                <!-- Cash Drawer Tab -->
                <div class="tab-pane fade" id="drawerTab">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="cash-drawer">
                                <h4><i class="fas fa-cash-register fa-2x mb-3"></i></h4>
                                <h3 id="currentCashAmount">0.00 جنيه</h3>
                                <p>الرصيد الحالي في الخزنة</p>
                                <div class="d-flex gap-2 justify-content-center mt-3">
                                    <button class="btn btn-light" onclick="showCashInModal()">
                                        <i class="fas fa-plus me-2"></i>إيداع
                                    </button>
                                    <button class="btn btn-outline-light" onclick="showCashOutModal()">
                                        <i class="fas fa-minus me-2"></i>سحب
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>حركات الخزنة اليوم</h6>
                                </div>
                                <div class="card-body" id="drawerMovements">
                                    <!-- Cash drawer movements will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6>تفاصيل الفئات النقدية</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>الفئة</th>
                                                    <th>العدد</th>
                                                    <th>القيمة</th>
                                                </tr>
                                            </thead>
                                            <tbody id="denominationsTableBody">
                                                <!-- Denominations will be loaded here -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reports Tab -->
                <div class="tab-pane fade" id="reportsTab">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>أداء الكاشيرات</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="cashierPerformanceChart"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>المبيعات اليومية</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="dailySalesChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="text-primary" id="avgTransactionValue">0</h5>
                                    <p class="text-muted">متوسط قيمة المعاملة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="text-success" id="totalCommissions">0</h5>
                                    <p class="text-muted">إجمالي العمولات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="text-info" id="avgShiftDuration">0</h5>
                                    <p class="text-muted">متوسط مدة المناوبة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Cashier Modal -->
    <div class="modal fade" id="addCashierModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">كاشير جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addCashierForm">
                        <div class="mb-3">
                            <label class="form-label">اسم الكاشير</label>
                            <input type="text" class="form-control" id="cashierName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="cashierPhone" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="cashierEmail">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">معرف المستخدم</label>
                            <input type="text" class="form-control" id="cashierUsername" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="cashierPassword" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">نسبة العمولة (%)</label>
                            <input type="number" class="form-control" id="cashierCommission" step="0.01" min="0" max="100">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الحالة</label>
                            <select class="form-select" id="cashierStatus">
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-gradient" onclick="saveCashier()">حفظ الكاشير</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Open Shift Modal -->
    <div class="modal fade" id="openShiftModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">بدء مناوبة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="openShiftForm">
                        <div class="mb-3">
                            <label class="form-label">الكاشير</label>
                            <select class="form-select" id="shiftCashier" required>
                                <option value="">اختر الكاشير</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">المبلغ الافتتاحي</label>
                            <input type="number" class="form-control" id="openingAmount" step="0.01" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="shiftNotes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-gradient" onclick="saveShift()">بدء المناوبة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Cash In Modal -->
    <div class="modal fade" id="cashInModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إيداع نقدي</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="cashInForm">
                        <div class="mb-3">
                            <label class="form-label">المبلغ</label>
                            <input type="number" class="form-control" id="cashInAmount" step="0.01" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">السبب</label>
                            <select class="form-select" id="cashInReason">
                                <option value="opening_balance">رصيد افتتاحي</option>
                                <option value="bank_deposit">إيداع من البنك</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="cashInNotes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-gradient" onclick="processCashIn()">تأكيد الإيداع</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Cash Out Modal -->
    <div class="modal fade" id="cashOutModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">سحب نقدي</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="cashOutForm">
                        <div class="mb-3">
                            <label class="form-label">المبلغ</label>
                            <input type="number" class="form-control" id="cashOutAmount" step="0.01" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">السبب</label>
                            <select class="form-select" id="cashOutReason">
                                <option value="bank_deposit">إيداع في البنك</option>
                                <option value="expenses">مصروفات</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="cashOutNotes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-gradient" onclick="processCashOut()">تأكيد السحب</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="cashier.js"></script>
</body>
</html>
