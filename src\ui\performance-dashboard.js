/**
 * Black Horse POS - Performance Dashboard
 * لوحة مراقبة الأداء
 * Developer: Augment Agent
 */

class PerformanceDashboard {
    constructor() {
        this.isVisible = false;
        this.updateInterval = 2000; // تحديث كل ثانيتين
        this.updateTimer = null;
        this.metrics = {
            memory: { current: 0, peak: 0, history: [] },
            loadTime: { current: 0, average: 0, history: [] },
            cacheHits: { total: 0, ratio: 0 },
            dbOperations: { total: 0, avgTime: 0 },
            uiPerformance: { fps: 0, renderTime: 0 }
        };
        
        console.log('📊 Performance Dashboard initialized');
        this.init();
    }

    // تهيئة لوحة المراقبة
    init() {
        try {
            // إنشاء واجهة لوحة المراقبة
            this.createDashboardUI();
            
            // إعداد مراقبة الأداء
            this.setupPerformanceMonitoring();
            
            // إعداد اختصارات لوحة المفاتيح
            this.setupKeyboardShortcuts();
            
            console.log('✅ Performance Dashboard ready');
            
        } catch (error) {
            console.error('❌ Error initializing Performance Dashboard:', error);
        }
    }

    // إنشاء واجهة لوحة المراقبة
    createDashboardUI() {
        try {
            // إنشاء الحاوية الرئيسية
            const dashboard = document.createElement('div');
            dashboard.id = 'performance-dashboard';
            dashboard.className = 'performance-dashboard hidden';
            dashboard.innerHTML = `
                <div class="dashboard-header">
                    <h3>📊 لوحة مراقبة الأداء</h3>
                    <div class="dashboard-controls">
                        <button id="dashboard-toggle-auto" class="btn-small">تحديث تلقائي</button>
                        <button id="dashboard-export" class="btn-small">تصدير</button>
                        <button id="dashboard-close" class="btn-small">✕</button>
                    </div>
                </div>
                
                <div class="dashboard-content">
                    <!-- قسم الذاكرة -->
                    <div class="metric-section">
                        <h4>💾 استخدام الذاكرة</h4>
                        <div class="metric-row">
                            <span class="metric-label">الحالي:</span>
                            <span id="memory-current" class="metric-value">0 MB</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-label">الذروة:</span>
                            <span id="memory-peak" class="metric-value">0 MB</span>
                        </div>
                        <div class="metric-chart">
                            <canvas id="memory-chart" width="200" height="60"></canvas>
                        </div>
                    </div>
                    
                    <!-- قسم أوقات التحميل -->
                    <div class="metric-section">
                        <h4>⚡ أوقات التحميل</h4>
                        <div class="metric-row">
                            <span class="metric-label">الحالي:</span>
                            <span id="load-current" class="metric-value">0 ms</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-label">المتوسط:</span>
                            <span id="load-average" class="metric-value">0 ms</span>
                        </div>
                        <div class="metric-chart">
                            <canvas id="load-chart" width="200" height="60"></canvas>
                        </div>
                    </div>
                    
                    <!-- قسم التخزين المؤقت -->
                    <div class="metric-section">
                        <h4>🗄️ التخزين المؤقت</h4>
                        <div class="metric-row">
                            <span class="metric-label">النجاحات:</span>
                            <span id="cache-hits" class="metric-value">0</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-label">معدل النجاح:</span>
                            <span id="cache-ratio" class="metric-value">0%</span>
                        </div>
                        <div class="cache-status">
                            <div id="cache-bar" class="cache-bar">
                                <div id="cache-fill" class="cache-fill"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- قسم قاعدة البيانات -->
                    <div class="metric-section">
                        <h4>🗃️ قاعدة البيانات</h4>
                        <div class="metric-row">
                            <span class="metric-label">العمليات:</span>
                            <span id="db-operations" class="metric-value">0</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-label">متوسط الوقت:</span>
                            <span id="db-avg-time" class="metric-value">0 ms</span>
                        </div>
                    </div>
                    
                    <!-- قسم واجهة المستخدم -->
                    <div class="metric-section">
                        <h4>🎨 واجهة المستخدم</h4>
                        <div class="metric-row">
                            <span class="metric-label">FPS:</span>
                            <span id="ui-fps" class="metric-value">0</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-label">وقت الرسم:</span>
                            <span id="ui-render-time" class="metric-value">0 ms</span>
                        </div>
                    </div>
                    
                    <!-- قسم التحسينات -->
                    <div class="metric-section">
                        <h4>🚀 التحسينات</h4>
                        <div class="optimization-status">
                            <div class="status-item">
                                <span class="status-label">محسن الأداء:</span>
                                <span id="perf-optimizer-status" class="status-indicator">●</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">محسن قاعدة البيانات:</span>
                                <span id="db-optimizer-status" class="status-indicator">●</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">محسن واجهة المستخدم:</span>
                                <span id="ui-optimizer-status" class="status-indicator">●</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // إضافة الأنماط
            this.addDashboardStyles();
            
            // إضافة لوحة المراقبة للصفحة
            document.body.appendChild(dashboard);
            
            // ربط الأحداث
            this.bindDashboardEvents();
            
            console.log('🎨 Dashboard UI created');
            
        } catch (error) {
            console.error('❌ Error creating dashboard UI:', error);
        }
    }

    // إضافة أنماط لوحة المراقبة
    addDashboardStyles() {
        try {
            const style = document.createElement('style');
            style.textContent = `
                .performance-dashboard {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    width: 350px;
                    max-height: 80vh;
                    background: rgba(0, 0, 0, 0.9);
                    color: white;
                    border-radius: 8px;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                    z-index: 10000;
                    font-family: 'Cairo', Arial, sans-serif;
                    font-size: 12px;
                    overflow-y: auto;
                    backdrop-filter: blur(10px);
                }
                
                .performance-dashboard.hidden {
                    display: none;
                }
                
                .dashboard-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 10px 15px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 8px 8px 0 0;
                }
                
                .dashboard-header h3 {
                    margin: 0;
                    font-size: 14px;
                    font-weight: bold;
                }
                
                .dashboard-controls {
                    display: flex;
                    gap: 5px;
                }
                
                .btn-small {
                    padding: 4px 8px;
                    font-size: 10px;
                    background: rgba(255, 255, 255, 0.2);
                    color: white;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    transition: background 0.2s;
                }
                
                .btn-small:hover {
                    background: rgba(255, 255, 255, 0.3);
                }
                
                .dashboard-content {
                    padding: 15px;
                }
                
                .metric-section {
                    margin-bottom: 20px;
                    padding: 10px;
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
                
                .metric-section h4 {
                    margin: 0 0 10px 0;
                    font-size: 12px;
                    color: #4CAF50;
                }
                
                .metric-row {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 5px;
                }
                
                .metric-label {
                    color: #ccc;
                }
                
                .metric-value {
                    color: #fff;
                    font-weight: bold;
                }
                
                .metric-chart {
                    margin-top: 10px;
                    text-align: center;
                }
                
                .cache-status {
                    margin-top: 10px;
                }
                
                .cache-bar {
                    width: 100%;
                    height: 8px;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 4px;
                    overflow: hidden;
                }
                
                .cache-fill {
                    height: 100%;
                    background: linear-gradient(90deg, #4CAF50, #8BC34A);
                    width: 0%;
                    transition: width 0.3s ease;
                }
                
                .optimization-status {
                    display: flex;
                    flex-direction: column;
                    gap: 5px;
                }
                
                .status-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                .status-label {
                    color: #ccc;
                    font-size: 11px;
                }
                
                .status-indicator {
                    font-size: 16px;
                }
                
                .status-indicator.active {
                    color: #4CAF50;
                }
                
                .status-indicator.inactive {
                    color: #f44336;
                }
            `;
            
            document.head.appendChild(style);
            
        } catch (error) {
            console.error('❌ Error adding dashboard styles:', error);
        }
    }

    // ربط أحداث لوحة المراقبة
    bindDashboardEvents() {
        try {
            // زر الإغلاق
            document.getElementById('dashboard-close').addEventListener('click', () => {
                this.hide();
            });
            
            // زر التحديث التلقائي
            document.getElementById('dashboard-toggle-auto').addEventListener('click', () => {
                this.toggleAutoUpdate();
            });
            
            // زر التصدير
            document.getElementById('dashboard-export').addEventListener('click', () => {
                this.exportMetrics();
            });
            
        } catch (error) {
            console.error('❌ Error binding dashboard events:', error);
        }
    }

    // إعداد مراقبة الأداء
    setupPerformanceMonitoring() {
        try {
            // مراقبة الذاكرة
            this.setupMemoryMonitoring();
            
            // مراقبة أوقات التحميل
            this.setupLoadTimeMonitoring();
            
            // مراقبة FPS
            this.setupFPSMonitoring();
            
            console.log('📈 Performance monitoring setup complete');
            
        } catch (error) {
            console.error('❌ Error setting up performance monitoring:', error);
        }
    }

    // مراقبة الذاكرة
    setupMemoryMonitoring() {
        try {
            if (performance.memory) {
                setInterval(() => {
                    const memory = performance.memory;
                    const currentMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
                    
                    this.metrics.memory.current = currentMB;
                    this.metrics.memory.peak = Math.max(this.metrics.memory.peak, currentMB);
                    this.metrics.memory.history.push(currentMB);
                    
                    // الاحتفاظ بآخر 50 قراءة فقط
                    if (this.metrics.memory.history.length > 50) {
                        this.metrics.memory.history.shift();
                    }
                }, 1000);
            }
            
        } catch (error) {
            console.error('❌ Error setting up memory monitoring:', error);
        }
    }

    // مراقبة أوقات التحميل
    setupLoadTimeMonitoring() {
        try {
            // مراقبة أوقات تحميل الوحدات
            const originalLoad = window.PerformanceOptimizer?.loadModule;
            if (originalLoad) {
                window.PerformanceOptimizer.loadModule = (...args) => {
                    const startTime = performance.now();
                    const result = originalLoad.apply(window.PerformanceOptimizer, args);
                    const loadTime = performance.now() - startTime;
                    
                    this.metrics.loadTime.current = Math.round(loadTime);
                    this.metrics.loadTime.history.push(loadTime);
                    
                    if (this.metrics.loadTime.history.length > 20) {
                        this.metrics.loadTime.history.shift();
                    }
                    
                    this.metrics.loadTime.average = Math.round(
                        this.metrics.loadTime.history.reduce((a, b) => a + b, 0) / 
                        this.metrics.loadTime.history.length
                    );
                    
                    return result;
                };
            }
            
        } catch (error) {
            console.error('❌ Error setting up load time monitoring:', error);
        }
    }

    // مراقبة FPS
    setupFPSMonitoring() {
        try {
            let lastTime = performance.now();
            let frameCount = 0;
            
            const measureFPS = () => {
                frameCount++;
                const currentTime = performance.now();
                
                if (currentTime - lastTime >= 1000) {
                    this.metrics.uiPerformance.fps = frameCount;
                    frameCount = 0;
                    lastTime = currentTime;
                }
                
                requestAnimationFrame(measureFPS);
            };
            
            requestAnimationFrame(measureFPS);
            
        } catch (error) {
            console.error('❌ Error setting up FPS monitoring:', error);
        }
    }

    // إعداد اختصارات لوحة المفاتيح
    setupKeyboardShortcuts() {
        try {
            document.addEventListener('keydown', (event) => {
                // Ctrl + Shift + P لإظهار/إخفاء لوحة المراقبة
                if (event.ctrlKey && event.shiftKey && event.key === 'P') {
                    event.preventDefault();
                    this.toggle();
                }
            });
            
        } catch (error) {
            console.error('❌ Error setting up keyboard shortcuts:', error);
        }
    }

    // إظهار لوحة المراقبة
    show() {
        try {
            const dashboard = document.getElementById('performance-dashboard');
            if (dashboard) {
                dashboard.classList.remove('hidden');
                this.isVisible = true;
                this.startAutoUpdate();
                console.log('👁️ Performance dashboard shown');
            }
            
        } catch (error) {
            console.error('❌ Error showing dashboard:', error);
        }
    }

    // إخفاء لوحة المراقبة
    hide() {
        try {
            const dashboard = document.getElementById('performance-dashboard');
            if (dashboard) {
                dashboard.classList.add('hidden');
                this.isVisible = false;
                this.stopAutoUpdate();
                console.log('🙈 Performance dashboard hidden');
            }
            
        } catch (error) {
            console.error('❌ Error hiding dashboard:', error);
        }
    }

    // تبديل إظهار/إخفاء لوحة المراقبة
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    // بدء التحديث التلقائي
    startAutoUpdate() {
        try {
            if (this.updateTimer) return;
            
            this.updateTimer = setInterval(() => {
                this.updateDashboard();
            }, this.updateInterval);
            
            // تحديث فوري
            this.updateDashboard();
            
        } catch (error) {
            console.error('❌ Error starting auto update:', error);
        }
    }

    // إيقاف التحديث التلقائي
    stopAutoUpdate() {
        try {
            if (this.updateTimer) {
                clearInterval(this.updateTimer);
                this.updateTimer = null;
            }
            
        } catch (error) {
            console.error('❌ Error stopping auto update:', error);
        }
    }

    // تبديل التحديث التلقائي
    toggleAutoUpdate() {
        try {
            if (this.updateTimer) {
                this.stopAutoUpdate();
                document.getElementById('dashboard-toggle-auto').textContent = 'تحديث تلقائي';
            } else {
                this.startAutoUpdate();
                document.getElementById('dashboard-toggle-auto').textContent = 'إيقاف التحديث';
            }
            
        } catch (error) {
            console.error('❌ Error toggling auto update:', error);
        }
    }

    // تحديث لوحة المراقبة
    updateDashboard() {
        try {
            // تحديث معلومات الذاكرة
            this.updateMemoryMetrics();
            
            // تحديث معلومات التحميل
            this.updateLoadTimeMetrics();
            
            // تحديث معلومات التخزين المؤقت
            this.updateCacheMetrics();
            
            // تحديث معلومات قاعدة البيانات
            this.updateDatabaseMetrics();
            
            // تحديث معلومات واجهة المستخدم
            this.updateUIMetrics();
            
            // تحديث حالة التحسينات
            this.updateOptimizationStatus();
            
        } catch (error) {
            console.error('❌ Error updating dashboard:', error);
        }
    }

    // تحديث معلومات الذاكرة
    updateMemoryMetrics() {
        try {
            document.getElementById('memory-current').textContent = `${this.metrics.memory.current} MB`;
            document.getElementById('memory-peak').textContent = `${this.metrics.memory.peak} MB`;
            
            // رسم مخطط الذاكرة
            this.drawMemoryChart();
            
        } catch (error) {
            console.error('❌ Error updating memory metrics:', error);
        }
    }

    // رسم مخطط الذاكرة
    drawMemoryChart() {
        try {
            const canvas = document.getElementById('memory-chart');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;
            
            // مسح الرسم السابق
            ctx.clearRect(0, 0, width, height);
            
            const history = this.metrics.memory.history;
            if (history.length < 2) return;
            
            // حساب القيم
            const max = Math.max(...history);
            const min = Math.min(...history);
            const range = max - min || 1;
            
            // رسم الخط
            ctx.strokeStyle = '#4CAF50';
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            history.forEach((value, index) => {
                const x = (index / (history.length - 1)) * width;
                const y = height - ((value - min) / range) * height;
                
                if (index === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            });
            
            ctx.stroke();
            
        } catch (error) {
            console.error('❌ Error drawing memory chart:', error);
        }
    }

    // تحديث معلومات التحميل
    updateLoadTimeMetrics() {
        try {
            document.getElementById('load-current').textContent = `${this.metrics.loadTime.current} ms`;
            document.getElementById('load-average').textContent = `${this.metrics.loadTime.average} ms`;
            
        } catch (error) {
            console.error('❌ Error updating load time metrics:', error);
        }
    }

    // تحديث معلومات التخزين المؤقت
    updateCacheMetrics() {
        try {
            if (window.PerformanceOptimizer) {
                const metrics = window.PerformanceOptimizer.performanceMetrics;
                const hits = metrics.cacheHits || 0;
                const misses = metrics.cacheMisses || 0;
                const total = hits + misses;
                const ratio = total > 0 ? Math.round((hits / total) * 100) : 0;
                
                document.getElementById('cache-hits').textContent = hits.toString();
                document.getElementById('cache-ratio').textContent = `${ratio}%`;
                
                // تحديث شريط التقدم
                const fill = document.getElementById('cache-fill');
                if (fill) {
                    fill.style.width = `${ratio}%`;
                }
            }
            
        } catch (error) {
            console.error('❌ Error updating cache metrics:', error);
        }
    }

    // تحديث معلومات قاعدة البيانات
    updateDatabaseMetrics() {
        try {
            if (window.DatabaseOptimizer) {
                const stats = window.DatabaseOptimizer.getDatabaseStats();
                if (stats) {
                    document.getElementById('db-operations').textContent = stats.queueSize?.toString() || '0';
                    document.getElementById('db-avg-time').textContent = '< 1 ms'; // مبسط
                }
            }
            
        } catch (error) {
            console.error('❌ Error updating database metrics:', error);
        }
    }

    // تحديث معلومات واجهة المستخدم
    updateUIMetrics() {
        try {
            document.getElementById('ui-fps').textContent = this.metrics.uiPerformance.fps.toString();
            document.getElementById('ui-render-time').textContent = `${this.metrics.uiPerformance.renderTime} ms`;
            
        } catch (error) {
            console.error('❌ Error updating UI metrics:', error);
        }
    }

    // تحديث حالة التحسينات
    updateOptimizationStatus() {
        try {
            // حالة محسن الأداء
            const perfStatus = document.getElementById('perf-optimizer-status');
            if (perfStatus) {
                perfStatus.className = window.PerformanceOptimizer ? 'status-indicator active' : 'status-indicator inactive';
            }
            
            // حالة محسن قاعدة البيانات
            const dbStatus = document.getElementById('db-optimizer-status');
            if (dbStatus) {
                dbStatus.className = window.DatabaseOptimizer ? 'status-indicator active' : 'status-indicator inactive';
            }
            
            // حالة محسن واجهة المستخدم
            const uiStatus = document.getElementById('ui-optimizer-status');
            if (uiStatus) {
                uiStatus.className = window.UIOptimizer ? 'status-indicator active' : 'status-indicator inactive';
            }
            
        } catch (error) {
            console.error('❌ Error updating optimization status:', error);
        }
    }

    // تصدير المقاييس
    exportMetrics() {
        try {
            const exportData = {
                timestamp: new Date().toISOString(),
                metrics: this.metrics,
                optimizers: {
                    performance: !!window.PerformanceOptimizer,
                    database: !!window.DatabaseOptimizer,
                    ui: !!window.UIOptimizer
                }
            };
            
            const jsonData = JSON.stringify(exportData, null, 2);
            const blob = new Blob([jsonData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `performance_metrics_${Date.now()}.json`;
            a.click();
            
            URL.revokeObjectURL(url);
            console.log('📤 Performance metrics exported');
            
        } catch (error) {
            console.error('❌ Error exporting metrics:', error);
        }
    }

    // تنظيف الموارد
    cleanup() {
        try {
            this.stopAutoUpdate();
            
            const dashboard = document.getElementById('performance-dashboard');
            if (dashboard) {
                dashboard.remove();
            }
            
            console.log('✅ Performance Dashboard cleaned up');
            
        } catch (error) {
            console.error('❌ Error cleaning up Performance Dashboard:', error);
        }
    }
}

// إنشاء instance عام
window.PerformanceDashboard = new PerformanceDashboard();

// تصدير للاستخدام في الوحدات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceDashboard;
}

console.log('✅ Performance Dashboard loaded successfully');
