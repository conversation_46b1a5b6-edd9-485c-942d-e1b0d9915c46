<!-- وحدة التكاملات والدفع الإلكتروني -->
<div class="container-fluid" dir="rtl">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">🔗 التكاملات والدفع الإلكتروني</h2>
                    <p class="text-muted">إدارة طرق الدفع والتكاملات الخارجية</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" onclick="performBackup()">
                        <i class="fas fa-download"></i> نسخ احتياطي
                    </button>
                    <button class="btn btn-success" onclick="testConnections()">
                        <i class="fas fa-plug"></i> اختبار الاتصالات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">طرق الدفع النشطة</h6>
                            <h3 id="activePaymentMethods">0</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-credit-card fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">التكاملات المتصلة</h6>
                            <h3 id="connectedIntegrations">0</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-link fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">المعاملات اليوم</h6>
                            <h3 id="todayTransactions">0</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exchange-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">آخر نسخة احتياطية</h6>
                            <h6 id="lastBackup">لا توجد</h6>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-save fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تبويبات التكاملات -->
    <div class="row">
        <div class="col-12">
            <ul class="nav nav-tabs" id="integrationsTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="payment-tab" data-bs-toggle="tab" data-bs-target="#payment" type="button" role="tab">
                        <i class="fas fa-credit-card"></i> طرق الدفع
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="shipping-tab" data-bs-toggle="tab" data-bs-target="#shipping" type="button" role="tab">
                        <i class="fas fa-shipping-fast"></i> الشحن
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="accounting-tab" data-bs-toggle="tab" data-bs-target="#accounting" type="button" role="tab">
                        <i class="fas fa-calculator"></i> المحاسبة
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="marketing-tab" data-bs-toggle="tab" data-bs-target="#marketing" type="button" role="tab">
                        <i class="fas fa-bullhorn"></i> التسويق
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="backup-tab" data-bs-toggle="tab" data-bs-target="#backup" type="button" role="tab">
                        <i class="fas fa-save"></i> النسخ الاحتياطي
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="integrationsTabContent">
                <!-- تبويب طرق الدفع -->
                <div class="tab-pane fade show active" id="payment" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">إعدادات طرق الدفع الإلكتروني</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- فودافون كاش -->
                                <div class="col-md-6 mb-4">
                                    <div class="card border">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-0">فودافون كاش</h6>
                                                <small class="text-muted">محفظة إلكترونية</small>
                                            </div>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="vodafone_cash_enabled" onchange="togglePaymentMethod('vodafone_cash', this.checked)">
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label class="form-label">معرف التاجر</label>
                                                <input type="text" class="form-control" id="vodafone_merchant_id" placeholder="أدخل معرف التاجر">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">مفتاح API</label>
                                                <input type="password" class="form-control" id="vodafone_api_key" placeholder="أدخل مفتاح API">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">البيئة</label>
                                                <select class="form-select" id="vodafone_environment">
                                                    <option value="sandbox">تجريبي</option>
                                                    <option value="production">إنتاج</option>
                                                </select>
                                            </div>
                                            <div class="row">
                                                <div class="col-6">
                                                    <small class="text-muted">رسوم: 2.5%</small>
                                                </div>
                                                <div class="col-6">
                                                    <small class="text-muted">الحد الأقصى: 50,000 ج.م</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- انستاباي -->
                                <div class="col-md-6 mb-4">
                                    <div class="card border">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-0">انستاباي</h6>
                                                <small class="text-muted">تحويل بنكي فوري</small>
                                            </div>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="instapay_enabled" onchange="togglePaymentMethod('instapay', this.checked)">
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label class="form-label">كود البنك</label>
                                                <input type="text" class="form-control" id="instapay_bank_code" placeholder="أدخل كود البنك">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">رقم الحساب</label>
                                                <input type="text" class="form-control" id="instapay_account" placeholder="أدخل رقم الحساب">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">نقطة النهاية API</label>
                                                <input type="url" class="form-control" id="instapay_endpoint" placeholder="https://api.instapay.com">
                                            </div>
                                            <div class="row">
                                                <div class="col-6">
                                                    <small class="text-muted">رسوم: 5 ج.م ثابت</small>
                                                </div>
                                                <div class="col-6">
                                                    <small class="text-muted">الحد الأقصى: 100,000 ج.م</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- بطاقة ائتمان -->
                                <div class="col-md-6 mb-4">
                                    <div class="card border">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-0">بطاقة ائتمان</h6>
                                                <small class="text-muted">فيزا/ماستركارد</small>
                                            </div>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="credit_card_enabled" onchange="togglePaymentMethod('credit_card', this.checked)">
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label class="form-label">بوابة الدفع</label>
                                                <select class="form-select" id="credit_card_gateway">
                                                    <option value="paymob">PayMob</option>
                                                    <option value="fawry">فوري</option>
                                                    <option value="accept">Accept</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">مفتاح API</label>
                                                <input type="password" class="form-control" id="credit_card_api_key" placeholder="أدخل مفتاح API">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">معرف التكامل</label>
                                                <input type="text" class="form-control" id="credit_card_integration_id" placeholder="أدخل معرف التكامل">
                                            </div>
                                            <div class="row">
                                                <div class="col-6">
                                                    <small class="text-muted">رسوم: 3.5%</small>
                                                </div>
                                                <div class="col-6">
                                                    <small class="text-muted">الحد الأقصى: 200,000 ج.م</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- فوري -->
                                <div class="col-md-6 mb-4">
                                    <div class="card border">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-0">فوري</h6>
                                                <small class="text-muted">خدمة دفع متعددة</small>
                                            </div>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="fawry_enabled" onchange="togglePaymentMethod('fawry', this.checked)">
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label class="form-label">كود التاجر</label>
                                                <input type="text" class="form-control" id="fawry_merchant_code" placeholder="أدخل كود التاجر">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">مفتاح الأمان</label>
                                                <input type="password" class="form-control" id="fawry_security_key" placeholder="أدخل مفتاح الأمان">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">البيئة</label>
                                                <select class="form-select" id="fawry_environment">
                                                    <option value="sandbox">تجريبي</option>
                                                    <option value="production">إنتاج</option>
                                                </select>
                                            </div>
                                            <div class="row">
                                                <div class="col-6">
                                                    <small class="text-muted">رسوم: 2% + 2 ج.م</small>
                                                </div>
                                                <div class="col-6">
                                                    <small class="text-muted">الحد الأقصى: 75,000 ج.م</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center mt-4">
                                <button class="btn btn-primary" onclick="savePaymentSettings()">
                                    <i class="fas fa-save"></i> حفظ إعدادات الدفع
                                </button>
                                <button class="btn btn-secondary ms-2" onclick="testPaymentMethods()">
                                    <i class="fas fa-test-tube"></i> اختبار طرق الدفع
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تبويب الشحن -->
                <div class="tab-pane fade" id="shipping" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">تكاملات شركات الشحن</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- أرامكس -->
                                <div class="col-md-6 mb-4">
                                    <div class="card border">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-0">أرامكس</h6>
                                                <small class="text-muted">شحن دولي ومحلي</small>
                                            </div>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="aramex_enabled">
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label class="form-label">اسم المستخدم</label>
                                                <input type="text" class="form-control" id="aramex_username">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">كلمة المرور</label>
                                                <input type="password" class="form-control" id="aramex_password">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">رقم الحساب</label>
                                                <input type="text" class="form-control" id="aramex_account">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- DHL -->
                                <div class="col-md-6 mb-4">
                                    <div class="card border">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-0">DHL</h6>
                                                <small class="text-muted">شحن سريع دولي</small>
                                            </div>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="dhl_enabled">
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label class="form-label">مفتاح API</label>
                                                <input type="password" class="form-control" id="dhl_api_key">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">رقم الحساب</label>
                                                <input type="text" class="form-control" id="dhl_account">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تبويب المحاسبة -->
                <div class="tab-pane fade" id="accounting" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">تكاملات أنظمة المحاسبة</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- QuickBooks -->
                                <div class="col-md-6 mb-4">
                                    <div class="card border">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-0">QuickBooks</h6>
                                                <small class="text-muted">نظام محاسبة شامل</small>
                                            </div>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="quickbooks_enabled">
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label class="form-label">معرف العميل</label>
                                                <input type="text" class="form-control" id="quickbooks_client_id">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">سر العميل</label>
                                                <input type="password" class="form-control" id="quickbooks_client_secret">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- نظام ERP مخصص -->
                                <div class="col-md-6 mb-4">
                                    <div class="card border">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-0">نظام ERP مخصص</h6>
                                                <small class="text-muted">تكامل مع نظام خارجي</small>
                                            </div>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="custom_erp_enabled">
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label class="form-label">نقطة النهاية API</label>
                                                <input type="url" class="form-control" id="custom_erp_endpoint">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">مفتاح API</label>
                                                <input type="password" class="form-control" id="custom_erp_api_key">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تبويب التسويق -->
                <div class="tab-pane fade" id="marketing" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">تكاملات التسويق والتواصل</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- واتساب بيزنس -->
                                <div class="col-md-6 mb-4">
                                    <div class="card border">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-0">واتساب بيزنس</h6>
                                                <small class="text-muted">رسائل تلقائية</small>
                                            </div>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="whatsapp_enabled">
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label class="form-label">معرف رقم الهاتف</label>
                                                <input type="text" class="form-control" id="whatsapp_phone_id">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">رمز الوصول</label>
                                                <input type="password" class="form-control" id="whatsapp_access_token">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- بوابة SMS -->
                                <div class="col-md-6 mb-4">
                                    <div class="card border">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-0">رسائل SMS</h6>
                                                <small class="text-muted">إشعارات نصية</small>
                                            </div>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="sms_enabled">
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label class="form-label">مقدم الخدمة</label>
                                                <select class="form-select" id="sms_provider">
                                                    <option value="twilio">Twilio</option>
                                                    <option value="nexmo">Nexmo</option>
                                                    <option value="local">محلي</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">مفتاح API</label>
                                                <input type="password" class="form-control" id="sms_api_key">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تبويب النسخ الاحتياطي -->
                <div class="tab-pane fade" id="backup" role="tabpanel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">إعدادات النسخ الاحتياطي والاسترداد</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- إعدادات النسخ الاحتياطي -->
                                <div class="col-md-6">
                                    <h6>إعدادات النسخ الاحتياطي التلقائي</h6>
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="backup_enabled" checked>
                                            <label class="form-check-label" for="backup_enabled">
                                                تفعيل النسخ الاحتياطي التلقائي
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">تكرار النسخ</label>
                                        <select class="form-select" id="backup_frequency">
                                            <option value="daily">يومي</option>
                                            <option value="weekly">أسبوعي</option>
                                            <option value="monthly">شهري</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">وقت النسخ</label>
                                        <input type="time" class="form-control" id="backup_time" value="02:00">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">مدة الاحتفاظ (أيام)</label>
                                        <input type="number" class="form-control" id="backup_retention" value="30" min="1" max="365">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">موقع النسخ</label>
                                        <select class="form-select" id="backup_location">
                                            <option value="local">محلي</option>
                                            <option value="cloud">سحابي</option>
                                            <option value="both">كلاهما</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="backup_encryption" checked>
                                            <label class="form-check-label" for="backup_encryption">
                                                تشفير النسخ الاحتياطية
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="backup_compression" checked>
                                            <label class="form-check-label" for="backup_compression">
                                                ضغط النسخ الاحتياطية
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- إدارة النسخ الاحتياطية -->
                                <div class="col-md-6">
                                    <h6>إدارة النسخ الاحتياطية</h6>
                                    <div class="mb-3">
                                        <button class="btn btn-primary w-100 mb-2" onclick="performBackup()">
                                            <i class="fas fa-download"></i> إنشاء نسخة احتياطية الآن
                                        </button>
                                        <button class="btn btn-success w-100 mb-2" onclick="showRestoreModal()">
                                            <i class="fas fa-upload"></i> استرداد نسخة احتياطية
                                        </button>
                                        <button class="btn btn-info w-100 mb-2" onclick="listBackups()">
                                            <i class="fas fa-list"></i> عرض النسخ المتاحة
                                        </button>
                                        <button class="btn btn-warning w-100" onclick="cleanOldBackups()">
                                            <i class="fas fa-trash"></i> تنظيف النسخ القديمة
                                        </button>
                                    </div>

                                    <!-- قائمة النسخ الاحتياطية -->
                                    <div class="mt-4">
                                        <h6>النسخ الاحتياطية المتاحة</h6>
                                        <div id="backupsList" class="list-group">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center mt-4">
                                <button class="btn btn-primary" onclick="saveBackupSettings()">
                                    <i class="fas fa-save"></i> حفظ إعدادات النسخ الاحتياطي
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة استرداد النسخة الاحتياطية -->
<div class="modal fade" id="restoreModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">استرداد نسخة احتياطية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    تحذير: سيتم استبدال جميع البيانات الحالية بالنسخة الاحتياطية المحددة.
                </div>
                <div class="mb-3">
                    <label class="form-label">اختر النسخة الاحتياطية</label>
                    <select class="form-select" id="restoreBackupSelect">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </select>
                </div>
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="confirmRestore">
                        <label class="form-check-label" for="confirmRestore">
                            أؤكد أنني أريد استرداد هذه النسخة الاحتياطية
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" onclick="restoreBackup()" id="restoreBtn" disabled>
                    <i class="fas fa-upload"></i> استرداد
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// متغيرات عامة
let integrationsManager;

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeIntegrations();
    loadIntegrationsData();
    updateStatistics();
});

// تهيئة مدير التكاملات
function initializeIntegrations() {
    integrationsManager = new IntegrationsManager();
    console.log('✅ تم تهيئة مدير التكاملات');
}

// تحميل بيانات التكاملات
function loadIntegrationsData() {
    // تحميل إعدادات طرق الدفع
    loadPaymentMethodsSettings();

    // تحميل قائمة النسخ الاحتياطية
    listBackups();

    // تحميل إعدادات النسخ الاحتياطي
    loadBackupSettings();
}

// تحميل إعدادات طرق الدفع
function loadPaymentMethodsSettings() {
    const paymentMethods = integrationsManager.paymentMethods;

    // فودافون كاش
    const vodafone = paymentMethods.get('vodafone_cash');
    if (vodafone) {
        document.getElementById('vodafone_cash_enabled').checked = vodafone.enabled;
        document.getElementById('vodafone_merchant_id').value = vodafone.config.merchant_id || '';
        document.getElementById('vodafone_api_key').value = vodafone.config.api_key || '';
        document.getElementById('vodafone_environment').value = vodafone.config.environment || 'sandbox';
    }

    // انستاباي
    const instapay = paymentMethods.get('instapay');
    if (instapay) {
        document.getElementById('instapay_enabled').checked = instapay.enabled;
        document.getElementById('instapay_bank_code').value = instapay.config.bank_code || '';
        document.getElementById('instapay_account').value = instapay.config.account_number || '';
        document.getElementById('instapay_endpoint').value = instapay.config.api_endpoint || '';
    }

    // بطاقة ائتمان
    const creditCard = paymentMethods.get('credit_card');
    if (creditCard) {
        document.getElementById('credit_card_enabled').checked = creditCard.enabled;
        document.getElementById('credit_card_gateway').value = creditCard.config.gateway || 'paymob';
        document.getElementById('credit_card_api_key').value = creditCard.config.api_key || '';
        document.getElementById('credit_card_integration_id').value = creditCard.config.integration_id || '';
    }

    // فوري
    const fawry = paymentMethods.get('fawry');
    if (fawry) {
        document.getElementById('fawry_enabled').checked = fawry.enabled;
        document.getElementById('fawry_merchant_code').value = fawry.config.merchant_code || '';
        document.getElementById('fawry_security_key').value = fawry.config.security_key || '';
        document.getElementById('fawry_environment').value = fawry.config.environment || 'sandbox';
    }
}

// تحديث الإحصائيات
function updateStatistics() {
    // عدد طرق الدفع النشطة
    const activePaymentMethods = integrationsManager.getAvailablePaymentMethods().length;
    document.getElementById('activePaymentMethods').textContent = activePaymentMethods;

    // عدد التكاملات المتصلة
    let connectedIntegrations = 0;
    integrationsManager.apiConnections.forEach(category => {
        Object.values(category).forEach(integration => {
            if (integration.enabled) connectedIntegrations++;
        });
    });
    document.getElementById('connectedIntegrations').textContent = connectedIntegrations;

    // المعاملات اليوم (محاكاة)
    document.getElementById('todayTransactions').textContent = Math.floor(Math.random() * 50);

    // آخر نسخة احتياطية
    const lastBackup = localStorage.getItem('last_backup_date');
    document.getElementById('lastBackup').textContent = lastBackup ?
        new Date(lastBackup).toLocaleDateString('ar-EG') : 'لا توجد';
}

// تفعيل/إلغاء تفعيل طريقة دفع
function togglePaymentMethod(method, enabled) {
    integrationsManager.togglePaymentMethod(method, enabled);
    updateStatistics();

    showNotification(
        enabled ? 'تم تفعيل طريقة الدفع' : 'تم إلغاء تفعيل طريقة الدفع',
        enabled ? 'success' : 'warning'
    );
}

// حفظ إعدادات الدفع
function savePaymentSettings() {
    try {
        // فودافون كاش
        integrationsManager.updatePaymentMethodConfig('vodafone_cash', {
            merchant_id: document.getElementById('vodafone_merchant_id').value,
            api_key: document.getElementById('vodafone_api_key').value,
            environment: document.getElementById('vodafone_environment').value
        });

        // انستاباي
        integrationsManager.updatePaymentMethodConfig('instapay', {
            bank_code: document.getElementById('instapay_bank_code').value,
            account_number: document.getElementById('instapay_account').value,
            api_endpoint: document.getElementById('instapay_endpoint').value
        });

        // بطاقة ائتمان
        integrationsManager.updatePaymentMethodConfig('credit_card', {
            gateway: document.getElementById('credit_card_gateway').value,
            api_key: document.getElementById('credit_card_api_key').value,
            integration_id: document.getElementById('credit_card_integration_id').value
        });

        // فوري
        integrationsManager.updatePaymentMethodConfig('fawry', {
            merchant_code: document.getElementById('fawry_merchant_code').value,
            security_key: document.getElementById('fawry_security_key').value,
            environment: document.getElementById('fawry_environment').value
        });

        showNotification('تم حفظ إعدادات الدفع بنجاح', 'success');

    } catch (error) {
        console.error('خطأ في حفظ إعدادات الدفع:', error);
        showNotification('خطأ في حفظ الإعدادات', 'error');
    }
}

// اختبار طرق الدفع
async function testPaymentMethods() {
    const availableMethods = integrationsManager.getAvailablePaymentMethods();

    if (availableMethods.length === 0) {
        showNotification('لا توجد طرق دفع مفعلة للاختبار', 'warning');
        return;
    }

    showNotification('جاري اختبار طرق الدفع...', 'info');

    for (const method of availableMethods) {
        try {
            // اختبار بمبلغ صغير
            const result = await integrationsManager.processPayment(method.key, 1, {
                test: true,
                order_id: 'TEST_' + Date.now()
            });

            console.log(`✅ اختبار ${method.name} نجح:`, result);
        } catch (error) {
            console.error(`❌ اختبار ${method.name} فشل:`, error);
        }
    }

    showNotification('تم الانتهاء من اختبار طرق الدفع', 'success');
}

// تنفيذ نسخة احتياطية
async function performBackup() {
    try {
        showNotification('جاري إنشاء النسخة الاحتياطية...', 'info');

        const backupId = await integrationsManager.performBackup();
        localStorage.setItem('last_backup_date', new Date().toISOString());

        updateStatistics();
        listBackups();

        showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');

    } catch (error) {
        console.error('خطأ في النسخ الاحتياطي:', error);
        showNotification('خطأ في إنشاء النسخة الاحتياطية', 'error');
    }
}

// عرض قائمة النسخ الاحتياطية
function listBackups() {
    const backupsList = document.getElementById('backupsList');
    const restoreSelect = document.getElementById('restoreBackupSelect');

    backupsList.innerHTML = '';
    restoreSelect.innerHTML = '<option value="">اختر نسخة احتياطية...</option>';

    // البحث عن النسخ الاحتياطية في localStorage
    const backups = [];
    for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('backup_')) {
            try {
                const backup = JSON.parse(localStorage.getItem(key));
                backups.push({
                    id: key,
                    timestamp: backup.timestamp,
                    version: backup.version
                });
            } catch (error) {
                console.warn('نسخة احتياطية تالفة:', key);
            }
        }
    }

    // ترتيب النسخ حسب التاريخ
    backups.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    if (backups.length === 0) {
        backupsList.innerHTML = '<div class="text-muted text-center p-3">لا توجد نسخ احتياطية</div>';
        return;
    }

    backups.forEach(backup => {
        const date = new Date(backup.timestamp);
        const dateStr = date.toLocaleDateString('ar-EG') + ' ' + date.toLocaleTimeString('ar-EG');

        // إضافة للقائمة
        const listItem = document.createElement('div');
        listItem.className = 'list-group-item d-flex justify-content-between align-items-center';
        listItem.innerHTML = `
            <div>
                <strong>نسخة ${dateStr}</strong><br>
                <small class="text-muted">الإصدار: ${backup.version}</small>
            </div>
            <div>
                <button class="btn btn-sm btn-success me-1" onclick="restoreSpecificBackup('${backup.id}')">
                    <i class="fas fa-upload"></i>
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteBackup('${backup.id}')">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        backupsList.appendChild(listItem);

        // إضافة لقائمة الاستрداد
        const option = document.createElement('option');
        option.value = backup.id;
        option.textContent = `نسخة ${dateStr}`;
        restoreSelect.appendChild(option);
    });
}

// عرض نافذة الاسترداد
function showRestoreModal() {
    const modal = new bootstrap.Modal(document.getElementById('restoreModal'));
    modal.show();
}

// تفعيل زر الاسترداد
document.getElementById('confirmRestore').addEventListener('change', function() {
    document.getElementById('restoreBtn').disabled = !this.checked;
});

// استرداد نسخة احتياطية
async function restoreBackup() {
    const backupId = document.getElementById('restoreBackupSelect').value;
    const confirmed = document.getElementById('confirmRestore').checked;

    if (!backupId || !confirmed) {
        showNotification('يرجى اختيار نسخة احتياطية والتأكيد', 'warning');
        return;
    }

    try {
        showNotification('جاري استرداد النسخة الاحتياطية...', 'info');

        await integrationsManager.restoreBackup(backupId);

        // إغلاق النافذة
        bootstrap.Modal.getInstance(document.getElementById('restoreModal')).hide();

        showNotification('تم استرداد النسخة الاحتياطية بنجاح', 'success');

        // إعادة تحميل الصفحة
        setTimeout(() => {
            location.reload();
        }, 2000);

    } catch (error) {
        console.error('خطأ في الاسترداد:', error);
        showNotification('خطأ في استرداد النسخة الاحتياطية', 'error');
    }
}

// استرداد نسخة احتياطية محددة
async function restoreSpecificBackup(backupId) {
    if (confirm('هل أنت متأكد من استرداد هذه النسخة الاحتياطية؟ سيتم استبدال جميع البيانات الحالية.')) {
        try {
            await integrationsManager.restoreBackup(backupId);
            showNotification('تم استرداد النسخة الاحتياطية بنجاح', 'success');
            setTimeout(() => location.reload(), 2000);
        } catch (error) {
            showNotification('خطأ في استرداد النسخة الاحتياطية', 'error');
        }
    }
}

// حذف نسخة احتياطية
function deleteBackup(backupId) {
    if (confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')) {
        localStorage.removeItem(backupId);
        listBackups();
        showNotification('تم حذف النسخة الاحتياطية', 'success');
    }
}

// تنظيف النسخ القديمة
function cleanOldBackups() {
    const retention = parseInt(document.getElementById('backup_retention').value) || 30;
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retention);

    let deletedCount = 0;

    for (let i = localStorage.length - 1; i >= 0; i--) {
        const key = localStorage.key(i);
        if (key && key.startsWith('backup_')) {
            try {
                const backup = JSON.parse(localStorage.getItem(key));
                const backupDate = new Date(backup.timestamp);

                if (backupDate < cutoffDate) {
                    localStorage.removeItem(key);
                    deletedCount++;
                }
            } catch (error) {
                // حذف النسخ التالفة
                localStorage.removeItem(key);
                deletedCount++;
            }
        }
    }

    listBackups();
    showNotification(`تم حذف ${deletedCount} نسخة احتياطية قديمة`, 'success');
}

// تحميل إعدادات النسخ الاحتياطي
function loadBackupSettings() {
    const settings = integrationsManager.backupSettings;

    document.getElementById('backup_enabled').checked = settings.enabled || true;
    document.getElementById('backup_frequency').value = settings.frequency || 'daily';
    document.getElementById('backup_time').value = settings.time || '02:00';
    document.getElementById('backup_retention').value = settings.retention || 30;
    document.getElementById('backup_location').value = settings.location || 'local';
    document.getElementById('backup_encryption').checked = settings.encryption || true;
    document.getElementById('backup_compression').checked = settings.compression || true;
}

// حفظ إعدادات النسخ الاحتياطي
function saveBackupSettings() {
    integrationsManager.backupSettings = {
        enabled: document.getElementById('backup_enabled').checked,
        frequency: document.getElementById('backup_frequency').value,
        time: document.getElementById('backup_time').value,
        retention: parseInt(document.getElementById('backup_retention').value),
        location: document.getElementById('backup_location').value,
        encryption: document.getElementById('backup_encryption').checked,
        compression: document.getElementById('backup_compression').checked,
        include: {
            database: true,
            files: true,
            settings: true,
            logs: false
        }
    };

    integrationsManager.saveSettings();
    showNotification('تم حفظ إعدادات النسخ الاحتياطي', 'success');
}

// اختبار الاتصالات
function testConnections() {
    showNotification('جاري اختبار جميع الاتصالات...', 'info');

    // محاكاة اختبار الاتصالات
    setTimeout(() => {
        const results = [
            'قاعدة البيانات: متصلة ✅',
            'طرق الدفع: ' + integrationsManager.getAvailablePaymentMethods().length + ' متاحة ✅',
            'التكاملات الخارجية: جاري الفحص...',
            'النسخ الاحتياطي: نشط ✅'
        ];

        console.log('نتائج اختبار الاتصالات:', results);
        showNotification('تم اختبار جميع الاتصالات بنجاح', 'success');
    }, 2000);
}

// عرض الإشعارات
function showNotification(message, type = 'info') {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';

    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // إزالة الإشعار تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
</script>