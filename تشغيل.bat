@echo off
chcp 65001 >nul
title Black Horse ERP - نظام إدارة الأعمال

echo.
echo ========================================
echo 🐎 Black Horse ERP
echo نظام إدارة الأعمال المتكامل
echo ========================================
echo.

:: التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت على النظام
    echo.
    echo 📥 يرجى تحميل Python من:
    echo https://www.python.org/downloads/
    echo.
    echo ⚠️  تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo.
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

:: التحقق من وجود ملف التشغيل
if not exist "run.py" (
    echo ❌ ملف run.py غير موجود
    echo.
    pause
    exit /b 1
)

echo 🚀 جاري تشغيل النظام...
echo.

:: تشغيل النظام
python run.py

:: في حالة حدوث خطأ
if %errorlevel% neq 0 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل النظام
    echo.
    echo 🔧 حلول مقترحة:
    echo 1. تأكد من تثبيت Python بشكل صحيح
    echo 2. تأكد من وجود جميع الملفات المطلوبة
    echo 3. جرب تشغيل الأمر يدوياً: python run.py
    echo.
)

pause
