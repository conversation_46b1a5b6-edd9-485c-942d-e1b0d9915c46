<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الموارد البشرية - Black Horse ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .module-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .employee-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }
        .employee-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
        }
        .btn-gradient:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            color: white;
        }
        .nav-tabs .nav-link {
            border: none;
            color: #667eea;
            font-weight: 600;
        }
        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0;
        }
        .attendance-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-present { background: #d4edda; color: #155724; }
        .status-absent { background: #f8d7da; color: #721c24; }
        .status-late { background: #fff3cd; color: #856404; }
        .status-leave { background: #d1ecf1; color: #0c5460; }
        .payroll-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="module-card p-4 mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-users me-2"></i>إدارة الموارد البشرية</h2>
                    <p class="text-muted mb-0">إدارة شاملة للموظفين والحضور والرواتب</p>
                </div>
                <button class="btn btn-gradient" onclick="showAddEmployeeModal()">
                    <i class="fas fa-plus me-2"></i>موظف جديد
                </button>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h4 id="totalEmployees">0</h4>
                    <p>إجمالي الموظفين</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-user-check fa-2x mb-2"></i>
                    <h4 id="presentToday">0</h4>
                    <p>حاضر اليوم</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-calendar-times fa-2x mb-2"></i>
                    <h4 id="onLeave">0</h4>
                    <p>في إجازة</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                    <h4 id="monthlyPayroll">0</h4>
                    <p>رواتب الشهر</p>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="module-card p-4">
            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs mb-4" id="hrTabs">
                <li class="nav-item">
                    <a class="nav-link active" data-bs-toggle="tab" href="#employeesTab">الموظفين</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#attendanceTab">الحضور والانصراف</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#leavesTab">الإجازات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#payrollTab">الرواتب</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#reportsTab">التقارير</a>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Employees Tab -->
                <div class="tab-pane fade show active" id="employeesTab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>قائمة الموظفين</h5>
                        <div class="d-flex gap-2">
                            <select class="form-select" id="departmentFilter">
                                <option value="">جميع الأقسام</option>
                                <option value="sales">المبيعات</option>
                                <option value="inventory">المخزون</option>
                                <option value="accounting">المحاسبة</option>
                                <option value="hr">الموارد البشرية</option>
                            </select>
                            <input type="text" class="form-control" placeholder="البحث..." id="employeeSearch">
                        </div>
                    </div>
                    <div class="row" id="employeesGrid">
                        <!-- Employees will be loaded here -->
                    </div>
                </div>

                <!-- Attendance Tab -->
                <div class="tab-pane fade" id="attendanceTab">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h5>تسجيل الحضور</h5>
                            <div class="d-flex gap-2">
                                <select class="form-select" id="attendanceEmployee">
                                    <option value="">اختر الموظف</option>
                                </select>
                                <button class="btn btn-success" onclick="markAttendance('in')">
                                    <i class="fas fa-sign-in-alt me-2"></i>حضور
                                </button>
                                <button class="btn btn-danger" onclick="markAttendance('out')">
                                    <i class="fas fa-sign-out-alt me-2"></i>انصراف
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5>تاريخ الحضور</h5>
                            <input type="date" class="form-control" id="attendanceDate" value="">
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الموظف</th>
                                    <th>وقت الحضور</th>
                                    <th>وقت الانصراف</th>
                                    <th>ساعات العمل</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="attendanceTableBody">
                                <!-- Attendance data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Leaves Tab -->
                <div class="tab-pane fade" id="leavesTab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>طلبات الإجازات</h5>
                        <button class="btn btn-gradient" onclick="showAddLeaveModal()">
                            <i class="fas fa-plus me-2"></i>طلب إجازة جديد
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الموظف</th>
                                    <th>نوع الإجازة</th>
                                    <th>من تاريخ</th>
                                    <th>إلى تاريخ</th>
                                    <th>عدد الأيام</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="leavesTableBody">
                                <!-- Leaves data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Payroll Tab -->
                <div class="tab-pane fade" id="payrollTab">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h5>إعداد الرواتب</h5>
                            <div class="d-flex gap-2">
                                <select class="form-select" id="payrollMonth">
                                    <option value="1">يناير</option>
                                    <option value="2">فبراير</option>
                                    <option value="3">مارس</option>
                                    <option value="4">أبريل</option>
                                    <option value="5">مايو</option>
                                    <option value="6">يونيو</option>
                                    <option value="7">يوليو</option>
                                    <option value="8">أغسطس</option>
                                    <option value="9">سبتمبر</option>
                                    <option value="10">أكتوبر</option>
                                    <option value="11">نوفمبر</option>
                                    <option value="12">ديسمبر</option>
                                </select>
                                <select class="form-select" id="payrollYear">
                                    <option value="2024">2024</option>
                                    <option value="2023">2023</option>
                                </select>
                                <button class="btn btn-gradient" onclick="generatePayroll()">
                                    <i class="fas fa-calculator me-2"></i>حساب الرواتب
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5>إجمالي الرواتب</h5>
                            <div class="alert alert-info">
                                <strong>إجمالي رواتب الشهر: </strong><span id="totalPayrollAmount">0</span> جنيه
                            </div>
                        </div>
                    </div>
                    <div id="payrollList">
                        <!-- Payroll items will be loaded here -->
                    </div>
                </div>

                <!-- Reports Tab -->
                <div class="tab-pane fade" id="reportsTab">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>تقرير الحضور الشهري</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="monthlyAttendanceChart"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>توزيع الموظفين حسب القسم</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="departmentChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>تقرير الإجازات</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="leavesChart"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>تقرير الرواتب</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="salaryChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Employee Modal -->
    <div class="modal fade" id="addEmployeeModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">موظف جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addEmployeeForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الاسم الكامل</label>
                                    <input type="text" class="form-control" id="employeeName" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="employeeEmail" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" id="employeePhone" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">القسم</label>
                                    <select class="form-select" id="employeeDepartment" required>
                                        <option value="">اختر القسم</option>
                                        <option value="sales">المبيعات</option>
                                        <option value="inventory">المخزون</option>
                                        <option value="accounting">المحاسبة</option>
                                        <option value="hr">الموارد البشرية</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المنصب</label>
                                    <input type="text" class="form-control" id="employeePosition" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الراتب الأساسي</label>
                                    <input type="number" class="form-control" id="employeeSalary" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">تاريخ التوظيف</label>
                                    <input type="date" class="form-control" id="employeeHireDate" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الحالة</label>
                                    <select class="form-select" id="employeeStatus">
                                        <option value="active">نشط</option>
                                        <option value="inactive">غير نشط</option>
                                        <option value="terminated">منتهي الخدمة</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" id="employeeAddress" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-gradient" onclick="saveEmployee()">حفظ الموظف</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Leave Modal -->
    <div class="modal fade" id="addLeaveModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">طلب إجازة جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addLeaveForm">
                        <div class="mb-3">
                            <label class="form-label">الموظف</label>
                            <select class="form-select" id="leaveEmployee" required>
                                <option value="">اختر الموظف</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">نوع الإجازة</label>
                            <select class="form-select" id="leaveType" required>
                                <option value="">اختر نوع الإجازة</option>
                                <option value="annual">إجازة سنوية</option>
                                <option value="sick">إجازة مرضية</option>
                                <option value="emergency">إجازة طارئة</option>
                                <option value="maternity">إجازة أمومة</option>
                                <option value="unpaid">إجازة بدون راتب</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="leaveFromDate" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="leaveToDate" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">السبب</label>
                            <textarea class="form-control" id="leaveReason" rows="3" required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-gradient" onclick="saveLeave()">حفظ الطلب</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="hr.js"></script>
</body>
</html>
