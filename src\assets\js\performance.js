/**
 * Performance Optimization Module
 * تحسين الأداء والسرعة في نظام Black Horse ERP
 */

class PerformanceOptimizer {
    constructor() {
        this.cache = new Map();
        this.observers = new Map();
        this.lazyLoadQueue = [];
        this.isOptimizing = false;
        
        this.init();
    }
    
    /**
     * Initialize performance optimizations
     */
    init() {
        console.log('🚀 Initializing Performance Optimizer...');
        
        // Setup lazy loading
        this.setupLazyLoading();
        
        // Setup image optimization
        this.setupImageOptimization();
        
        // Setup memory management
        this.setupMemoryManagement();
        
        // Setup network optimization
        this.setupNetworkOptimization();
        
        // Setup DOM optimization
        this.setupDOMOptimization();
        
        // Monitor performance
        this.startPerformanceMonitoring();
        
        console.log('✅ Performance Optimizer initialized');
    }
    
    /**
     * Setup lazy loading for modules and images
     */
    setupLazyLoading() {
        // Intersection Observer for lazy loading
        if ('IntersectionObserver' in window) {
            this.observers.set('lazy', new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadLazyElement(entry.target);
                        this.observers.get('lazy').unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '50px'
            }));
        }
        
        // Setup lazy loading for images
        document.addEventListener('DOMContentLoaded', () => {
            this.setupLazyImages();
        });
    }
    
    /**
     * Setup lazy loading for images
     */
    setupLazyImages() {
        const images = document.querySelectorAll('img[data-src]');
        images.forEach(img => {
            if (this.observers.has('lazy')) {
                this.observers.get('lazy').observe(img);
            } else {
                // Fallback for browsers without IntersectionObserver
                this.loadLazyElement(img);
            }
        });
    }
    
    /**
     * Load lazy element
     */
    loadLazyElement(element) {
        if (element.dataset.src) {
            element.src = element.dataset.src;
            element.removeAttribute('data-src');
            element.classList.add('loaded');
        }
    }
    
    /**
     * Setup image optimization
     */
    setupImageOptimization() {
        // Preload critical images
        this.preloadCriticalImages();
        
        // Setup responsive images
        this.setupResponsiveImages();
    }
    
    /**
     * Preload critical images
     */
    preloadCriticalImages() {
        const criticalImages = [
            // Add paths to critical images here
        ];
        
        criticalImages.forEach(src => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'image';
            link.href = src;
            document.head.appendChild(link);
        });
    }
    
    /**
     * Setup responsive images
     */
    setupResponsiveImages() {
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            if (!img.loading) {
                img.loading = 'lazy';
            }
        });
    }
    
    /**
     * Setup memory management
     */
    setupMemoryManagement() {
        // Clear cache periodically
        setInterval(() => {
            this.cleanupCache();
        }, 300000); // Every 5 minutes
        
        // Monitor memory usage
        if (performance.memory) {
            setInterval(() => {
                this.checkMemoryUsage();
            }, 30000); // Every 30 seconds
        }
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
    }
    
    /**
     * Check memory usage
     */
    checkMemoryUsage() {
        if (!performance.memory) return;
        
        const memory = performance.memory;
        const usedMB = memory.usedJSHeapSize / 1024 / 1024;
        const limitMB = memory.jsHeapSizeLimit / 1024 / 1024;
        const usagePercent = (usedMB / limitMB) * 100;
        
        if (usagePercent > 80) {
            console.warn('⚠️ High memory usage detected:', usedMB.toFixed(2), 'MB');
            this.forceGarbageCollection();
        }
    }
    
    /**
     * Force garbage collection (if available)
     */
    forceGarbageCollection() {
        // Clear caches
        this.cleanupCache();
        
        // Clear unused DOM references
        this.cleanupDOMReferences();
        
        // Trigger garbage collection if available
        if (window.gc) {
            window.gc();
        }
    }
    
    /**
     * Cleanup cache
     */
    cleanupCache() {
        const now = Date.now();
        const maxAge = 300000; // 5 minutes
        
        for (const [key, value] of this.cache.entries()) {
            if (value.timestamp && (now - value.timestamp) > maxAge) {
                this.cache.delete(key);
            }
        }
        
        console.log('🧹 Cache cleaned up, size:', this.cache.size);
    }
    
    /**
     * Cleanup DOM references
     */
    cleanupDOMReferences() {
        // Remove event listeners from removed elements
        const elements = document.querySelectorAll('[data-cleanup]');
        elements.forEach(element => {
            if (!element.isConnected) {
                // Element is no longer in DOM, clean up
                element.removeAttribute('data-cleanup');
            }
        });
    }
    
    /**
     * Setup network optimization
     */
    setupNetworkOptimization() {
        // Preload critical resources
        this.preloadCriticalResources();
        
        // Setup service worker for caching (if supported)
        this.setupServiceWorker();
        
        // Optimize AJAX requests
        this.setupRequestOptimization();
    }
    
    /**
     * Preload critical resources
     */
    preloadCriticalResources() {
        const criticalResources = [
            { href: 'src/core/database.js', as: 'script' },
            { href: 'src/core/utils.js', as: 'script' },
            { href: 'src/assets/css/custom.css', as: 'style' }
        ];
        
        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource.href;
            link.as = resource.as;
            document.head.appendChild(link);
        });
    }
    
    /**
     * Setup service worker
     */
    setupServiceWorker() {
        if ('serviceWorker' in navigator) {
            // Service worker implementation would go here
            console.log('📡 Service Worker support detected');
        }
    }
    
    /**
     * Setup request optimization
     */
    setupRequestOptimization() {
        // Cache successful requests
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            const cacheKey = args[0];
            
            // Check cache first
            if (this.cache.has(cacheKey)) {
                const cached = this.cache.get(cacheKey);
                if (Date.now() - cached.timestamp < 60000) { // 1 minute cache
                    return Promise.resolve(cached.response.clone());
                }
            }
            
            // Make request
            const response = await originalFetch(...args);
            
            // Cache successful responses
            if (response.ok && response.status === 200) {
                this.cache.set(cacheKey, {
                    response: response.clone(),
                    timestamp: Date.now()
                });
            }
            
            return response;
        };
    }
    
    /**
     * Setup DOM optimization
     */
    setupDOMOptimization() {
        // Debounce scroll events
        this.debounceScrollEvents();
        
        // Optimize animations
        this.optimizeAnimations();
        
        // Virtual scrolling for large lists
        this.setupVirtualScrolling();
    }
    
    /**
     * Debounce scroll events
     */
    debounceScrollEvents() {
        let scrollTimeout;
        const originalAddEventListener = Element.prototype.addEventListener;
        
        Element.prototype.addEventListener = function(type, listener, options) {
            if (type === 'scroll') {
                const debouncedListener = (event) => {
                    clearTimeout(scrollTimeout);
                    scrollTimeout = setTimeout(() => {
                        listener.call(this, event);
                    }, 16); // ~60fps
                };
                return originalAddEventListener.call(this, type, debouncedListener, options);
            }
            return originalAddEventListener.call(this, type, listener, options);
        };
    }
    
    /**
     * Optimize animations
     */
    optimizeAnimations() {
        // Use requestAnimationFrame for smooth animations
        window.smoothAnimate = (callback) => {
            requestAnimationFrame(callback);
        };
        
        // Reduce motion for users who prefer it
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            document.documentElement.style.setProperty('--animation-duration', '0.01ms');
        }
    }
    
    /**
     * Setup virtual scrolling
     */
    setupVirtualScrolling() {
        // Virtual scrolling implementation for large datasets
        const largeContainers = document.querySelectorAll('[data-virtual-scroll]');
        largeContainers.forEach(container => {
            this.enableVirtualScrolling(container);
        });
    }
    
    /**
     * Enable virtual scrolling for container
     */
    enableVirtualScrolling(container) {
        // Virtual scrolling implementation would go here
        console.log('📜 Virtual scrolling enabled for:', container);
    }
    
    /**
     * Start performance monitoring
     */
    startPerformanceMonitoring() {
        // Monitor page load performance
        window.addEventListener('load', () => {
            setTimeout(() => {
                this.reportPerformanceMetrics();
            }, 1000);
        });
        
        // Monitor navigation performance
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                list.getEntries().forEach(entry => {
                    if (entry.entryType === 'navigation') {
                        console.log('📊 Navigation timing:', entry);
                    }
                });
            });
            observer.observe({ entryTypes: ['navigation'] });
        }
    }
    
    /**
     * Report performance metrics
     */
    reportPerformanceMetrics() {
        const metrics = {
            loadTime: performance.now(),
            memory: performance.memory ? {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            } : null,
            cacheSize: this.cache.size,
            timestamp: new Date().toISOString()
        };
        
        console.log('📈 Performance Metrics:', metrics);
        
        // Store metrics for analysis
        this.storeMetrics(metrics);
    }
    
    /**
     * Store performance metrics
     */
    storeMetrics(metrics) {
        try {
            const stored = JSON.parse(localStorage.getItem('performance_metrics') || '[]');
            stored.push(metrics);
            
            // Keep only last 10 entries
            if (stored.length > 10) {
                stored.splice(0, stored.length - 10);
            }
            
            localStorage.setItem('performance_metrics', JSON.stringify(stored));
        } catch (error) {
            console.warn('Could not store performance metrics:', error);
        }
    }
    
    /**
     * Get performance metrics
     */
    getMetrics() {
        try {
            return JSON.parse(localStorage.getItem('performance_metrics') || '[]');
        } catch (error) {
            return [];
        }
    }
    
    /**
     * Optimize module loading
     */
    optimizeModuleLoading(moduleName) {
        return new Promise((resolve, reject) => {
            // Check if module is already loaded
            if (window[moduleName]) {
                resolve(window[moduleName]);
                return;
            }
            
            // Check cache
            const cacheKey = `module_${moduleName}`;
            if (this.cache.has(cacheKey)) {
                resolve(this.cache.get(cacheKey).module);
                return;
            }
            
            // Load module
            const script = document.createElement('script');
            script.src = `src/modules/${moduleName}/${moduleName}.js`;
            script.onload = () => {
                const module = window[moduleName];
                this.cache.set(cacheKey, {
                    module: module,
                    timestamp: Date.now()
                });
                resolve(module);
            };
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }
    
    /**
     * Cleanup resources
     */
    cleanup() {
        // Clear all caches
        this.cache.clear();
        
        // Disconnect observers
        this.observers.forEach(observer => {
            if (observer.disconnect) {
                observer.disconnect();
            }
        });
        this.observers.clear();
        
        console.log('🧹 Performance Optimizer cleaned up');
    }
}

// Initialize performance optimizer
const performanceOptimizer = new PerformanceOptimizer();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceOptimizer;
} else {
    window.PerformanceOptimizer = PerformanceOptimizer;
    window.performanceOptimizer = performanceOptimizer;
}
