/**
 * Black Horse POS - Broken Functions Fix
 * إصلاح الوظائف والأزرار المعطلة
 * Developer: Augment Agent
 */

// ===== BROKEN FUNCTIONS REPAIR SYSTEM =====
const BrokenFunctionsFix = {
    // قائمة الوظائف المكسورة المكتشفة
    brokenFunctions: {
        // وظائف الحفظ والإضافة
        saveAndAdd: [
            'addProduct', 'saveProduct', 'addCustomer', 'saveCustomer', 
            'addSupplier', 'saveSupplier', 'addExpense', 'saveExpense',
            'addUser', 'saveUser', 'addBranch', 'saveBranch'
        ],
        
        // وظائف التصدير والطباعة
        exportAndPrint: [
            'exportProducts', 'exportCustomers', 'exportSuppliers', 'exportSales',
            'printDailySalesReport', 'printFinancialReport', 'printProductsReport'
        ],
        
        // وظائف البحث والتصفية
        searchAndFilter: [
            'searchProducts', 'searchCustomers', 'searchSuppliers', 
            'filterSalesByDate', 'filterExpensesByDate'
        ],
        
        // وظائف نقطة البيع
        posOperations: [
            'addToCart', 'removeFromCart', 'clearCart', 'processPayment',
            'holdSale', 'printLastReceipt', 'selectPOSPaymentMethod'
        ],
        
        // وظائف النوافذ المنبثقة
        modalOperations: [
            'showAddProductModal', 'showAddCustomerModal', 'showAddSupplierModal',
            'showExpenseModal', 'showPurchaseOrderModal', 'closeModal'
        ],
        
        // وظائف التقارير
        reportFunctions: [
            'generateSalesReport', 'generateInventoryReport', 'generateProfitLossReport',
            'generateCustomersReport', 'generateExpensesReport'
        ]
    },

    // تهيئة النظام
    init: function() {
        try {
            console.log('🔧 Initializing Broken Functions Fix...');
            
            // إصلاح وظائف الحفظ والإضافة
            this.fixSaveAndAddFunctions();
            
            // إصلاح وظائف التصدير والطباعة
            this.fixExportAndPrintFunctions();
            
            // إصلاح وظائف البحث والتصفية
            this.fixSearchAndFilterFunctions();
            
            // إصلاح وظائف نقطة البيع
            this.fixPOSFunctions();
            
            // إصلاح وظائف النوافذ المنبثقة
            this.fixModalFunctions();
            
            // إصلاح وظائف التقارير
            this.fixReportFunctions();
            
            // إصلاح الوظائف المتنوعة
            this.fixMiscellaneousFunctions();
            
            console.log('✅ All broken functions fixed successfully');
        } catch (error) {
            console.error('❌ Error fixing broken functions:', error);
        }
    },

    // إصلاح وظائف الحفظ والإضافة
    fixSaveAndAddFunctions: function() {
        try {
            console.log('💾 Fixing save and add functions...');

            // إضافة منتج
            if (!window.addProduct || typeof window.addProduct !== 'function') {
                window.addProduct = function() {
                    try {
                        console.log('📦 Adding new product...');
                        
                        const name = document.getElementById('productName')?.value;
                        const price = document.getElementById('productPrice')?.value;
                        const category = document.getElementById('productCategory')?.value;
                        
                        if (!name || !price) {
                            alert('يرجى ملء جميع الحقول المطلوبة');
                            return;
                        }
                        
                        const product = {
                            id: Date.now(),
                            name: name,
                            price: parseFloat(price),
                            category: category || 'عام',
                            stock: parseInt(document.getElementById('productStock')?.value) || 0,
                            code: document.getElementById('productCode')?.value || 'P' + Date.now(),
                            barcode: document.getElementById('productBarcode')?.value || '',
                            createdAt: new Date().toISOString()
                        };
                        
                        // حفظ في localStorage
                        let products = JSON.parse(localStorage.getItem('products') || '[]');
                        products.push(product);
                        localStorage.setItem('products', JSON.stringify(products));
                        
                        // إغلاق النافذة وتحديث القائمة
                        closeModal('addProductModal');
                        if (typeof loadProducts === 'function') loadProducts();
                        
                        alert('تم إضافة المنتج بنجاح');
                        console.log('✅ Product added successfully');
                    } catch (error) {
                        console.error('❌ Error adding product:', error);
                        alert('حدث خطأ في إضافة المنتج');
                    }
                };
            }

            // إضافة عميل
            if (!window.addCustomer || typeof window.addCustomer !== 'function') {
                window.addCustomer = function() {
                    try {
                        console.log('👤 Adding new customer...');
                        
                        const name = document.getElementById('customerName')?.value;
                        const phone = document.getElementById('customerPhone')?.value;
                        
                        if (!name) {
                            alert('يرجى إدخال اسم العميل');
                            return;
                        }
                        
                        const customer = {
                            id: Date.now(),
                            name: name,
                            phone: phone || '',
                            email: document.getElementById('customerEmail')?.value || '',
                            address: document.getElementById('customerAddress')?.value || '',
                            balance: 0,
                            createdAt: new Date().toISOString()
                        };
                        
                        // حفظ في localStorage
                        let customers = JSON.parse(localStorage.getItem('customers') || '[]');
                        customers.push(customer);
                        localStorage.setItem('customers', JSON.stringify(customers));
                        
                        // إغلاق النافذة وتحديث القائمة
                        closeModal('addCustomerModal');
                        if (typeof loadCustomers === 'function') loadCustomers();
                        
                        alert('تم إضافة العميل بنجاح');
                        console.log('✅ Customer added successfully');
                    } catch (error) {
                        console.error('❌ Error adding customer:', error);
                        alert('حدث خطأ في إضافة العميل');
                    }
                };
            }

            // إضافة مورد
            if (!window.addSupplier || typeof window.addSupplier !== 'function') {
                window.addSupplier = function() {
                    try {
                        console.log('🚚 Adding new supplier...');
                        
                        const name = document.getElementById('supplierName')?.value;
                        const phone = document.getElementById('supplierPhone')?.value;
                        
                        if (!name) {
                            alert('يرجى إدخال اسم المورد');
                            return;
                        }
                        
                        const supplier = {
                            id: Date.now(),
                            name: name,
                            phone: phone || '',
                            email: document.getElementById('supplierEmail')?.value || '',
                            address: document.getElementById('supplierAddress')?.value || '',
                            balance: 0,
                            createdAt: new Date().toISOString()
                        };
                        
                        // حفظ في localStorage
                        let suppliers = JSON.parse(localStorage.getItem('suppliers') || '[]');
                        suppliers.push(supplier);
                        localStorage.setItem('suppliers', JSON.stringify(suppliers));
                        
                        // إغلاق النافذة وتحديث القائمة
                        closeModal('addSupplierModal');
                        if (typeof loadSuppliers === 'function') loadSuppliers();
                        
                        alert('تم إضافة المورد بنجاح');
                        console.log('✅ Supplier added successfully');
                    } catch (error) {
                        console.error('❌ Error adding supplier:', error);
                        alert('حدث خطأ في إضافة المورد');
                    }
                };
            }

            console.log('✅ Save and add functions fixed');
        } catch (error) {
            console.error('❌ Error fixing save and add functions:', error);
        }
    },

    // إصلاح وظائف التصدير والطباعة
    fixExportAndPrintFunctions: function() {
        try {
            console.log('🖨️ Fixing export and print functions...');

            // تصدير المنتجات
            if (!window.exportProducts || typeof window.exportProducts !== 'function') {
                window.exportProducts = function() {
                    try {
                        console.log('📤 Exporting products...');
                        
                        const products = JSON.parse(localStorage.getItem('products') || '[]');
                        if (products.length === 0) {
                            alert('لا توجد منتجات للتصدير');
                            return;
                        }
                        
                        // تحويل إلى CSV
                        let csv = 'الاسم,السعر,الفئة,المخزون,الكود\n';
                        products.forEach(product => {
                            csv += `"${product.name}","${product.price}","${product.category}","${product.stock}","${product.code}"\n`;
                        });
                        
                        // تحميل الملف
                        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
                        const link = document.createElement('a');
                        link.href = URL.createObjectURL(blob);
                        link.download = `products_${new Date().toISOString().split('T')[0]}.csv`;
                        link.click();
                        
                        alert('تم تصدير المنتجات بنجاح');
                        console.log('✅ Products exported successfully');
                    } catch (error) {
                        console.error('❌ Error exporting products:', error);
                        alert('حدث خطأ في تصدير المنتجات');
                    }
                };
            }

            // طباعة تقرير يومي
            if (!window.printDailySalesReport || typeof window.printDailySalesReport !== 'function') {
                window.printDailySalesReport = function() {
                    try {
                        console.log('🖨️ Printing daily sales report...');
                        
                        const sales = JSON.parse(localStorage.getItem('sales') || '[]');
                        const today = new Date().toISOString().split('T')[0];
                        const todaySales = sales.filter(sale => sale.date?.startsWith(today));
                        
                        const totalSales = todaySales.reduce((sum, sale) => sum + (sale.total || 0), 0);
                        
                        // إنشاء نافذة طباعة
                        const printWindow = window.open('', '_blank');
                        printWindow.document.write(`
                            <html dir="rtl">
                            <head>
                                <title>تقرير المبيعات اليومي</title>
                                <style>
                                    body { font-family: Arial, sans-serif; direction: rtl; }
                                    .header { text-align: center; margin-bottom: 20px; }
                                    .summary { background: #f5f5f5; padding: 10px; margin: 10px 0; }
                                    table { width: 100%; border-collapse: collapse; }
                                    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                                    th { background-color: #f2f2f2; }
                                </style>
                            </head>
                            <body>
                                <div class="header">
                                    <h1>تقرير المبيعات اليومي</h1>
                                    <p>التاريخ: ${new Date().toLocaleDateString('ar-EG')}</p>
                                </div>
                                <div class="summary">
                                    <h3>ملخص المبيعات</h3>
                                    <p>عدد المبيعات: ${todaySales.length}</p>
                                    <p>إجمالي المبيعات: ${totalSales.toFixed(2)} جنيه</p>
                                </div>
                                <table>
                                    <thead>
                                        <tr>
                                            <th>رقم الفاتورة</th>
                                            <th>العميل</th>
                                            <th>المبلغ</th>
                                            <th>الوقت</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${todaySales.map(sale => `
                                            <tr>
                                                <td>${sale.id || 'غير محدد'}</td>
                                                <td>${sale.customer || 'عميل عادي'}</td>
                                                <td>${(sale.total || 0).toFixed(2)} جنيه</td>
                                                <td>${new Date(sale.date || Date.now()).toLocaleTimeString('ar-EG')}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </body>
                            </html>
                        `);
                        printWindow.document.close();
                        printWindow.print();
                        
                        console.log('✅ Daily sales report printed successfully');
                    } catch (error) {
                        console.error('❌ Error printing daily sales report:', error);
                        alert('حدث خطأ في طباعة التقرير');
                    }
                };
            }

            console.log('✅ Export and print functions fixed');
        } catch (error) {
            console.error('❌ Error fixing export and print functions:', error);
        }
    },

    // إصلاح وظائف البحث والتصفية
    fixSearchAndFilterFunctions: function() {
        try {
            console.log('🔍 Fixing search and filter functions...');

            // البحث في المنتجات
            if (!window.searchProducts || typeof window.searchProducts !== 'function') {
                window.searchProducts = function() {
                    try {
                        const searchTerm = document.getElementById('productSearch')?.value?.toLowerCase() || '';
                        const products = JSON.parse(localStorage.getItem('products') || '[]');

                        const filteredProducts = products.filter(product =>
                            product.name?.toLowerCase().includes(searchTerm) ||
                            product.code?.toLowerCase().includes(searchTerm) ||
                            product.barcode?.toLowerCase().includes(searchTerm)
                        );

                        // عرض النتائج
                        if (typeof displayProducts === 'function') {
                            displayProducts(filteredProducts);
                        }

                        console.log(`🔍 Found ${filteredProducts.length} products`);
                    } catch (error) {
                        console.error('❌ Error searching products:', error);
                    }
                };
            }

            // البحث في العملاء
            if (!window.searchCustomers || typeof window.searchCustomers !== 'function') {
                window.searchCustomers = function() {
                    try {
                        const searchTerm = document.getElementById('customersSearch')?.value?.toLowerCase() || '';
                        const customers = JSON.parse(localStorage.getItem('customers') || '[]');

                        const filteredCustomers = customers.filter(customer =>
                            customer.name?.toLowerCase().includes(searchTerm) ||
                            customer.phone?.includes(searchTerm) ||
                            customer.email?.toLowerCase().includes(searchTerm)
                        );

                        if (typeof displayCustomers === 'function') {
                            displayCustomers(filteredCustomers);
                        }

                        console.log(`🔍 Found ${filteredCustomers.length} customers`);
                    } catch (error) {
                        console.error('❌ Error searching customers:', error);
                    }
                };
            }

            console.log('✅ Search and filter functions fixed');
        } catch (error) {
            console.error('❌ Error fixing search and filter functions:', error);
        }
    },

    // إصلاح وظائف نقطة البيع
    fixPOSFunctions: function() {
        try {
            console.log('🛒 Fixing POS functions...');

            // إضافة منتج للسلة
            if (!window.addToCart || typeof window.addToCart !== 'function') {
                window.addToCart = function(productId, quantity = 1) {
                    try {
                        const products = JSON.parse(localStorage.getItem('products') || '[]');
                        const product = products.find(p => p.id == productId);

                        if (!product) {
                            alert('المنتج غير موجود');
                            return;
                        }

                        let cart = JSON.parse(localStorage.getItem('cart') || '[]');
                        const existingItem = cart.find(item => item.productId == productId);

                        if (existingItem) {
                            existingItem.quantity += quantity;
                        } else {
                            cart.push({
                                productId: productId,
                                name: product.name,
                                price: product.price,
                                quantity: quantity
                            });
                        }

                        localStorage.setItem('cart', JSON.stringify(cart));
                        if (typeof updateCartDisplay === 'function') updateCartDisplay();

                        console.log('✅ Product added to cart');
                    } catch (error) {
                        console.error('❌ Error adding to cart:', error);
                    }
                };
            }

            // مسح السلة
            if (!window.clearCart || typeof window.clearCart !== 'function') {
                window.clearCart = function() {
                    try {
                        localStorage.removeItem('cart');
                        if (typeof updateCartDisplay === 'function') updateCartDisplay();
                        console.log('✅ Cart cleared');
                    } catch (error) {
                        console.error('❌ Error clearing cart:', error);
                    }
                };
            }

            // معالجة الدفع
            if (!window.processPayment || typeof window.processPayment !== 'function') {
                window.processPayment = function() {
                    try {
                        const cart = JSON.parse(localStorage.getItem('cart') || '[]');
                        if (cart.length === 0) {
                            alert('السلة فارغة');
                            return;
                        }

                        const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
                        const customer = document.getElementById('customerSelect')?.value || 'عميل عادي';

                        const sale = {
                            id: Date.now(),
                            items: cart,
                            total: total,
                            customer: customer,
                            date: new Date().toISOString(),
                            paymentMethod: 'نقدي'
                        };

                        // حفظ المبيعة
                        let sales = JSON.parse(localStorage.getItem('sales') || '[]');
                        sales.push(sale);
                        localStorage.setItem('sales', JSON.stringify(sales));

                        // مسح السلة
                        clearCart();

                        alert(`تم إتمام البيع بنجاح\nالمبلغ الإجمالي: ${total.toFixed(2)} جنيه`);
                        console.log('✅ Payment processed successfully');
                    } catch (error) {
                        console.error('❌ Error processing payment:', error);
                        alert('حدث خطأ في معالجة الدفع');
                    }
                };
            }

            console.log('✅ POS functions fixed');
        } catch (error) {
            console.error('❌ Error fixing POS functions:', error);
        }
    },

    // إصلاح وظائف النوافذ المنبثقة
    fixModalFunctions: function() {
        try {
            console.log('🪟 Fixing modal functions...');

            // إظهار نافذة إضافة منتج
            if (!window.showAddProductModal || typeof window.showAddProductModal !== 'function') {
                window.showAddProductModal = function() {
                    try {
                        const modal = document.getElementById('addProductModal');
                        if (modal) {
                            modal.style.display = 'block';
                            modal.classList.add('show');
                        }
                        console.log('✅ Product modal opened');
                    } catch (error) {
                        console.error('❌ Error opening product modal:', error);
                    }
                };
            }

            // إغلاق النوافذ المنبثقة
            if (!window.closeModal || typeof window.closeModal !== 'function') {
                window.closeModal = function(modalId) {
                    try {
                        const modal = document.getElementById(modalId);
                        if (modal) {
                            modal.style.display = 'none';
                            modal.classList.remove('show');

                            // مسح النماذج
                            const forms = modal.querySelectorAll('form');
                            forms.forEach(form => form.reset());
                        }
                        console.log(`✅ Modal ${modalId} closed`);
                    } catch (error) {
                        console.error(`❌ Error closing modal ${modalId}:`, error);
                    }
                };
            }

            console.log('✅ Modal functions fixed');
        } catch (error) {
            console.error('❌ Error fixing modal functions:', error);
        }
    },

    // إصلاح وظائف التقارير
    fixReportFunctions: function() {
        try {
            console.log('📊 Fixing report functions...');

            // تقرير المبيعات
            if (!window.generateSalesReport || typeof window.generateSalesReport !== 'function') {
                window.generateSalesReport = function() {
                    try {
                        const sales = JSON.parse(localStorage.getItem('sales') || '[]');
                        const totalSales = sales.reduce((sum, sale) => sum + (sale.total || 0), 0);

                        alert(`تقرير المبيعات\nعدد المبيعات: ${sales.length}\nإجمالي المبيعات: ${totalSales.toFixed(2)} جنيه`);
                        console.log('✅ Sales report generated');
                    } catch (error) {
                        console.error('❌ Error generating sales report:', error);
                    }
                };
            }

            console.log('✅ Report functions fixed');
        } catch (error) {
            console.error('❌ Error fixing report functions:', error);
        }
    },

    // إصلاح الوظائف المتنوعة
    fixMiscellaneousFunctions: function() {
        try {
            console.log('🔧 Fixing miscellaneous functions...');

            // تحديث لوحة التحكم
            if (!window.refreshDashboard || typeof window.refreshDashboard !== 'function') {
                window.refreshDashboard = function() {
                    try {
                        location.reload();
                        console.log('✅ Dashboard refreshed');
                    } catch (error) {
                        console.error('❌ Error refreshing dashboard:', error);
                    }
                };
            }

            // فتح الصندوق
            if (!window.openCashDrawer || typeof window.openCashDrawer !== 'function') {
                window.openCashDrawer = function() {
                    try {
                        alert('تم فتح الصندوق');
                        console.log('✅ Cash drawer opened');
                    } catch (error) {
                        console.error('❌ Error opening cash drawer:', error);
                    }
                };
            }

            console.log('✅ Miscellaneous functions fixed');
        } catch (error) {
            console.error('❌ Error fixing miscellaneous functions:', error);
        }
    }
};

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    BrokenFunctionsFix.init();
});

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.BrokenFunctionsFix = BrokenFunctionsFix;
}

console.log('✅ Broken Functions Fix loaded successfully');
