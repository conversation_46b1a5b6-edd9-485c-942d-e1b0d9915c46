/**
 * Cashier Management Module
 * إدارة الصناديق والكاشيرين
 */

class CashierModule {
    constructor() {
        this.currentView = 'cashiers';
        this.selectedCashiers = new Set();
        this.filters = {
            status: 'all',
            shift: 'all',
            date_from: '',
            date_to: '',
            search: ''
        };
        this.pagination = {
            page: 1,
            limit: 20,
            total: 0
        };
        this.currentCashier = null;
        this.cashiers = new Map();
        this.shifts = new Map();
        this.transactions = new Map();
        this.currentShift = null;
    }

    /**
     * Initialize cashier module
     */
    async initialize() {
        console.log('Initializing Cashier Module...');
        await this.loadCashiers();
        await this.loadShifts();
        await this.createSampleData();
        await this.checkActiveShift();
    }

    /**
     * Show cashier module
     */
    async show(params = {}) {
        this.currentView = params.view || 'cashiers';
        await this.render();
        await this.loadData();
        this.setupEventListeners();
    }

    /**
     * Render cashier module HTML
     */
    async render() {
        const container = document.getElementById('moduleContainer');
        container.innerHTML = `
            <div class="cashier-module">
                <!-- Header -->
                <div class="module-header d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="fas fa-cash-register me-2"></i>إدارة الصناديق</h2>
                        <p class="text-muted">إدارة الكاشيرين والورديات والمعاملات المالية</p>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="Cashier.showAddCashierModal()">
                            <i class="fas fa-plus me-1"></i>كاشير جديد
                        </button>
                        <button class="btn btn-success" onclick="Cashier.startNewShift()">
                            <i class="fas fa-play me-1"></i>بدء وردية
                        </button>
                        <button class="btn btn-warning" onclick="Cashier.endCurrentShift()">
                            <i class="fas fa-stop me-1"></i>إنهاء وردية
                        </button>
                        <button class="btn btn-info" onclick="Cashier.showDailyReport()">
                            <i class="fas fa-chart-line me-1"></i>تقرير يومي
                        </button>
                    </div>
                </div>

                <!-- Current Shift Status -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-3">
                                        <h6 class="mb-1">الوردية الحالية</h6>
                                        <h5 class="mb-0" id="currentShiftStatus">لا توجد وردية نشطة</h5>
                                    </div>
                                    <div class="col-md-3">
                                        <h6 class="mb-1">الكاشير</h6>
                                        <h5 class="mb-0" id="currentCashierName">-</h5>
                                    </div>
                                    <div class="col-md-3">
                                        <h6 class="mb-1">رصيد الصندوق</h6>
                                        <h5 class="mb-0" id="currentCashBalance">0 ج.م</h5>
                                    </div>
                                    <div class="col-md-3">
                                        <h6 class="mb-1">وقت البداية</h6>
                                        <h5 class="mb-0" id="shiftStartTime">-</h5>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navigation Tabs -->
                <ul class="nav nav-tabs mb-4" id="cashierTabs">
                    <li class="nav-item">
                        <a class="nav-link ${this.currentView === 'cashiers' ? 'active' : ''}" 
                           href="#" onclick="Cashier.switchView('cashiers')">
                            <i class="fas fa-users me-1"></i>الكاشيرين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link ${this.currentView === 'shifts' ? 'active' : ''}" 
                           href="#" onclick="Cashier.switchView('shifts')">
                            <i class="fas fa-clock me-1"></i>الورديات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link ${this.currentView === 'transactions' ? 'active' : ''}" 
                           href="#" onclick="Cashier.switchView('transactions')">
                            <i class="fas fa-exchange-alt me-1"></i>المعاملات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link ${this.currentView === 'reports' ? 'active' : ''}" 
                           href="#" onclick="Cashier.switchView('reports')">
                            <i class="fas fa-chart-bar me-1"></i>التقارير
                        </a>
                    </li>
                </ul>

                <!-- Content Area -->
                <div id="cashierContent">
                    ${await this.renderCurrentView()}
                </div>
            </div>

            <!-- Modals -->
            ${this.renderModals()}
        `;
    }

    /**
     * Render current view content
     */
    async renderCurrentView() {
        switch (this.currentView) {
            case 'cashiers':
                return await this.renderCashiersView();
            case 'shifts':
                return await this.renderShiftsView();
            case 'transactions':
                return await this.renderTransactionsView();
            case 'reports':
                return await this.renderReportsView();
            default:
                return await this.renderCashiersView();
        }
    }

    /**
     * Render cashiers view
     */
    async renderCashiersView() {
        return `
            <div class="cashiers-view">
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">إجمالي الكاشيرين</h6>
                                        <h4 class="mb-0" id="totalCashiersCount">0</h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">نشط حالياً</h6>
                                        <h4 class="mb-0" id="activeCashiersCount">0</h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-user-check fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">في وردية</h6>
                                        <h4 class="mb-0" id="onShiftCashiersCount">0</h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-clock fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">إجمالي المبيعات اليوم</h6>
                                        <h4 class="mb-0" id="todaySalesAmount">0 ج.م</h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-money-bill fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">البحث</label>
                                <input type="text" class="form-control" id="searchInput" 
                                       placeholder="اسم الكاشير، الكود...">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">الحالة</label>
                                <select class="form-select" id="statusFilter">
                                    <option value="all">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                    <option value="on_shift">في وردية</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">الوردية</label>
                                <select class="form-select" id="shiftFilter">
                                    <option value="all">جميع الورديات</option>
                                    <option value="morning">صباحية</option>
                                    <option value="evening">مسائية</option>
                                    <option value="night">ليلية</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="dateFromFilter">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="dateToFilter">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button class="btn btn-primary me-2" onclick="Cashier.applyFilters()">
                                    <i class="fas fa-search me-1"></i>بحث
                                </button>
                                <button class="btn btn-secondary" onclick="Cashier.clearFilters()">
                                    <i class="fas fa-times me-1"></i>مسح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cashiers Grid -->
                <div class="row" id="cashiersGrid">
                    <!-- Cashiers will be loaded here -->
                </div>
            </div>
        `;
    }

    /**
     * Render shifts view
     */
    async renderShiftsView() {
        return `
            <div class="shifts-view">
                <!-- Shifts Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">ورديات اليوم</h6>
                                        <h4 class="mb-0" id="todayShiftsCount">0</h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-calendar-day fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">ورديات مكتملة</h6>
                                        <h4 class="mb-0" id="completedShiftsCount">0</h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">ورديات نشطة</h6>
                                        <h4 class="mb-0" id="activeShiftsCount">0</h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-play-circle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">متوسط مدة الوردية</h6>
                                        <h4 class="mb-0" id="avgShiftDuration">0 ساعة</h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-clock fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Shifts Table -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الوردية</th>
                                        <th>الكاشير</th>
                                        <th>تاريخ البداية</th>
                                        <th>وقت البداية</th>
                                        <th>وقت النهاية</th>
                                        <th>المدة</th>
                                        <th>رصيد البداية</th>
                                        <th>رصيد النهاية</th>
                                        <th>إجمالي المبيعات</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="shiftsTableBody">
                                    <!-- Shifts will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render transactions view
     */
    async renderTransactionsView() {
        return `
            <div class="transactions-view">
                <!-- Transaction Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">إجمالي المعاملات</h6>
                                        <h4 class="mb-0" id="totalTransactionsCount">0</h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-exchange-alt fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">معاملات اليوم</h6>
                                        <h4 class="mb-0" id="todayTransactionsCount">0</h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-calendar-day fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">إجمالي الداخل</h6>
                                        <h4 class="mb-0" id="totalInAmount">0 ج.م</h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-arrow-down fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">إجمالي الخارج</h6>
                                        <h4 class="mb-0" id="totalOutAmount">0 ج.م</h4>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-arrow-up fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Transactions Table -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم المعاملة</th>
                                        <th>التاريخ والوقت</th>
                                        <th>النوع</th>
                                        <th>الكاشير</th>
                                        <th>المبلغ</th>
                                        <th>الوصف</th>
                                        <th>المرجع</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="transactionsTableBody">
                                    <!-- Transactions will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render reports view
     */
    async renderReportsView() {
        return `
            <div class="reports-view">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-bar me-2"></i>تقرير الكاشيرين</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="cashiersChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-line me-2"></i>تقرير الورديات</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="shiftsChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render modals
     */
    renderModals() {
        return `
            <!-- Add Cashier Modal -->
            <div class="modal fade" id="addCashierModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إضافة كاشير جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="addCashierForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الاسم الأول *</label>
                                        <input type="text" class="form-control" name="first_name" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الاسم الأخير *</label>
                                        <input type="text" class="form-control" name="last_name" required>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">كود الكاشير *</label>
                                        <input type="text" class="form-control" name="cashier_code" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" name="phone">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" name="email">
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">كلمة المرور *</label>
                                        <input type="password" class="form-control" name="password" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تأكيد كلمة المرور *</label>
                                        <input type="password" class="form-control" name="confirm_password" required>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الراتب الأساسي</label>
                                        <input type="number" class="form-control" name="base_salary" step="0.01">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">نسبة العمولة (%)</label>
                                        <input type="number" class="form-control" name="commission_rate" step="0.01" max="100">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الوردية المفضلة</label>
                                        <select class="form-select" name="preferred_shift">
                                            <option value="">اختر الوردية</option>
                                            <option value="morning">صباحية</option>
                                            <option value="evening">مسائية</option>
                                            <option value="night">ليلية</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الحالة</label>
                                        <select class="form-select" name="status">
                                            <option value="active">نشط</option>
                                            <option value="inactive">غير نشط</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الصلاحيات</label>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="permissions" value="sales" checked>
                                                <label class="form-check-label">المبيعات</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="permissions" value="returns">
                                                <label class="form-check-label">المرتجعات</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="permissions" value="discounts">
                                                <label class="form-check-label">الخصومات</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="permissions" value="cash_operations">
                                                <label class="form-check-label">عمليات نقدية</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="permissions" value="reports">
                                                <label class="form-check-label">التقارير</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="permissions" value="admin">
                                                <label class="form-check-label">إدارة</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="Cashier.saveCashier()">حفظ</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Start Shift Modal -->
            <div class="modal fade" id="startShiftModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">بدء وردية جديدة</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="startShiftForm">
                                <div class="mb-3">
                                    <label class="form-label">الكاشير *</label>
                                    <select class="form-select" name="cashier_id" required>
                                        <option value="">اختر الكاشير</option>
                                        <!-- Cashiers will be loaded here -->
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">نوع الوردية *</label>
                                    <select class="form-select" name="shift_type" required>
                                        <option value="morning">صباحية (8:00 ص - 4:00 م)</option>
                                        <option value="evening">مسائية (4:00 م - 12:00 ص)</option>
                                        <option value="night">ليلية (12:00 ص - 8:00 ص)</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رصيد بداية الوردية *</label>
                                    <input type="number" class="form-control" name="opening_balance" step="0.01" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-success" onclick="Cashier.saveStartShift()">بدء الوردية</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Switch between views
     */
    async switchView(view) {
        this.currentView = view;
        await this.show({ view });
    }

    /**
     * Load data based on current view
     */
    async loadData() {
        switch (this.currentView) {
            case 'cashiers':
                await this.loadCashiersData();
                break;
            case 'shifts':
                await this.loadShiftsData();
                break;
            case 'transactions':
                await this.loadTransactionsData();
                break;
            case 'reports':
                await this.loadReportsData();
                break;
        }
        await this.updateCurrentShiftStatus();
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Search input
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce(() => {
                this.filters.search = searchInput.value;
                this.loadData();
            }, 300));
        }

        // Filter selects
        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', () => {
                this.filters.status = statusFilter.value;
                this.loadData();
            });
        }

        const shiftFilter = document.getElementById('shiftFilter');
        if (shiftFilter) {
            shiftFilter.addEventListener('change', () => {
                this.filters.shift = shiftFilter.value;
                this.loadData();
            });
        }

        // Date filters
        const dateFromFilter = document.getElementById('dateFromFilter');
        if (dateFromFilter) {
            dateFromFilter.addEventListener('change', () => {
                this.filters.date_from = dateFromFilter.value;
                this.loadData();
            });
        }

        const dateToFilter = document.getElementById('dateToFilter');
        if (dateToFilter) {
            dateToFilter.addEventListener('change', () => {
                this.filters.date_to = dateToFilter.value;
                this.loadData();
            });
        }
    }

    /**
     * Load cashiers
     */
    async loadCashiers() {
        try {
            const cashiers = await Database.getAll('cashiers');
            this.cashiers.clear();
            cashiers.forEach(cashier => {
                this.cashiers.set(cashier.id, cashier);
            });
        } catch (error) {
            console.error('Error loading cashiers:', error);
        }
    }

    /**
     * Load shifts
     */
    async loadShifts() {
        try {
            const shifts = await Database.getAll('shifts');
            this.shifts.clear();
            shifts.forEach(shift => {
                this.shifts.set(shift.id, shift);
            });
        } catch (error) {
            console.error('Error loading shifts:', error);
        }
    }

    /**
     * Check for active shift
     */
    async checkActiveShift() {
        try {
            const shifts = Array.from(this.shifts.values());
            this.currentShift = shifts.find(shift => shift.status === 'active');
        } catch (error) {
            console.error('Error checking active shift:', error);
        }
    }

    /**
     * Load cashiers data for display
     */
    async loadCashiersData() {
        try {
            const cashiers = Array.from(this.cashiers.values());

            // Apply filters
            let filteredCashiers = cashiers.filter(cashier => {
                let matches = true;

                if (this.filters.search) {
                    const search = this.filters.search.toLowerCase();
                    matches = matches && (
                        cashier.first_name.toLowerCase().includes(search) ||
                        cashier.last_name.toLowerCase().includes(search) ||
                        cashier.cashier_code.toLowerCase().includes(search) ||
                        cashier.email?.toLowerCase().includes(search)
                    );
                }

                if (this.filters.status !== 'all') {
                    matches = matches && cashier.status === this.filters.status;
                }

                return matches;
            });

            // Update statistics
            this.updateCashiersStatistics(cashiers);

            // Render cashiers grid
            this.renderCashiersGrid(filteredCashiers);

        } catch (error) {
            console.error('Error loading cashiers data:', error);
        }
    }

    /**
     * Update cashiers statistics
     */
    updateCashiersStatistics(cashiers) {
        const totalCount = cashiers.length;
        const activeCount = cashiers.filter(c => c.status === 'active').length;
        const onShiftCount = cashiers.filter(c => c.current_shift_id).length;

        // Update DOM elements
        const totalElement = document.getElementById('totalCashiersCount');
        if (totalElement) totalElement.textContent = totalCount;

        const activeElement = document.getElementById('activeCashiersCount');
        if (activeElement) activeElement.textContent = activeCount;

        const onShiftElement = document.getElementById('onShiftCashiersCount');
        if (onShiftElement) onShiftElement.textContent = onShiftCount;

        // Calculate today's sales (mock data for now)
        const todaySalesElement = document.getElementById('todaySalesAmount');
        if (todaySalesElement) todaySalesElement.textContent = Utils.formatCurrency(15000);
    }

    /**
     * Render cashiers grid
     */
    renderCashiersGrid(cashiers) {
        const grid = document.getElementById('cashiersGrid');
        if (!grid) return;

        if (cashiers.length === 0) {
            grid.innerHTML = `
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5>لا توجد كاشيرين</h5>
                            <p class="text-muted">لم يتم العثور على كاشيرين مطابقين للبحث</p>
                        </div>
                    </div>
                </div>
            `;
            return;
        }

        grid.innerHTML = cashiers.map(cashier => `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="avatar-lg bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                ${cashier.first_name.charAt(0)}${cashier.last_name.charAt(0)}
                            </div>
                            <div>
                                <h6 class="mb-1">${cashier.first_name} ${cashier.last_name}</h6>
                                <small class="text-muted">${cashier.cashier_code}</small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="d-flex justify-content-between mb-2">
                                <span>الحالة:</span>
                                <span class="badge ${cashier.status === 'active' ? 'bg-success' : 'bg-secondary'}">
                                    ${cashier.status === 'active' ? 'نشط' : 'غير نشط'}
                                </span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>الوردية:</span>
                                <span class="badge ${cashier.current_shift_id ? 'bg-warning' : 'bg-light text-dark'}">
                                    ${cashier.current_shift_id ? 'في وردية' : 'خارج الوردية'}
                                </span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>الهاتف:</span>
                                <span>${cashier.phone || '-'}</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>الراتب:</span>
                                <span>${Utils.formatCurrency(cashier.base_salary || 0)}</span>
                            </div>
                        </div>

                        <div class="btn-group w-100">
                            <button class="btn btn-sm btn-outline-primary" onclick="Cashier.editCashier('${cashier.id}')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-info" onclick="Cashier.viewCashierDetails('${cashier.id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="Cashier.deleteCashier('${cashier.id}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * Update current shift status
     */
    async updateCurrentShiftStatus() {
        const statusElement = document.getElementById('currentShiftStatus');
        const cashierElement = document.getElementById('currentCashierName');
        const balanceElement = document.getElementById('currentCashBalance');
        const timeElement = document.getElementById('shiftStartTime');

        if (this.currentShift) {
            const cashier = this.cashiers.get(this.currentShift.cashier_id);

            if (statusElement) statusElement.textContent = 'وردية نشطة';
            if (cashierElement) cashierElement.textContent = cashier ? `${cashier.first_name} ${cashier.last_name}` : 'غير محدد';
            if (balanceElement) balanceElement.textContent = Utils.formatCurrency(this.currentShift.current_balance || 0);
            if (timeElement) timeElement.textContent = Utils.formatTime(this.currentShift.start_time);
        } else {
            if (statusElement) statusElement.textContent = 'لا توجد وردية نشطة';
            if (cashierElement) cashierElement.textContent = '-';
            if (balanceElement) balanceElement.textContent = '0 ج.م';
            if (timeElement) timeElement.textContent = '-';
        }
    }

    /**
     * Create sample data
     */
    async createSampleData() {
        try {
            // Check if data already exists
            const existingCashiers = await Database.getAll('cashiers');
            if (existingCashiers.length > 0) return;

            // Create sample cashiers
            const sampleCashiers = [
                {
                    id: Utils.generateId(),
                    first_name: 'أحمد',
                    last_name: 'محمد',
                    cashier_code: 'CASH001',
                    phone: '01234567890',
                    email: '<EMAIL>',
                    base_salary: 3000,
                    commission_rate: 2.5,
                    preferred_shift: 'morning',
                    status: 'active',
                    permissions: ['sales', 'returns', 'discounts'],
                    created_at: new Date().toISOString(),
                    current_shift_id: null
                },
                {
                    id: Utils.generateId(),
                    first_name: 'فاطمة',
                    last_name: 'علي',
                    cashier_code: 'CASH002',
                    phone: '01234567891',
                    email: '<EMAIL>',
                    base_salary: 2800,
                    commission_rate: 2.0,
                    preferred_shift: 'evening',
                    status: 'active',
                    permissions: ['sales', 'returns'],
                    created_at: new Date().toISOString(),
                    current_shift_id: null
                },
                {
                    id: Utils.generateId(),
                    first_name: 'محمود',
                    last_name: 'حسن',
                    cashier_code: 'CASH003',
                    phone: '01234567892',
                    email: '<EMAIL>',
                    base_salary: 3200,
                    commission_rate: 3.0,
                    preferred_shift: 'night',
                    status: 'active',
                    permissions: ['sales', 'returns', 'discounts', 'cash_operations'],
                    created_at: new Date().toISOString(),
                    current_shift_id: null
                }
            ];

            // Save cashiers
            for (const cashier of sampleCashiers) {
                await Database.add('cashiers', cashier);
                this.cashiers.set(cashier.id, cashier);
            }

            console.log('Sample cashiers created successfully');

        } catch (error) {
            console.error('Error creating sample data:', error);
        }
    }

    /**
     * Show add cashier modal
     */
    async showAddCashierModal() {
        const modal = new bootstrap.Modal(document.getElementById('addCashierModal'));
        modal.show();
    }

    /**
     * Save cashier
     */
    async saveCashier() {
        try {
            const form = document.getElementById('addCashierForm');
            const formData = new FormData(form);

            // Validate passwords match
            const password = formData.get('password');
            const confirmPassword = formData.get('confirm_password');

            if (password !== confirmPassword) {
                Utils.showToast('خطأ', 'كلمات المرور غير متطابقة', 'error');
                return;
            }

            // Get permissions
            const permissions = [];
            const permissionInputs = form.querySelectorAll('input[name="permissions"]:checked');
            permissionInputs.forEach(input => permissions.push(input.value));

            const cashier = {
                id: Utils.generateId(),
                first_name: formData.get('first_name'),
                last_name: formData.get('last_name'),
                cashier_code: formData.get('cashier_code'),
                phone: formData.get('phone'),
                email: formData.get('email'),
                password: password, // In real app, this should be hashed
                base_salary: parseFloat(formData.get('base_salary')) || 0,
                commission_rate: parseFloat(formData.get('commission_rate')) || 0,
                preferred_shift: formData.get('preferred_shift'),
                status: formData.get('status'),
                permissions: permissions,
                notes: formData.get('notes'),
                created_at: new Date().toISOString(),
                current_shift_id: null
            };

            await Database.add('cashiers', cashier);
            this.cashiers.set(cashier.id, cashier);

            // Close modal and refresh data
            bootstrap.Modal.getInstance(document.getElementById('addCashierModal')).hide();
            form.reset();

            if (this.currentView === 'cashiers') {
                await this.loadCashiersData();
            }

            Utils.showToast('نجح', 'تم إضافة الكاشير بنجاح', 'success');

        } catch (error) {
            console.error('Error saving cashier:', error);
            Utils.showToast('خطأ', 'فشل في حفظ الكاشير', 'error');
        }
    }

    /**
     * Start new shift
     */
    async startNewShift() {
        // Load available cashiers in modal
        await this.loadCashiersInModal();

        const modal = new bootstrap.Modal(document.getElementById('startShiftModal'));
        modal.show();
    }

    /**
     * Load cashiers in start shift modal
     */
    async loadCashiersInModal() {
        const select = document.querySelector('#startShiftModal select[name="cashier_id"]');
        if (!select) return;

        const availableCashiers = Array.from(this.cashiers.values())
            .filter(cashier => cashier.status === 'active' && !cashier.current_shift_id);

        select.innerHTML = '<option value="">اختر الكاشير</option>' +
            availableCashiers.map(cashier =>
                `<option value="${cashier.id}">${cashier.first_name} ${cashier.last_name} (${cashier.cashier_code})</option>`
            ).join('');
    }

    /**
     * Save start shift
     */
    async saveStartShift() {
        try {
            const form = document.getElementById('startShiftForm');
            const formData = new FormData(form);

            const cashierId = formData.get('cashier_id');
            if (!cashierId) {
                Utils.showToast('خطأ', 'يجب اختيار الكاشير', 'error');
                return;
            }

            // Check if there's already an active shift
            if (this.currentShift) {
                Utils.showToast('خطأ', 'يوجد وردية نشطة بالفعل. يجب إنهاؤها أولاً', 'error');
                return;
            }

            const shift = {
                id: Utils.generateId(),
                cashier_id: cashierId,
                shift_type: formData.get('shift_type'),
                start_time: new Date().toISOString(),
                end_time: null,
                opening_balance: parseFloat(formData.get('opening_balance')) || 0,
                closing_balance: null,
                current_balance: parseFloat(formData.get('opening_balance')) || 0,
                total_sales: 0,
                total_transactions: 0,
                status: 'active',
                notes: formData.get('notes'),
                created_at: new Date().toISOString()
            };

            await Database.add('shifts', shift);
            this.shifts.set(shift.id, shift);
            this.currentShift = shift;

            // Update cashier's current shift
            const cashier = this.cashiers.get(cashierId);
            if (cashier) {
                cashier.current_shift_id = shift.id;
                await Database.update('cashiers', cashier);
            }

            // Close modal and refresh data
            bootstrap.Modal.getInstance(document.getElementById('startShiftModal')).hide();
            form.reset();

            await this.updateCurrentShiftStatus();
            if (this.currentView === 'shifts') {
                await this.loadShiftsData();
            }

            Utils.showToast('نجح', 'تم بدء الوردية بنجاح', 'success');

        } catch (error) {
            console.error('Error starting shift:', error);
            Utils.showToast('خطأ', 'فشل في بدء الوردية', 'error');
        }
    }

    /**
     * End current shift
     */
    async endCurrentShift() {
        if (!this.currentShift) {
            Utils.showToast('تنبيه', 'لا توجد وردية نشطة لإنهائها', 'warning');
            return;
        }

        try {
            // Update shift
            this.currentShift.end_time = new Date().toISOString();
            this.currentShift.status = 'completed';
            this.currentShift.closing_balance = this.currentShift.current_balance;

            await Database.update('shifts', this.currentShift);

            // Update cashier
            const cashier = this.cashiers.get(this.currentShift.cashier_id);
            if (cashier) {
                cashier.current_shift_id = null;
                await Database.update('cashiers', cashier);
            }

            this.currentShift = null;
            await this.updateCurrentShiftStatus();

            Utils.showToast('نجح', 'تم إنهاء الوردية بنجاح', 'success');

        } catch (error) {
            console.error('Error ending shift:', error);
            Utils.showToast('خطأ', 'فشل في إنهاء الوردية', 'error');
        }
    }

    // Placeholder methods for future implementation
    async loadShiftsData() {
        Utils.showToast('قريباً', 'ميزة عرض الورديات قيد التطوير', 'info');
    }

    async loadTransactionsData() {
        Utils.showToast('قريباً', 'ميزة عرض المعاملات قيد التطوير', 'info');
    }

    async loadReportsData() {
        Utils.showToast('قريباً', 'ميزة التقارير قيد التطوير', 'info');
    }

    async showDailyReport() {
        Utils.showToast('قريباً', 'ميزة التقرير اليومي قيد التطوير', 'info');
    }

    async editCashier(cashierId) {
        Utils.showToast('قريباً', 'ميزة تعديل الكاشير قيد التطوير', 'info');
    }

    async viewCashierDetails(cashierId) {
        Utils.showToast('قريباً', 'ميزة عرض تفاصيل الكاشير قيد التطوير', 'info');
    }

    async deleteCashier(cashierId) {
        if (confirm('هل أنت متأكد من حذف هذا الكاشير؟')) {
            try {
                await Database.delete('cashiers', cashierId);
                this.cashiers.delete(cashierId);

                if (this.currentView === 'cashiers') {
                    await this.loadCashiersData();
                }

                Utils.showToast('نجح', 'تم حذف الكاشير بنجاح', 'success');
            } catch (error) {
                console.error('Error deleting cashier:', error);
                Utils.showToast('خطأ', 'فشل في حذف الكاشير', 'error');
            }
        }
    }

    async applyFilters() {
        await this.loadData();
    }

    async clearFilters() {
        this.filters = {
            status: 'all',
            shift: 'all',
            date_from: '',
            date_to: '',
            search: ''
        };

        // Clear form inputs
        const searchInput = document.getElementById('searchInput');
        if (searchInput) searchInput.value = '';

        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) statusFilter.value = 'all';

        const shiftFilter = document.getElementById('shiftFilter');
        if (shiftFilter) shiftFilter.value = 'all';

        const dateFromFilter = document.getElementById('dateFromFilter');
        if (dateFromFilter) dateFromFilter.value = '';

        const dateToFilter = document.getElementById('dateToFilter');
        if (dateToFilter) dateToFilter.value = '';

        await this.loadData();
    }
}

// Create global instance
window.Cashier = new CashierModule();
