/**
 * Black Horse POS - Helper Functions
 * الدوال المساعدة
 * Developer: Augment Agent
 */

const Utils = {
    // ===== دوال التاريخ والوقت =====
    
    // تنسيق التاريخ
    formatDate: function(date, format = 'YYYY-MM-DD') {
        try {
            const d = new Date(date);
            const year = d.getFullYear();
            const month = String(d.getMonth() + 1).padStart(2, '0');
            const day = String(d.getDate()).padStart(2, '0');
            const hours = String(d.getHours()).padStart(2, '0');
            const minutes = String(d.getMinutes()).padStart(2, '0');
            
            switch (format) {
                case 'YYYY-MM-DD':
                    return `${year}-${month}-${day}`;
                case 'DD/MM/YYYY':
                    return `${day}/${month}/${year}`;
                case 'DD-MM-YYYY HH:mm':
                    return `${day}-${month}-${year} ${hours}:${minutes}`;
                case 'arabic':
                    return `${day}/${month}/${year}`;
                default:
                    return `${year}-${month}-${day}`;
            }
        } catch (error) {
            console.error('❌ Error formatting date:', error);
            return date;
        }
    },

    // الحصول على التاريخ الحالي
    getCurrentDate: function() {
        return new Date().toISOString();
    },

    // التحقق من صحة التاريخ
    isValidDate: function(date) {
        return date instanceof Date && !isNaN(date);
    },

    // ===== دوال الأرقام والعملة =====
    
    // تنسيق الأرقام
    formatNumber: function(number, decimals = 2) {
        try {
            const num = parseFloat(number);
            if (isNaN(num)) return '0.00';
            return num.toFixed(decimals);
        } catch (error) {
            console.error('❌ Error formatting number:', error);
            return '0.00';
        }
    },

    // تنسيق العملة
    formatCurrency: function(amount, currency = 'ج.م') {
        try {
            const formatted = this.formatNumber(amount, 2);
            return `${formatted} ${currency}`;
        } catch (error) {
            console.error('❌ Error formatting currency:', error);
            return `0.00 ${currency}`;
        }
    },

    // تحويل النص إلى رقم
    parseNumber: function(value, defaultValue = 0) {
        try {
            const parsed = parseFloat(value);
            return isNaN(parsed) ? defaultValue : parsed;
        } catch (error) {
            return defaultValue;
        }
    },

    // ===== دوال النصوص =====
    
    // تنظيف النص
    cleanText: function(text) {
        if (!text) return '';
        return text.toString().trim().replace(/\s+/g, ' ');
    },

    // تحويل إلى أحرف كبيرة
    toUpperCase: function(text) {
        return this.cleanText(text).toUpperCase();
    },

    // تحويل إلى أحرف صغيرة
    toLowerCase: function(text) {
        return this.cleanText(text).toLowerCase();
    },

    // اقتطاع النص
    truncateText: function(text, maxLength = 50) {
        if (!text) return '';
        const cleaned = this.cleanText(text);
        return cleaned.length > maxLength ? cleaned.substring(0, maxLength) + '...' : cleaned;
    },

    // ===== دوال التحقق =====
    
    // التحقق من البريد الإلكتروني
    isValidEmail: function(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    // التحقق من رقم الهاتف
    isValidPhone: function(phone) {
        const phoneRegex = /^[\+]?[0-9\-\(\)\s]+$/;
        return phoneRegex.test(phone) && phone.replace(/\D/g, '').length >= 10;
    },

    // التحقق من الكود الشريطي
    isValidBarcode: function(barcode) {
        return barcode && barcode.toString().length >= 8;
    },

    // ===== دوال المصفوفات والكائنات =====
    
    // البحث في المصفوفة
    searchArray: function(array, searchTerm, searchFields = []) {
        if (!Array.isArray(array) || !searchTerm) return array;
        
        const term = searchTerm.toLowerCase();
        
        return array.filter(item => {
            if (searchFields.length === 0) {
                // البحث في جميع الخصائص
                return Object.values(item).some(value => 
                    value && value.toString().toLowerCase().includes(term)
                );
            } else {
                // البحث في الحقول المحددة
                return searchFields.some(field => 
                    item[field] && item[field].toString().toLowerCase().includes(term)
                );
            }
        });
    },

    // ترتيب المصفوفة
    sortArray: function(array, field, direction = 'asc') {
        if (!Array.isArray(array)) return array;
        
        return [...array].sort((a, b) => {
            let aVal = a[field];
            let bVal = b[field];
            
            // التعامل مع الأرقام
            if (!isNaN(aVal) && !isNaN(bVal)) {
                aVal = parseFloat(aVal);
                bVal = parseFloat(bVal);
            }
            
            if (direction === 'asc') {
                return aVal > bVal ? 1 : -1;
            } else {
                return aVal < bVal ? 1 : -1;
            }
        });
    },

    // تجميع المصفوفة
    groupBy: function(array, field) {
        if (!Array.isArray(array)) return {};
        
        return array.reduce((groups, item) => {
            const key = item[field] || 'غير محدد';
            if (!groups[key]) {
                groups[key] = [];
            }
            groups[key].push(item);
            return groups;
        }, {});
    },

    // ===== دوال التوليد =====
    
    // توليد ID فريد
    generateId: function(prefix = 'id') {
        return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    },

    // توليد كود شريطي
    generateBarcode: function() {
        return Date.now().toString() + Math.random().toString().substr(2, 4);
    },

    // توليد رقم فاتورة
    generateInvoiceNumber: function() {
        const date = new Date();
        const year = date.getFullYear().toString().substr(-2);
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const time = Date.now().toString().substr(-4);
        
        return `INV-${year}${month}${day}-${time}`;
    },

    // ===== دوال التخزين المحلي =====
    
    // حفظ في localStorage مع معالجة الأخطاء
    saveToStorage: function(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('❌ Error saving to localStorage:', error);
            return false;
        }
    },

    // تحميل من localStorage مع معالجة الأخطاء
    loadFromStorage: function(key, defaultValue = null) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : defaultValue;
        } catch (error) {
            console.error('❌ Error loading from localStorage:', error);
            return defaultValue;
        }
    },

    // ===== دوال واجهة المستخدم =====
    
    // إظهار رسالة
    showMessage: function(message, type = 'info', duration = 3000) {
        try {
            // إنشاء عنصر الرسالة
            const messageEl = document.createElement('div');
            messageEl.className = `message message-${type}`;
            messageEl.textContent = message;
            messageEl.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 5px;
                color: white;
                font-weight: bold;
                z-index: 10000;
                max-width: 300px;
                word-wrap: break-word;
            `;
            
            // تحديد لون الخلفية حسب النوع
            switch (type) {
                case 'success':
                    messageEl.style.backgroundColor = '#28a745';
                    break;
                case 'error':
                    messageEl.style.backgroundColor = '#dc3545';
                    break;
                case 'warning':
                    messageEl.style.backgroundColor = '#ffc107';
                    messageEl.style.color = '#000';
                    break;
                default:
                    messageEl.style.backgroundColor = '#17a2b8';
            }
            
            // إضافة الرسالة للصفحة
            document.body.appendChild(messageEl);
            
            // إزالة الرسالة بعد المدة المحددة
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.parentNode.removeChild(messageEl);
                }
            }, duration);
            
        } catch (error) {
            console.error('❌ Error showing message:', error);
            // fallback إلى alert
            alert(message);
        }
    },

    // تأكيد العملية
    confirm: function(message, callback) {
        try {
            const result = confirm(message);
            if (callback && typeof callback === 'function') {
                callback(result);
            }
            return result;
        } catch (error) {
            console.error('❌ Error showing confirmation:', error);
            return false;
        }
    },

    // ===== دوال التصدير والطباعة =====
    
    // تصدير إلى CSV
    exportToCSV: function(data, filename = 'export.csv') {
        try {
            if (!Array.isArray(data) || data.length === 0) {
                this.showMessage('لا توجد بيانات للتصدير', 'warning');
                return;
            }
            
            // الحصول على العناوين
            const headers = Object.keys(data[0]);
            
            // تحويل البيانات إلى CSV
            const csvContent = [
                headers.join(','),
                ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
            ].join('\n');
            
            // إنشاء وتحميل الملف
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = filename;
            link.click();
            
            this.showMessage('تم تصدير البيانات بنجاح', 'success');
            
        } catch (error) {
            console.error('❌ Error exporting to CSV:', error);
            this.showMessage('خطأ في تصدير البيانات', 'error');
        }
    },

    // طباعة المحتوى
    printContent: function(content, title = 'طباعة') {
        try {
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head>
                    <title>${title}</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    ${content}
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        } catch (error) {
            console.error('❌ Error printing content:', error);
            this.showMessage('خطأ في الطباعة', 'error');
        }
    },

    // ===== دوال عامة =====
    
    // تأخير التنفيذ
    delay: function(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    },

    // نسخ النص إلى الحافظة
    copyToClipboard: function(text) {
        try {
            navigator.clipboard.writeText(text).then(() => {
                this.showMessage('تم نسخ النص', 'success');
            });
        } catch (error) {
            console.error('❌ Error copying to clipboard:', error);
            this.showMessage('خطأ في النسخ', 'error');
        }
    },

    // التحقق من دعم الميزة
    isFeatureSupported: function(feature) {
        switch (feature) {
            case 'indexedDB':
                return !!window.indexedDB;
            case 'localStorage':
                return !!window.localStorage;
            case 'clipboard':
                return !!navigator.clipboard;
            default:
                return false;
        }
    }
};

// إتاحة Utils عالمياً
window.Utils = Utils;

// تصدير للاستخدام في الوحدات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Utils;
}

console.log('✅ Utils loaded successfully');
