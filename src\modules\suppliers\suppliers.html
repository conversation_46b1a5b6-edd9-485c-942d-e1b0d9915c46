<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الموردين - Black Horse ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/custom.css" rel="stylesheet">
    <style>
        .supplier-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-right: 4px solid #28a745;
            position: relative;
            overflow: hidden;
        }
        
        .supplier-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        
        .supplier-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-approved {
            background: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-suspended {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-blacklisted {
            background: #343a40;
            color: white;
        }
        
        .rating-stars {
            color: #ffc107;
            font-size: 0.9rem;
        }
        
        .rating-stars .empty {
            color: #e9ecef;
        }
        
        .supplier-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 1.1rem;
            font-weight: 600;
            color: #28a745;
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: #6c757d;
        }
        
        .contract-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-right: 4px solid #007bff;
        }
        
        .contract-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .contract-active {
            background: #d4edda;
            color: #155724;
        }
        
        .contract-expired {
            background: #f8d7da;
            color: #721c24;
        }
        
        .contract-draft {
            background: #e2e3e5;
            color: #495057;
        }
        
        .evaluation-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .evaluation-criteria {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .criteria-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .criteria-score {
            font-size: 1.5rem;
            font-weight: 700;
            color: #007bff;
        }
        
        .criteria-label {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .payment-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-right: 3px solid #28a745;
        }
        
        .payment-overdue {
            border-right-color: #dc3545;
        }
        
        .payment-pending {
            border-right-color: #ffc107;
        }
        
        .filter-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .quick-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .action-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            background: #28a745;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .action-btn:hover {
            background: #1e7e34;
            transform: translateY(-1px);
            color: white;
        }
        
        .action-btn.secondary {
            background: #6c757d;
        }
        
        .action-btn.primary {
            background: #007bff;
        }
        
        .action-btn.warning {
            background: #ffc107;
            color: #212529;
        }
        
        .action-btn.info {
            background: #17a2b8;
        }
        
        .view-tabs {
            background: white;
            border-radius: 8px;
            padding: 5px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .view-tab {
            padding: 12px 20px;
            border: none;
            background: none;
            border-radius: 6px;
            transition: all 0.3s ease;
            color: #6c757d;
            font-weight: 500;
        }
        
        .view-tab.active {
            background: #28a745;
            color: white;
        }
        
        .search-box {
            position: relative;
        }
        
        .search-box .form-control {
            padding-right: 45px;
        }
        
        .search-box .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }
        
        .stats-widget {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .stats-widget:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .stats-change {
            font-size: 0.8rem;
            margin-top: 5px;
        }
        
        .stats-up {
            color: #28a745;
        }
        
        .stats-down {
            color: #dc3545;
        }
        
        .contact-info {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }
        
        .contact-info i {
            width: 16px;
            color: #6c757d;
        }
        
        .supplier-tags {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
            margin-top: 10px;
        }
        
        .supplier-tag {
            background: #e9ecef;
            color: #495057;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        .empty-state h4 {
            margin-bottom: 10px;
        }
        
        .empty-state p {
            margin-bottom: 20px;
        }
        
        .modal-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 8px 8px 0 0;
        }
        
        .modal-header .btn-close {
            filter: invert(1);
        }
        
        .form-floating label {
            right: 1rem;
            left: auto;
        }
        
        .table-responsive {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .table th {
            background: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #495057;
        }
        
        .table td {
            border: none;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: middle;
        }
        
        .table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .progress-ring {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: conic-gradient(#28a745 0deg, #e9ecef 0deg);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        
        .progress-ring::before {
            content: '';
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: white;
            position: absolute;
        }
        
        .progress-text {
            position: relative;
            z-index: 1;
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .category-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            background: #e9ecef;
            color: #495057;
        }
        
        .supplier-code {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.8rem;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- رأس الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-truck text-success"></i> إدارة الموردين</h2>
                        <p class="text-muted">مركز إدارة الموردين والعقود والتقييمات</p>
                    </div>
                    <div class="quick-actions">
                        <button class="action-btn" onclick="showAddSupplierModal()">
                            <i class="fas fa-plus"></i> مورد جديد
                        </button>
                        <button class="action-btn primary" onclick="showAddContractModal()">
                            <i class="fas fa-file-contract"></i> عقد جديد
                        </button>
                        <button class="action-btn warning" onclick="showEvaluationModal()">
                            <i class="fas fa-star"></i> تقييم مورد
                        </button>
                        <button class="action-btn info" onclick="showPaymentModal()">
                            <i class="fas fa-money-bill"></i> دفعة جديدة
                        </button>
                        <button class="action-btn secondary" onclick="showImportModal()">
                            <i class="fas fa-upload"></i> استيراد
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-widget">
                    <div class="stats-number text-success" id="totalSuppliers">0</div>
                    <div class="stats-label">إجمالي الموردين</div>
                    <div class="stats-change stats-up" id="suppliersChange">
                        <i class="fas fa-arrow-up"></i> +3.2%
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-widget">
                    <div class="stats-number text-primary" id="activeContracts">0</div>
                    <div class="stats-label">العقود النشطة</div>
                    <div class="stats-change stats-up" id="contractsChange">
                        <i class="fas fa-arrow-up"></i> +8.1%
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-widget">
                    <div class="stats-number text-warning" id="pendingPayments">0</div>
                    <div class="stats-label">الدفعات المعلقة</div>
                    <div class="stats-change stats-down" id="paymentsChange">
                        <i class="fas fa-arrow-down"></i> -2.5%
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-widget">
                    <div class="stats-number text-info" id="averageRating">0.0</div>
                    <div class="stats-label">متوسط التقييم</div>
                    <div class="stats-change stats-up" id="ratingChange">
                        <i class="fas fa-arrow-up"></i> +0.3
                    </div>
                </div>
            </div>
        </div>

        <!-- فلاتر البحث -->
        <div class="filter-panel">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="search-box">
                        <input type="text" class="form-control" id="searchInput" 
                               placeholder="البحث في الموردين..." onkeyup="searchSuppliers()">
                        <i class="fas fa-search search-icon"></i>
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="statusFilter" onchange="applyFilters()">
                        <option value="">جميع الحالات</option>
                        <option value="approved">معتمد</option>
                        <option value="pending">في الانتظار</option>
                        <option value="suspended">معلق</option>
                        <option value="blacklisted">محظور</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="categoryFilter" onchange="applyFilters()">
                        <option value="">جميع الفئات</option>
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="ratingFilter" onchange="applyFilters()">
                        <option value="">جميع التقييمات</option>
                        <option value="5">5 نجوم</option>
                        <option value="4">4+ نجوم</option>
                        <option value="3">3+ نجوم</option>
                        <option value="2">2+ نجوم</option>
                        <option value="1">1+ نجوم</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-success" onclick="exportSuppliers()">
                            <i class="fas fa-download"></i> تصدير
                        </button>
                        <button class="btn btn-outline-primary" onclick="generateReport()">
                            <i class="fas fa-chart-bar"></i> تقرير
                        </button>
                        <button class="btn btn-outline-secondary" onclick="refreshData()">
                            <i class="fas fa-sync"></i> تحديث
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- تبويبات العرض -->
        <div class="view-tabs">
            <button class="view-tab active" onclick="setView('suppliers')" id="suppliersTab">
                <i class="fas fa-truck"></i> الموردين
            </button>
            <button class="view-tab" onclick="setView('contracts')" id="contractsTab">
                <i class="fas fa-file-contract"></i> العقود
            </button>
            <button class="view-tab" onclick="setView('evaluations')" id="evaluationsTab">
                <i class="fas fa-star"></i> التقييمات
            </button>
            <button class="view-tab" onclick="setView('payments')" id="paymentsTab">
                <i class="fas fa-money-bill"></i> الدفعات
            </button>
            <button class="view-tab" onclick="setView('categories')" id="categoriesTab">
                <i class="fas fa-tags"></i> الفئات
            </button>
            <button class="view-tab" onclick="setView('reports')" id="reportsTab">
                <i class="fas fa-chart-line"></i> التقارير
            </button>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="row">
            <!-- عرض الموردين -->
            <div id="suppliersView" class="col-12">
                <div class="row" id="suppliersGrid">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>

            <!-- عرض العقود -->
            <div id="contractsView" class="col-12" style="display: none;">
                <div id="contractsList">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>

            <!-- عرض التقييمات -->
            <div id="evaluationsView" class="col-12" style="display: none;">
                <div id="evaluationsList">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>

            <!-- عرض الدفعات -->
            <div id="paymentsView" class="col-12" style="display: none;">
                <div id="paymentsList">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>

            <!-- عرض الفئات -->
            <div id="categoriesView" class="col-12" style="display: none;">
                <div class="row" id="categoriesGrid">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>

            <!-- عرض التقارير -->
            <div id="reportsView" class="col-12" style="display: none;">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">توزيع الموردين</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="suppliersChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">أداء الموردين</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="performanceChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="../../core/database.js"></script>
    <script src="../../core/utils.js"></script>
    <script src="suppliers.js"></script>

    <script>
        // متغيرات عامة
        let suppliersManager;
        let currentView = 'suppliers';
        let currentFilters = {};
        let charts = {};

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeSuppliers();
            loadData();
            updateStatistics();
            setupEventListeners();
        });

        // تهيئة نظام الموردين
        function initializeSuppliers() {
            suppliersManager = new SuppliersManager();
            console.log('✅ تم تهيئة واجهة إدارة الموردين');
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // تحديث الإحصائيات كل دقيقة
            setInterval(updateStatistics, 60000);
        }

        // تحميل البيانات
        function loadData() {
            switch(currentView) {
                case 'suppliers':
                    loadSuppliers();
                    break;
                case 'contracts':
                    loadContracts();
                    break;
                case 'evaluations':
                    loadEvaluations();
                    break;
                case 'payments':
                    loadPayments();
                    break;
                case 'categories':
                    loadCategories();
                    break;
                case 'reports':
                    loadReports();
                    break;
            }
        }

        // تحميل الموردين
        function loadSuppliers() {
            const suppliers = Array.from(suppliersManager.suppliers.values());
            displaySuppliers(suppliers);
            loadCategoriesFilter();
        }

        // عرض الموردين
        function displaySuppliers(suppliers) {
            const grid = document.getElementById('suppliersGrid');

            if (suppliers.length === 0) {
                grid.innerHTML = `
                    <div class="col-12">
                        <div class="empty-state">
                            <i class="fas fa-truck"></i>
                            <h4>لا توجد موردين</h4>
                            <p>ابدأ بإضافة موردين جدد لإدارة سلسلة التوريد</p>
                            <button class="btn btn-success" onclick="showAddSupplierModal()">
                                <i class="fas fa-plus"></i> إضافة مورد جديد
                            </button>
                        </div>
                    </div>
                `;
                return;
            }

            grid.innerHTML = suppliers.map(supplier => createSupplierCard(supplier)).join('');
        }

        // إنشاء بطاقة مورد
        function createSupplierCard(supplier) {
            const initials = supplier.name.split(' ').map(word => word.charAt(0)).join('').substr(0, 2);
            const category = suppliersManager.supplierCategories.get(supplier.category);

            return `
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="supplier-card">
                        <div class="p-3">
                            <div class="d-flex align-items-start mb-3">
                                <div class="supplier-avatar me-3">
                                    ${initials}
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">${supplier.name}</h6>
                                    <div class="supplier-code mb-1">${supplier.code}</div>
                                    <span class="status-badge status-${supplier.status}">
                                        ${getStatusLabel(supplier.status)}
                                    </span>
                                    ${category ? `<span class="category-badge ms-2">${category.name}</span>` : ''}
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="viewSupplier('${supplier.id}')">
                                            <i class="fas fa-eye"></i> عرض
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="editSupplier('${supplier.id}')">
                                            <i class="fas fa-edit"></i> تعديل
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="evaluateSupplier('${supplier.id}')">
                                            <i class="fas fa-star"></i> تقييم
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="createContract('${supplier.id}')">
                                            <i class="fas fa-file-contract"></i> عقد جديد
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        ${supplier.status === 'approved' ?
                                            `<li><a class="dropdown-item text-warning" href="#" onclick="suspendSupplier('${supplier.id}')">
                                                <i class="fas fa-pause"></i> تعليق
                                            </a></li>` :
                                            supplier.status === 'pending' ?
                                            `<li><a class="dropdown-item text-success" href="#" onclick="approveSupplier('${supplier.id}')">
                                                <i class="fas fa-check"></i> موافقة
                                            </a></li>` : ''
                                        }
                                        <li><a class="dropdown-item text-danger" href="#" onclick="deleteSupplier('${supplier.id}')">
                                            <i class="fas fa-trash"></i> حذف
                                        </a></li>
                                    </ul>
                                </div>
                            </div>

                            <div class="contact-info">
                                <i class="fas fa-envelope"></i>
                                <span>${supplier.contact.email}</span>
                            </div>
                            <div class="contact-info">
                                <i class="fas fa-phone"></i>
                                <span>${supplier.contact.phone}</span>
                            </div>
                            <div class="contact-info">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>${supplier.address.city}, ${supplier.address.country}</span>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <div class="rating-stars">
                                    ${generateStars(supplier.rating)}
                                    <span class="ms-1">(${supplier.rating.toFixed(1)})</span>
                                </div>
                                <div class="progress-ring" style="background: conic-gradient(#28a745 ${supplier.rating * 72}deg, #e9ecef 0deg)">
                                    <div class="progress-text">${supplier.rating.toFixed(1)}</div>
                                </div>
                            </div>

                            <div class="supplier-stats">
                                <div class="stat-item">
                                    <div class="stat-value">${supplier.statistics.totalOrders}</div>
                                    <div class="stat-label">الطلبات</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">${formatCurrency(supplier.statistics.totalAmount)}</div>
                                    <div class="stat-label">إجمالي المبلغ</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">${supplier.statistics.onTimeDelivery}%</div>
                                    <div class="stat-label">التسليم في الوقت</div>
                                </div>
                            </div>

                            ${supplier.tags.length > 0 ? `
                                <div class="supplier-tags">
                                    ${supplier.tags.map(tag => `<span class="supplier-tag">${tag}</span>`).join('')}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;
        }

        // تحميل العقود
        function loadContracts() {
            const contracts = Array.from(suppliersManager.supplierContracts.values());
            displayContracts(contracts);
        }

        // عرض العقود
        function displayContracts(contracts) {
            const list = document.getElementById('contractsList');

            if (contracts.length === 0) {
                list.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-file-contract"></i>
                        <h4>لا توجد عقود</h4>
                        <p>ابدأ بإنشاء عقود مع الموردين</p>
                        <button class="btn btn-primary" onclick="showAddContractModal()">
                            <i class="fas fa-plus"></i> إنشاء عقد جديد
                        </button>
                    </div>
                `;
                return;
            }

            list.innerHTML = contracts.map(contract => createContractCard(contract)).join('');
        }

        // إنشاء بطاقة عقد
        function createContractCard(contract) {
            const supplier = suppliersManager.suppliers.get(contract.supplierId);
            const startDate = new Date(contract.startDate).toLocaleDateString('ar-EG');
            const endDate = new Date(contract.endDate).toLocaleDateString('ar-EG');

            return `
                <div class="contract-card">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h6 class="mb-1">${contract.title}</h6>
                            <p class="text-muted mb-1">${supplier ? supplier.name : 'مورد غير معروف'}</p>
                            <span class="contract-status contract-${contract.status}">
                                ${getContractStatusLabel(contract.status)}
                            </span>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold text-success">${formatCurrency(contract.value)}</div>
                            <small class="text-muted">${contract.currency}</small>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">تاريخ البداية:</small>
                            <div>${startDate}</div>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">تاريخ النهاية:</small>
                            <div>${endDate}</div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <span class="badge bg-secondary">${contract.type}</span>
                            <span class="badge bg-info ms-1">${contract.items.length} عنصر</span>
                        </div>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="viewContract('${contract.id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-secondary" onclick="editContract('${contract.id}')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteContract('${contract.id}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            const reports = suppliersManager.getSuppliersReports();

            document.getElementById('totalSuppliers').textContent = reports.suppliers.total;
            document.getElementById('activeContracts').textContent = reports.contracts.active;
            document.getElementById('pendingPayments').textContent = reports.payments.pending;
            document.getElementById('averageRating').textContent = reports.suppliers.averageRating.toFixed(1);
        }

        // تعيين العرض
        function setView(view) {
            currentView = view;

            // إخفاء جميع العروض
            document.querySelectorAll('[id$="View"]').forEach(el => {
                el.style.display = 'none';
            });

            // إزالة الفئة النشطة من جميع التبويبات
            document.querySelectorAll('.view-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // عرض العرض المحدد
            document.getElementById(view + 'View').style.display = 'block';
            document.getElementById(view + 'Tab').classList.add('active');

            // تحميل البيانات
            loadData();
        }

        // البحث في الموردين
        function searchSuppliers() {
            const query = document.getElementById('searchInput').value;
            if (!query.trim()) {
                loadSuppliers();
                return;
            }

            const results = suppliersManager.searchSuppliers(query, currentFilters);
            displaySuppliers(results);
        }

        // تطبيق الفلاتر
        function applyFilters() {
            currentFilters = {
                status: document.getElementById('statusFilter').value,
                category: document.getElementById('categoryFilter').value,
                rating: parseFloat(document.getElementById('ratingFilter').value) || 0
            };

            // إزالة الفلاتر الفارغة
            Object.keys(currentFilters).forEach(key => {
                if (!currentFilters[key]) {
                    delete currentFilters[key];
                }
            });

            searchSuppliers();
        }

        // تحميل فلتر الفئات
        function loadCategoriesFilter() {
            const categoryFilter = document.getElementById('categoryFilter');
            const categories = Array.from(suppliersManager.supplierCategories.values());

            // إضافة الخيارات
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                categoryFilter.appendChild(option);
            });
        }

        // توليد النجوم
        function generateStars(rating) {
            let stars = '';
            for (let i = 1; i <= 5; i++) {
                if (i <= rating) {
                    stars += '<i class="fas fa-star"></i>';
                } else {
                    stars += '<i class="fas fa-star empty"></i>';
                }
            }
            return stars;
        }

        // الحصول على تسمية الحالة
        function getStatusLabel(status) {
            const labels = {
                'approved': 'معتمد',
                'pending': 'في الانتظار',
                'suspended': 'معلق',
                'blacklisted': 'محظور'
            };
            return labels[status] || status;
        }

        // الحصول على تسمية حالة العقد
        function getContractStatusLabel(status) {
            const labels = {
                'draft': 'مسودة',
                'active': 'نشط',
                'expired': 'منتهي',
                'terminated': 'ملغي'
            };
            return labels[status] || status;
        }

        // تنسيق العملة
        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: 'SAR'
            }).format(amount);
        }

        // عرض الإشعارات
        function showNotification(message, type = 'info') {
            const alertClass = {
                'success': 'alert-success',
                'error': 'alert-danger',
                'warning': 'alert-warning',
                'info': 'alert-info'
            }[type] || 'alert-info';

            const notification = document.createElement('div');
            notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // دوال النوافذ المنبثقة (ستكتمل لاحقاً)
        function showAddSupplierModal() {
            showNotification('سيتم إضافة نافذة إضافة المورد قريباً', 'info');
        }

        function showAddContractModal() {
            showNotification('سيتم إضافة نافذة إنشاء العقد قريباً', 'info');
        }

        function showEvaluationModal() {
            showNotification('سيتم إضافة نافذة التقييم قريباً', 'info');
        }

        function showPaymentModal() {
            showNotification('سيتم إضافة نافذة الدفعة قريباً', 'info');
        }

        function showImportModal() {
            showNotification('سيتم إضافة نافذة الاستيراد قريباً', 'info');
        }

        // دوال إضافية (ستكتمل لاحقاً)
        function viewSupplier(supplierId) {
            // عرض تفاصيل المورد
        }

        function editSupplier(supplierId) {
            // تعديل المورد
        }

        function deleteSupplier(supplierId) {
            if (confirm('هل أنت متأكد من حذف هذا المورد؟')) {
                try {
                    suppliersManager.suppliers.delete(supplierId);
                    suppliersManager.saveSuppliers();
                    loadSuppliers();
                    updateStatistics();
                    showNotification('تم حذف المورد بنجاح', 'success');
                } catch (error) {
                    showNotification('خطأ في حذف المورد: ' + error.message, 'error');
                }
            }
        }

        function approveSupplier(supplierId) {
            try {
                suppliersManager.approveSupplier(supplierId);
                loadSuppliers();
                updateStatistics();
                showNotification('تم الموافقة على المورد بنجاح', 'success');
            } catch (error) {
                showNotification('خطأ في الموافقة على المورد: ' + error.message, 'error');
            }
        }

        function suspendSupplier(supplierId) {
            const reason = prompt('سبب التعليق:');
            if (reason) {
                try {
                    suppliersManager.suspendSupplier(supplierId, reason);
                    loadSuppliers();
                    updateStatistics();
                    showNotification('تم تعليق المورد بنجاح', 'success');
                } catch (error) {
                    showNotification('خطأ في تعليق المورد: ' + error.message, 'error');
                }
            }
        }

        function evaluateSupplier(supplierId) {
            // تقييم المورد
        }

        function createContract(supplierId) {
            // إنشاء عقد للمورد
        }

        function viewContract(contractId) {
            // عرض تفاصيل العقد
        }

        function editContract(contractId) {
            // تعديل العقد
        }

        function deleteContract(contractId) {
            if (confirm('هل أنت متأكد من حذف هذا العقد؟')) {
                try {
                    suppliersManager.supplierContracts.delete(contractId);
                    suppliersManager.saveContracts();
                    loadContracts();
                    updateStatistics();
                    showNotification('تم حذف العقد بنجاح', 'success');
                } catch (error) {
                    showNotification('خطأ في حذف العقد: ' + error.message, 'error');
                }
            }
        }

        function loadEvaluations() {
            // تحميل التقييمات
        }

        function loadPayments() {
            // تحميل الدفعات
        }

        function loadCategories() {
            // تحميل الفئات
        }

        function loadReports() {
            // تحميل التقارير
        }

        function exportSuppliers() {
            // تصدير الموردين
        }

        function generateReport() {
            // إنشاء تقرير
        }

        function refreshData() {
            loadData();
            updateStatistics();
            showNotification('تم تحديث البيانات بنجاح', 'success');
        }
    </script>
</body>
</html>
