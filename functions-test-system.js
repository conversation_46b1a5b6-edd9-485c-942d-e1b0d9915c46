/**
 * Black Horse POS - Functions Test System
 * نظام اختبار الوظائف والأزرار
 * Developer: Augment Agent
 */

// ===== FUNCTIONS TEST SYSTEM =====
const FunctionsTestSystem = {
    // قائمة الوظائف للاختبار
    testCategories: {
        // وظائف التنقل
        navigation: [
            'showPage', 'toggleDropdown'
        ],
        
        // وظائف الحفظ والإضافة
        saveAndAdd: [
            'addProduct', 'addCustomer', 'addSupplier', 'addExpense'
        ],
        
        // وظائف البحث
        search: [
            'searchProducts', 'searchCustomers', 'searchSuppliers'
        ],
        
        // وظائف نقطة البيع
        pos: [
            'addToCart', 'clearCart', 'processPayment'
        ],
        
        // وظائف النوافذ المنبثقة
        modals: [
            'showAddProductModal', 'showAddCustomerModal', 'closeModal'
        ],
        
        // وظائف التصدير والطباعة
        exportPrint: [
            'exportProducts', 'printDailySalesReport'
        ],
        
        // وظائف التقارير
        reports: [
            'generateSalesReport', 'generateInventoryReport'
        ],
        
        // وظائف متنوعة
        misc: [
            'refreshDashboard', 'openCashDrawer'
        ]
    },

    // نتائج الاختبار
    testResults: {
        passed: [],
        failed: [],
        missing: []
    },

    // تشغيل جميع الاختبارات
    runAllTests: function() {
        try {
            console.log('🧪 Starting comprehensive function tests...');
            
            // إعادة تعيين النتائج
            this.testResults = { passed: [], failed: [], missing: [] };
            
            // اختبار كل فئة
            Object.keys(this.testCategories).forEach(category => {
                console.log(`\n📋 Testing ${category} functions...`);
                this.testCategory(category);
            });
            
            // عرض النتائج النهائية
            this.displayResults();
            
            console.log('✅ All function tests completed');
        } catch (error) {
            console.error('❌ Error running tests:', error);
        }
    },

    // اختبار فئة معينة
    testCategory: function(category) {
        try {
            const functions = this.testCategories[category];
            
            functions.forEach(funcName => {
                const result = this.testFunction(funcName);
                console.log(`  ${result.status} ${funcName}: ${result.message}`);
            });
            
        } catch (error) {
            console.error(`❌ Error testing category ${category}:`, error);
        }
    },

    // اختبار وظيفة واحدة
    testFunction: function(funcName) {
        try {
            // التحقق من وجود الوظيفة
            if (typeof window[funcName] !== 'function') {
                this.testResults.missing.push(funcName);
                return {
                    status: '❌',
                    message: 'Function not found'
                };
            }

            // اختبار خاص لكل وظيفة
            const testResult = this.runSpecificTest(funcName);
            
            if (testResult.success) {
                this.testResults.passed.push(funcName);
                return {
                    status: '✅',
                    message: testResult.message || 'Test passed'
                };
            } else {
                this.testResults.failed.push(funcName);
                return {
                    status: '⚠️',
                    message: testResult.message || 'Test failed'
                };
            }
            
        } catch (error) {
            this.testResults.failed.push(funcName);
            return {
                status: '❌',
                message: `Error: ${error.message}`
            };
        }
    },

    // تشغيل اختبار خاص لكل وظيفة
    runSpecificTest: function(funcName) {
        try {
            switch (funcName) {
                case 'showPage':
                    // اختبار التنقل
                    const currentPage = document.querySelector('.page.active');
                    return {
                        success: true,
                        message: 'Navigation function available'
                    };

                case 'toggleDropdown':
                    // اختبار القوائم المنسدلة
                    return {
                        success: typeof window.toggleDropdown === 'function',
                        message: 'Dropdown function available'
                    };

                case 'addProduct':
                    // اختبار إضافة منتج
                    return {
                        success: typeof window.addProduct === 'function',
                        message: 'Add product function available'
                    };

                case 'addCustomer':
                    // اختبار إضافة عميل
                    return {
                        success: typeof window.addCustomer === 'function',
                        message: 'Add customer function available'
                    };

                case 'searchProducts':
                    // اختبار البحث في المنتجات
                    return {
                        success: typeof window.searchProducts === 'function',
                        message: 'Product search function available'
                    };

                case 'addToCart':
                    // اختبار إضافة للسلة
                    return {
                        success: typeof window.addToCart === 'function',
                        message: 'Add to cart function available'
                    };

                case 'processPayment':
                    // اختبار معالجة الدفع
                    return {
                        success: typeof window.processPayment === 'function',
                        message: 'Payment processing function available'
                    };

                case 'exportProducts':
                    // اختبار تصدير المنتجات
                    return {
                        success: typeof window.exportProducts === 'function',
                        message: 'Export products function available'
                    };

                case 'closeModal':
                    // اختبار إغلاق النوافذ
                    return {
                        success: typeof window.closeModal === 'function',
                        message: 'Close modal function available'
                    };

                default:
                    // اختبار عام للوظائف الأخرى
                    return {
                        success: typeof window[funcName] === 'function',
                        message: 'Function exists and callable'
                    };
            }
        } catch (error) {
            return {
                success: false,
                message: `Test error: ${error.message}`
            };
        }
    },

    // عرض النتائج
    displayResults: function() {
        try {
            console.log('\n📊 TEST RESULTS SUMMARY');
            console.log('========================');
            
            const total = this.testResults.passed.length + 
                         this.testResults.failed.length + 
                         this.testResults.missing.length;
            
            console.log(`✅ Passed: ${this.testResults.passed.length}`);
            console.log(`⚠️ Failed: ${this.testResults.failed.length}`);
            console.log(`❌ Missing: ${this.testResults.missing.length}`);
            console.log(`📊 Total: ${total}`);
            
            const successRate = ((this.testResults.passed.length / total) * 100).toFixed(1);
            console.log(`🎯 Success Rate: ${successRate}%`);
            
            // عرض الوظائف المفقودة
            if (this.testResults.missing.length > 0) {
                console.log('\n❌ Missing Functions:');
                this.testResults.missing.forEach(func => {
                    console.log(`  - ${func}`);
                });
            }
            
            // عرض الوظائف الفاشلة
            if (this.testResults.failed.length > 0) {
                console.log('\n⚠️ Failed Functions:');
                this.testResults.failed.forEach(func => {
                    console.log(`  - ${func}`);
                });
            }
            
            // إنشاء تقرير HTML
            this.generateHTMLReport();
            
        } catch (error) {
            console.error('❌ Error displaying results:', error);
        }
    },

    // إنشاء تقرير HTML
    generateHTMLReport: function() {
        try {
            const total = this.testResults.passed.length + 
                         this.testResults.failed.length + 
                         this.testResults.missing.length;
            
            const successRate = ((this.testResults.passed.length / total) * 100).toFixed(1);
            
            const reportHTML = `
                <div id="testReport" style="
                    position: fixed; top: 20px; right: 20px; 
                    background: white; border: 2px solid #007bff; 
                    border-radius: 8px; padding: 20px; 
                    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                    z-index: 10000; max-width: 400px;
                    font-family: Arial, sans-serif;
                ">
                    <h3 style="margin: 0 0 15px 0; color: #007bff;">
                        🧪 تقرير اختبار الوظائف
                    </h3>
                    <div style="margin-bottom: 10px;">
                        <strong>معدل النجاح: ${successRate}%</strong>
                    </div>
                    <div style="margin-bottom: 5px;">
                        ✅ نجح: ${this.testResults.passed.length}
                    </div>
                    <div style="margin-bottom: 5px;">
                        ⚠️ فشل: ${this.testResults.failed.length}
                    </div>
                    <div style="margin-bottom: 15px;">
                        ❌ مفقود: ${this.testResults.missing.length}
                    </div>
                    <button onclick="document.getElementById('testReport').remove()" 
                            style="background: #007bff; color: white; border: none; 
                                   padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                        إغلاق
                    </button>
                </div>
            `;
            
            // إزالة التقرير السابق إن وجد
            const existingReport = document.getElementById('testReport');
            if (existingReport) {
                existingReport.remove();
            }
            
            // إضافة التقرير الجديد
            document.body.insertAdjacentHTML('beforeend', reportHTML);
            
        } catch (error) {
            console.error('❌ Error generating HTML report:', error);
        }
    },

    // اختبار سريع للوظائف الأساسية
    quickTest: function() {
        try {
            console.log('⚡ Running quick function test...');
            
            const essentialFunctions = [
                'showPage', 'toggleDropdown', 'addProduct', 
                'addCustomer', 'searchProducts', 'processPayment'
            ];
            
            let working = 0;
            let total = essentialFunctions.length;
            
            essentialFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    working++;
                    console.log(`✅ ${funcName} - Working`);
                } else {
                    console.log(`❌ ${funcName} - Missing`);
                }
            });
            
            const rate = ((working / total) * 100).toFixed(1);
            console.log(`\n⚡ Quick Test Result: ${working}/${total} (${rate}%)`);
            
            return { working, total, rate };
            
        } catch (error) {
            console.error('❌ Error in quick test:', error);
            return { working: 0, total: 0, rate: 0 };
        }
    }
};

// إضافة أزرار الاختبار للواجهة
document.addEventListener('DOMContentLoaded', function() {
    try {
        // إضافة زر الاختبار السريع
        const quickTestBtn = document.createElement('button');
        quickTestBtn.innerHTML = '⚡ اختبار سريع';
        quickTestBtn.style.cssText = `
            position: fixed; bottom: 20px; left: 20px; 
            background: #28a745; color: white; border: none; 
            padding: 10px 15px; border-radius: 5px; 
            cursor: pointer; z-index: 9999;
            font-family: Arial, sans-serif;
        `;
        quickTestBtn.onclick = () => FunctionsTestSystem.quickTest();
        document.body.appendChild(quickTestBtn);
        
        // إضافة زر الاختبار الشامل
        const fullTestBtn = document.createElement('button');
        fullTestBtn.innerHTML = '🧪 اختبار شامل';
        fullTestBtn.style.cssText = `
            position: fixed; bottom: 20px; left: 140px; 
            background: #007bff; color: white; border: none; 
            padding: 10px 15px; border-radius: 5px; 
            cursor: pointer; z-index: 9999;
            font-family: Arial, sans-serif;
        `;
        fullTestBtn.onclick = () => FunctionsTestSystem.runAllTests();
        document.body.appendChild(fullTestBtn);
        
    } catch (error) {
        console.error('❌ Error adding test buttons:', error);
    }
});

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.FunctionsTestSystem = FunctionsTestSystem;
    window.testAllFunctions = () => FunctionsTestSystem.runAllTests();
    window.quickTestFunctions = () => FunctionsTestSystem.quickTest();
}

console.log('✅ Functions Test System loaded successfully');
