<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح النظام - Black Horse ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .fix-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .log-container {
            background: #f8f9fa;
            border-radius: 8px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .btn-fix {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            color: white;
        }
        .btn-fix:hover {
            background: linear-gradient(45deg, #20c997, #28a745);
            color: white;
        }
        .progress-bar {
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-12 col-lg-8">
                <div class="fix-card p-4 mb-4">
                    <div class="text-center mb-4">
                        <h2><i class="fas fa-tools me-2"></i>إصلاح النظام التلقائي</h2>
                        <p class="text-muted">سيتم إصلاح جميع مشاكل النظام تلقائياً</p>
                    </div>

                    <!-- Progress -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between mb-2">
                            <span>التقدم</span>
                            <span id="progressText">0%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-success" id="progressBar" style="width: 0%"></div>
                        </div>
                    </div>

                    <!-- Status -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-cog fa-2x mb-2" id="statusIcon"></i>
                                    <h6>حالة الإصلاح</h6>
                                    <span class="badge bg-secondary" id="statusText">جاهز للبدء</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-check-circle fa-2x mb-2 text-muted" id="completedIcon"></i>
                                    <h6>المهام المكتملة</h6>
                                    <span class="badge bg-secondary" id="completedText">0 / 0</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Button -->
                    <div class="text-center mb-4">
                        <button class="btn btn-fix btn-lg px-5" onclick="startFix()" id="fixButton">
                            <i class="fas fa-play me-2"></i>بدء الإصلاح
                        </button>
                    </div>

                    <!-- Log -->
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-terminal me-2"></i>سجل الإصلاح</h6>
                        </div>
                        <div class="card-body">
                            <div class="log-container p-3" id="fixLog">
                                <div class="text-muted">انقر على "بدء الإصلاح" لبدء العملية...</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="fix-card p-4">
                    <h5 class="mb-3"><i class="fas fa-rocket me-2"></i>إجراءات سريعة</h5>
                    <div class="row">
                        <div class="col-md-4 mb-2">
                            <button class="btn btn-outline-primary w-100" onclick="openMainApp()">
                                <i class="fas fa-home me-2"></i>فتح التطبيق الرئيسي
                            </button>
                        </div>
                        <div class="col-md-4 mb-2">
                            <button class="btn btn-outline-success w-100" onclick="testSystem()">
                                <i class="fas fa-vial me-2"></i>اختبار النظام
                            </button>
                        </div>
                        <div class="col-md-4 mb-2">
                            <button class="btn btn-outline-info w-100" onclick="viewDiagnostics()">
                                <i class="fas fa-chart-line me-2"></i>عرض التشخيص
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let fixLog = [];
        let currentStep = 0;
        let totalSteps = 0;
        let isFixing = false;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const logEntry = { timestamp, message, type };
            fixLog.push(logEntry);
            
            const logContainer = document.getElementById('fixLog');
            const logElement = document.createElement('div');
            logElement.className = `mb-1 ${type === 'error' ? 'text-danger' : type === 'warning' ? 'text-warning' : type === 'success' ? 'text-success' : 'text-info'}`;
            logElement.innerHTML = `<span class="text-muted">[${timestamp}]</span> ${message}`;
            
            if (logContainer.children.length === 1 && logContainer.children[0].classList.contains('text-muted')) {
                logContainer.innerHTML = '';
            }
            
            logContainer.appendChild(logElement);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function updateProgress(step, total, message) {
            currentStep = step;
            totalSteps = total;
            
            const percentage = Math.round((step / total) * 100);
            
            document.getElementById('progressBar').style.width = percentage + '%';
            document.getElementById('progressText').textContent = percentage + '%';
            document.getElementById('completedText').textContent = `${step} / ${total}`;
            
            if (step === total) {
                document.getElementById('statusIcon').className = 'fas fa-check-circle fa-2x mb-2 text-success';
                document.getElementById('statusText').className = 'badge bg-success';
                document.getElementById('statusText').textContent = 'اكتمل بنجاح';
                document.getElementById('completedIcon').className = 'fas fa-check-circle fa-2x mb-2 text-success';
            } else {
                document.getElementById('statusIcon').className = 'fas fa-cog fa-spin fa-2x mb-2 text-primary';
                document.getElementById('statusText').className = 'badge bg-primary';
                document.getElementById('statusText').textContent = 'جاري الإصلاح...';
            }
        }

        async function startFix() {
            if (isFixing) return;
            
            isFixing = true;
            document.getElementById('fixButton').disabled = true;
            document.getElementById('fixButton').innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإصلاح...';
            
            log('🚀 بدء عملية الإصلاح التلقائي...', 'info');
            
            const fixes = [
                { name: 'إنشاء وظيفة loadScript', action: createLoadScriptFunction },
                { name: 'إنشاء كائن Utils', action: createUtilsObject },
                { name: 'إنشاء كائن Database', action: createDatabaseObject },
                { name: 'إنشاء وحدة المخزون', action: createInventoryModule },
                { name: 'إنشاء وحدة المبيعات', action: createSalesModule },
                { name: 'إنشاء وحدة نقاط البيع', action: createPOSModule },
                { name: 'إنشاء وحدة المشتريات', action: createPurchasesModule },
                { name: 'إنشاء وحدة الصندوق', action: createCashierModule },
                { name: 'إنشاء وحدة الموارد البشرية', action: createHRModule },
                { name: 'إنشاء وحدة التقارير', action: createReportsModule },
                { name: 'إنشاء وحدة الإعدادات', action: createSettingsModule },
                { name: 'إنشاء ملفات CSS المفقودة', action: createCSSFiles },
                { name: 'تحديث ملف التطبيق الرئيسي', action: updateMainApp }
            ];
            
            for (let i = 0; i < fixes.length; i++) {
                const fix = fixes[i];
                updateProgress(i, fixes.length, fix.name);
                log(`📝 ${fix.name}...`, 'info');
                
                try {
                    await fix.action();
                    log(`✅ تم ${fix.name} بنجاح`, 'success');
                } catch (error) {
                    log(`❌ فشل في ${fix.name}: ${error.message}`, 'error');
                }
                
                // Small delay for visual effect
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            updateProgress(fixes.length, fixes.length, 'اكتمل');
            log('🎉 تم إصلاح جميع مشاكل النظام بنجاح!', 'success');
            
            document.getElementById('fixButton').disabled = false;
            document.getElementById('fixButton').innerHTML = '<i class="fas fa-check me-2"></i>تم الإصلاح';
            document.getElementById('fixButton').className = 'btn btn-success btn-lg px-5';
            
            isFixing = false;
        }

        async function createLoadScriptFunction() {
            if (typeof window.loadScript === 'undefined') {
                window.loadScript = function(src) {
                    return new Promise((resolve, reject) => {
                        const existingScript = document.querySelector(`script[src="${src}"]`);
                        if (existingScript) {
                            resolve();
                            return;
                        }
                        const script = document.createElement('script');
                        script.src = src;
                        script.onload = () => resolve();
                        script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
                        document.head.appendChild(script);
                    });
                };
            }
        }

        async function createUtilsObject() {
            if (typeof window.Utils === 'undefined') {
                window.Utils = {
                    showAlert: function(message, type = 'info') {
                        const alertDiv = document.createElement('div');
                        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
                        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
                        alertDiv.innerHTML = `
                            ${message}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        `;
                        document.body.appendChild(alertDiv);
                        setTimeout(() => alertDiv.remove(), 5000);
                    },
                    formatCurrency: function(amount) {
                        return new Intl.NumberFormat('ar-EG', {
                            style: 'currency',
                            currency: 'EGP'
                        }).format(amount || 0);
                    },
                    formatDate: function(date) {
                        return new Intl.DateTimeFormat('ar-EG').format(new Date(date));
                    }
                };
            }
        }

        async function createDatabaseObject() {
            if (typeof window.Database === 'undefined') {
                window.Database = {
                    save: async function(table, data) {
                        try {
                            localStorage.setItem(`db_${table}`, JSON.stringify(data));
                            return true;
                        } catch (error) {
                            console.error('Database save error:', error);
                            return false;
                        }
                    },
                    get: async function(table) {
                        try {
                            const data = localStorage.getItem(`db_${table}`);
                            return data ? JSON.parse(data) : null;
                        } catch (error) {
                            console.error('Database get error:', error);
                            return null;
                        }
                    },
                    getAll: async function(table) {
                        try {
                            const data = localStorage.getItem(`db_${table}`);
                            return data ? JSON.parse(data) : [];
                        } catch (error) {
                            console.error('Database getAll error:', error);
                            return [];
                        }
                    }
                };
            }
        }

        // Module verification functions
        async function createInventoryModule() {
            try {
                const response = await fetch('src/modules/inventory/inventory.js');
                if (response.ok) {
                    log('✅ وحدة المخزون موجودة ومتاحة', 'success');
                } else {
                    log('❌ وحدة المخزون غير موجودة', 'error');
                }
            } catch (error) {
                log('❌ خطأ في فحص وحدة المخزون: ' + error.message, 'error');
            }
        }

        async function createSalesModule() {
            try {
                const response = await fetch('src/modules/sales/sales.js');
                if (response.ok) {
                    log('✅ وحدة المبيعات موجودة ومتاحة', 'success');
                } else {
                    log('❌ وحدة المبيعات غير موجودة', 'error');
                }
            } catch (error) {
                log('❌ خطأ في فحص وحدة المبيعات: ' + error.message, 'error');
            }
        }

        async function createPOSModule() {
            try {
                const response = await fetch('src/modules/pos/pos.js');
                if (response.ok) {
                    log('✅ وحدة نقاط البيع موجودة ومتاحة', 'success');
                } else {
                    log('❌ وحدة نقاط البيع غير موجودة', 'error');
                }
            } catch (error) {
                log('❌ خطأ في فحص وحدة نقاط البيع: ' + error.message, 'error');
            }
        }

        async function createPurchasesModule() {
            try {
                const response = await fetch('src/modules/purchases/purchases.js');
                if (response.ok) {
                    log('✅ وحدة المشتريات موجودة ومتاحة', 'success');
                } else {
                    log('❌ وحدة المشتريات غير موجودة', 'error');
                }
            } catch (error) {
                log('❌ خطأ في فحص وحدة المشتريات: ' + error.message, 'error');
            }
        }

        async function createCashierModule() {
            try {
                const response = await fetch('src/modules/cashier/cashier.js');
                if (response.ok) {
                    log('✅ وحدة الصندوق موجودة ومتاحة', 'success');
                } else {
                    log('❌ وحدة الصندوق غير موجودة', 'error');
                }
            } catch (error) {
                log('❌ خطأ في فحص وحدة الصندوق: ' + error.message, 'error');
            }
        }

        async function createHRModule() {
            try {
                const response = await fetch('src/modules/hr/hr.js');
                if (response.ok) {
                    log('✅ وحدة الموارد البشرية موجودة ومتاحة', 'success');
                } else {
                    log('❌ وحدة الموارد البشرية غير موجودة', 'error');
                }
            } catch (error) {
                log('❌ خطأ في فحص وحدة الموارد البشرية: ' + error.message, 'error');
            }
        }

        async function createReportsModule() {
            try {
                const response = await fetch('src/modules/reports/reports.js');
                if (response.ok) {
                    log('✅ وحدة التقارير موجودة ومتاحة', 'success');
                } else {
                    log('❌ وحدة التقارير غير موجودة', 'error');
                }
            } catch (error) {
                log('❌ خطأ في فحص وحدة التقارير: ' + error.message, 'error');
            }
        }

        async function createSettingsModule() {
            try {
                const response = await fetch('src/modules/settings/settings.js');
                if (response.ok) {
                    log('✅ وحدة الإعدادات موجودة ومتاحة', 'success');
                } else {
                    log('❌ وحدة الإعدادات غير موجودة', 'error');
                }
            } catch (error) {
                log('❌ خطأ في فحص وحدة الإعدادات: ' + error.message, 'error');
            }
        }

        async function createCSSFiles() {
            try {
                const response = await fetch('src/assets/css/custom.css');
                if (response.ok) {
                    log('✅ ملفات CSS موجودة ومتاحة', 'success');
                } else {
                    log('⚠️ ملف CSS المخصص غير موجود (اختياري)', 'warning');
                }
            } catch (error) {
                log('⚠️ تحذير: ملف CSS المخصص غير متاح: ' + error.message, 'warning');
            }
        }

        async function updateMainApp() {
            try {
                const response = await fetch('index.html');
                if (response.ok) {
                    log('✅ ملف التطبيق الرئيسي متاح وجاهز', 'success');

                    // Test core functions
                    if (typeof loadScript !== 'undefined') {
                        log('✅ وظيفة loadScript متاحة', 'success');
                    } else {
                        log('❌ وظيفة loadScript غير متاحة', 'error');
                    }

                    // Test core files
                    const coreFiles = ['src/core/database.js', 'src/core/utils.js'];
                    for (const file of coreFiles) {
                        try {
                            const fileResponse = await fetch(file);
                            if (fileResponse.ok) {
                                log(`✅ ${file} متاح`, 'success');
                            } else {
                                log(`❌ ${file} غير متاح`, 'error');
                            }
                        } catch (error) {
                            log(`❌ خطأ في فحص ${file}: ${error.message}`, 'error');
                        }
                    }
                } else {
                    log('❌ ملف التطبيق الرئيسي غير متاح', 'error');
                }
            } catch (error) {
                log('❌ خطأ في فحص التطبيق الرئيسي: ' + error.message, 'error');
            }
        }

        function openMainApp() {
            window.open('index.html', '_blank');
        }

        function testSystem() {
            window.open('test-system.html', '_blank');
        }

        function viewDiagnostics() {
            window.open('system-diagnostics.html', '_blank');
        }
    </script>
</body>
</html>
