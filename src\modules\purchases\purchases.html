<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المشتريات - Black Horse ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .module-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
        }
        .btn-gradient:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            color: white;
        }
        .nav-tabs .nav-link {
            border: none;
            color: #667eea;
            font-weight: 600;
        }
        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0;
        }
        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-approved { background: #d1ecf1; color: #0c5460; }
        .status-received { background: #d4edda; color: #155724; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="module-card p-4 mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-shopping-bag me-2"></i>إدارة المشتريات</h2>
                    <p class="text-muted mb-0">إدارة شاملة للمشتريات والموردين وطلبات الشراء</p>
                </div>
                <button class="btn btn-gradient" onclick="showAddPurchaseModal()">
                    <i class="fas fa-plus me-2"></i>طلب شراء جديد
                </button>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                    <h4 id="totalPurchases">0</h4>
                    <p>إجمالي المشتريات</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h4 id="pendingOrders">0</h4>
                    <p>طلبات معلقة</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-truck fa-2x mb-2"></i>
                    <h4 id="totalSuppliers">0</h4>
                    <p>إجمالي الموردين</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-calendar-day fa-2x mb-2"></i>
                    <h4 id="monthlyPurchases">0</h4>
                    <p>مشتريات الشهر</p>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="module-card p-4">
            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs mb-4" id="purchasesTabs">
                <li class="nav-item">
                    <a class="nav-link active" data-bs-toggle="tab" href="#ordersTab">طلبات الشراء</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#suppliersTab">الموردين</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#receivedTab">البضائع المستلمة</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#reportsTab">التقارير</a>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Purchase Orders Tab -->
                <div class="tab-pane fade show active" id="ordersTab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>طلبات الشراء</h5>
                        <div class="d-flex gap-2">
                            <select class="form-select" id="orderStatusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="pending">معلق</option>
                                <option value="approved">موافق عليه</option>
                                <option value="received">مستلم</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                            <input type="text" class="form-control" placeholder="البحث..." id="ordersSearch">
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>المورد</th>
                                    <th>التاريخ</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="ordersTableBody">
                                <!-- Orders data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Suppliers Tab -->
                <div class="tab-pane fade" id="suppliersTab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>قائمة الموردين</h5>
                        <button class="btn btn-gradient" onclick="showAddSupplierModal()">
                            <i class="fas fa-plus me-2"></i>مورد جديد
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>اسم المورد</th>
                                    <th>الهاتف</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>إجمالي المشتريات</th>
                                    <th>التقييم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="suppliersTableBody">
                                <!-- Suppliers data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Received Goods Tab -->
                <div class="tab-pane fade" id="receivedTab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>البضائع المستلمة</h5>
                        <div class="d-flex gap-2">
                            <input type="date" class="form-control" id="receivedDateFilter">
                            <input type="text" class="form-control" placeholder="البحث..." id="receivedSearch">
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الاستلام</th>
                                    <th>رقم الطلب</th>
                                    <th>المورد</th>
                                    <th>تاريخ الاستلام</th>
                                    <th>المبلغ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="receivedTableBody">
                                <!-- Received goods data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Reports Tab -->
                <div class="tab-pane fade" id="reportsTab">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>مشتريات الشهر</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="monthlyPurchasesChart"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>أفضل الموردين</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="topSuppliersChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6>تقرير المشتريات التفصيلي</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-md-3">
                                            <label class="form-label">من تاريخ</label>
                                            <input type="date" class="form-control" id="reportFromDate">
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">إلى تاريخ</label>
                                            <input type="date" class="form-control" id="reportToDate">
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">المورد</label>
                                            <select class="form-select" id="reportSupplier">
                                                <option value="">جميع الموردين</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">&nbsp;</label>
                                            <button class="btn btn-gradient d-block w-100" onclick="generateReport()">
                                                <i class="fas fa-chart-bar me-2"></i>إنشاء التقرير
                                            </button>
                                        </div>
                                    </div>
                                    <div id="reportResults"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Purchase Order Modal -->
    <div class="modal fade" id="addPurchaseModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">طلب شراء جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addPurchaseForm">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">المورد</label>
                                <select class="form-select" id="purchaseSupplier" required>
                                    <option value="">اختر المورد</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">تاريخ الطلب</label>
                                <input type="date" class="form-control" id="purchaseDate" required>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">تاريخ التسليم المتوقع</label>
                                <input type="date" class="form-control" id="expectedDelivery">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">الأولوية</label>
                                <select class="form-select" id="purchasePriority">
                                    <option value="normal">عادي</option>
                                    <option value="high">عالي</option>
                                    <option value="urgent">عاجل</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <label class="form-label">المنتجات</label>
                                <div id="purchaseProducts">
                                    <!-- Products will be added here -->
                                </div>
                                <button type="button" class="btn btn-outline-primary mt-2" onclick="addProductToPurchase()">
                                    <i class="fas fa-plus me-2"></i>إضافة منتج
                                </button>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">إجمالي المبلغ</label>
                                <input type="number" class="form-control" id="totalPurchaseAmount" readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="purchaseNotes" rows="2"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-gradient" onclick="savePurchaseOrder()">حفظ الطلب</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Supplier Modal -->
    <div class="modal fade" id="addSupplierModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">مورد جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addSupplierForm">
                        <div class="mb-3">
                            <label class="form-label">اسم المورد</label>
                            <input type="text" class="form-control" id="supplierName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="supplierPhone" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="supplierEmail">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-control" id="supplierAddress" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">نوع المنتجات</label>
                            <input type="text" class="form-control" id="supplierCategory">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-gradient" onclick="saveSupplier()">حفظ المورد</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="purchases.js"></script>
</body>
</html>
