<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المخازن المتقدمة - Black Horse POS</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .page-header h1 {
            color: #2c3e50;
            font-weight: 700;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }

        .page-header p {
            color: #6c757d;
            font-size: 1.1rem;
            margin: 0;
        }

        .stats-section {
            margin-bottom: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
            pointer-events: none;
        }

        .stat-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.9;
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }

        .view-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .view-tab {
            padding: 12px 24px;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            text-decoration: none;
            color: #495057;
        }

        .view-tab:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            color: #495057;
            text-decoration: none;
        }

        .view-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }

        .filters-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
        }

        .quick-actions {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .quick-action-btn {
            padding: 12px 24px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
            color: white;
            text-decoration: none;
        }

        .warehouses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }

        .warehouse-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .warehouse-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .warehouse-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .warehouse-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .warehouse-code {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .warehouse-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .status-maintenance {
            background: #fff3cd;
            color: #856404;
        }

        .warehouse-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .stat-item .stat-value {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-item .stat-label {
            font-size: 0.8rem;
            color: #6c757d;
        }

        .warehouse-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .action-btn {
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            background: white;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            color: #495057;
        }

        .action-btn:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h4 {
            margin-bottom: 15px;
            color: #495057;
        }

        .empty-state p {
            margin-bottom: 25px;
            font-size: 1.1rem;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
            border: none;
        }

        .card-header h5 {
            margin: 0;
            font-weight: 600;
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 1px solid #e9ecef;
            padding: 12px 15px;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .modal-content {
            border-radius: 20px;
            border: none;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .modal-header {
            border-radius: 20px 20px 0 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }

        .modal-footer {
            border: none;
            padding: 20px 30px;
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 20px;
                margin: 10px;
            }
            
            .page-header h1 {
                font-size: 2rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .warehouses-grid {
                grid-template-columns: 1fr;
            }
            
            .view-tabs {
                justify-content: center;
            }
            
            .quick-actions {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <h1><i class="fas fa-warehouse"></i> إدارة المخازن المتقدمة</h1>
            <p>نظام شامل لإدارة المخازن والمواقع وحركة المخزون</p>
        </div>

        <!-- قسم الإحصائيات -->
        <div class="stats-section">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-warehouse"></i>
                    </div>
                    <div class="stat-value" id="totalWarehouses">0</div>
                    <div class="stat-label">إجمالي المخازن</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="stat-value" id="totalLocations">0</div>
                    <div class="stat-label">إجمالي المواقع</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-value" id="utilizationRate">0%</div>
                    <div class="stat-label">معدل الاستخدام</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="stat-value" id="todayMovements">0</div>
                    <div class="stat-label">حركات اليوم</div>
                </div>
            </div>
        </div>

        <!-- تبويبات العرض -->
        <div class="view-tabs">
            <a href="#" class="view-tab active" id="warehousesTab" onclick="setView('warehouses')">
                <i class="fas fa-warehouse"></i> المخازن
            </a>
            <a href="#" class="view-tab" id="zonesTab" onclick="setView('zones')">
                <i class="fas fa-th-large"></i> المناطق
            </a>
            <a href="#" class="view-tab" id="locationsTab" onclick="setView('locations')">
                <i class="fas fa-map-marker-alt"></i> المواقع
            </a>
            <a href="#" class="view-tab" id="movementsTab" onclick="setView('movements')">
                <i class="fas fa-exchange-alt"></i> الحركات
            </a>
            <a href="#" class="view-tab" id="transfersTab" onclick="setView('transfers')">
                <i class="fas fa-truck"></i> النقل
            </a>
            <a href="#" class="view-tab" id="reportsTab" onclick="setView('reports')">
                <i class="fas fa-chart-bar"></i> التقارير
            </a>
        </div>

        <!-- قسم الفلاتر -->
        <div class="filters-section">
            <div class="row">
                <div class="col-md-3">
                    <input type="text" class="form-control" id="searchInput" placeholder="البحث..." onkeyup="searchWarehouses()">
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="typeFilter" onchange="applyFilters()">
                        <option value="">جميع الأنواع</option>
                        <option value="main">رئيسي</option>
                        <option value="branch">فرعي</option>
                        <option value="temporary">مؤقت</option>
                        <option value="external">خارجي</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="statusFilter" onchange="applyFilters()">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                        <option value="maintenance">صيانة</option>
                        <option value="closed">مغلق</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="cityFilter" onchange="applyFilters()">
                        <option value="">جميع المدن</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary" onclick="applyFilters()">
                            <i class="fas fa-filter"></i> تطبيق
                        </button>
                        <button class="btn btn-outline-secondary" onclick="clearFilters()">
                            <i class="fas fa-times"></i> مسح
                        </button>
                        <button class="btn btn-outline-success" onclick="refreshData()">
                            <i class="fas fa-sync"></i> تحديث
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="content-area">
            <!-- عرض المخازن -->
            <div id="warehousesView">
                <div class="quick-actions">
                    <button class="quick-action-btn" onclick="showCreateWarehouseModal()">
                        <i class="fas fa-plus"></i> مخزن جديد
                    </button>
                    <button class="quick-action-btn" onclick="showImportWarehousesModal()">
                        <i class="fas fa-upload"></i> استيراد
                    </button>
                    <button class="quick-action-btn" onclick="exportWarehouses()">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                    <button class="quick-action-btn" onclick="showWarehouseSettings()">
                        <i class="fas fa-cog"></i> الإعدادات
                    </button>
                </div>
                
                <!-- شبكة المخازن -->
                <div class="warehouses-grid" id="warehousesGrid">
                    <!-- سيتم ملء المخازن هنا -->
                </div>
            </div>

            <!-- عرض المناطق -->
            <div id="zonesView" style="display: none;">
                <div class="quick-actions">
                    <button class="quick-action-btn" onclick="showCreateZoneModal()">
                        <i class="fas fa-plus"></i> منطقة جديدة
                    </button>
                    <button class="quick-action-btn" onclick="showZoneLayout()">
                        <i class="fas fa-map"></i> مخطط المناطق
                    </button>
                    <button class="quick-action-btn" onclick="generateZoneReport()">
                        <i class="fas fa-chart-bar"></i> تقرير المناطق
                    </button>
                </div>

                <div id="zonesList">
                    <!-- سيتم ملء المناطق هنا -->
                </div>
            </div>

            <!-- عرض المواقع -->
            <div id="locationsView" style="display: none;">
                <div class="quick-actions">
                    <button class="quick-action-btn" onclick="showCreateLocationModal()">
                        <i class="fas fa-plus"></i> موقع جديد
                    </button>
                    <button class="quick-action-btn" onclick="showLocationMap()">
                        <i class="fas fa-map-marked-alt"></i> خريطة المواقع
                    </button>
                    <button class="quick-action-btn" onclick="generateLocationBarcodes()">
                        <i class="fas fa-barcode"></i> طباعة الباركود
                    </button>
                </div>

                <div id="locationsList">
                    <!-- سيتم ملء المواقع هنا -->
                </div>
            </div>

            <!-- عرض الحركات -->
            <div id="movementsView" style="display: none;">
                <div class="quick-actions">
                    <button class="quick-action-btn" onclick="showCreateMovementModal()">
                        <i class="fas fa-plus"></i> حركة جديدة
                    </button>
                    <button class="quick-action-btn" onclick="showBulkMovement()">
                        <i class="fas fa-list"></i> حركة مجمعة
                    </button>
                    <button class="quick-action-btn" onclick="generateMovementReport()">
                        <i class="fas fa-file-alt"></i> تقرير الحركات
                    </button>
                </div>

                <div id="movementsList">
                    <!-- سيتم ملء الحركات هنا -->
                </div>
            </div>

            <!-- عرض النقل -->
            <div id="transfersView" style="display: none;">
                <div class="quick-actions">
                    <button class="quick-action-btn" onclick="showCreateTransferModal()">
                        <i class="fas fa-plus"></i> نقل جديد
                    </button>
                    <button class="quick-action-btn" onclick="showPendingTransfers()">
                        <i class="fas fa-clock"></i> النقل المعلق
                    </button>
                    <button class="quick-action-btn" onclick="generateTransferReport()">
                        <i class="fas fa-truck"></i> تقرير النقل
                    </button>
                </div>

                <div id="transfersList">
                    <!-- سيتم ملء النقل هنا -->
                </div>
            </div>

            <!-- عرض التقارير -->
            <div id="reportsView" style="display: none;">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-pie"></i> توزيع المخازن</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="warehousesDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-bar"></i> معدل الاستخدام</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="utilizationChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-line"></i> حركة المخزون</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="movementsChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-exchange-alt"></i> عمليات النقل</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="transfersChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.min.js"></script>
    <script src="../../core/database.js"></script>
    <script src="../../core/utils.js"></script>
    <script src="advanced-warehouses.js"></script>

    <script>
        // متغيرات عامة
        let advancedWarehousesManager;
        let currentView = 'warehouses';
        let currentFilters = {};
        let charts = {};

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeAdvancedWarehouses();
            loadData();
            updateStatistics();
            setupEventListeners();
            loadCities();
        });

        // تهيئة نظام المخازن المتقدم
        function initializeAdvancedWarehouses() {
            advancedWarehousesManager = new AdvancedWarehousesManager();
            console.log('✅ تم تهيئة واجهة إدارة المخازن المتقدمة');
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // تحديث الإحصائيات كل دقيقة
            setInterval(updateStatistics, 60000);
        }

        // تحميل المدن
        function loadCities() {
            const cityFilter = document.getElementById('cityFilter');
            const warehouses = Array.from(advancedWarehousesManager.warehouses.values());
            const cities = [...new Set(warehouses.map(w => w.location.city).filter(city => city))];

            cityFilter.innerHTML = '<option value="">جميع المدن</option>';
            cities.forEach(city => {
                const option = document.createElement('option');
                option.value = city;
                option.textContent = city;
                cityFilter.appendChild(option);
            });
        }

        // تحميل البيانات
        function loadData() {
            switch(currentView) {
                case 'warehouses':
                    loadWarehouses();
                    break;
                case 'zones':
                    loadZones();
                    break;
                case 'locations':
                    loadLocations();
                    break;
                case 'movements':
                    loadMovements();
                    break;
                case 'transfers':
                    loadTransfers();
                    break;
                case 'reports':
                    loadReports();
                    break;
            }
        }

        // تحميل المخازن
        function loadWarehouses() {
            const warehouses = Array.from(advancedWarehousesManager.warehouses.values());
            displayWarehouses(warehouses);
        }

        // عرض المخازن
        function displayWarehouses(warehouses) {
            const grid = document.getElementById('warehousesGrid');

            if (warehouses.length === 0) {
                grid.innerHTML = `
                    <div class="col-12">
                        <div class="empty-state">
                            <i class="fas fa-warehouse"></i>
                            <h4>لا توجد مخازن</h4>
                            <p>ابدأ بإضافة مخازن جديدة لإدارة المخزون</p>
                            <button class="btn btn-primary" onclick="showCreateWarehouseModal()">
                                <i class="fas fa-plus"></i> إضافة مخزن جديد
                            </button>
                        </div>
                    </div>
                `;
                return;
            }

            grid.innerHTML = warehouses.map(warehouse => createWarehouseCard(warehouse)).join('');
        }

        // إنشاء بطاقة مخزن
        function createWarehouseCard(warehouse) {
            const utilizationRate = warehouse.statistics.utilizationRate || 0;

            return `
                <div class="warehouse-card">
                    <div class="warehouse-header">
                        <div>
                            <div class="warehouse-title">${warehouse.name}</div>
                            <div class="warehouse-code">${warehouse.code}</div>
                        </div>
                        <span class="warehouse-status status-${warehouse.status}">
                            ${getStatusLabel(warehouse.status)}
                        </span>
                    </div>

                    <div class="warehouse-stats">
                        <div class="stat-item">
                            <div class="stat-value">${warehouse.specifications.totalArea}</div>
                            <div class="stat-label">المساحة (م²)</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${warehouse.specifications.capacity}</div>
                            <div class="stat-label">السعة</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${utilizationRate.toFixed(1)}%</div>
                            <div class="stat-label">الاستخدام</div>
                        </div>
                    </div>

                    <div class="warehouse-actions">
                        <button class="action-btn" onclick="viewWarehouse('${warehouse.id}')">
                            <i class="fas fa-eye"></i> عرض
                        </button>
                        <button class="action-btn" onclick="editWarehouse('${warehouse.id}')">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="action-btn" onclick="manageZones('${warehouse.id}')">
                            <i class="fas fa-th-large"></i> المناطق
                        </button>
                        <button class="action-btn" onclick="viewInventory('${warehouse.id}')">
                            <i class="fas fa-boxes"></i> المخزون
                        </button>
                    </div>
                </div>
            `;
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            const reports = advancedWarehousesManager.getWarehousesReports();

            document.getElementById('totalWarehouses').textContent = reports.summary.totalWarehouses;
            document.getElementById('totalLocations').textContent = Array.from(advancedWarehousesManager.warehouseLocations.values()).length;
            document.getElementById('utilizationRate').textContent = reports.summary.averageUtilization.toFixed(1) + '%';
            document.getElementById('todayMovements').textContent = reports.movements.todayMovements;
        }

        // تعيين العرض
        function setView(view) {
            currentView = view;

            // إخفاء جميع العروض
            document.querySelectorAll('[id$="View"]').forEach(el => {
                el.style.display = 'none';
            });

            // إزالة الفئة النشطة من جميع التبويبات
            document.querySelectorAll('.view-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // عرض العرض المحدد
            document.getElementById(view + 'View').style.display = 'block';
            document.getElementById(view + 'Tab').classList.add('active');

            // تحميل البيانات
            loadData();
        }

        // البحث في المخازن
        function searchWarehouses() {
            const query = document.getElementById('searchInput').value;
            if (!query.trim()) {
                loadWarehouses();
                return;
            }

            const results = advancedWarehousesManager.searchWarehouses(query, currentFilters);
            displayWarehouses(results);
        }

        // تطبيق الفلاتر
        function applyFilters() {
            currentFilters = {
                type: document.getElementById('typeFilter').value,
                status: document.getElementById('statusFilter').value,
                city: document.getElementById('cityFilter').value
            };

            // إزالة الفلاتر الفارغة
            Object.keys(currentFilters).forEach(key => {
                if (!currentFilters[key]) {
                    delete currentFilters[key];
                }
            });

            searchWarehouses();
        }

        // مسح الفلاتر
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('typeFilter').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('cityFilter').value = '';
            currentFilters = {};
            loadWarehouses();
        }

        // الحصول على تسمية الحالة
        function getStatusLabel(status) {
            const labels = {
                'active': 'نشط',
                'inactive': 'غير نشط',
                'maintenance': 'صيانة',
                'closed': 'مغلق'
            };
            return labels[status] || status;
        }

        // عرض مخزن
        function viewWarehouse(warehouseId) {
            const warehouse = advancedWarehousesManager.warehouses.get(warehouseId);
            if (!warehouse) return;

            // إنشاء نافذة منبثقة لعرض تفاصيل المخزن
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل المخزن: ${warehouse.name}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>المعلومات الأساسية</h6>
                                    <p><strong>الكود:</strong> ${warehouse.code}</p>
                                    <p><strong>النوع:</strong> ${warehouse.type}</p>
                                    <p><strong>الحالة:</strong> ${getStatusLabel(warehouse.status)}</p>
                                    <p><strong>المدينة:</strong> ${warehouse.location.city}</p>
                                    <p><strong>الحي:</strong> ${warehouse.location.district}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6>المواصفات</h6>
                                    <p><strong>المساحة الإجمالية:</strong> ${warehouse.specifications.totalArea} م²</p>
                                    <p><strong>مساحة التخزين:</strong> ${warehouse.specifications.storageArea} م²</p>
                                    <p><strong>السعة:</strong> ${warehouse.specifications.capacity}</p>
                                    <p><strong>الارتفاع:</strong> ${warehouse.specifications.height} م</p>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <h6>الإحصائيات</h6>
                                    <p><strong>معدل الاستخدام:</strong> ${warehouse.statistics.utilizationRate.toFixed(1)}%</p>
                                    <p><strong>إجمالي المنتجات:</strong> ${warehouse.statistics.totalProducts}</p>
                                    <p><strong>إجمالي الكمية:</strong> ${warehouse.statistics.totalQuantity}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6>المرافق</h6>
                                    <p><strong>تكييف الهواء:</strong> ${warehouse.facilities.airConditioning ? 'متوفر' : 'غير متوفر'}</p>
                                    <p><strong>نظام الأمان:</strong> ${warehouse.facilities.securitySystem ? 'متوفر' : 'غير متوفر'}</p>
                                    <p><strong>كاميرات المراقبة:</strong> ${warehouse.facilities.cctv ? 'متوفر' : 'غير متوفر'}</p>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="editWarehouse('${warehouse.id}')">تعديل</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();

            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        }

        // تعديل مخزن
        function editWarehouse(warehouseId) {
            const warehouse = advancedWarehousesManager.warehouses.get(warehouseId);
            if (!warehouse) return;

            // إنشاء نافذة تعديل المخزن
            showCreateWarehouseModal(warehouse);
        }

        // إدارة المناطق
        function manageZones(warehouseId) {
            // تبديل إلى عرض المناطق مع فلترة حسب المخزن
            setView('zones');
            // يمكن إضافة فلترة هنا
        }

        // عرض المخزون
        function viewInventory(warehouseId) {
            const inventory = advancedWarehousesManager.getInventoryByLocation(warehouseId);

            // إنشاء نافذة عرض المخزون
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">مخزون المخزن</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>كود الموقع</th>
                                            <th>المنتج</th>
                                            <th>الكمية</th>
                                            <th>آخر تحديث</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${inventory.map(item => `
                                            <tr>
                                                <td>${item.locationCode}</td>
                                                <td>${item.productId}</td>
                                                <td>${item.quantity}</td>
                                                <td>${new Date(item.lastUpdated).toLocaleDateString('ar-SA')}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();

            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        }

        // تحديث البيانات
        function refreshData() {
            loadData();
            updateStatistics();
            loadCities();
            showNotification('تم تحديث البيانات', 'success');
        }

        // دوال العروض الأخرى (ستكون بسيطة للآن)
        function loadZones() {
            document.getElementById('zonesList').innerHTML = '<p class="text-center">قريباً...</p>';
        }

        function loadLocations() {
            document.getElementById('locationsList').innerHTML = '<p class="text-center">قريباً...</p>';
        }

        function loadMovements() {
            document.getElementById('movementsList').innerHTML = '<p class="text-center">قريباً...</p>';
        }

        function loadTransfers() {
            document.getElementById('transfersList').innerHTML = '<p class="text-center">قريباً...</p>';
        }

        function loadReports() {
            // تحميل الرسوم البيانية
            setTimeout(() => {
                createChartsIfNeeded();
            }, 100);
        }

        function createChartsIfNeeded() {
            // رسم بياني لتوزيع المخازن
            const ctx1 = document.getElementById('warehousesDistributionChart');
            if (ctx1 && !charts.distribution) {
                const reports = advancedWarehousesManager.getWarehousesReports();
                charts.distribution = new Chart(ctx1, {
                    type: 'pie',
                    data: {
                        labels: ['نشط', 'غير نشط', 'صيانة', 'مغلق'],
                        datasets: [{
                            data: [
                                reports.summary.activeWarehouses,
                                reports.summary.totalWarehouses - reports.summary.activeWarehouses,
                                0, 0
                            ],
                            backgroundColor: ['#28a745', '#dc3545', '#ffc107', '#6c757d']
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
        }

        // إظهار الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }

        // دوال الإجراءات السريعة (ستكون بسيطة للآن)
        function showCreateWarehouseModal(warehouse = null) {
            showNotification('ميزة إنشاء المخازن قيد التطوير', 'info');
        }

        function showImportWarehousesModal() {
            showNotification('ميزة الاستيراد قيد التطوير', 'info');
        }

        function exportWarehouses() {
            showNotification('ميزة التصدير قيد التطوير', 'info');
        }

        function showWarehouseSettings() {
            showNotification('ميزة الإعدادات قيد التطوير', 'info');
        }
    </script>
</body>
</html>
