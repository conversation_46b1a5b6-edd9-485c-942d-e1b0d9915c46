<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المبيعات - Black Horse ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .module-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
        }
        .btn-gradient:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            color: white;
        }
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }
        .nav-tabs .nav-link {
            border: none;
            color: #667eea;
            font-weight: 600;
        }
        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="module-card p-4 mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-shopping-cart me-2"></i>إدارة المبيعات</h2>
                    <p class="text-muted mb-0">إدارة شاملة للمبيعات والفواتير والعملاء</p>
                </div>
                <button class="btn btn-gradient" onclick="showAddSaleModal()">
                    <i class="fas fa-plus me-2"></i>مبيعة جديدة
                </button>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-chart-line fa-2x mb-2"></i>
                    <h4 id="totalSales">0</h4>
                    <p>إجمالي المبيعات</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-receipt fa-2x mb-2"></i>
                    <h4 id="todaySales">0</h4>
                    <p>مبيعات اليوم</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h4 id="totalCustomers">0</h4>
                    <p>إجمالي العملاء</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h4 id="pendingOrders">0</h4>
                    <p>طلبات معلقة</p>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="module-card p-4">
            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs mb-4" id="salesTabs">
                <li class="nav-item">
                    <a class="nav-link active" data-bs-toggle="tab" href="#salesTab">المبيعات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#customersTab">العملاء</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#invoicesTab">الفواتير</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#reportsTab">التقارير</a>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Sales Tab -->
                <div class="tab-pane fade show active" id="salesTab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>قائمة المبيعات</h5>
                        <div class="d-flex gap-2">
                            <input type="text" class="form-control" placeholder="البحث..." id="salesSearch">
                            <button class="btn btn-outline-primary" onclick="refreshSales()">
                                <i class="fas fa-refresh"></i>
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>التاريخ</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="salesTableBody">
                                <!-- Sales data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Customers Tab -->
                <div class="tab-pane fade" id="customersTab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>قائمة العملاء</h5>
                        <button class="btn btn-gradient" onclick="showAddCustomerModal()">
                            <i class="fas fa-plus me-2"></i>عميل جديد
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الاسم</th>
                                    <th>الهاتف</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>إجمالي المشتريات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="customersTableBody">
                                <!-- Customers data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Invoices Tab -->
                <div class="tab-pane fade" id="invoicesTab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>الفواتير</h5>
                        <div class="d-flex gap-2">
                            <select class="form-select" id="invoiceStatusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="paid">مدفوعة</option>
                                <option value="pending">معلقة</option>
                                <option value="cancelled">ملغية</option>
                            </select>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>التاريخ</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="invoicesTableBody">
                                <!-- Invoices data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Reports Tab -->
                <div class="tab-pane fade" id="reportsTab">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>مبيعات الأسبوع</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="weeklySalesChart"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>أفضل العملاء</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="topCustomersChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Sale Modal -->
    <div class="modal fade" id="addSaleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">مبيعة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addSaleForm">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">العميل</label>
                                <select class="form-select" id="saleCustomer" required>
                                    <option value="">اختر العميل</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">تاريخ البيع</label>
                                <input type="date" class="form-control" id="saleDate" required>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <label class="form-label">المنتجات</label>
                                <div id="saleProducts">
                                    <!-- Products will be added here -->
                                </div>
                                <button type="button" class="btn btn-outline-primary mt-2" onclick="addProductToSale()">
                                    <i class="fas fa-plus me-2"></i>إضافة منتج
                                </button>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">طريقة الدفع</label>
                                <select class="form-select" id="paymentMethod" required>
                                    <option value="cash">نقدي</option>
                                    <option value="card">بطاقة</option>
                                    <option value="transfer">تحويل</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">إجمالي المبلغ</label>
                                <input type="number" class="form-control" id="totalAmount" readonly>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-gradient" onclick="saveSale()">حفظ المبيعة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Customer Modal -->
    <div class="modal fade" id="addCustomerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">عميل جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addCustomerForm">
                        <div class="mb-3">
                            <label class="form-label">اسم العميل</label>
                            <input type="text" class="form-control" id="customerName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="customerPhone" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="customerEmail">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-control" id="customerAddress" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-gradient" onclick="saveCustomer()">حفظ العميل</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="sales.js"></script>
</body>
</html>
