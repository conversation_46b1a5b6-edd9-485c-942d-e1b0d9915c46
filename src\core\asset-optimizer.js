/**
 * Black Horse POS - Asset Optimizer
 * محسن الأصول والموارد
 * Developer: Augment Agent
 */

class AssetOptimizer {
    constructor() {
        this.isInitialized = false;
        this.optimizationEnabled = true;
        this.compressionLevel = 'medium'; // 'low', 'medium', 'high'
        this.cacheEnabled = true;
        this.preloadEnabled = true;
        
        // إحصائيات التحسين
        this.stats = {
            originalSize: 0,
            optimizedSize: 0,
            compressionRatio: 0,
            loadTimeImprovement: 0,
            cacheHits: 0,
            cacheMisses: 0
        };
        
        console.log('🚀 Asset Optimizer initialized');
    }

    // تهيئة محسن الأصول
    async init() {
        try {
            console.log('🔧 Initializing Asset Optimizer...');
            
            // تحسين CSS
            await this.optimizeCSS();
            
            // تحسين JavaScript
            await this.optimizeJavaScript();
            
            // تحسين الصور
            await this.optimizeImages();
            
            // إعداد التحميل المسبق
            this.setupPreloading();
            
            // إعداد ضغط الموارد
            this.setupResourceCompression();
            
            // إعداد التخزين المؤقت الذكي
            this.setupSmartCaching();
            
            this.isInitialized = true;
            console.log('✅ Asset Optimizer ready');
            
        } catch (error) {
            console.error('❌ Error initializing Asset Optimizer:', error);
            this.isInitialized = true;
        }
    }

    // تحسين CSS
    async optimizeCSS() {
        try {
            console.log('🎨 Optimizing CSS...');
            
            // البحث عن جميع ملفات CSS
            const styleSheets = document.querySelectorAll('link[rel="stylesheet"]');
            const inlineStyles = document.querySelectorAll('style');
            
            // تحسين ملفات CSS الخارجية
            for (const sheet of styleSheets) {
                await this.optimizeCSSFile(sheet);
            }
            
            // تحسين الأنماط المضمنة
            for (const style of inlineStyles) {
                this.optimizeInlineCSS(style);
            }
            
            // إزالة CSS غير المستخدم
            this.removeUnusedCSS();
            
            console.log('✅ CSS optimization completed');
            
        } catch (error) {
            console.error('❌ Error optimizing CSS:', error);
        }
    }

    // تحسين ملف CSS
    async optimizeCSSFile(linkElement) {
        try {
            const href = linkElement.href;
            if (!href || href.startsWith('data:')) return;
            
            // تحميل محتوى CSS
            const response = await fetch(href);
            const cssContent = await response.text();
            
            // ضغط CSS
            const optimizedCSS = this.compressCSS(cssContent);
            
            // إنشاء blob محسن
            const blob = new Blob([optimizedCSS], { type: 'text/css' });
            const optimizedURL = URL.createObjectURL(blob);
            
            // استبدال الرابط
            linkElement.href = optimizedURL;
            
            // تحديث الإحصائيات
            this.updateCompressionStats(cssContent.length, optimizedCSS.length);
            
            console.log(`📦 CSS file optimized: ${href}`);
            
        } catch (error) {
            console.error('❌ Error optimizing CSS file:', error);
        }
    }

    // ضغط CSS
    compressCSS(cssContent) {
        try {
            let compressed = cssContent;
            
            // إزالة التعليقات
            compressed = compressed.replace(/\/\*[\s\S]*?\*\//g, '');
            
            // إزالة المسافات الزائدة
            compressed = compressed.replace(/\s+/g, ' ');
            
            // إزالة المسافات حول الأقواس والفواصل
            compressed = compressed.replace(/\s*{\s*/g, '{');
            compressed = compressed.replace(/\s*}\s*/g, '}');
            compressed = compressed.replace(/\s*;\s*/g, ';');
            compressed = compressed.replace(/\s*:\s*/g, ':');
            compressed = compressed.replace(/\s*,\s*/g, ',');
            
            // إزالة الفواصل المنقوطة الأخيرة
            compressed = compressed.replace(/;}/g, '}');
            
            // إزالة المسافات في البداية والنهاية
            compressed = compressed.trim();
            
            return compressed;
            
        } catch (error) {
            console.error('❌ Error compressing CSS:', error);
            return cssContent;
        }
    }

    // تحسين الأنماط المضمنة
    optimizeInlineCSS(styleElement) {
        try {
            const originalContent = styleElement.textContent;
            const optimizedContent = this.compressCSS(originalContent);
            
            styleElement.textContent = optimizedContent;
            
            // تحديث الإحصائيات
            this.updateCompressionStats(originalContent.length, optimizedContent.length);
            
        } catch (error) {
            console.error('❌ Error optimizing inline CSS:', error);
        }
    }

    // إزالة CSS غير المستخدم
    removeUnusedCSS() {
        try {
            // هذا مبسط - في التطبيق الحقيقي يحتاج تحليل أكثر تعقيداً
            console.log('🧹 Removing unused CSS (simplified)');
            
            // يمكن تطوير هذا لاحقاً لتحليل CSS المستخدم فعلياً
            
        } catch (error) {
            console.error('❌ Error removing unused CSS:', error);
        }
    }

    // تحسين JavaScript
    async optimizeJavaScript() {
        try {
            console.log('⚡ Optimizing JavaScript...');
            
            // البحث عن جميع ملفات JavaScript
            const scripts = document.querySelectorAll('script[src]');
            const inlineScripts = document.querySelectorAll('script:not([src])');
            
            // تحسين ملفات JavaScript الخارجية
            for (const script of scripts) {
                await this.optimizeJSFile(script);
            }
            
            // تحسين النصوص المضمنة
            for (const script of inlineScripts) {
                this.optimizeInlineJS(script);
            }
            
            console.log('✅ JavaScript optimization completed');
            
        } catch (error) {
            console.error('❌ Error optimizing JavaScript:', error);
        }
    }

    // تحسين ملف JavaScript
    async optimizeJSFile(scriptElement) {
        try {
            const src = scriptElement.src;
            if (!src || src.startsWith('data:')) return;
            
            // تحميل محتوى JavaScript
            const response = await fetch(src);
            const jsContent = await response.text();
            
            // ضغط JavaScript
            const optimizedJS = this.compressJS(jsContent);
            
            // إنشاء blob محسن
            const blob = new Blob([optimizedJS], { type: 'application/javascript' });
            const optimizedURL = URL.createObjectURL(blob);
            
            // استبدال الرابط
            scriptElement.src = optimizedURL;
            
            // تحديث الإحصائيات
            this.updateCompressionStats(jsContent.length, optimizedJS.length);
            
            console.log(`📦 JS file optimized: ${src}`);
            
        } catch (error) {
            console.error('❌ Error optimizing JS file:', error);
        }
    }

    // ضغط JavaScript
    compressJS(jsContent) {
        try {
            let compressed = jsContent;
            
            // إزالة التعليقات أحادية السطر
            compressed = compressed.replace(/\/\/.*$/gm, '');
            
            // إزالة التعليقات متعددة الأسطر
            compressed = compressed.replace(/\/\*[\s\S]*?\*\//g, '');
            
            // إزالة المسافات الزائدة (مع الحفاظ على المسافات المهمة)
            compressed = compressed.replace(/\s+/g, ' ');
            
            // إزالة المسافات حول العوامل
            compressed = compressed.replace(/\s*([{}();,=+\-*/<>!&|])\s*/g, '$1');
            
            // إزالة المسافات في البداية والنهاية
            compressed = compressed.trim();
            
            return compressed;
            
        } catch (error) {
            console.error('❌ Error compressing JavaScript:', error);
            return jsContent;
        }
    }

    // تحسين النصوص المضمنة
    optimizeInlineJS(scriptElement) {
        try {
            const originalContent = scriptElement.textContent;
            const optimizedContent = this.compressJS(originalContent);
            
            scriptElement.textContent = optimizedContent;
            
            // تحديث الإحصائيات
            this.updateCompressionStats(originalContent.length, optimizedContent.length);
            
        } catch (error) {
            console.error('❌ Error optimizing inline JavaScript:', error);
        }
    }

    // تحسين الصور
    async optimizeImages() {
        try {
            console.log('🖼️ Optimizing images...');
            
            // البحث عن جميع الصور
            const images = document.querySelectorAll('img');
            
            for (const img of images) {
                await this.optimizeImage(img);
            }
            
            // تحسين صور الخلفية في CSS
            this.optimizeBackgroundImages();
            
            console.log('✅ Image optimization completed');
            
        } catch (error) {
            console.error('❌ Error optimizing images:', error);
        }
    }

    // تحسين صورة واحدة
    async optimizeImage(imgElement) {
        try {
            const src = imgElement.src;
            if (!src || src.startsWith('data:')) return;
            
            // إضافة lazy loading
            if (!imgElement.hasAttribute('loading')) {
                imgElement.setAttribute('loading', 'lazy');
            }
            
            // إضافة أبعاد إذا لم تكن موجودة
            if (!imgElement.width && !imgElement.height) {
                imgElement.addEventListener('load', () => {
                    if (!imgElement.width) imgElement.width = imgElement.naturalWidth;
                    if (!imgElement.height) imgElement.height = imgElement.naturalHeight;
                });
            }
            
            console.log(`🖼️ Image optimized: ${src}`);
            
        } catch (error) {
            console.error('❌ Error optimizing image:', error);
        }
    }

    // تحسين صور الخلفية
    optimizeBackgroundImages() {
        try {
            // البحث عن العناصر التي تحتوي على صور خلفية
            const elements = document.querySelectorAll('*');
            
            elements.forEach(element => {
                const style = window.getComputedStyle(element);
                const backgroundImage = style.backgroundImage;
                
                if (backgroundImage && backgroundImage !== 'none') {
                    // إضافة تحسينات لصور الخلفية
                    element.style.backgroundSize = element.style.backgroundSize || 'cover';
                    element.style.backgroundRepeat = element.style.backgroundRepeat || 'no-repeat';
                }
            });
            
        } catch (error) {
            console.error('❌ Error optimizing background images:', error);
        }
    }

    // إعداد التحميل المسبق
    setupPreloading() {
        try {
            if (!this.preloadEnabled) return;
            
            console.log('🚀 Setting up resource preloading...');
            
            // تحميل مسبق للخطوط
            this.preloadFonts();
            
            // تحميل مسبق للموارد المهمة
            this.preloadCriticalResources();
            
            console.log('✅ Preloading setup completed');
            
        } catch (error) {
            console.error('❌ Error setting up preloading:', error);
        }
    }

    // تحميل مسبق للخطوط
    preloadFonts() {
        try {
            // البحث عن الخطوط المستخدمة
            const fontFaces = document.fonts;
            
            fontFaces.forEach(font => {
                if (font.status === 'unloaded') {
                    font.load().catch(error => {
                        console.warn('⚠️ Font preload failed:', error);
                    });
                }
            });
            
        } catch (error) {
            console.error('❌ Error preloading fonts:', error);
        }
    }

    // تحميل مسبق للموارد المهمة
    preloadCriticalResources() {
        try {
            // قائمة الموارد المهمة
            const criticalResources = [
                'src/css/main.css',
                'src/js/app.js',
                'src/images/logo.png'
            ];
            
            criticalResources.forEach(resource => {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.href = resource;
                
                // تحديد نوع المورد
                if (resource.endsWith('.css')) {
                    link.as = 'style';
                } else if (resource.endsWith('.js')) {
                    link.as = 'script';
                } else if (resource.match(/\.(png|jpg|jpeg|gif|webp)$/)) {
                    link.as = 'image';
                }
                
                document.head.appendChild(link);
            });
            
        } catch (error) {
            console.error('❌ Error preloading critical resources:', error);
        }
    }

    // إعداد ضغط الموارد
    setupResourceCompression() {
        try {
            console.log('🗜️ Setting up resource compression...');
            
            // إعداد ضغط gzip للاستجابات
            this.setupGzipCompression();
            
            // إعداد ضغط Brotli إذا كان متاحاً
            this.setupBrotliCompression();
            
            console.log('✅ Resource compression setup completed');
            
        } catch (error) {
            console.error('❌ Error setting up resource compression:', error);
        }
    }

    // إعداد ضغط gzip
    setupGzipCompression() {
        try {
            // هذا يتطلب دعم من الخادم
            // يمكن إضافة headers للطلب
            const originalFetch = window.fetch;
            
            window.fetch = function(resource, options = {}) {
                options.headers = options.headers || {};
                options.headers['Accept-Encoding'] = 'gzip, deflate, br';
                
                return originalFetch(resource, options);
            };
            
        } catch (error) {
            console.error('❌ Error setting up gzip compression:', error);
        }
    }

    // إعداد ضغط Brotli
    setupBrotliCompression() {
        try {
            // التحقق من دعم Brotli
            if ('CompressionStream' in window) {
                console.log('✅ Brotli compression supported');
            } else {
                console.log('ℹ️ Brotli compression not supported');
            }
            
        } catch (error) {
            console.error('❌ Error setting up Brotli compression:', error);
        }
    }

    // إعداد التخزين المؤقت الذكي
    setupSmartCaching() {
        try {
            if (!this.cacheEnabled) return;
            
            console.log('🗄️ Setting up smart caching...');
            
            // إعداد Service Worker للتخزين المؤقت
            this.setupServiceWorkerCaching();
            
            // إعداد Cache API
            this.setupCacheAPI();
            
            console.log('✅ Smart caching setup completed');
            
        } catch (error) {
            console.error('❌ Error setting up smart caching:', error);
        }
    }

    // إعداد Service Worker للتخزين المؤقت
    setupServiceWorkerCaching() {
        try {
            if ('serviceWorker' in navigator) {
                // تسجيل Service Worker (مبسط)
                console.log('🔧 Service Worker caching available');
            } else {
                console.log('ℹ️ Service Worker not supported');
            }
            
        } catch (error) {
            console.error('❌ Error setting up Service Worker caching:', error);
        }
    }

    // إعداد Cache API
    setupCacheAPI() {
        try {
            if ('caches' in window) {
                // إنشاء cache للموارد
                caches.open('blackhorse-pos-v1').then(cache => {
                    console.log('✅ Cache API ready');
                }).catch(error => {
                    console.error('❌ Cache API error:', error);
                });
            } else {
                console.log('ℹ️ Cache API not supported');
            }
            
        } catch (error) {
            console.error('❌ Error setting up Cache API:', error);
        }
    }

    // تحديث إحصائيات الضغط
    updateCompressionStats(originalSize, optimizedSize) {
        try {
            this.stats.originalSize += originalSize;
            this.stats.optimizedSize += optimizedSize;
            
            if (this.stats.originalSize > 0) {
                this.stats.compressionRatio = 
                    ((this.stats.originalSize - this.stats.optimizedSize) / this.stats.originalSize) * 100;
            }
            
        } catch (error) {
            console.error('❌ Error updating compression stats:', error);
        }
    }

    // الحصول على إحصائيات التحسين
    getOptimizationStats() {
        try {
            return {
                ...this.stats,
                compressionRatio: Math.round(this.stats.compressionRatio * 100) / 100,
                sizeSaved: this.stats.originalSize - this.stats.optimizedSize,
                cacheHitRatio: this.stats.cacheHits + this.stats.cacheMisses > 0 ?
                    (this.stats.cacheHits / (this.stats.cacheHits + this.stats.cacheMisses)) * 100 : 0
            };
            
        } catch (error) {
            console.error('❌ Error getting optimization stats:', error);
            return null;
        }
    }

    // تحسين شامل للصفحة
    async optimizePage() {
        try {
            console.log('🚀 Starting full page optimization...');
            
            const startTime = performance.now();
            
            // تحسين جميع الأصول
            await this.optimizeCSS();
            await this.optimizeJavaScript();
            await this.optimizeImages();
            
            // تحسين DOM
            this.optimizeDOM();
            
            // تحسين الأحداث
            this.optimizeEvents();
            
            const endTime = performance.now();
            const optimizationTime = endTime - startTime;
            
            console.log(`✅ Page optimization completed in ${Math.round(optimizationTime)}ms`);
            
            return this.getOptimizationStats();
            
        } catch (error) {
            console.error('❌ Error optimizing page:', error);
            return null;
        }
    }

    // تحسين DOM
    optimizeDOM() {
        try {
            // إزالة العقد الفارغة
            this.removeEmptyNodes();
            
            // تحسين الجداول الكبيرة
            this.optimizeLargeTables();
            
            // تحسين القوائم الطويلة
            this.optimizeLongLists();
            
        } catch (error) {
            console.error('❌ Error optimizing DOM:', error);
        }
    }

    // إزالة العقد الفارغة
    removeEmptyNodes() {
        try {
            const walker = document.createTreeWalker(
                document.body,
                NodeFilter.SHOW_TEXT,
                {
                    acceptNode: function(node) {
                        return node.textContent.trim() === '' ? 
                            NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT;
                    }
                }
            );
            
            const emptyNodes = [];
            let node;
            
            while (node = walker.nextNode()) {
                emptyNodes.push(node);
            }
            
            emptyNodes.forEach(node => {
                if (node.parentNode) {
                    node.parentNode.removeChild(node);
                }
            });
            
            console.log(`🧹 Removed ${emptyNodes.length} empty text nodes`);
            
        } catch (error) {
            console.error('❌ Error removing empty nodes:', error);
        }
    }

    // تحسين الجداول الكبيرة
    optimizeLargeTables() {
        try {
            const tables = document.querySelectorAll('table');
            
            tables.forEach(table => {
                const rows = table.querySelectorAll('tr');
                
                if (rows.length > 100) {
                    // تطبيق virtual scrolling للجداول الكبيرة
                    if (window.UIOptimizer) {
                        console.log(`📊 Optimizing large table with ${rows.length} rows`);
                        // يمكن تطبيق virtual scrolling هنا
                    }
                }
            });
            
        } catch (error) {
            console.error('❌ Error optimizing large tables:', error);
        }
    }

    // تحسين القوائم الطويلة
    optimizeLongLists() {
        try {
            const lists = document.querySelectorAll('ul, ol');
            
            lists.forEach(list => {
                const items = list.querySelectorAll('li');
                
                if (items.length > 50) {
                    // تطبيق lazy loading للقوائم الطويلة
                    console.log(`📋 Optimizing long list with ${items.length} items`);
                    // يمكن تطبيق lazy loading هنا
                }
            });
            
        } catch (error) {
            console.error('❌ Error optimizing long lists:', error);
        }
    }

    // تحسين الأحداث
    optimizeEvents() {
        try {
            // تحسين أحداث التمرير
            this.optimizeScrollEvents();
            
            // تحسين أحداث تغيير الحجم
            this.optimizeResizeEvents();
            
            // تحسين أحداث الإدخال
            this.optimizeInputEvents();
            
        } catch (error) {
            console.error('❌ Error optimizing events:', error);
        }
    }

    // تحسين أحداث التمرير
    optimizeScrollEvents() {
        try {
            // استخدام throttling لأحداث التمرير
            if (window.UIOptimizer && window.UIOptimizer.throttle) {
                const originalAddEventListener = EventTarget.prototype.addEventListener;
                
                EventTarget.prototype.addEventListener = function(type, listener, options) {
                    if (type === 'scroll' && typeof listener === 'function') {
                        listener = window.UIOptimizer.throttle(listener, 16); // 60fps
                    }
                    
                    return originalAddEventListener.call(this, type, listener, options);
                };
            }
            
        } catch (error) {
            console.error('❌ Error optimizing scroll events:', error);
        }
    }

    // تحسين أحداث تغيير الحجم
    optimizeResizeEvents() {
        try {
            // استخدام debouncing لأحداث تغيير الحجم
            if (window.UIOptimizer && window.UIOptimizer.debounce) {
                const resizeHandlers = [];
                
                window.addEventListener('resize', window.UIOptimizer.debounce(() => {
                    resizeHandlers.forEach(handler => handler());
                }, 250));
            }
            
        } catch (error) {
            console.error('❌ Error optimizing resize events:', error);
        }
    }

    // تحسين أحداث الإدخال
    optimizeInputEvents() {
        try {
            // استخدام debouncing لأحداث البحث
            const searchInputs = document.querySelectorAll('input[type="search"], input[data-search]');
            
            searchInputs.forEach(input => {
                if (window.UIOptimizer && window.UIOptimizer.debounce) {
                    const originalHandler = input.oninput;
                    if (originalHandler) {
                        input.oninput = window.UIOptimizer.debounce(originalHandler, 300);
                    }
                }
            });
            
        } catch (error) {
            console.error('❌ Error optimizing input events:', error);
        }
    }

    // تنظيف الموارد
    cleanup() {
        try {
            // تنظيف URLs المؤقتة
            // (في التطبيق الحقيقي يجب تتبع URLs المنشأة)
            
            console.log('✅ Asset Optimizer cleaned up');
            
        } catch (error) {
            console.error('❌ Error cleaning up Asset Optimizer:', error);
        }
    }
}

// إنشاء instance عام
window.AssetOptimizer = new AssetOptimizer();

// تصدير للاستخدام في الوحدات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AssetOptimizer;
}

console.log('✅ Asset Optimizer loaded successfully');
