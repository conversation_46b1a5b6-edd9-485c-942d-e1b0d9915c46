/**
 * Black Horse POS - Unified Database System
 * نظام قاعدة البيانات الموحد
 * Developer: Ahmed <PERSON>horbagy - 01096359521
 */

// ===== UNIFIED DATABASE SYSTEM =====
const UnifiedDatabase = {
    // إعدادات قاعدة البيانات
    config: {
        name: 'BlackHorsePOS',
        version: 1,
        preferIndexedDB: true,
        fallbackToLocalStorage: true,
        autoBackup: true,
        backupInterval: 300000 // 5 دقائق
    },
    
    // حالة النظام
    isInitialized: false,
    currentProvider: null,
    backupTimer: null,
    
    // مقدمي قاعدة البيانات
    providers: {
        indexedDB: null,
        localStorage: null
    },
    
    // تهيئة النظام
    init: async function() {
        try {
            console.log('🗄️ Initializing Unified Database System...');
            
            // تحديد مقدم قاعدة البيانات المناسب
            await this.selectProvider();
            
            // تهيئة مقدم قاعدة البيانات
            await this.initializeProvider();
            
            // بدء النسخ الاحتياطي التلقائي
            this.startAutoBackup();
            
            this.isInitialized = true;
            console.log(`✅ Unified Database initialized with ${this.currentProvider}`);
            
            return true;
        } catch (error) {
            console.error('❌ Error initializing unified database:', error);
            return false;
        }
    },
    
    // تحديد مقدم قاعدة البيانات
    selectProvider: async function() {
        try {
            // التحقق من دعم IndexedDB
            if (this.config.preferIndexedDB && this.isIndexedDBSupported()) {
                this.currentProvider = 'indexedDB';
                console.log('📊 Selected IndexedDB as primary database provider');
                return;
            }
            
            // الرجوع إلى localStorage
            if (this.config.fallbackToLocalStorage && this.isLocalStorageSupported()) {
                this.currentProvider = 'localStorage';
                console.log('💾 Selected localStorage as fallback database provider');
                return;
            }
            
            throw new Error('No supported database provider found');
        } catch (error) {
            console.error('❌ Error selecting database provider:', error);
            throw error;
        }
    },
    
    // التحقق من دعم IndexedDB
    isIndexedDBSupported: function() {
        try {
            return typeof window !== 'undefined' && 
                   'indexedDB' in window && 
                   window.indexedDB !== null;
        } catch (error) {
            return false;
        }
    },
    
    // التحقق من دعم localStorage
    isLocalStorageSupported: function() {
        try {
            const test = 'test';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch (error) {
            return false;
        }
    },
    
    // تهيئة مقدم قاعدة البيانات
    initializeProvider: async function() {
        try {
            if (this.currentProvider === 'indexedDB') {
                await this.initIndexedDB();
            } else if (this.currentProvider === 'localStorage') {
                await this.initLocalStorage();
            }
        } catch (error) {
            console.error('❌ Error initializing database provider:', error);
            throw error;
        }
    },
    
    // تهيئة IndexedDB
    initIndexedDB: function() {
        return new Promise((resolve, reject) => {
            try {
                const request = indexedDB.open(this.config.name, this.config.version);
                
                request.onerror = () => {
                    reject(new Error('Failed to open IndexedDB'));
                };
                
                request.onsuccess = (event) => {
                    this.providers.indexedDB = event.target.result;
                    console.log('✅ IndexedDB initialized successfully');
                    resolve();
                };
                
                request.onupgradeneeded = (event) => {
                    const db = event.target.result;
                    
                    // إنشاء الجداول المطلوبة
                    const tables = [
                        'products', 'customers', 'sales', 'expenses',
                        'users', 'suppliers', 'inventory', 'settings',
                        'backups', 'sessions'
                    ];
                    
                    tables.forEach(tableName => {
                        if (!db.objectStoreNames.contains(tableName)) {
                            const store = db.createObjectStore(tableName, { 
                                keyPath: 'id', 
                                autoIncrement: true 
                            });
                            
                            // إضافة فهارس مفيدة
                            if (tableName === 'products') {
                                store.createIndex('barcode', 'barcode', { unique: false });
                                store.createIndex('category', 'category', { unique: false });
                            } else if (tableName === 'customers') {
                                store.createIndex('phone', 'phone', { unique: false });
                                store.createIndex('email', 'email', { unique: false });
                            } else if (tableName === 'sales') {
                                store.createIndex('date', 'date', { unique: false });
                                store.createIndex('customer', 'customer', { unique: false });
                            }
                            
                            console.log(`📊 Created table: ${tableName}`);
                        }
                    });
                };
            } catch (error) {
                reject(error);
            }
        });
    },
    
    // تهيئة localStorage
    initLocalStorage: async function() {
        try {
            this.providers.localStorage = {
                // محاكاة واجهة IndexedDB
                get: (table, id) => {
                    const data = JSON.parse(localStorage.getItem(`${this.config.name}_${table}`) || '[]');
                    return data.find(item => item.id === id);
                },
                
                getAll: (table) => {
                    return JSON.parse(localStorage.getItem(`${this.config.name}_${table}`) || '[]');
                },
                
                add: (table, item) => {
                    const data = JSON.parse(localStorage.getItem(`${this.config.name}_${table}`) || '[]');
                    item.id = item.id || Date.now() + Math.random();
                    data.push(item);
                    localStorage.setItem(`${this.config.name}_${table}`, JSON.stringify(data));
                    return item.id;
                },
                
                update: (table, item) => {
                    const data = JSON.parse(localStorage.getItem(`${this.config.name}_${table}`) || '[]');
                    const index = data.findIndex(i => i.id === item.id);
                    if (index !== -1) {
                        data[index] = item;
                        localStorage.setItem(`${this.config.name}_${table}`, JSON.stringify(data));
                        return true;
                    }
                    return false;
                },
                
                delete: (table, id) => {
                    const data = JSON.parse(localStorage.getItem(`${this.config.name}_${table}`) || '[]');
                    const filteredData = data.filter(item => item.id !== id);
                    localStorage.setItem(`${this.config.name}_${table}`, JSON.stringify(filteredData));
                    return true;
                },
                
                clear: (table) => {
                    localStorage.removeItem(`${this.config.name}_${table}`);
                    return true;
                }
            };
            
            console.log('✅ localStorage provider initialized successfully');
        } catch (error) {
            console.error('❌ Error initializing localStorage provider:', error);
            throw error;
        }
    },
    
    // حفظ البيانات
    save: async function(table, data) {
        try {
            if (!this.isInitialized) {
                throw new Error('Database not initialized');
            }
            
            if (this.currentProvider === 'indexedDB') {
                return await this.saveToIndexedDB(table, data);
            } else {
                return this.providers.localStorage.add(table, data);
            }
        } catch (error) {
            console.error(`❌ Error saving to ${table}:`, error);
            throw error;
        }
    },
    
    // استرجاع البيانات
    load: async function(table, id = null) {
        try {
            if (!this.isInitialized) {
                throw new Error('Database not initialized');
            }
            
            if (this.currentProvider === 'indexedDB') {
                return await this.loadFromIndexedDB(table, id);
            } else {
                return id ? this.providers.localStorage.get(table, id) : 
                           this.providers.localStorage.getAll(table);
            }
        } catch (error) {
            console.error(`❌ Error loading from ${table}:`, error);
            throw error;
        }
    },
    
    // حفظ إلى IndexedDB
    saveToIndexedDB: function(table, data) {
        return new Promise((resolve, reject) => {
            try {
                const transaction = this.providers.indexedDB.transaction([table], 'readwrite');
                const store = transaction.objectStore(table);
                
                const request = data.id ? store.put(data) : store.add(data);
                
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            } catch (error) {
                reject(error);
            }
        });
    },
    
    // استرجاع من IndexedDB
    loadFromIndexedDB: function(table, id = null) {
        return new Promise((resolve, reject) => {
            try {
                const transaction = this.providers.indexedDB.transaction([table], 'readonly');
                const store = transaction.objectStore(table);
                
                const request = id ? store.get(id) : store.getAll();
                
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            } catch (error) {
                reject(error);
            }
        });
    },
    
    // بدء النسخ الاحتياطي التلقائي
    startAutoBackup: function() {
        if (this.config.autoBackup) {
            this.backupTimer = setInterval(() => {
                this.createBackup().catch(error => {
                    console.error('❌ Auto backup failed:', error);
                });
            }, this.config.backupInterval);
            
            console.log('🔄 Auto backup started');
        }
    },
    
    // إنشاء نسخة احتياطية
    createBackup: async function() {
        try {
            const backup = {
                timestamp: new Date().toISOString(),
                version: this.config.version,
                provider: this.currentProvider,
                data: {}
            };
            
            // نسخ جميع الجداول
            const tables = ['products', 'customers', 'sales', 'expenses', 'users', 'suppliers'];
            
            for (const table of tables) {
                backup.data[table] = await this.load(table);
            }
            
            // حفظ النسخة الاحتياطية
            const backupKey = `backup_${Date.now()}`;
            localStorage.setItem(backupKey, JSON.stringify(backup));
            
            console.log(`💾 Backup created: ${backupKey}`);
            return backupKey;
        } catch (error) {
            console.error('❌ Error creating backup:', error);
            throw error;
        }
    },
    
    // إيقاف النظام
    destroy: function() {
        try {
            if (this.backupTimer) {
                clearInterval(this.backupTimer);
                this.backupTimer = null;
            }
            
            if (this.providers.indexedDB) {
                this.providers.indexedDB.close();
                this.providers.indexedDB = null;
            }
            
            this.isInitialized = false;
            this.currentProvider = null;
            
            console.log('🗄️ Unified Database destroyed');
        } catch (error) {
            console.error('❌ Error destroying database:', error);
        }
    }
};

// تهيئة تلقائية عند تحميل الملف
if (typeof window !== 'undefined') {
    window.UnifiedDatabase = UnifiedDatabase;
    
    // تهيئة عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', () => {
        UnifiedDatabase.init().then(() => {
            console.log('🎉 Unified Database ready for use');
        }).catch(error => {
            console.error('💥 Failed to initialize Unified Database:', error);
        });
    });
}

console.log('📦 Unified Database System loaded');
