/**
 * نظام إدارة الملفات المتقدم
 * Black Horse ERP System
 */

class FileManager {
    constructor() {
        this.files = new Map();
        this.folders = new Map();
        this.uploadQueue = [];
        this.settings = {
            maxFileSize: 10 * 1024 * 1024, // 10MB
            allowedTypes: ['image/*', 'application/pdf', 'text/*', '.doc', '.docx', '.xls', '.xlsx'],
            autoBackup: true,
            compression: true,
            encryption: false
        };
        
        this.currentPath = '/';
        this.selectedItems = new Set();
        this.clipboard = null;
        this.viewMode = 'grid'; // grid, list, details
        
        this.initializeFileManager();
        this.setupEventListeners();
        this.loadFiles();
    }

    // تهيئة مدير الملفات
    initializeFileManager() {
        console.log('📁 تهيئة مدير الملفات...');
        
        // إنشاء المجلدات الافتراضية
        this.createDefaultFolders();
        
        // إعداد قاعدة البيانات
        this.setupDatabase();
        
        console.log('✅ تم تهيئة مدير الملفات');
    }

    // إنشاء المجلدات الافتراضية
    createDefaultFolders() {
        const defaultFolders = [
            { path: '/documents', name: 'المستندات', icon: 'fas fa-file-alt' },
            { path: '/images', name: 'الصور', icon: 'fas fa-image' },
            { path: '/reports', name: 'التقارير', icon: 'fas fa-chart-bar' },
            { path: '/invoices', name: 'الفواتير', icon: 'fas fa-receipt' },
            { path: '/contracts', name: 'العقود', icon: 'fas fa-handshake' },
            { path: '/backup', name: 'النسخ الاحتياطية', icon: 'fas fa-database' },
            { path: '/temp', name: 'ملفات مؤقتة', icon: 'fas fa-clock' },
            { path: '/archive', name: 'الأرشيف', icon: 'fas fa-archive' }
        ];

        defaultFolders.forEach(folder => {
            this.folders.set(folder.path, {
                id: this.generateId(),
                path: folder.path,
                name: folder.name,
                icon: folder.icon,
                parent: folder.path === '/' ? null : '/',
                created: new Date().toISOString(),
                modified: new Date().toISOString(),
                size: 0,
                fileCount: 0,
                permissions: {
                    read: true,
                    write: true,
                    delete: true
                }
            });
        });
    }

    // رفع ملف
    async uploadFile(file, targetPath = null) {
        try {
            // التحقق من صحة الملف
            this.validateFile(file);
            
            const path = targetPath || this.currentPath;
            const fileId = this.generateId();
            const fileName = this.generateUniqueFileName(file.name, path);
            
            // قراءة محتوى الملف
            const content = await this.readFileContent(file);
            
            // ضغط الملف إذا كان مفعلاً
            const processedContent = this.settings.compression ? 
                await this.compressFile(content) : content;
            
            // تشفير الملف إذا كان مفعلاً
            const finalContent = this.settings.encryption ? 
                await this.encryptFile(processedContent) : processedContent;
            
            // إنشاء معلومات الملف
            const fileInfo = {
                id: fileId,
                name: fileName,
                originalName: file.name,
                path: path + '/' + fileName,
                type: file.type,
                size: file.size,
                processedSize: finalContent.length,
                content: finalContent,
                created: new Date().toISOString(),
                modified: new Date().toISOString(),
                uploaded: new Date().toISOString(),
                checksum: await this.calculateChecksum(content),
                metadata: {
                    compressed: this.settings.compression,
                    encrypted: this.settings.encryption,
                    uploader: 'current_user', // يجب تحديد المستخدم الحالي
                    tags: [],
                    description: ''
                },
                permissions: {
                    read: true,
                    write: true,
                    delete: true
                }
            };
            
            // حفظ الملف
            this.files.set(fileId, fileInfo);
            
            // تحديث إحصائيات المجلد
            this.updateFolderStats(path);
            
            // حفظ في قاعدة البيانات
            await this.saveFile(fileInfo);
            
            // إنشاء نسخة احتياطية إذا كان مفعلاً
            if (this.settings.autoBackup) {
                await this.createFileBackup(fileInfo);
            }
            
            console.log('✅ تم رفع الملف:', fileName);
            return fileInfo;
            
        } catch (error) {
            console.error('خطأ في رفع الملف:', error);
            throw error;
        }
    }

    // رفع متعدد الملفات
    async uploadMultipleFiles(files, targetPath = null) {
        const results = [];
        const errors = [];
        
        for (const file of files) {
            try {
                const result = await this.uploadFile(file, targetPath);
                results.push(result);
            } catch (error) {
                errors.push({ file: file.name, error: error.message });
            }
        }
        
        return { success: results, errors };
    }

    // تحميل ملف
    async downloadFile(fileId) {
        try {
            const fileInfo = this.files.get(fileId);
            if (!fileInfo) {
                throw new Error('الملف غير موجود');
            }
            
            // فك التشفير إذا كان مشفراً
            let content = fileInfo.content;
            if (fileInfo.metadata.encrypted) {
                content = await this.decryptFile(content);
            }
            
            // فك الضغط إذا كان مضغوطاً
            if (fileInfo.metadata.compressed) {
                content = await this.decompressFile(content);
            }
            
            // إنشاء رابط التحميل
            const blob = new Blob([content], { type: fileInfo.type });
            const url = URL.createObjectURL(blob);
            
            // إنشاء رابط التحميل
            const link = document.createElement('a');
            link.href = url;
            link.download = fileInfo.originalName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // تنظيف الذاكرة
            URL.revokeObjectURL(url);
            
            console.log('✅ تم تحميل الملف:', fileInfo.name);
            
        } catch (error) {
            console.error('خطأ في تحميل الملف:', error);
            throw error;
        }
    }

    // حذف ملف
    async deleteFile(fileId) {
        try {
            const fileInfo = this.files.get(fileId);
            if (!fileInfo) {
                throw new Error('الملف غير موجود');
            }
            
            // نقل إلى سلة المحذوفات بدلاً من الحذف النهائي
            await this.moveToTrash(fileInfo);
            
            // إزالة من الذاكرة
            this.files.delete(fileId);
            
            // تحديث إحصائيات المجلد
            const folderPath = this.getFolderPath(fileInfo.path);
            this.updateFolderStats(folderPath);
            
            console.log('✅ تم حذف الملف:', fileInfo.name);
            
        } catch (error) {
            console.error('خطأ في حذف الملف:', error);
            throw error;
        }
    }

    // إنشاء مجلد جديد
    createFolder(name, parentPath = null) {
        try {
            const path = (parentPath || this.currentPath) + '/' + name;
            
            if (this.folders.has(path)) {
                throw new Error('المجلد موجود بالفعل');
            }
            
            const folderId = this.generateId();
            const folderInfo = {
                id: folderId,
                path: path,
                name: name,
                parent: parentPath || this.currentPath,
                created: new Date().toISOString(),
                modified: new Date().toISOString(),
                size: 0,
                fileCount: 0,
                permissions: {
                    read: true,
                    write: true,
                    delete: true
                }
            };
            
            this.folders.set(path, folderInfo);
            this.saveFolders();
            
            console.log('✅ تم إنشاء المجلد:', name);
            return folderInfo;
            
        } catch (error) {
            console.error('خطأ في إنشاء المجلد:', error);
            throw error;
        }
    }

    // نسخ ملف
    async copyFile(fileId, targetPath) {
        try {
            const fileInfo = this.files.get(fileId);
            if (!fileInfo) {
                throw new Error('الملف غير موجود');
            }
            
            const newFileName = this.generateUniqueFileName(fileInfo.originalName, targetPath);
            const newFileId = this.generateId();
            
            const newFileInfo = {
                ...fileInfo,
                id: newFileId,
                name: newFileName,
                path: targetPath + '/' + newFileName,
                created: new Date().toISOString(),
                copied: new Date().toISOString(),
                originalFile: fileId
            };
            
            this.files.set(newFileId, newFileInfo);
            this.updateFolderStats(targetPath);
            await this.saveFile(newFileInfo);
            
            console.log('✅ تم نسخ الملف:', newFileName);
            return newFileInfo;
            
        } catch (error) {
            console.error('خطأ في نسخ الملف:', error);
            throw error;
        }
    }

    // نقل ملف
    async moveFile(fileId, targetPath) {
        try {
            const fileInfo = this.files.get(fileId);
            if (!fileInfo) {
                throw new Error('الملف غير موجود');
            }
            
            const oldPath = this.getFolderPath(fileInfo.path);
            const newFileName = this.generateUniqueFileName(fileInfo.originalName, targetPath);
            
            // تحديث معلومات الملف
            fileInfo.name = newFileName;
            fileInfo.path = targetPath + '/' + newFileName;
            fileInfo.modified = new Date().toISOString();
            fileInfo.moved = new Date().toISOString();
            
            // تحديث إحصائيات المجلدات
            this.updateFolderStats(oldPath);
            this.updateFolderStats(targetPath);
            
            await this.saveFile(fileInfo);
            
            console.log('✅ تم نقل الملف:', newFileName);
            return fileInfo;
            
        } catch (error) {
            console.error('خطأ في نقل الملف:', error);
            throw error;
        }
    }

    // البحث في الملفات
    searchFiles(query, options = {}) {
        const results = [];
        const searchTerm = query.toLowerCase();
        
        this.files.forEach(file => {
            let match = false;
            
            // البحث في اسم الملف
            if (file.name.toLowerCase().includes(searchTerm) || 
                file.originalName.toLowerCase().includes(searchTerm)) {
                match = true;
            }
            
            // البحث في الوصف
            if (file.metadata.description && 
                file.metadata.description.toLowerCase().includes(searchTerm)) {
                match = true;
            }
            
            // البحث في العلامات
            if (file.metadata.tags && 
                file.metadata.tags.some(tag => tag.toLowerCase().includes(searchTerm))) {
                match = true;
            }
            
            // فلترة حسب النوع
            if (options.type && file.type !== options.type) {
                match = false;
            }
            
            // فلترة حسب التاريخ
            if (options.dateFrom && new Date(file.created) < new Date(options.dateFrom)) {
                match = false;
            }
            
            if (options.dateTo && new Date(file.created) > new Date(options.dateTo)) {
                match = false;
            }
            
            // فلترة حسب الحجم
            if (options.minSize && file.size < options.minSize) {
                match = false;
            }
            
            if (options.maxSize && file.size > options.maxSize) {
                match = false;
            }
            
            if (match) {
                results.push(file);
            }
        });
        
        return results;
    }

    // الحصول على ملفات المجلد
    getFolderFiles(folderPath) {
        const files = [];
        
        this.files.forEach(file => {
            const fileFolderPath = this.getFolderPath(file.path);
            if (fileFolderPath === folderPath) {
                files.push(file);
            }
        });
        
        return files;
    }

    // الحصول على المجلدات الفرعية
    getSubFolders(parentPath) {
        const subFolders = [];
        
        this.folders.forEach(folder => {
            if (folder.parent === parentPath) {
                subFolders.push(folder);
            }
        });
        
        return subFolders;
    }

    // التحقق من صحة الملف
    validateFile(file) {
        // التحقق من الحجم
        if (file.size > this.settings.maxFileSize) {
            throw new Error(`حجم الملف كبير جداً. الحد الأقصى: ${this.formatFileSize(this.settings.maxFileSize)}`);
        }
        
        // التحقق من النوع
        const isAllowed = this.settings.allowedTypes.some(type => {
            if (type.includes('*')) {
                return file.type.startsWith(type.replace('*', ''));
            } else if (type.startsWith('.')) {
                return file.name.toLowerCase().endsWith(type.toLowerCase());
            } else {
                return file.type === type;
            }
        });
        
        if (!isAllowed) {
            throw new Error('نوع الملف غير مدعوم');
        }
    }

    // قراءة محتوى الملف
    readFileContent(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                resolve(e.target.result);
            };
            
            reader.onerror = () => {
                reject(new Error('خطأ في قراءة الملف'));
            };
            
            reader.readAsArrayBuffer(file);
        });
    }

    // ضغط الملف
    async compressFile(content) {
        // محاكاة ضغط الملف
        // في التطبيق الحقيقي، يمكن استخدام مكتبة ضغط
        return content;
    }

    // فك ضغط الملف
    async decompressFile(content) {
        // محاكاة فك ضغط الملف
        return content;
    }

    // تشفير الملف
    async encryptFile(content) {
        // محاكاة تشفير الملف
        // في التطبيق الحقيقي، يمكن استخدام Web Crypto API
        return content;
    }

    // فك تشفير الملف
    async decryptFile(content) {
        // محاكاة فك تشفير الملف
        return content;
    }

    // حساب المجموع الاختباري
    async calculateChecksum(content) {
        // محاكاة حساب المجموع الاختباري
        return 'checksum_' + Date.now();
    }

    // توليد اسم ملف فريد
    generateUniqueFileName(originalName, path) {
        let fileName = originalName;
        let counter = 1;
        
        while (this.fileExists(path + '/' + fileName)) {
            const extension = originalName.substring(originalName.lastIndexOf('.'));
            const nameWithoutExt = originalName.substring(0, originalName.lastIndexOf('.'));
            fileName = `${nameWithoutExt} (${counter})${extension}`;
            counter++;
        }
        
        return fileName;
    }

    // التحقق من وجود الملف
    fileExists(filePath) {
        for (const file of this.files.values()) {
            if (file.path === filePath) {
                return true;
            }
        }
        return false;
    }

    // الحصول على مسار المجلد من مسار الملف
    getFolderPath(filePath) {
        return filePath.substring(0, filePath.lastIndexOf('/')) || '/';
    }

    // تحديث إحصائيات المجلد
    updateFolderStats(folderPath) {
        const folder = this.folders.get(folderPath);
        if (!folder) return;
        
        const files = this.getFolderFiles(folderPath);
        folder.fileCount = files.length;
        folder.size = files.reduce((total, file) => total + file.size, 0);
        folder.modified = new Date().toISOString();
        
        this.saveFolders();
    }

    // نقل إلى سلة المحذوفات
    async moveToTrash(fileInfo) {
        const trashPath = '/trash';
        
        // إنشاء مجلد سلة المحذوفات إذا لم يكن موجوداً
        if (!this.folders.has(trashPath)) {
            this.createFolder('trash', '/');
        }
        
        // نقل الملف إلى سلة المحذوفات
        const trashFileName = `${Date.now()}_${fileInfo.originalName}`;
        fileInfo.path = trashPath + '/' + trashFileName;
        fileInfo.name = trashFileName;
        fileInfo.deleted = new Date().toISOString();
        
        await this.saveFile(fileInfo);
    }

    // تنسيق حجم الملف
    formatFileSize(bytes) {
        if (bytes === 0) return '0 بايت';
        
        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // توليد معرف فريد
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // إعداد قاعدة البيانات
    setupDatabase() {
        // محاكاة إعداد قاعدة البيانات
        console.log('📊 إعداد قاعدة بيانات الملفات');
    }

    // حفظ الملف في قاعدة البيانات
    async saveFile(fileInfo) {
        try {
            const files = JSON.parse(localStorage.getItem('file_manager_files') || '{}');
            files[fileInfo.id] = fileInfo;
            localStorage.setItem('file_manager_files', JSON.stringify(files));
        } catch (error) {
            console.error('خطأ في حفظ الملف:', error);
        }
    }

    // حفظ المجلدات
    saveFolders() {
        try {
            const foldersObj = {};
            this.folders.forEach((folder, path) => {
                foldersObj[path] = folder;
            });
            localStorage.setItem('file_manager_folders', JSON.stringify(foldersObj));
        } catch (error) {
            console.error('خطأ في حفظ المجلدات:', error);
        }
    }

    // تحميل الملفات
    loadFiles() {
        try {
            const files = JSON.parse(localStorage.getItem('file_manager_files') || '{}');
            Object.values(files).forEach(file => {
                this.files.set(file.id, file);
            });
            
            const folders = JSON.parse(localStorage.getItem('file_manager_folders') || '{}');
            Object.values(folders).forEach(folder => {
                this.folders.set(folder.path, folder);
            });
            
            console.log(`📁 تم تحميل ${this.files.size} ملف و ${this.folders.size} مجلد`);
        } catch (error) {
            console.error('خطأ في تحميل الملفات:', error);
        }
    }

    // إنشاء نسخة احتياطية للملف
    async createFileBackup(fileInfo) {
        // محاكاة إنشاء نسخة احتياطية
        console.log('💾 إنشاء نسخة احتياطية للملف:', fileInfo.name);
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // سيتم إضافة مستمعي الأحداث هنا
    }
}

// تصدير الكلاس
window.FileManager = FileManager;
