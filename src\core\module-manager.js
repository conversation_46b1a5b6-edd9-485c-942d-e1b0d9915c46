/**
 * Black Horse POS - Module Manager
 * مدير الوحدات المحسن
 * Developer: Augment Agent
 */

class ModuleManager {
    constructor() {
        this.modules = new Map();
        this.loadedModules = new Set();
        this.dependencies = new Map();
        this.loadingPromises = new Map();
        this.isInitialized = false;
        
        // مسارات الوحدات
        this.modulePaths = {
            // الوحدات الأساسية
            'UnifiedStorage': 'src/database/unified-storage.js',
            'DataMigration': 'src/database/data-migration.js',
            
            // وحدات الأعمال
            'ProductsManager': 'src/modules/products/products-manager.js',
            'CustomersManager': 'src/modules/customers/customers-manager.js',
            'SalesManager': 'src/modules/sales/sales-manager.js',
            'ReportsManager': 'src/modules/reports/reports-manager.js',
            
            // وحدات الواجهة
            'UIManager': 'src/ui/ui-manager.js',
            'NavigationManager': 'src/ui/navigation-manager.js',
            
            // الأدوات المساعدة
            'Utils': 'src/utils/helpers.js',
            'Validators': 'src/utils/validators.js'
        };
        
        // تبعيات الوحدات
        this.moduleDependencies = {
            'ProductsManager': ['UnifiedStorage', 'Utils', 'Validators'],
            'CustomersManager': ['UnifiedStorage', 'Utils', 'Validators'],
            'SalesManager': ['UnifiedStorage', 'ProductsManager', 'CustomersManager', 'Utils'],
            'ReportsManager': ['UnifiedStorage', 'SalesManager', 'Utils'],
            'UIManager': ['NavigationManager', 'Utils'],
            'NavigationManager': ['Utils']
        };
        
        console.log('🔧 Module Manager initialized');
    }

    // تهيئة النظام
    async init() {
        try {
            console.log('🚀 Initializing Module Manager...');
            
            // تحميل الوحدات الأساسية أولاً
            await this.loadCoreModules();
            
            // تشغيل ترحيل البيانات إذا لزم الأمر
            await this.runDataMigration();
            
            // تحميل وحدات الأعمال
            await this.loadBusinessModules();
            
            // تحميل وحدات الواجهة
            await this.loadUIModules();
            
            this.isInitialized = true;
            console.log('✅ Module Manager initialized successfully');
            
            return true;
            
        } catch (error) {
            console.error('❌ Error initializing Module Manager:', error);
            return false;
        }
    }

    // تحميل الوحدات الأساسية
    async loadCoreModules() {
        const coreModules = ['UnifiedStorage', 'DataMigration', 'Utils', 'Validators'];
        
        for (const moduleName of coreModules) {
            await this.loadModule(moduleName);
        }
        
        console.log('✅ Core modules loaded');
    }

    // تشغيل ترحيل البيانات
    async runDataMigration() {
        try {
            if (window.DataMigration) {
                console.log('🔄 Running data migration...');
                await window.DataMigration.startMigration();
                console.log('✅ Data migration completed');
            }
        } catch (error) {
            console.error('❌ Error during data migration:', error);
        }
    }

    // تحميل وحدات الأعمال
    async loadBusinessModules() {
        const businessModules = ['ProductsManager', 'CustomersManager', 'SalesManager', 'ReportsManager'];
        
        for (const moduleName of businessModules) {
            await this.loadModule(moduleName);
        }
        
        console.log('✅ Business modules loaded');
    }

    // تحميل وحدات الواجهة
    async loadUIModules() {
        const uiModules = ['NavigationManager', 'UIManager'];
        
        for (const moduleName of uiModules) {
            await this.loadModule(moduleName);
        }
        
        console.log('✅ UI modules loaded');
    }

    // تحميل وحدة واحدة
    async loadModule(moduleName) {
        try {
            // التحقق من التحميل المسبق
            if (this.loadedModules.has(moduleName)) {
                return this.modules.get(moduleName);
            }
            
            // التحقق من وجود عملية تحميل جارية
            if (this.loadingPromises.has(moduleName)) {
                return await this.loadingPromises.get(moduleName);
            }
            
            // إنشاء promise للتحميل
            const loadingPromise = this._loadModuleInternal(moduleName);
            this.loadingPromises.set(moduleName, loadingPromise);
            
            const module = await loadingPromise;
            
            // تنظيف promise التحميل
            this.loadingPromises.delete(moduleName);
            
            return module;
            
        } catch (error) {
            console.error(`❌ Error loading module ${moduleName}:`, error);
            this.loadingPromises.delete(moduleName);
            
            // إنشاء وحدة بديلة
            return this.createFallbackModule(moduleName);
        }
    }

    // التحميل الداخلي للوحدة
    async _loadModuleInternal(moduleName) {
        console.log(`📦 Loading module: ${moduleName}`);
        
        // تحميل التبعيات أولاً
        await this.loadDependencies(moduleName);
        
        // الحصول على مسار الوحدة
        const modulePath = this.modulePaths[moduleName];
        if (!modulePath) {
            throw new Error(`Module path not found: ${moduleName}`);
        }
        
        // تحميل الوحدة
        const module = await this.loadScript(modulePath);
        
        // التحقق من تحميل الوحدة
        const moduleInstance = window[moduleName];
        if (!moduleInstance) {
            throw new Error(`Module not found in global scope: ${moduleName}`);
        }
        
        // حفظ الوحدة
        this.modules.set(moduleName, moduleInstance);
        this.loadedModules.add(moduleName);
        
        // تهيئة الوحدة إذا كانت تحتوي على دالة init
        if (typeof moduleInstance.init === 'function') {
            await moduleInstance.init();
        }
        
        console.log(`✅ Module loaded successfully: ${moduleName}`);
        return moduleInstance;
    }

    // تحميل تبعيات الوحدة
    async loadDependencies(moduleName) {
        const dependencies = this.moduleDependencies[moduleName] || [];
        
        for (const dependency of dependencies) {
            if (!this.loadedModules.has(dependency)) {
                await this.loadModule(dependency);
            }
        }
    }

    // تحميل ملف JavaScript
    async loadScript(src) {
        return new Promise((resolve, reject) => {
            // التحقق من وجود الملف مسبقاً
            const existingScript = document.querySelector(`script[src="${src}"]`);
            if (existingScript) {
                resolve(true);
                return;
            }
            
            const script = document.createElement('script');
            script.src = src;
            script.async = true;
            
            script.onload = () => {
                console.log(`📜 Script loaded: ${src}`);
                resolve(true);
            };
            
            script.onerror = () => {
                console.error(`❌ Failed to load script: ${src}`);
                reject(new Error(`Failed to load script: ${src}`));
            };
            
            document.head.appendChild(script);
        });
    }

    // إنشاء وحدة بديلة
    createFallbackModule(moduleName) {
        console.warn(`⚠️ Creating fallback module: ${moduleName}`);
        
        const fallbackModule = {
            name: moduleName,
            isFallback: true,
            init: async () => {
                console.log(`🔄 Fallback module initialized: ${moduleName}`);
            },
            // دوال أساسية حسب نوع الوحدة
            ...this.getFallbackMethods(moduleName)
        };
        
        // حفظ الوحدة البديلة
        this.modules.set(moduleName, fallbackModule);
        this.loadedModules.add(moduleName);
        window[moduleName] = fallbackModule;
        
        return fallbackModule;
    }

    // الحصول على دوال بديلة حسب نوع الوحدة
    getFallbackMethods(moduleName) {
        const commonMethods = {
            save: async (data) => {
                console.warn(`⚠️ Fallback save called for ${moduleName}`);
                return data;
            },
            load: async () => {
                console.warn(`⚠️ Fallback load called for ${moduleName}`);
                return [];
            },
            delete: async (id) => {
                console.warn(`⚠️ Fallback delete called for ${moduleName}`);
                return true;
            }
        };
        
        switch (moduleName) {
            case 'ProductsManager':
                return {
                    ...commonMethods,
                    addProduct: async (product) => commonMethods.save(product),
                    getProducts: async () => commonMethods.load(),
                    searchProducts: async (query) => []
                };
                
            case 'CustomersManager':
                return {
                    ...commonMethods,
                    addCustomer: async (customer) => commonMethods.save(customer),
                    getCustomers: async () => commonMethods.load(),
                    searchCustomers: async (query) => []
                };
                
            case 'SalesManager':
                return {
                    ...commonMethods,
                    processSale: async (sale) => commonMethods.save(sale),
                    getSales: async () => commonMethods.load(),
                    getSalesReport: async () => ({ total: 0, count: 0 })
                };
                
            case 'NavigationManager':
                return {
                    showPage: (pageId) => {
                        console.warn(`⚠️ Fallback navigation to ${pageId}`);
                        // محاولة إظهار الصفحة بالطريقة التقليدية
                        const page = document.getElementById(pageId);
                        if (page) {
                            document.querySelectorAll('.page').forEach(p => p.style.display = 'none');
                            page.style.display = 'block';
                        }
                    }
                };
                
            default:
                return commonMethods;
        }
    }

    // الحصول على وحدة
    getModule(moduleName) {
        return this.modules.get(moduleName) || null;
    }

    // التحقق من تحميل الوحدة
    isModuleLoaded(moduleName) {
        return this.loadedModules.has(moduleName);
    }

    // الحصول على قائمة الوحدات المحملة
    getLoadedModules() {
        return Array.from(this.loadedModules);
    }

    // إعادة تحميل وحدة
    async reloadModule(moduleName) {
        // إزالة الوحدة من الذاكرة
        this.modules.delete(moduleName);
        this.loadedModules.delete(moduleName);
        
        // إزالة script tag إذا وجد
        const script = document.querySelector(`script[src="${this.modulePaths[moduleName]}"]`);
        if (script) {
            script.remove();
        }
        
        // إعادة التحميل
        return await this.loadModule(moduleName);
    }

    // إحصائيات النظام
    getStats() {
        return {
            totalModules: Object.keys(this.modulePaths).length,
            loadedModules: this.loadedModules.size,
            failedModules: Object.keys(this.modulePaths).length - this.loadedModules.size,
            isInitialized: this.isInitialized,
            modules: this.getLoadedModules()
        };
    }
}

// إنشاء instance عام
window.ModuleManager = new ModuleManager();

// تصدير للاستخدام في الوحدات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModuleManager;
}

console.log('✅ Module Manager loaded successfully');
