<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الاختبار الشامل - Black Horse POS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .test-header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .test-header p {
            margin: 10px 0 0 0;
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .test-content {
            padding: 30px;
        }

        .test-controls {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .btn-test {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }

        .btn-test:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }

        .progress-section {
            margin: 20px 0;
        }

        .progress {
            height: 20px;
            border-radius: 10px;
            background: #e9ecef;
        }

        .progress-bar {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        .test-status {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            min-height: 200px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .test-results {
            margin-top: 30px;
        }

        .suite-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            margin: 10px 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .suite-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .suite-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .suite-header:hover {
            background: #e9ecef;
        }

        .suite-body {
            padding: 20px;
            display: none;
        }

        .suite-body.show {
            display: block;
        }

        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .test-item:last-child {
            border-bottom: none;
        }

        .test-name {
            font-weight: 500;
        }

        .test-result {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .badge-success {
            background: #28a745;
        }

        .badge-danger {
            background: #dc3545;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .summary-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .summary-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .summary-card .number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .summary-card .label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .export-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }

        .recommendations {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .recommendation-item {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }

        .recommendation-item.success {
            background: #d4edda;
            border-left: 4px solid #28a745;
        }

        .recommendation-item.warning {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
        }

        .recommendation-item.error {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
        }

        .recommendation-item.critical {
            background: #f5c6cb;
            border-left: 4px solid #721c24;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .rtl {
            direction: rtl;
            text-align: right;
        }

        .text-success { color: #28a745 !important; }
        .text-danger { color: #dc3545 !important; }
        .text-warning { color: #ffc107 !important; }
        .text-info { color: #17a2b8 !important; }
    </style>
</head>
<body class="rtl">
    <div class="test-container">
        <!-- رأس الصفحة -->
        <div class="test-header">
            <h1><i class="fas fa-vials me-3"></i>الاختبار الشامل</h1>
            <p>نظام الاختبار الشامل لـ Black Horse POS</p>
        </div>

        <!-- محتوى الاختبار -->
        <div class="test-content">
            <!-- أدوات التحكم -->
            <div class="test-controls">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <button id="runAllTestsBtn" class="btn btn-test">
                            <i class="fas fa-play me-2"></i>تشغيل جميع الاختبارات
                        </button>
                        <button id="runSelectedTestsBtn" class="btn btn-test">
                            <i class="fas fa-check-square me-2"></i>تشغيل المحدد
                        </button>
                        <button id="stopTestsBtn" class="btn btn-test" disabled>
                            <i class="fas fa-stop me-2"></i>إيقاف
                        </button>
                    </div>
                    <div class="col-md-6 text-end">
                        <button id="exportJsonBtn" class="btn btn-outline-primary" disabled>
                            <i class="fas fa-download me-2"></i>تصدير JSON
                        </button>
                        <button id="exportCsvBtn" class="btn btn-outline-success" disabled>
                            <i class="fas fa-file-csv me-2"></i>تصدير CSV
                        </button>
                        <button id="exportTxtBtn" class="btn btn-outline-info" disabled>
                            <i class="fas fa-file-alt me-2"></i>تصدير TXT
                        </button>
                    </div>
                </div>

                <!-- اختيار مجموعات الاختبار -->
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>اختر مجموعات الاختبار:</h6>
                        <div class="form-check-container">
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="testDatabase" value="database" checked>
                                <label class="form-check-label" for="testDatabase">قاعدة البيانات</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="testUI" value="ui" checked>
                                <label class="form-check-label" for="testUI">واجهة المستخدم</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="testPerformance" value="performance" checked>
                                <label class="form-check-label" for="testPerformance">الأداء</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="testModules" value="modules" checked>
                                <label class="form-check-label" for="testModules">الوحدات</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="testIntegration" value="integration" checked>
                                <label class="form-check-label" for="testIntegration">التكامل</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="testSecurity" value="security" checked>
                                <label class="form-check-label" for="testSecurity">الأمان</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="testOptimization" value="optimization" checked>
                                <label class="form-check-label" for="testOptimization">التحسينات</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- شريط التقدم -->
            <div class="progress-section">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span id="progressText">جاهز للبدء</span>
                    <span id="progressPercent">0%</span>
                </div>
                <div class="progress">
                    <div id="progressBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
            </div>

            <!-- حالة الاختبار -->
            <div class="test-status" id="testStatus">
                <div class="text-center text-muted">
                    <i class="fas fa-info-circle me-2"></i>
                    اضغط على "تشغيل جميع الاختبارات" لبدء الاختبار الشامل
                </div>
            </div>

            <!-- ملخص النتائج -->
            <div id="summarySection" class="summary-section" style="display: none;">
                <h4><i class="fas fa-chart-bar me-2"></i>ملخص النتائج</h4>
                <div class="summary-cards" id="summaryCards">
                    <!-- سيتم ملؤها بواسطة JavaScript -->
                </div>
            </div>

            <!-- النتائج التفصيلية -->
            <div id="resultsSection" class="test-results" style="display: none;">
                <h4><i class="fas fa-list-check me-2"></i>النتائج التفصيلية</h4>
                <div id="testSuites">
                    <!-- سيتم ملؤها بواسطة JavaScript -->
                </div>
            </div>

            <!-- التوصيات -->
            <div id="recommendationsSection" class="recommendations" style="display: none;">
                <h5><i class="fas fa-lightbulb me-2"></i>التوصيات</h5>
                <div id="recommendationsList">
                    <!-- سيتم ملؤها بواسطة JavaScript -->
                </div>
            </div>

            <!-- قسم التصدير -->
            <div id="exportSection" class="export-section" style="display: none;">
                <h5><i class="fas fa-download me-2"></i>تصدير النتائج</h5>
                <p class="text-muted">يمكنك تصدير نتائج الاختبار بتنسيقات مختلفة للمراجعة أو الأرشفة.</p>
                <div class="d-flex gap-2 flex-wrap">
                    <button class="btn btn-outline-primary" onclick="exportReport('json')">
                        <i class="fas fa-file-code me-2"></i>JSON
                    </button>
                    <button class="btn btn-outline-success" onclick="exportReport('csv')">
                        <i class="fas fa-file-csv me-2"></i>CSV
                    </button>
                    <button class="btn btn-outline-info" onclick="exportReport('txt')">
                        <i class="fas fa-file-alt me-2"></i>نص عادي
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- تحميل نظام الاختبار -->
    <script src="src/tests/comprehensive-system-tests.js"></script>
    
    <!-- تحميل الوحدات المطلوبة للاختبار -->
    <script src="src/core/unified-storage.js"></script>
    <script src="src/core/database-optimizer.js"></script>
    <script src="src/core/asset-optimizer.js"></script>
    <script src="src/ui/ui-optimizer.js"></script>
    <script src="src/modules/products/products-manager.js"></script>
    <script src="src/modules/customers/customers-manager.js"></script>
    <script src="src/modules/sales/sales-manager.js"></script>
    <script src="src/modules/reports/reports-manager.js"></script>
    <script src="src/modules/expenses/expenses-manager.js"></script>
    <script src="src/ui/performance-dashboard.js"></script>
    <script src="src/core/cache-manager.js"></script>
    <script src="src/core/performance-optimizer.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lz-string/1.4.4/lz-string.min.js"></script>

    <script>
        // متغيرات عامة
        let testRunner = null;
        let currentReport = null;
        let isTestRunning = false;

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeTestRunner();
            setupEventListeners();
        });

        // تهيئة نظام الاختبار
        function initializeTestRunner() {
            try {
                testRunner = new ComprehensiveSystemTests();
                updateStatus('تم تهيئة نظام الاختبار بنجاح', 'success');
            } catch (error) {
                updateStatus('خطأ في تهيئة نظام الاختبار: ' + error.message, 'error');
                console.error('خطأ في التهيئة:', error);
            }
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            document.getElementById('runAllTestsBtn').addEventListener('click', runAllTests);
            document.getElementById('runSelectedTestsBtn').addEventListener('click', runSelectedTests);
            document.getElementById('stopTestsBtn').addEventListener('click', stopTests);

            document.getElementById('exportJsonBtn').addEventListener('click', () => exportReport('json'));
            document.getElementById('exportCsvBtn').addEventListener('click', () => exportReport('csv'));
            document.getElementById('exportTxtBtn').addEventListener('click', () => exportReport('txt'));
        }

        // تشغيل جميع الاختبارات
        async function runAllTests() {
            if (isTestRunning) return;

            try {
                isTestRunning = true;
                setTestingState(true);
                clearResults();

                updateStatus('بدء الاختبار الشامل...', 'info');
                updateProgress(0, 'تهيئة الاختبارات...');

                // تشغيل الاختبارات
                currentReport = await testRunner.runAllTests();

                updateProgress(100, 'اكتمل الاختبار');
                updateStatus('اكتمل الاختبار الشامل بنجاح', 'success');

                // عرض النتائج
                displayResults(currentReport);

            } catch (error) {
                updateStatus('خطأ في تشغيل الاختبارات: ' + error.message, 'error');
                console.error('خطأ في الاختبار:', error);
            } finally {
                isTestRunning = false;
                setTestingState(false);
            }
        }

        // تشغيل الاختبارات المحددة
        async function runSelectedTests() {
            if (isTestRunning) return;

            const selectedSuites = getSelectedTestSuites();
            if (selectedSuites.length === 0) {
                updateStatus('يرجى اختيار مجموعة اختبار واحدة على الأقل', 'warning');
                return;
            }

            try {
                isTestRunning = true;
                setTestingState(true);
                clearResults();

                updateStatus('بدء الاختبارات المحددة...', 'info');
                updateProgress(0, 'تهيئة الاختبارات...');

                // تشغيل الاختبارات المحددة
                currentReport = await runSpecificTests(selectedSuites);

                updateProgress(100, 'اكتمل الاختبار');
                updateStatus('اكتملت الاختبارات المحددة بنجاح', 'success');

                // عرض النتائج
                displayResults(currentReport);

            } catch (error) {
                updateStatus('خطأ في تشغيل الاختبارات: ' + error.message, 'error');
                console.error('خطأ في الاختبار:', error);
            } finally {
                isTestRunning = false;
                setTestingState(false);
            }
        }

        // إيقاف الاختبارات
        function stopTests() {
            if (!isTestRunning) return;

            isTestRunning = false;
            setTestingState(false);
            updateStatus('تم إيقاف الاختبارات', 'warning');
            updateProgress(0, 'متوقف');
        }

        // الحصول على مجموعات الاختبار المحددة
        function getSelectedTestSuites() {
            const checkboxes = document.querySelectorAll('.form-check-input:checked');
            return Array.from(checkboxes).map(cb => cb.value);
        }

        // تشغيل اختبارات محددة
        async function runSpecificTests(suites) {
            const report = {
                summary: {
                    totalTests: 0,
                    passedTests: 0,
                    failedTests: 0,
                    successRate: '0%',
                    duration: '0ms',
                    timestamp: new Date().toISOString()
                },
                testSuites: {},
                results: [],
                recommendations: []
            };

            const startTime = Date.now();

            for (const suite of suites) {
                updateProgress((suites.indexOf(suite) / suites.length) * 100, `تشغيل اختبارات ${suite}...`);

                switch (suite) {
                    case 'database':
                        await testRunner.runDatabaseTests();
                        break;
                    case 'ui':
                        await testRunner.runUITests();
                        break;
                    case 'performance':
                        await testRunner.runPerformanceTests();
                        break;
                    case 'modules':
                        await testRunner.runModuleTests();
                        break;
                    case 'integration':
                        await testRunner.runIntegrationTests();
                        break;
                    case 'security':
                        await testRunner.runSecurityTests();
                        break;
                    case 'optimization':
                        await testRunner.runOptimizationTests();
                        break;
                }
            }

            const endTime = Date.now();

            // جمع النتائج
            report.results = testRunner.testResults;
            report.summary.totalTests = testRunner.totalTests;
            report.summary.passedTests = testRunner.passedTests;
            report.summary.failedTests = testRunner.failedTests;
            report.summary.successRate = ((testRunner.passedTests / testRunner.totalTests) * 100).toFixed(2) + '%';
            report.summary.duration = (endTime - startTime) + 'ms';
            report.recommendations = testRunner.generateRecommendations();

            return report;
        }

        // تحديث حالة الاختبار
        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('testStatus');
            const timestamp = new Date().toLocaleTimeString('ar-SA');

            let icon = 'fas fa-info-circle';
            let colorClass = 'text-info';

            switch (type) {
                case 'success':
                    icon = 'fas fa-check-circle';
                    colorClass = 'text-success';
                    break;
                case 'error':
                    icon = 'fas fa-exclamation-circle';
                    colorClass = 'text-danger';
                    break;
                case 'warning':
                    icon = 'fas fa-exclamation-triangle';
                    colorClass = 'text-warning';
                    break;
            }

            const logEntry = `
                <div class="log-entry ${colorClass}">
                    <i class="${icon} me-2"></i>
                    <span class="timestamp">[${timestamp}]</span>
                    <span class="message">${message}</span>
                </div>
            `;

            statusElement.innerHTML += logEntry;
            statusElement.scrollTop = statusElement.scrollHeight;
        }

        // تحديث شريط التقدم
        function updateProgress(percent, text) {
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            const progressPercent = document.getElementById('progressPercent');

            progressBar.style.width = percent + '%';
            progressText.textContent = text;
            progressPercent.textContent = Math.round(percent) + '%';
        }

        // تعيين حالة الاختبار
        function setTestingState(testing) {
            document.getElementById('runAllTestsBtn').disabled = testing;
            document.getElementById('runSelectedTestsBtn').disabled = testing;
            document.getElementById('stopTestsBtn').disabled = !testing;

            const exportButtons = ['exportJsonBtn', 'exportCsvBtn', 'exportTxtBtn'];
            exportButtons.forEach(btnId => {
                document.getElementById(btnId).disabled = testing || !currentReport;
            });

            if (testing) {
                document.getElementById('runAllTestsBtn').innerHTML = '<span class="loading-spinner me-2"></span>جاري التشغيل...';
                document.getElementById('runSelectedTestsBtn').innerHTML = '<span class="loading-spinner me-2"></span>جاري التشغيل...';
            } else {
                document.getElementById('runAllTestsBtn').innerHTML = '<i class="fas fa-play me-2"></i>تشغيل جميع الاختبارات';
                document.getElementById('runSelectedTestsBtn').innerHTML = '<i class="fas fa-check-square me-2"></i>تشغيل المحدد';
            }
        }

        // مسح النتائج
        function clearResults() {
            document.getElementById('summarySection').style.display = 'none';
            document.getElementById('resultsSection').style.display = 'none';
            document.getElementById('recommendationsSection').style.display = 'none';
            document.getElementById('exportSection').style.display = 'none';
            document.getElementById('testStatus').innerHTML = '';
        }

        // عرض النتائج
        function displayResults(report) {
            if (!report) return;

            // عرض الملخص
            displaySummary(report.summary);

            // عرض النتائج التفصيلية
            displayDetailedResults(report);

            // عرض التوصيات
            displayRecommendations(report.recommendations);

            // إظهار قسم التصدير
            document.getElementById('exportSection').style.display = 'block';

            // تفعيل أزرار التصدير
            const exportButtons = ['exportJsonBtn', 'exportCsvBtn', 'exportTxtBtn'];
            exportButtons.forEach(btnId => {
                document.getElementById(btnId).disabled = false;
            });
        }

        // عرض الملخص
        function displaySummary(summary) {
            const summaryCards = document.getElementById('summaryCards');
            const successRate = parseFloat(summary.successRate);

            summaryCards.innerHTML = `
                <div class="summary-card">
                    <div class="number text-primary">${summary.totalTests}</div>
                    <div class="label">إجمالي الاختبارات</div>
                </div>
                <div class="summary-card">
                    <div class="number text-success">${summary.passedTests}</div>
                    <div class="label">نجح</div>
                </div>
                <div class="summary-card">
                    <div class="number text-danger">${summary.failedTests}</div>
                    <div class="label">فشل</div>
                </div>
                <div class="summary-card">
                    <div class="number ${successRate >= 90 ? 'text-success' : successRate >= 70 ? 'text-warning' : 'text-danger'}">${summary.successRate}</div>
                    <div class="label">معدل النجاح</div>
                </div>
                <div class="summary-card">
                    <div class="number text-info">${summary.duration}</div>
                    <div class="label">المدة</div>
                </div>
            `;

            document.getElementById('summarySection').style.display = 'block';
            document.getElementById('summarySection').classList.add('fade-in');
        }

        // عرض النتائج التفصيلية
        function displayDetailedResults(report) {
            const testSuites = document.getElementById('testSuites');
            testSuites.innerHTML = '';

            // تجميع النتائج حسب مجموعة الاختبارات
            const suiteGroups = {};
            report.results.forEach(result => {
                if (!suiteGroups[result.suite]) {
                    suiteGroups[result.suite] = [];
                }
                suiteGroups[result.suite].push(result);
            });

            // إنشاء بطاقة لكل مجموعة
            Object.keys(suiteGroups).forEach(suiteName => {
                const suiteResults = suiteGroups[suiteName];
                const passedCount = suiteResults.filter(r => r.passed).length;
                const totalCount = suiteResults.length;
                const successRate = ((passedCount / totalCount) * 100).toFixed(1);

                const suiteCard = document.createElement('div');
                suiteCard.className = 'suite-card';
                suiteCard.innerHTML = `
                    <div class="suite-header" onclick="toggleSuite('${suiteName}')">
                        <div>
                            <h6 class="mb-1">${getSuiteDisplayName(suiteName)}</h6>
                            <small class="text-muted">${passedCount}/${totalCount} نجح (${successRate}%)</small>
                        </div>
                        <div>
                            <span class="badge ${successRate >= 90 ? 'badge-success' : successRate >= 70 ? 'bg-warning' : 'badge-danger'}">${successRate}%</span>
                            <i class="fas fa-chevron-down ms-2" id="chevron-${suiteName}"></i>
                        </div>
                    </div>
                    <div class="suite-body" id="suite-${suiteName}">
                        ${suiteResults.map(result => `
                            <div class="test-item">
                                <div class="test-name">${result.testName}</div>
                                <div class="test-result">
                                    ${result.details ? `<small class="text-muted me-2">${result.details}</small>` : ''}
                                    <span class="badge ${result.passed ? 'badge-success' : 'badge-danger'}">
                                        <i class="fas ${result.passed ? 'fa-check' : 'fa-times'} me-1"></i>
                                        ${result.passed ? 'نجح' : 'فشل'}
                                    </span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;

                testSuites.appendChild(suiteCard);
            });

            document.getElementById('resultsSection').style.display = 'block';
            document.getElementById('resultsSection').classList.add('fade-in');
        }

        // تبديل عرض مجموعة الاختبارات
        function toggleSuite(suiteName) {
            const suiteBody = document.getElementById(`suite-${suiteName}`);
            const chevron = document.getElementById(`chevron-${suiteName}`);

            if (suiteBody.classList.contains('show')) {
                suiteBody.classList.remove('show');
                chevron.classList.remove('fa-chevron-up');
                chevron.classList.add('fa-chevron-down');
            } else {
                suiteBody.classList.add('show');
                chevron.classList.remove('fa-chevron-down');
                chevron.classList.add('fa-chevron-up');
            }
        }

        // الحصول على اسم العرض لمجموعة الاختبارات
        function getSuiteDisplayName(suiteName) {
            const displayNames = {
                'Database': 'اختبارات قاعدة البيانات',
                'UI': 'اختبارات واجهة المستخدم',
                'Performance': 'اختبارات الأداء',
                'Modules': 'اختبارات الوحدات',
                'Integration': 'اختبارات التكامل',
                'Security': 'اختبارات الأمان',
                'Optimization': 'اختبارات التحسينات'
            };
            return displayNames[suiteName] || suiteName;
        }

        // عرض التوصيات
        function displayRecommendations(recommendations) {
            if (!recommendations || recommendations.length === 0) return;

            const recommendationsList = document.getElementById('recommendationsList');
            recommendationsList.innerHTML = '';

            recommendations.forEach((rec, index) => {
                const recItem = document.createElement('div');
                recItem.className = `recommendation-item ${rec.type}`;

                let icon = 'fas fa-info-circle';
                switch (rec.type) {
                    case 'success':
                        icon = 'fas fa-check-circle';
                        break;
                    case 'warning':
                        icon = 'fas fa-exclamation-triangle';
                        break;
                    case 'error':
                        icon = 'fas fa-exclamation-circle';
                        break;
                    case 'critical':
                        icon = 'fas fa-times-circle';
                        break;
                }

                recItem.innerHTML = `
                    <div>
                        <i class="${icon}"></i>
                    </div>
                    <div>
                        <strong>${rec.message}</strong>
                        ${rec.details ? `<br><small>التفاصيل: ${Array.isArray(rec.details) ? rec.details.join(', ') : rec.details}</small>` : ''}
                        <br><small class="text-muted">الأولوية: ${getPriorityText(rec.priority)}</small>
                    </div>
                `;

                recommendationsList.appendChild(recItem);
            });

            document.getElementById('recommendationsSection').style.display = 'block';
            document.getElementById('recommendationsSection').classList.add('fade-in');
        }

        // الحصول على نص الأولوية
        function getPriorityText(priority) {
            const priorities = {
                'low': 'منخفضة',
                'medium': 'متوسطة',
                'high': 'عالية',
                'critical': 'حرجة'
            };
            return priorities[priority] || priority;
        }

        // تصدير التقرير
        function exportReport(format) {
            if (!currentReport) {
                updateStatus('لا يوجد تقرير للتصدير', 'warning');
                return;
            }

            try {
                const success = testRunner.exportReport(currentReport, format);
                if (success) {
                    updateStatus(`تم تصدير التقرير بتنسيق ${format.toUpperCase()} بنجاح`, 'success');
                } else {
                    updateStatus(`فشل في تصدير التقرير بتنسيق ${format.toUpperCase()}`, 'error');
                }
            } catch (error) {
                updateStatus(`خطأ في تصدير التقرير: ${error.message}`, 'error');
                console.error('خطأ في التصدير:', error);
            }
        }

        // حفظ التقرير في قاعدة البيانات
        async function saveReport() {
            if (!currentReport) {
                updateStatus('لا يوجد تقرير للحفظ', 'warning');
                return;
            }

            try {
                const reportId = await testRunner.saveReportToDatabase(currentReport);
                if (reportId) {
                    updateStatus(`تم حفظ التقرير برقم: ${reportId}`, 'success');
                } else {
                    updateStatus('فشل في حفظ التقرير', 'error');
                }
            } catch (error) {
                updateStatus(`خطأ في حفظ التقرير: ${error.message}`, 'error');
                console.error('خطأ في الحفظ:', error);
            }
        }

        // تحديث الحالة كل ثانية أثناء تشغيل الاختبارات
        setInterval(() => {
            if (isTestRunning && testRunner) {
                const progress = (testRunner.passedTests + testRunner.failedTests) / testRunner.totalTests * 100;
                updateProgress(progress, `تم إنجاز ${testRunner.passedTests + testRunner.failedTests} من ${testRunner.totalTests} اختبار`);
            }
        }, 1000);

        // إضافة مستمع لتحديث الحالة من نظام الاختبار
        if (typeof window !== 'undefined') {
            window.addEventListener('testProgress', (event) => {
                const { current, total, message } = event.detail;
                const progress = (current / total) * 100;
                updateProgress(progress, message);
            });

            window.addEventListener('testResult', (event) => {
                const { suite, testName, passed, details } = event.detail;
                const status = passed ? 'نجح' : 'فشل';
                const icon = passed ? '✅' : '❌';
                updateStatus(`${icon} ${suite}: ${testName} - ${status}${details ? ` (${details})` : ''}`, passed ? 'success' : 'error');
            });
        }

        // وظائف مساعدة إضافية

        // تنظيف الذاكرة
        function cleanupMemory() {
            if (window.gc && typeof window.gc === 'function') {
                window.gc();
            }
        }

        // إعادة تعيين النظام
        function resetSystem() {
            if (isTestRunning) {
                stopTests();
            }

            currentReport = null;
            clearResults();
            updateProgress(0, 'جاهز للبدء');
            updateStatus('تم إعادة تعيين النظام', 'info');

            // إعادة تهيئة نظام الاختبار
            setTimeout(() => {
                initializeTestRunner();
            }, 100);
        }

        // التحقق من حالة النظام
        function checkSystemHealth() {
            const healthStatus = {
                testRunner: !!testRunner,
                unifiedStorage: !!window.UnifiedStorage,
                modules: {
                    products: !!window.ProductsManager,
                    customers: !!window.CustomersManager,
                    sales: !!window.SalesManager,
                    reports: !!window.ReportsManager,
                    expenses: !!window.ExpensesManager
                },
                optimizers: {
                    database: !!window.DatabaseOptimizer,
                    asset: !!window.AssetOptimizer,
                    ui: !!window.UIOptimizer,
                    performance: !!window.PerformanceOptimizer
                }
            };

            console.log('حالة النظام:', healthStatus);
            return healthStatus;
        }

        // إضافة اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (event) => {
            if (event.ctrlKey || event.metaKey) {
                switch (event.key) {
                    case 'r':
                        event.preventDefault();
                        if (!isTestRunning) {
                            runAllTests();
                        }
                        break;
                    case 's':
                        event.preventDefault();
                        if (currentReport) {
                            exportReport('json');
                        }
                        break;
                    case 'Escape':
                        event.preventDefault();
                        if (isTestRunning) {
                            stopTests();
                        }
                        break;
                }
            }
        });

        // إضافة معلومات النظام في وحدة التحكم
        console.log('%c🐎 Black Horse POS - نظام الاختبار الشامل', 'color: #667eea; font-size: 16px; font-weight: bold;');
        console.log('الاختصارات:');
        console.log('Ctrl+R: تشغيل جميع الاختبارات');
        console.log('Ctrl+S: تصدير التقرير');
        console.log('Esc: إيقاف الاختبارات');
        console.log('---');
        console.log('للتحقق من حالة النظام: checkSystemHealth()');
        console.log('لإعادة تعيين النظام: resetSystem()');
        console.log('لتنظيف الذاكرة: cleanupMemory()');
    </script>
</body>
</html>
    </script>
</body>
</html>
