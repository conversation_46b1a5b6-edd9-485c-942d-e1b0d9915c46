/**
 * نظام الإدارة المالية المتقدم
 * Advanced Financial Management System
 */

class AdvancedFinanceManager {
    constructor() {
        this.accounts = new Map();
        this.transactions = new Map();
        this.budgets = new Map();
        this.invoices = new Map();
        this.payments = new Map();
        this.expenses = new Map();
        this.revenues = new Map();
        this.financialReports = new Map();
        this.taxSettings = new Map();
        this.currencies = new Map();
        
        this.settings = {
            baseCurrency: 'SAR',
            fiscalYearStart: '01-01', // MM-DD
            taxRate: 15, // VAT rate in Saudi Arabia
            accountingMethod: 'accrual', // accrual or cash
            autoBackup: true,
            reportingPeriod: 'monthly', // daily, weekly, monthly, quarterly, yearly
            decimalPlaces: 2,
            thousandsSeparator: ',',
            currencySymbol: 'ر.س',
            dateFormat: 'DD/MM/YYYY',
            timeFormat: '24h'
        };
        
        this.loadData();
        this.initializeDefaultAccounts();
        this.initializeDefaultCurrencies();
    }

    // إنشاء حساب جديد
    createAccount(accountData) {
        const account = {
            id: this.generateAccountId(),
            code: accountData.code || this.generateAccountCode(),
            name: accountData.name,
            nameEn: accountData.nameEn || '',
            type: accountData.type, // asset, liability, equity, revenue, expense
            category: accountData.category || '',
            
            // التصنيف المحاسبي
            classification: {
                mainCategory: accountData.mainCategory || '', // current, non-current, etc.
                subCategory: accountData.subCategory || '',
                nature: accountData.nature || 'debit', // debit or credit
                isControlAccount: accountData.isControlAccount || false,
                parentAccountId: accountData.parentAccountId || null
            },
            
            // الأرصدة
            balances: {
                openingBalance: accountData.openingBalance || 0,
                currentBalance: accountData.openingBalance || 0,
                debitBalance: 0,
                creditBalance: 0,
                lastYearBalance: 0,
                budgetedBalance: accountData.budgetedBalance || 0
            },
            
            // الإعدادات
            settings: {
                isActive: accountData.isActive !== false,
                allowDirectPosting: accountData.allowDirectPosting !== false,
                requireApproval: accountData.requireApproval || false,
                trackBudget: accountData.trackBudget || false,
                trackCostCenter: accountData.trackCostCenter || false,
                trackProject: accountData.trackProject || false,
                autoReconcile: accountData.autoReconcile || false
            },
            
            // معلومات البنك (للحسابات البنكية)
            bankInfo: accountData.bankInfo || {
                bankName: '',
                branchName: '',
                accountNumber: '',
                iban: '',
                swiftCode: '',
                routingNumber: '',
                contactPerson: '',
                contactPhone: '',
                contactEmail: ''
            },
            
            // الحدود والقيود
            limits: {
                creditLimit: accountData.creditLimit || 0,
                debitLimit: accountData.debitLimit || 0,
                dailyLimit: accountData.dailyLimit || 0,
                monthlyLimit: accountData.monthlyLimit || 0,
                warningThreshold: accountData.warningThreshold || 0
            },
            
            // التواريخ والمراجعة
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            createdBy: accountData.createdBy || 'system',
            lastReconciledDate: null,
            lastTransactionDate: null,
            
            // معلومات إضافية
            description: accountData.description || '',
            notes: accountData.notes || '',
            tags: accountData.tags || [],
            attachments: accountData.attachments || []
        };
        
        this.accounts.set(account.id, account);
        this.saveData();
        
        console.log(`✅ تم إنشاء الحساب: ${account.name}`);
        return account;
    }

    // إنشاء معاملة مالية
    createTransaction(transactionData) {
        const transaction = {
            id: this.generateTransactionId(),
            number: transactionData.number || this.generateTransactionNumber(),
            type: transactionData.type, // journal, payment, receipt, transfer, adjustment
            
            // التواريخ
            date: transactionData.date || new Date().toISOString(),
            valueDate: transactionData.valueDate || transactionData.date || new Date().toISOString(),
            dueDate: transactionData.dueDate || null,
            
            // المبلغ والعملة
            amount: transactionData.amount,
            currency: transactionData.currency || this.settings.baseCurrency,
            exchangeRate: transactionData.exchangeRate || 1,
            baseAmount: transactionData.amount * (transactionData.exchangeRate || 1),
            
            // الأطراف
            fromAccountId: transactionData.fromAccountId,
            toAccountId: transactionData.toAccountId,
            customerId: transactionData.customerId || null,
            supplierId: transactionData.supplierId || null,
            employeeId: transactionData.employeeId || null,
            
            // التفاصيل
            description: transactionData.description || '',
            reference: transactionData.reference || '',
            referenceType: transactionData.referenceType || null, // invoice, purchase, salary, etc.
            referenceId: transactionData.referenceId || null,
            
            // التصنيف
            category: transactionData.category || '',
            costCenter: transactionData.costCenter || null,
            project: transactionData.project || null,
            department: transactionData.department || null,
            
            // الضرائب
            taxInfo: {
                taxable: transactionData.taxable || false,
                taxRate: transactionData.taxRate || 0,
                taxAmount: transactionData.taxAmount || 0,
                taxAccountId: transactionData.taxAccountId || null,
                taxNumber: transactionData.taxNumber || ''
            },
            
            // الحالة والموافقة
            status: transactionData.status || 'pending', // pending, approved, posted, cancelled
            approvalRequired: transactionData.approvalRequired || false,
            approvedBy: null,
            approvedAt: null,
            postedBy: null,
            postedAt: null,
            
            // قيود اليومية
            journalEntries: transactionData.journalEntries || [],
            
            // معلومات الدفع
            paymentInfo: {
                method: transactionData.paymentMethod || '', // cash, bank, card, check, transfer
                checkNumber: transactionData.checkNumber || '',
                cardNumber: transactionData.cardNumber || '',
                bankReference: transactionData.bankReference || '',
                receiptNumber: transactionData.receiptNumber || ''
            },
            
            // المرفقات والملاحظات
            attachments: transactionData.attachments || [],
            notes: transactionData.notes || '',
            tags: transactionData.tags || [],
            
            // التواريخ
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            createdBy: transactionData.createdBy || 'system'
        };
        
        // إنشاء قيود اليومية التلقائية
        if (transaction.journalEntries.length === 0) {
            transaction.journalEntries = this.createAutoJournalEntries(transaction);
        }
        
        this.transactions.set(transaction.id, transaction);
        
        // تحديث أرصدة الحسابات
        if (transaction.status === 'posted') {
            this.updateAccountBalances(transaction);
        }
        
        this.saveData();
        
        console.log(`✅ تم إنشاء المعاملة: ${transaction.number}`);
        return transaction;
    }

    // إنشاء فاتورة
    createInvoice(invoiceData) {
        const invoice = {
            id: this.generateInvoiceId(),
            number: invoiceData.number || this.generateInvoiceNumber(),
            type: invoiceData.type || 'sales', // sales, purchase, credit_note, debit_note
            
            // التواريخ
            date: invoiceData.date || new Date().toISOString(),
            dueDate: invoiceData.dueDate || this.calculateDueDate(invoiceData.date, invoiceData.paymentTerms),
            
            // العميل/المورد
            customerId: invoiceData.customerId || null,
            customerName: invoiceData.customerName || '',
            customerAddress: invoiceData.customerAddress || '',
            customerTaxNumber: invoiceData.customerTaxNumber || '',
            
            // البنود
            items: invoiceData.items || [],
            
            // المبالغ
            subtotal: 0,
            discountAmount: invoiceData.discountAmount || 0,
            discountPercentage: invoiceData.discountPercentage || 0,
            taxAmount: 0,
            totalAmount: 0,
            paidAmount: 0,
            remainingAmount: 0,
            
            // العملة
            currency: invoiceData.currency || this.settings.baseCurrency,
            exchangeRate: invoiceData.exchangeRate || 1,
            
            // الضرائب
            taxInfo: {
                taxRate: invoiceData.taxRate || this.settings.taxRate,
                taxNumber: invoiceData.taxNumber || '',
                taxExempt: invoiceData.taxExempt || false,
                taxExemptReason: invoiceData.taxExemptReason || ''
            },
            
            // شروط الدفع
            paymentTerms: invoiceData.paymentTerms || 'net_30', // immediate, net_15, net_30, net_60, net_90
            paymentMethod: invoiceData.paymentMethod || '',
            
            // الحالة
            status: invoiceData.status || 'draft', // draft, sent, paid, overdue, cancelled
            
            // معلومات إضافية
            notes: invoiceData.notes || '',
            internalNotes: invoiceData.internalNotes || '',
            terms: invoiceData.terms || '',
            footer: invoiceData.footer || '',
            
            // التواريخ
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            sentAt: null,
            paidAt: null,
            createdBy: invoiceData.createdBy || 'system'
        };
        
        // حساب المبالغ
        this.calculateInvoiceAmounts(invoice);
        
        this.invoices.set(invoice.id, invoice);
        this.saveData();
        
        console.log(`✅ تم إنشاء الفاتورة: ${invoice.number}`);
        return invoice;
    }

    // إنشاء ميزانية
    createBudget(budgetData) {
        const budget = {
            id: this.generateBudgetId(),
            name: budgetData.name,
            description: budgetData.description || '',
            
            // الفترة الزمنية
            period: {
                type: budgetData.periodType || 'yearly', // monthly, quarterly, yearly
                startDate: budgetData.startDate,
                endDate: budgetData.endDate,
                fiscalYear: budgetData.fiscalYear || new Date().getFullYear()
            },
            
            // الحسابات والمبالغ
            accounts: budgetData.accounts || [], // [{accountId, budgetedAmount, actualAmount, variance}]
            
            // الإجماليات
            totals: {
                budgetedRevenue: 0,
                actualRevenue: 0,
                budgetedExpenses: 0,
                actualExpenses: 0,
                budgetedProfit: 0,
                actualProfit: 0,
                variance: 0,
                variancePercentage: 0
            },
            
            // الحالة
            status: budgetData.status || 'draft', // draft, approved, active, closed
            approvedBy: null,
            approvedAt: null,
            
            // الإعدادات
            settings: {
                autoUpdate: budgetData.autoUpdate || true,
                alertThreshold: budgetData.alertThreshold || 90, // تنبيه عند 90% من الميزانية
                allowOverspend: budgetData.allowOverspend || false,
                trackVariance: budgetData.trackVariance || true
            },
            
            // التواريخ
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            createdBy: budgetData.createdBy || 'system'
        };
        
        // حساب الإجماليات
        this.calculateBudgetTotals(budget);
        
        this.budgets.set(budget.id, budget);
        this.saveData();
        
        console.log(`✅ تم إنشاء الميزانية: ${budget.name}`);
        return budget;
    }

    // إنشاء تقرير مالي
    generateFinancialReport(reportType, options = {}) {
        const report = {
            id: this.generateReportId(),
            type: reportType, // balance_sheet, income_statement, cash_flow, trial_balance
            title: this.getReportTitle(reportType),
            
            // الفترة الزمنية
            period: {
                startDate: options.startDate || this.getDefaultStartDate(),
                endDate: options.endDate || new Date().toISOString(),
                comparison: options.comparison || null // previous_period, previous_year
            },
            
            // البيانات
            data: {},
            
            // الإعدادات
            settings: {
                currency: options.currency || this.settings.baseCurrency,
                showComparison: options.showComparison || false,
                showPercentages: options.showPercentages || false,
                showZeroBalances: options.showZeroBalances || false,
                groupByCategory: options.groupByCategory || true
            },
            
            // التواريخ
            generatedAt: new Date().toISOString(),
            generatedBy: options.generatedBy || 'system'
        };
        
        // إنشاء بيانات التقرير حسب النوع
        switch (reportType) {
            case 'balance_sheet':
                report.data = this.generateBalanceSheetData(report.period);
                break;
            case 'income_statement':
                report.data = this.generateIncomeStatementData(report.period);
                break;
            case 'cash_flow':
                report.data = this.generateCashFlowData(report.period);
                break;
            case 'trial_balance':
                report.data = this.generateTrialBalanceData(report.period);
                break;
        }
        
        this.financialReports.set(report.id, report);
        this.saveData();
        
        console.log(`✅ تم إنشاء التقرير المالي: ${report.title}`);
        return report;
    }

    // البحث في المعاملات
    searchTransactions(query, filters = {}) {
        let results = Array.from(this.transactions.values());
        
        // البحث النصي
        if (query && query.trim()) {
            const searchTerm = query.toLowerCase();
            results = results.filter(transaction => 
                transaction.number.toLowerCase().includes(searchTerm) ||
                transaction.description.toLowerCase().includes(searchTerm) ||
                transaction.reference.toLowerCase().includes(searchTerm)
            );
        }
        
        // تطبيق الفلاتر
        if (filters.type) {
            results = results.filter(t => t.type === filters.type);
        }
        
        if (filters.status) {
            results = results.filter(t => t.status === filters.status);
        }
        
        if (filters.dateFrom) {
            results = results.filter(t => new Date(t.date) >= new Date(filters.dateFrom));
        }
        
        if (filters.dateTo) {
            results = results.filter(t => new Date(t.date) <= new Date(filters.dateTo));
        }
        
        if (filters.amountFrom) {
            results = results.filter(t => t.amount >= filters.amountFrom);
        }
        
        if (filters.amountTo) {
            results = results.filter(t => t.amount <= filters.amountTo);
        }
        
        return results;
    }

    // الحصول على التقارير المالية
    getFinancialSummary() {
        const accounts = Array.from(this.accounts.values());
        const transactions = Array.from(this.transactions.values());
        const invoices = Array.from(this.invoices.values());
        
        // حساب الأرصدة
        const assets = accounts.filter(a => a.type === 'asset')
            .reduce((sum, a) => sum + a.balances.currentBalance, 0);
        
        const liabilities = accounts.filter(a => a.type === 'liability')
            .reduce((sum, a) => sum + a.balances.currentBalance, 0);
        
        const equity = accounts.filter(a => a.type === 'equity')
            .reduce((sum, a) => sum + a.balances.currentBalance, 0);
        
        const revenue = accounts.filter(a => a.type === 'revenue')
            .reduce((sum, a) => sum + a.balances.currentBalance, 0);
        
        const expenses = accounts.filter(a => a.type === 'expense')
            .reduce((sum, a) => sum + a.balances.currentBalance, 0);
        
        // إحصائيات المعاملات
        const todayTransactions = transactions.filter(t => {
            const today = new Date().toDateString();
            return new Date(t.date).toDateString() === today;
        });
        
        const pendingInvoices = invoices.filter(i => i.status === 'sent' || i.status === 'overdue');
        const overdueInvoices = invoices.filter(i => i.status === 'overdue');
        
        return {
            balances: {
                totalAssets: assets,
                totalLiabilities: liabilities,
                totalEquity: equity,
                totalRevenue: revenue,
                totalExpenses: expenses,
                netIncome: revenue - expenses,
                workingCapital: assets - liabilities
            },
            
            transactions: {
                totalTransactions: transactions.length,
                todayTransactions: todayTransactions.length,
                pendingTransactions: transactions.filter(t => t.status === 'pending').length,
                totalAmount: transactions.reduce((sum, t) => sum + t.amount, 0)
            },
            
            invoices: {
                totalInvoices: invoices.length,
                pendingInvoices: pendingInvoices.length,
                overdueInvoices: overdueInvoices.length,
                totalReceivables: pendingInvoices.reduce((sum, i) => sum + i.remainingAmount, 0),
                overdueAmount: overdueInvoices.reduce((sum, i) => sum + i.remainingAmount, 0)
            },
            
            performance: {
                profitMargin: revenue > 0 ? ((revenue - expenses) / revenue) * 100 : 0,
                expenseRatio: revenue > 0 ? (expenses / revenue) * 100 : 0,
                assetTurnover: revenue > 0 && assets > 0 ? revenue / assets : 0,
                debtToEquity: equity > 0 ? liabilities / equity : 0
            }
        };
    }

    // دوال مساعدة
    generateAccountId() {
        return 'ACC_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    generateAccountCode() {
        const count = this.accounts.size + 1;
        return count.toString().padStart(4, '0');
    }

    generateTransactionId() {
        return 'TXN_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    generateTransactionNumber() {
        const count = this.transactions.size + 1;
        return 'TXN' + count.toString().padStart(6, '0');
    }

    generateInvoiceId() {
        return 'INV_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    generateInvoiceNumber() {
        const count = this.invoices.size + 1;
        return 'INV' + count.toString().padStart(6, '0');
    }

    generateBudgetId() {
        return 'BUD_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    generateReportId() {
        return 'RPT_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // تهيئة الحسابات الافتراضية
    initializeDefaultAccounts() {
        if (this.accounts.size === 0) {
            const defaultAccounts = [
                // الأصول
                { code: '1000', name: 'النقدية', type: 'asset', category: 'current_assets' },
                { code: '1100', name: 'البنك', type: 'asset', category: 'current_assets' },
                { code: '1200', name: 'العملاء', type: 'asset', category: 'current_assets' },
                { code: '1300', name: 'المخزون', type: 'asset', category: 'current_assets' },
                { code: '1400', name: 'الأصول الثابتة', type: 'asset', category: 'fixed_assets' },

                // الخصوم
                { code: '2000', name: 'الموردين', type: 'liability', category: 'current_liabilities' },
                { code: '2100', name: 'القروض قصيرة الأجل', type: 'liability', category: 'current_liabilities' },
                { code: '2200', name: 'القروض طويلة الأجل', type: 'liability', category: 'long_term_liabilities' },

                // حقوق الملكية
                { code: '3000', name: 'رأس المال', type: 'equity', category: 'capital' },
                { code: '3100', name: 'الأرباح المحتجزة', type: 'equity', category: 'retained_earnings' },

                // الإيرادات
                { code: '4000', name: 'إيرادات المبيعات', type: 'revenue', category: 'sales_revenue' },
                { code: '4100', name: 'إيرادات أخرى', type: 'revenue', category: 'other_revenue' },

                // المصروفات
                { code: '5000', name: 'تكلفة البضاعة المباعة', type: 'expense', category: 'cost_of_goods' },
                { code: '5100', name: 'مصروفات التشغيل', type: 'expense', category: 'operating_expenses' },
                { code: '5200', name: 'مصروفات إدارية', type: 'expense', category: 'administrative_expenses' },
                { code: '5300', name: 'مصروفات تسويقية', type: 'expense', category: 'marketing_expenses' }
            ];

            defaultAccounts.forEach(accountData => {
                this.createAccount(accountData);
            });
        }
    }

    // تهيئة العملات الافتراضية
    initializeDefaultCurrencies() {
        if (this.currencies.size === 0) {
            const defaultCurrencies = [
                { code: 'SAR', name: 'ريال سعودي', symbol: 'ر.س', rate: 1 },
                { code: 'USD', name: 'دولار أمريكي', symbol: '$', rate: 3.75 },
                { code: 'EUR', name: 'يورو', symbol: '€', rate: 4.1 },
                { code: 'GBP', name: 'جنيه إسترليني', symbol: '£', rate: 4.7 },
                { code: 'AED', name: 'درهم إماراتي', symbol: 'د.إ', rate: 1.02 }
            ];

            defaultCurrencies.forEach(currency => {
                this.currencies.set(currency.code, currency);
            });
        }
    }

    // إنشاء قيود اليومية التلقائية
    createAutoJournalEntries(transaction) {
        const entries = [];

        if (transaction.type === 'payment' || transaction.type === 'receipt') {
            // قيد الدفع أو الاستلام
            entries.push({
                accountId: transaction.toAccountId,
                debit: transaction.type === 'receipt' ? transaction.amount : 0,
                credit: transaction.type === 'payment' ? transaction.amount : 0,
                description: transaction.description
            });

            entries.push({
                accountId: transaction.fromAccountId,
                debit: transaction.type === 'payment' ? transaction.amount : 0,
                credit: transaction.type === 'receipt' ? transaction.amount : 0,
                description: transaction.description
            });
        }

        return entries;
    }

    // تحديث أرصدة الحسابات
    updateAccountBalances(transaction) {
        transaction.journalEntries.forEach(entry => {
            const account = this.accounts.get(entry.accountId);
            if (account) {
                account.balances.debitBalance += entry.debit || 0;
                account.balances.creditBalance += entry.credit || 0;

                // حساب الرصيد الحالي حسب طبيعة الحساب
                if (account.classification.nature === 'debit') {
                    account.balances.currentBalance = account.balances.debitBalance - account.balances.creditBalance;
                } else {
                    account.balances.currentBalance = account.balances.creditBalance - account.balances.debitBalance;
                }

                account.lastTransactionDate = transaction.date;
                account.updatedAt = new Date().toISOString();
            }
        });
    }

    // حساب مبالغ الفاتورة
    calculateInvoiceAmounts(invoice) {
        // حساب المجموع الفرعي
        invoice.subtotal = invoice.items.reduce((sum, item) => {
            return sum + (item.quantity * item.unitPrice);
        }, 0);

        // حساب الخصم
        if (invoice.discountPercentage > 0) {
            invoice.discountAmount = (invoice.subtotal * invoice.discountPercentage) / 100;
        }

        const afterDiscount = invoice.subtotal - invoice.discountAmount;

        // حساب الضريبة
        if (!invoice.taxInfo.taxExempt) {
            invoice.taxAmount = (afterDiscount * invoice.taxInfo.taxRate) / 100;
        }

        // حساب المجموع الإجمالي
        invoice.totalAmount = afterDiscount + invoice.taxAmount;
        invoice.remainingAmount = invoice.totalAmount - invoice.paidAmount;
    }

    // حساب إجماليات الميزانية
    calculateBudgetTotals(budget) {
        budget.totals.budgetedRevenue = 0;
        budget.totals.actualRevenue = 0;
        budget.totals.budgetedExpenses = 0;
        budget.totals.actualExpenses = 0;

        budget.accounts.forEach(budgetAccount => {
            const account = this.accounts.get(budgetAccount.accountId);
            if (account) {
                if (account.type === 'revenue') {
                    budget.totals.budgetedRevenue += budgetAccount.budgetedAmount;
                    budget.totals.actualRevenue += budgetAccount.actualAmount;
                } else if (account.type === 'expense') {
                    budget.totals.budgetedExpenses += budgetAccount.budgetedAmount;
                    budget.totals.actualExpenses += budgetAccount.actualAmount;
                }
            }
        });

        budget.totals.budgetedProfit = budget.totals.budgetedRevenue - budget.totals.budgetedExpenses;
        budget.totals.actualProfit = budget.totals.actualRevenue - budget.totals.actualExpenses;
        budget.totals.variance = budget.totals.actualProfit - budget.totals.budgetedProfit;

        if (budget.totals.budgetedProfit !== 0) {
            budget.totals.variancePercentage = (budget.totals.variance / budget.totals.budgetedProfit) * 100;
        }
    }

    // حساب تاريخ الاستحقاق
    calculateDueDate(invoiceDate, paymentTerms) {
        const date = new Date(invoiceDate);

        switch (paymentTerms) {
            case 'immediate':
                return date.toISOString();
            case 'net_15':
                date.setDate(date.getDate() + 15);
                break;
            case 'net_30':
                date.setDate(date.getDate() + 30);
                break;
            case 'net_60':
                date.setDate(date.getDate() + 60);
                break;
            case 'net_90':
                date.setDate(date.getDate() + 90);
                break;
            default:
                date.setDate(date.getDate() + 30);
        }

        return date.toISOString();
    }

    // الحصول على عنوان التقرير
    getReportTitle(reportType) {
        const titles = {
            'balance_sheet': 'الميزانية العمومية',
            'income_statement': 'قائمة الدخل',
            'cash_flow': 'قائمة التدفقات النقدية',
            'trial_balance': 'ميزان المراجعة'
        };
        return titles[reportType] || reportType;
    }

    // الحصول على تاريخ البداية الافتراضي
    getDefaultStartDate() {
        const now = new Date();
        const startOfYear = new Date(now.getFullYear(), 0, 1);
        return startOfYear.toISOString();
    }

    // إنشاء بيانات الميزانية العمومية
    generateBalanceSheetData(period) {
        const accounts = Array.from(this.accounts.values());

        const assets = accounts.filter(a => a.type === 'asset');
        const liabilities = accounts.filter(a => a.type === 'liability');
        const equity = accounts.filter(a => a.type === 'equity');

        return {
            assets: {
                current: assets.filter(a => a.category.includes('current')),
                fixed: assets.filter(a => a.category.includes('fixed')),
                total: assets.reduce((sum, a) => sum + a.balances.currentBalance, 0)
            },
            liabilities: {
                current: liabilities.filter(a => a.category.includes('current')),
                longTerm: liabilities.filter(a => a.category.includes('long_term')),
                total: liabilities.reduce((sum, a) => sum + a.balances.currentBalance, 0)
            },
            equity: {
                accounts: equity,
                total: equity.reduce((sum, a) => sum + a.balances.currentBalance, 0)
            }
        };
    }

    // إنشاء بيانات قائمة الدخل
    generateIncomeStatementData(period) {
        const accounts = Array.from(this.accounts.values());

        const revenue = accounts.filter(a => a.type === 'revenue');
        const expenses = accounts.filter(a => a.type === 'expense');

        const totalRevenue = revenue.reduce((sum, a) => sum + a.balances.currentBalance, 0);
        const totalExpenses = expenses.reduce((sum, a) => sum + a.balances.currentBalance, 0);

        return {
            revenue: {
                accounts: revenue,
                total: totalRevenue
            },
            expenses: {
                accounts: expenses,
                total: totalExpenses
            },
            netIncome: totalRevenue - totalExpenses,
            grossProfit: totalRevenue - expenses.filter(e => e.category === 'cost_of_goods')
                .reduce((sum, e) => sum + e.balances.currentBalance, 0)
        };
    }

    // إنشاء بيانات التدفقات النقدية
    generateCashFlowData(period) {
        const transactions = Array.from(this.transactions.values())
            .filter(t => {
                const transactionDate = new Date(t.date);
                return transactionDate >= new Date(period.startDate) &&
                       transactionDate <= new Date(period.endDate);
            });

        const operating = transactions.filter(t => t.category === 'operating');
        const investing = transactions.filter(t => t.category === 'investing');
        const financing = transactions.filter(t => t.category === 'financing');

        return {
            operating: {
                transactions: operating,
                total: operating.reduce((sum, t) => sum + t.amount, 0)
            },
            investing: {
                transactions: investing,
                total: investing.reduce((sum, t) => sum + t.amount, 0)
            },
            financing: {
                transactions: financing,
                total: financing.reduce((sum, t) => sum + t.amount, 0)
            }
        };
    }

    // إنشاء بيانات ميزان المراجعة
    generateTrialBalanceData(period) {
        const accounts = Array.from(this.accounts.values());

        return {
            accounts: accounts.map(account => ({
                code: account.code,
                name: account.name,
                debitBalance: account.balances.debitBalance,
                creditBalance: account.balances.creditBalance,
                currentBalance: account.balances.currentBalance
            })),
            totals: {
                totalDebits: accounts.reduce((sum, a) => sum + a.balances.debitBalance, 0),
                totalCredits: accounts.reduce((sum, a) => sum + a.balances.creditBalance, 0)
            }
        };
    }

    // حفظ وتحميل البيانات
    saveData() {
        try {
            localStorage.setItem('advancedFinanceData', JSON.stringify({
                accounts: Array.from(this.accounts.entries()),
                transactions: Array.from(this.transactions.entries()),
                budgets: Array.from(this.budgets.entries()),
                invoices: Array.from(this.invoices.entries()),
                payments: Array.from(this.payments.entries()),
                expenses: Array.from(this.expenses.entries()),
                revenues: Array.from(this.revenues.entries()),
                reports: Array.from(this.financialReports.entries()),
                currencies: Array.from(this.currencies.entries()),
                settings: this.settings
            }));
        } catch (error) {
            console.error('خطأ في حفظ البيانات المالية:', error);
        }
    }

    loadData() {
        try {
            const data = localStorage.getItem('advancedFinanceData');
            if (data) {
                const parsed = JSON.parse(data);
                this.accounts = new Map(parsed.accounts || []);
                this.transactions = new Map(parsed.transactions || []);
                this.budgets = new Map(parsed.budgets || []);
                this.invoices = new Map(parsed.invoices || []);
                this.payments = new Map(parsed.payments || []);
                this.expenses = new Map(parsed.expenses || []);
                this.revenues = new Map(parsed.revenues || []);
                this.financialReports = new Map(parsed.reports || []);
                this.currencies = new Map(parsed.currencies || []);
                if (parsed.settings) {
                    this.settings = { ...this.settings, ...parsed.settings };
                }
            }
        } catch (error) {
            console.error('خطأ في تحميل البيانات المالية:', error);
        }
    }
}

// تصدير الفئة
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdvancedFinanceManager;
}
