<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المخزون - Black Horse ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .module-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .product-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }
        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
        }
        .btn-gradient:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            color: white;
        }
        .nav-tabs .nav-link {
            border: none;
            color: #667eea;
            font-weight: 600;
        }
        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0;
        }
        .stock-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-in-stock { background: #d4edda; color: #155724; }
        .status-low-stock { background: #fff3cd; color: #856404; }
        .status-out-of-stock { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="module-card p-4 mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-boxes me-2"></i>إدارة المخزون</h2>
                    <p class="text-muted mb-0">إدارة شاملة للمنتجات والمخزون والفئات</p>
                </div>
                <button class="btn btn-gradient" onclick="showAddProductModal()">
                    <i class="fas fa-plus me-2"></i>منتج جديد
                </button>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-box fa-2x mb-2"></i>
                    <h4 id="totalProducts">0</h4>
                    <p>إجمالي المنتجات</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-layer-group fa-2x mb-2"></i>
                    <h4 id="totalCategories">0</h4>
                    <p>الفئات</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <h4 id="lowStockItems">0</h4>
                    <p>منتجات منخفضة المخزون</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                    <h4 id="totalValue">0</h4>
                    <p>قيمة المخزون</p>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="module-card p-4">
            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs mb-4" id="inventoryTabs">
                <li class="nav-item">
                    <a class="nav-link active" data-bs-toggle="tab" href="#productsTab">المنتجات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#categoriesTab">الفئات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#stockTab">حركة المخزون</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#alertsTab">التنبيهات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#reportsTab">التقارير</a>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Products Tab -->
                <div class="tab-pane fade show active" id="productsTab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>قائمة المنتجات</h5>
                        <div class="d-flex gap-2">
                            <select class="form-select" id="categoryFilter">
                                <option value="">جميع الفئات</option>
                            </select>
                            <input type="text" class="form-control" placeholder="البحث..." id="productSearch">
                            <button class="btn btn-outline-primary" onclick="scanBarcode()">
                                <i class="fas fa-barcode me-2"></i>مسح باركود
                            </button>
                        </div>
                    </div>
                    <div class="row" id="productsGrid">
                        <!-- Products will be loaded here -->
                    </div>
                </div>

                <!-- Categories Tab -->
                <div class="tab-pane fade" id="categoriesTab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>فئات المنتجات</h5>
                        <button class="btn btn-gradient" onclick="showAddCategoryModal()">
                            <i class="fas fa-plus me-2"></i>فئة جديدة
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>اسم الفئة</th>
                                    <th>الوصف</th>
                                    <th>عدد المنتجات</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="categoriesTableBody">
                                <!-- Categories data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Stock Movement Tab -->
                <div class="tab-pane fade" id="stockTab">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>حركة المخزون</h5>
                        <div class="d-flex gap-2">
                            <input type="date" class="form-control" id="movementDate">
                            <select class="form-select" id="movementType">
                                <option value="">جميع الحركات</option>
                                <option value="in">وارد</option>
                                <option value="out">صادر</option>
                                <option value="adjustment">تسوية</option>
                            </select>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المنتج</th>
                                    <th>نوع الحركة</th>
                                    <th>الكمية</th>
                                    <th>المرجع</th>
                                    <th>الملاحظات</th>
                                </tr>
                            </thead>
                            <tbody id="stockMovementTableBody">
                                <!-- Stock movement data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Alerts Tab -->
                <div class="tab-pane fade" id="alertsTab">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيهات المخزون المنخفض</h6>
                                </div>
                                <div class="card-body" id="lowStockAlerts">
                                    <!-- Low stock alerts will be loaded here -->
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h6><i class="fas fa-times-circle me-2"></i>منتجات نفدت من المخزون</h6>
                                </div>
                                <div class="card-body" id="outOfStockAlerts">
                                    <!-- Out of stock alerts will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reports Tab -->
                <div class="tab-pane fade" id="reportsTab">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>تقرير المخزون حسب الفئة</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="categoryChart"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>حركة المخزون الشهرية</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="movementChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6>تقرير قيمة المخزون</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="valueChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Product Modal -->
    <div class="modal fade" id="addProductModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">منتج جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addProductForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم المنتج</label>
                                    <input type="text" class="form-control" id="productName" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الفئة</label>
                                    <select class="form-select" id="productCategory" required>
                                        <option value="">اختر الفئة</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الباركود</label>
                                    <input type="text" class="form-control" id="productBarcode">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">سعر الشراء</label>
                                    <input type="number" class="form-control" id="productCostPrice" step="0.01" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">سعر البيع</label>
                                    <input type="number" class="form-control" id="productSalePrice" step="0.01" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الكمية الحالية</label>
                                    <input type="number" class="form-control" id="productQuantity" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الحد الأدنى للمخزون</label>
                                    <input type="number" class="form-control" id="productMinStock" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الوحدة</label>
                                    <select class="form-select" id="productUnit">
                                        <option value="piece">قطعة</option>
                                        <option value="kg">كيلو</option>
                                        <option value="liter">لتر</option>
                                        <option value="meter">متر</option>
                                        <option value="box">صندوق</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea class="form-control" id="productDescription" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-gradient" onclick="saveProduct()">حفظ المنتج</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Category Modal -->
    <div class="modal fade" id="addCategoryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">فئة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addCategoryForm">
                        <div class="mb-3">
                            <label class="form-label">اسم الفئة</label>
                            <input type="text" class="form-control" id="categoryName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الوصف</label>
                            <textarea class="form-control" id="categoryDescription" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الحالة</label>
                            <select class="form-select" id="categoryStatus">
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-gradient" onclick="saveCategory()">حفظ الفئة</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="inventory.js"></script>
</body>
</html>
