/**
 * Reports Module - نظام التقارير الشامل
 * Comprehensive reporting system for Black Horse ERP
 */

class ReportsModule {
    constructor() {
        this.currentView = 'dashboard';
        this.dateRange = {
            from: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
            to: new Date().toISOString().split('T')[0]
        };
        this.filters = {
            period: 'month',
            category: 'all',
            customer: 'all',
            supplier: 'all',
            employee: 'all'
        };
        
        // Data caches
        this.salesData = new Map();
        this.purchasesData = new Map();
        this.inventoryData = new Map();
        this.hrData = new Map();
        this.financialData = new Map();
        
        // Chart instances
        this.charts = new Map();
    }

    /**
     * Initialize reports module
     */
    async initialize() {
        console.log('Initializing Reports Module...');
        await this.loadAllData();
        await this.calculateMetrics();
    }

    /**
     * Show reports module
     */
    async show() {
        const container = document.getElementById('moduleContainer');
        if (!container) return;

        container.innerHTML = this.getReportsHTML();
        await this.setupEventListeners();
        await this.renderCurrentView();
    }

    /**
     * Get reports HTML structure
     */
    getReportsHTML() {
        return `
            <div class="reports-module">
                <!-- Header with filters -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-3">
                                        <h5 class="mb-0">
                                            <i class="fas fa-chart-bar me-2"></i>التقارير والإحصائيات
                                        </h5>
                                    </div>
                                    <div class="col-md-9">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <select class="form-select" id="periodFilter">
                                                    <option value="today">اليوم</option>
                                                    <option value="week">هذا الأسبوع</option>
                                                    <option value="month" selected>هذا الشهر</option>
                                                    <option value="quarter">هذا الربع</option>
                                                    <option value="year">هذا العام</option>
                                                    <option value="custom">فترة مخصصة</option>
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <input type="date" class="form-control" id="dateFrom" value="${this.dateRange.from}">
                                            </div>
                                            <div class="col-md-3">
                                                <input type="date" class="form-control" id="dateTo" value="${this.dateRange.to}">
                                            </div>
                                            <div class="col-md-3">
                                                <button class="btn btn-primary w-100" onclick="Reports.applyFilters()">
                                                    <i class="fas fa-filter me-2"></i>تطبيق
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navigation tabs -->
                <div class="row mb-4">
                    <div class="col-12">
                        <ul class="nav nav-tabs" id="reportsNav">
                            <li class="nav-item">
                                <a class="nav-link active" href="#" onclick="Reports.switchView('dashboard')">
                                    <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" onclick="Reports.switchView('financial')">
                                    <i class="fas fa-chart-line me-2"></i>التقارير المالية
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" onclick="Reports.switchView('sales')">
                                    <i class="fas fa-shopping-cart me-2"></i>تقارير المبيعات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" onclick="Reports.switchView('purchases')">
                                    <i class="fas fa-truck me-2"></i>تقارير المشتريات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" onclick="Reports.switchView('inventory')">
                                    <i class="fas fa-boxes me-2"></i>تقارير المخزون
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" onclick="Reports.switchView('hr')">
                                    <i class="fas fa-users me-2"></i>تقارير الموارد البشرية
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Content area -->
                <div id="reportsContent">
                    <!-- Dynamic content will be loaded here -->
                </div>

                <!-- Export modal -->
                <div class="modal fade" id="exportModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">تصدير التقرير</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label class="form-label">نوع التصدير</label>
                                    <select class="form-select" id="exportType">
                                        <option value="pdf">PDF</option>
                                        <option value="excel">Excel</option>
                                        <option value="csv">CSV</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">اسم الملف</label>
                                    <input type="text" class="form-control" id="exportFilename" value="تقرير">
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeCharts" checked>
                                        <label class="form-check-label" for="includeCharts">
                                            تضمين الرسوم البيانية
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-primary" onclick="Reports.exportReport()">
                                    <i class="fas fa-download me-2"></i>تصدير
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Switch between report views
     */
    async switchView(view) {
        this.currentView = view;
        
        // Update active tab
        document.querySelectorAll('#reportsNav .nav-link').forEach(link => {
            link.classList.remove('active');
        });
        event.target.classList.add('active');
        
        await this.renderCurrentView();
    }

    /**
     * Render current view
     */
    async renderCurrentView() {
        const content = document.getElementById('reportsContent');
        if (!content) return;

        switch (this.currentView) {
            case 'dashboard':
                content.innerHTML = await this.renderDashboard();
                break;
            case 'financial':
                content.innerHTML = await this.renderFinancialReports();
                break;
            case 'sales':
                content.innerHTML = await this.renderSalesReports();
                break;
            case 'purchases':
                content.innerHTML = await this.renderPurchasesReports();
                break;
            case 'inventory':
                content.innerHTML = await this.renderInventoryReports();
                break;
            case 'hr':
                content.innerHTML = await this.renderHRReports();
                break;
        }

        // Initialize charts after content is rendered
        setTimeout(() => this.initializeCharts(), 100);
    }

    /**
     * Render dashboard view
     */
    async renderDashboard() {
        const metrics = await this.calculateMetrics();
        
        return `
            <div class="row">
                <!-- Key Performance Indicators -->
                <div class="col-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-pie me-2"></i>مؤشرات الأداء الرئيسية
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <div class="text-center p-3 bg-primary bg-opacity-10 rounded">
                                        <i class="fas fa-money-bill-wave fa-2x text-primary mb-2"></i>
                                        <h4 class="text-primary">${Utils.formatCurrency(metrics.totalRevenue)}</h4>
                                        <small class="text-muted">إجمالي الإيرادات</small>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="text-center p-3 bg-success bg-opacity-10 rounded">
                                        <i class="fas fa-chart-line fa-2x text-success mb-2"></i>
                                        <h4 class="text-success">${Utils.formatCurrency(metrics.netProfit)}</h4>
                                        <small class="text-muted">صافي الربح</small>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="text-center p-3 bg-info bg-opacity-10 rounded">
                                        <i class="fas fa-shopping-cart fa-2x text-info mb-2"></i>
                                        <h4 class="text-info">${metrics.totalOrders}</h4>
                                        <small class="text-muted">إجمالي الطلبات</small>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="text-center p-3 bg-warning bg-opacity-10 rounded">
                                        <i class="fas fa-users fa-2x text-warning mb-2"></i>
                                        <h4 class="text-warning">${metrics.activeCustomers}</h4>
                                        <small class="text-muted">العملاء النشطين</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h6 class="mb-0">المبيعات الشهرية</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="monthlySalesChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h6 class="mb-0">توزيع المبيعات حسب الفئة</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="categoryDistributionChart" height="300"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Top Products and Customers -->
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h6 class="mb-0">أفضل المنتجات مبيعاً</h6>
                        </div>
                        <div class="card-body">
                            <div id="topProductsList">
                                ${await this.renderTopProducts()}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h6 class="mb-0">أفضل العملاء</h6>
                        </div>
                        <div class="card-body">
                            <div id="topCustomersList">
                                ${await this.renderTopCustomers()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}

    /**
     * Load all data for reports
     */
    async loadAllData() {
        try {
            // Load sales data
            if (window.Sales && window.Sales.sales) {
                this.salesData.set('sales', window.Sales.sales);
                this.salesData.set('customers', window.Sales.customers);
            }

            // Load purchases data
            if (window.Purchases && window.Purchases.purchases) {
                this.purchasesData.set('purchases', window.Purchases.purchases);
                this.purchasesData.set('suppliers', window.Purchases.suppliers);
            }

            // Load inventory data
            if (window.Inventory && window.Inventory.products) {
                this.inventoryData.set('products', window.Inventory.products);
                this.inventoryData.set('categories', window.Inventory.categories);
            }

            // Load HR data
            if (window.HR && window.HR.employees) {
                this.hrData.set('employees', window.HR.employees);
                this.hrData.set('attendance', window.HR.attendance);
                this.hrData.set('payroll', window.HR.payroll);
            }

            console.log('All data loaded for reports');
        } catch (error) {
            console.error('Error loading data for reports:', error);
        }
    }

    /**
     * Calculate key metrics
     */
    async calculateMetrics() {
        const sales = this.salesData.get('sales') || new Map();
        const purchases = this.purchasesData.get('purchases') || new Map();
        const products = this.inventoryData.get('products') || new Map();
        const customers = this.salesData.get('customers') || new Map();

        // Filter data by date range
        const filteredSales = Array.from(sales.values()).filter(sale => {
            const saleDate = new Date(sale.date);
            const fromDate = new Date(this.dateRange.from);
            const toDate = new Date(this.dateRange.to);
            return saleDate >= fromDate && saleDate <= toDate;
        });

        const filteredPurchases = Array.from(purchases.values()).filter(purchase => {
            const purchaseDate = new Date(purchase.date);
            const fromDate = new Date(this.dateRange.from);
            const toDate = new Date(this.dateRange.to);
            return purchaseDate >= fromDate && purchaseDate <= toDate;
        });

        // Calculate metrics
        const totalRevenue = filteredSales.reduce((sum, sale) => sum + (sale.total || 0), 0);
        const totalCosts = filteredPurchases.reduce((sum, purchase) => sum + (purchase.total || 0), 0);
        const netProfit = totalRevenue - totalCosts;
        const totalOrders = filteredSales.length;
        const activeCustomers = new Set(filteredSales.map(sale => sale.customer_id)).size;

        return {
            totalRevenue,
            totalCosts,
            netProfit,
            totalOrders,
            activeCustomers,
            profitMargin: totalRevenue > 0 ? ((netProfit / totalRevenue) * 100).toFixed(2) : 0
        };
    }

    /**
     * Render top products
     */
    async renderTopProducts() {
        const sales = this.salesData.get('sales') || new Map();
        const products = this.inventoryData.get('products') || new Map();

        // Calculate product sales
        const productSales = new Map();

        Array.from(sales.values()).forEach(sale => {
            if (sale.items) {
                sale.items.forEach(item => {
                    const current = productSales.get(item.product_id) || { quantity: 0, revenue: 0 };
                    current.quantity += item.quantity;
                    current.revenue += item.total;
                    productSales.set(item.product_id, current);
                });
            }
        });

        // Sort by revenue
        const topProducts = Array.from(productSales.entries())
            .sort((a, b) => b[1].revenue - a[1].revenue)
            .slice(0, 5);

        return topProducts.map(([productId, data]) => {
            const product = products.get(productId) || { name: 'منتج غير معروف' };
            return `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div>
                        <div class="fw-bold">${product.name}</div>
                        <small class="text-muted">الكمية: ${data.quantity}</small>
                    </div>
                    <div class="text-end">
                        <div class="fw-bold text-success">${Utils.formatCurrency(data.revenue)}</div>
                    </div>
                </div>
            `;
        }).join('');
    }

    /**
     * Render top customers
     */
    async renderTopCustomers() {
        const sales = this.salesData.get('sales') || new Map();
        const customers = this.salesData.get('customers') || new Map();

        // Calculate customer purchases
        const customerPurchases = new Map();

        Array.from(sales.values()).forEach(sale => {
            const current = customerPurchases.get(sale.customer_id) || { orders: 0, total: 0 };
            current.orders += 1;
            current.total += sale.total || 0;
            customerPurchases.set(sale.customer_id, current);
        });

        // Sort by total
        const topCustomers = Array.from(customerPurchases.entries())
            .sort((a, b) => b[1].total - a[1].total)
            .slice(0, 5);

        return topCustomers.map(([customerId, data]) => {
            const customer = customers.get(customerId) || { name: 'عميل غير معروف' };
            return `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div>
                        <div class="fw-bold">${customer.name}</div>
                        <small class="text-muted">عدد الطلبات: ${data.orders}</small>
                    </div>
                    <div class="text-end">
                        <div class="fw-bold text-primary">${Utils.formatCurrency(data.total)}</div>
                    </div>
                </div>
            `;
        }).join('');
    }

    /**
     * Setup event listeners
     */
    async setupEventListeners() {
        // Period filter change
        const periodFilter = document.getElementById('periodFilter');
        if (periodFilter) {
            periodFilter.addEventListener('change', (e) => {
                this.updateDateRange(e.target.value);
            });
        }

        // Date inputs change
        const dateFrom = document.getElementById('dateFrom');
        const dateTo = document.getElementById('dateTo');

        if (dateFrom) {
            dateFrom.addEventListener('change', (e) => {
                this.dateRange.from = e.target.value;
            });
        }

        if (dateTo) {
            dateTo.addEventListener('change', (e) => {
                this.dateRange.to = e.target.value;
            });
        }
    }

    /**
     * Update date range based on period
     */
    updateDateRange(period) {
        const now = new Date();
        let from, to;

        switch (period) {
            case 'today':
                from = to = now.toISOString().split('T')[0];
                break;
            case 'week':
                from = new Date(now.setDate(now.getDate() - 7)).toISOString().split('T')[0];
                to = new Date().toISOString().split('T')[0];
                break;
            case 'month':
                from = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
                to = new Date().toISOString().split('T')[0];
                break;
            case 'quarter':
                const quarter = Math.floor(now.getMonth() / 3);
                from = new Date(now.getFullYear(), quarter * 3, 1).toISOString().split('T')[0];
                to = new Date().toISOString().split('T')[0];
                break;
            case 'year':
                from = new Date(now.getFullYear(), 0, 1).toISOString().split('T')[0];
                to = new Date().toISOString().split('T')[0];
                break;
            default:
                return; // Custom - don't change dates
        }

        this.dateRange = { from, to };

        // Update inputs
        const dateFromInput = document.getElementById('dateFrom');
        const dateToInput = document.getElementById('dateTo');

        if (dateFromInput) dateFromInput.value = from;
        if (dateToInput) dateToInput.value = to;
    }

    /**
     * Apply filters and refresh reports
     */
    async applyFilters() {
        await this.loadAllData();
        await this.renderCurrentView();
        Utils.showAlert('تم تحديث التقارير بنجاح', 'success');
    }

    /**
     * Initialize charts
     */
    initializeCharts() {
        // This would initialize Chart.js charts
        // For now, we'll show placeholder
        console.log('Charts would be initialized here with Chart.js');
    }

    /**
     * Export report
     */
    async exportReport() {
        const type = document.getElementById('exportType')?.value || 'pdf';
        const filename = document.getElementById('exportFilename')?.value || 'تقرير';
        const includeCharts = document.getElementById('includeCharts')?.checked || false;

        try {
            // This would implement actual export functionality
            Utils.showAlert(`سيتم تصدير التقرير كـ ${type.toUpperCase()}`, 'info');

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('exportModal'));
            if (modal) modal.hide();

        } catch (error) {
            console.error('Export error:', error);
            Utils.showAlert('حدث خطأ أثناء التصدير', 'error');
        }
    }
}

// Create global instance
window.Reports = new ReportsModule();
