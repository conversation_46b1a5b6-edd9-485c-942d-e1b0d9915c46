/**
 * نظام إدارة الموردين
 * Black Horse ERP System
 */

class SuppliersManager {
    constructor() {
        this.suppliers = new Map();
        this.supplierCategories = new Map();
        this.supplierContacts = new Map();
        this.supplierContracts = new Map();
        this.supplierEvaluations = new Map();
        this.supplierPayments = new Map();
        this.supplierOrders = new Map();
        
        this.settings = {
            autoApproval: false,
            requireContracts: true,
            evaluationPeriod: 90, // أيام
            paymentTerms: 30, // أيام
            defaultCurrency: 'SAR',
            minimumRating: 3.0
        };
        
        this.initializeSuppliers();
        this.loadData();
    }

    // تهيئة نظام الموردين
    initializeSuppliers() {
        console.log('🏭 تهيئة نظام إدارة الموردين...');
        
        // إعداد قاعدة البيانات
        this.setupDatabase();
        
        // إعداد الفئات الافتراضية
        this.setupDefaultCategories();
        
        // إعداد الأحداث
        this.setupEventListeners();
        
        console.log('✅ تم تهيئة نظام إدارة الموردين');
    }

    // إنشاء مورد جديد
    createSupplier(supplierData) {
        try {
            const supplierId = this.generateId();
            const supplier = {
                id: supplierId,
                code: supplierData.code || this.generateSupplierCode(),
                name: supplierData.name,
                nameEn: supplierData.nameEn || '',
                type: supplierData.type || 'company', // company, individual
                category: supplierData.category,
                status: 'pending', // pending, approved, suspended, blacklisted
                rating: 0,
                
                // معلومات الاتصال
                contact: {
                    email: supplierData.email,
                    phone: supplierData.phone,
                    mobile: supplierData.mobile || '',
                    website: supplierData.website || '',
                    fax: supplierData.fax || ''
                },
                
                // العنوان
                address: {
                    street: supplierData.street || '',
                    city: supplierData.city || '',
                    state: supplierData.state || '',
                    country: supplierData.country || 'السعودية',
                    postalCode: supplierData.postalCode || ''
                },
                
                // المعلومات المالية
                financial: {
                    taxNumber: supplierData.taxNumber || '',
                    commercialRegister: supplierData.commercialRegister || '',
                    bankAccount: supplierData.bankAccount || '',
                    bankName: supplierData.bankName || '',
                    iban: supplierData.iban || '',
                    paymentTerms: supplierData.paymentTerms || this.settings.paymentTerms,
                    creditLimit: supplierData.creditLimit || 0,
                    currency: supplierData.currency || this.settings.defaultCurrency
                },
                
                // معلومات إضافية
                products: supplierData.products || [],
                services: supplierData.services || [],
                certifications: supplierData.certifications || [],
                tags: supplierData.tags || [],
                notes: supplierData.notes || '',
                
                // الإحصائيات
                statistics: {
                    totalOrders: 0,
                    totalAmount: 0,
                    averageOrderValue: 0,
                    onTimeDelivery: 0,
                    qualityRating: 0,
                    lastOrderDate: null,
                    evaluationScore: 0
                },
                
                // التواريخ
                created: new Date().toISOString(),
                modified: new Date().toISOString(),
                approvedAt: null,
                lastEvaluation: null,
                
                // الملفات والمرفقات
                documents: [],
                attachments: []
            };
            
            this.suppliers.set(supplierId, supplier);
            this.saveSuppliers();
            
            console.log('✅ تم إنشاء المورد:', supplier.name);
            return supplier;
            
        } catch (error) {
            console.error('خطأ في إنشاء المورد:', error);
            throw error;
        }
    }

    // تحديث مورد
    updateSupplier(supplierId, updates) {
        try {
            const supplier = this.suppliers.get(supplierId);
            if (!supplier) {
                throw new Error('المورد غير موجود');
            }
            
            Object.assign(supplier, updates);
            supplier.modified = new Date().toISOString();
            
            this.saveSuppliers();
            
            console.log('✅ تم تحديث المورد:', supplier.name);
            return supplier;
            
        } catch (error) {
            console.error('خطأ في تحديث المورد:', error);
            throw error;
        }
    }

    // الموافقة على مورد
    approveSupplier(supplierId) {
        try {
            const supplier = this.suppliers.get(supplierId);
            if (!supplier) {
                throw new Error('المورد غير موجود');
            }
            
            supplier.status = 'approved';
            supplier.approvedAt = new Date().toISOString();
            supplier.modified = new Date().toISOString();
            
            this.saveSuppliers();
            
            console.log('✅ تم الموافقة على المورد:', supplier.name);
            return supplier;
            
        } catch (error) {
            console.error('خطأ في الموافقة على المورد:', error);
            throw error;
        }
    }

    // تعليق مورد
    suspendSupplier(supplierId, reason) {
        try {
            const supplier = this.suppliers.get(supplierId);
            if (!supplier) {
                throw new Error('المورد غير موجود');
            }
            
            supplier.status = 'suspended';
            supplier.suspensionReason = reason;
            supplier.suspendedAt = new Date().toISOString();
            supplier.modified = new Date().toISOString();
            
            this.saveSuppliers();
            
            console.log('✅ تم تعليق المورد:', supplier.name);
            return supplier;
            
        } catch (error) {
            console.error('خطأ في تعليق المورد:', error);
            throw error;
        }
    }

    // إنشاء تقييم مورد
    createSupplierEvaluation(evaluationData) {
        try {
            const evaluationId = this.generateId();
            const evaluation = {
                id: evaluationId,
                supplierId: evaluationData.supplierId,
                evaluatorId: evaluationData.evaluatorId,
                period: evaluationData.period, // monthly, quarterly, yearly
                
                // معايير التقييم
                criteria: {
                    quality: evaluationData.quality || 0, // 1-5
                    delivery: evaluationData.delivery || 0, // 1-5
                    pricing: evaluationData.pricing || 0, // 1-5
                    service: evaluationData.service || 0, // 1-5
                    communication: evaluationData.communication || 0, // 1-5
                    compliance: evaluationData.compliance || 0 // 1-5
                },
                
                // النتيجة الإجمالية
                totalScore: 0,
                rating: 0, // 1-5
                
                // التعليقات والملاحظات
                comments: evaluationData.comments || '',
                recommendations: evaluationData.recommendations || '',
                
                // خطة التحسين
                improvementPlan: evaluationData.improvementPlan || '',
                nextReviewDate: evaluationData.nextReviewDate,
                
                created: new Date().toISOString(),
                status: 'completed'
            };
            
            // حساب النتيجة الإجمالية
            const criteriaValues = Object.values(evaluation.criteria);
            evaluation.totalScore = criteriaValues.reduce((sum, score) => sum + score, 0);
            evaluation.rating = evaluation.totalScore / criteriaValues.length;
            
            this.supplierEvaluations.set(evaluationId, evaluation);
            
            // تحديث تقييم المورد
            const supplier = this.suppliers.get(evaluationData.supplierId);
            if (supplier) {
                supplier.rating = evaluation.rating;
                supplier.statistics.evaluationScore = evaluation.totalScore;
                supplier.lastEvaluation = new Date().toISOString();
                this.saveSuppliers();
            }
            
            this.saveEvaluations();
            
            console.log('✅ تم إنشاء تقييم المورد');
            return evaluation;
            
        } catch (error) {
            console.error('خطأ في إنشاء تقييم المورد:', error);
            throw error;
        }
    }

    // إنشاء عقد مورد
    createSupplierContract(contractData) {
        try {
            const contractId = this.generateId();
            const contract = {
                id: contractId,
                supplierId: contractData.supplierId,
                title: contractData.title,
                type: contractData.type, // purchase, service, framework
                
                // تفاصيل العقد
                startDate: contractData.startDate,
                endDate: contractData.endDate,
                value: contractData.value || 0,
                currency: contractData.currency || this.settings.defaultCurrency,
                
                // الشروط والأحكام
                terms: {
                    paymentTerms: contractData.paymentTerms || this.settings.paymentTerms,
                    deliveryTerms: contractData.deliveryTerms || '',
                    qualityStandards: contractData.qualityStandards || '',
                    penalties: contractData.penalties || '',
                    warranties: contractData.warranties || ''
                },
                
                // المنتجات/الخدمات المشمولة
                items: contractData.items || [],
                
                // الحالة
                status: 'draft', // draft, active, expired, terminated
                
                // الملفات
                documents: contractData.documents || [],
                
                created: new Date().toISOString(),
                modified: new Date().toISOString(),
                activatedAt: null,
                terminatedAt: null
            };
            
            this.supplierContracts.set(contractId, contract);
            this.saveContracts();
            
            console.log('✅ تم إنشاء عقد المورد');
            return contract;
            
        } catch (error) {
            console.error('خطأ في إنشاء عقد المورد:', error);
            throw error;
        }
    }

    // إنشاء دفعة للمورد
    createSupplierPayment(paymentData) {
        try {
            const paymentId = this.generateId();
            const payment = {
                id: paymentId,
                supplierId: paymentData.supplierId,
                orderId: paymentData.orderId,
                invoiceNumber: paymentData.invoiceNumber,
                
                // تفاصيل الدفعة
                amount: paymentData.amount,
                currency: paymentData.currency || this.settings.defaultCurrency,
                method: paymentData.method, // cash, bank_transfer, check, credit
                
                // التواريخ
                dueDate: paymentData.dueDate,
                paidDate: paymentData.paidDate || new Date().toISOString(),
                
                // الحالة
                status: paymentData.status || 'paid', // pending, paid, overdue, cancelled
                
                // معلومات إضافية
                reference: paymentData.reference || '',
                notes: paymentData.notes || '',
                
                created: new Date().toISOString()
            };
            
            this.supplierPayments.set(paymentId, payment);
            this.savePayments();
            
            // تحديث إحصائيات المورد
            this.updateSupplierStatistics(paymentData.supplierId, payment);
            
            console.log('✅ تم إنشاء دفعة المورد');
            return payment;
            
        } catch (error) {
            console.error('خطأ في إنشاء دفعة المورد:', error);
            throw error;
        }
    }

    // البحث في الموردين
    searchSuppliers(query, filters = {}) {
        const results = [];
        const searchTerm = query.toLowerCase();
        
        this.suppliers.forEach(supplier => {
            const name = supplier.name.toLowerCase();
            const code = supplier.code.toLowerCase();
            const email = supplier.contact.email.toLowerCase();
            
            if (name.includes(searchTerm) ||
                code.includes(searchTerm) ||
                email.includes(searchTerm)) {
                
                // تطبيق الفلاتر
                if (filters.status && supplier.status !== filters.status) return;
                if (filters.category && supplier.category !== filters.category) return;
                if (filters.rating && supplier.rating < filters.rating) return;
                
                results.push(supplier);
            }
        });
        
        return results;
    }

    // الحصول على أفضل الموردين
    getTopSuppliers(limit = 10) {
        return Array.from(this.suppliers.values())
            .filter(supplier => supplier.status === 'approved')
            .sort((a, b) => b.rating - a.rating)
            .slice(0, limit);
    }

    // الحصول على الموردين المتأخرين في التسليم
    getOverdueSuppliers() {
        const today = new Date();
        return Array.from(this.supplierPayments.values())
            .filter(payment => {
                return payment.status === 'pending' && 
                       new Date(payment.dueDate) < today;
            })
            .map(payment => ({
                payment,
                supplier: this.suppliers.get(payment.supplierId)
            }));
    }

    // تحديث إحصائيات المورد
    updateSupplierStatistics(supplierId, orderData) {
        const supplier = this.suppliers.get(supplierId);
        if (!supplier) return;
        
        supplier.statistics.totalOrders += 1;
        supplier.statistics.totalAmount += orderData.amount || 0;
        supplier.statistics.averageOrderValue = supplier.statistics.totalAmount / supplier.statistics.totalOrders;
        supplier.statistics.lastOrderDate = new Date().toISOString();
        
        this.saveSuppliers();
    }

    // إنشاء فئة موردين
    createSupplierCategory(categoryData) {
        try {
            const categoryId = this.generateId();
            const category = {
                id: categoryId,
                name: categoryData.name,
                description: categoryData.description || '',
                color: categoryData.color || '#007bff',
                icon: categoryData.icon || 'fas fa-folder',
                created: new Date().toISOString()
            };
            
            this.supplierCategories.set(categoryId, category);
            this.saveCategories();
            
            console.log('✅ تم إنشاء فئة الموردين:', category.name);
            return category;
            
        } catch (error) {
            console.error('خطأ في إنشاء فئة الموردين:', error);
            throw error;
        }
    }

    // الحصول على تقارير الموردين
    getSuppliersReports() {
        const suppliers = Array.from(this.suppliers.values());
        const evaluations = Array.from(this.supplierEvaluations.values());
        const payments = Array.from(this.supplierPayments.values());
        const contracts = Array.from(this.supplierContracts.values());
        
        return {
            suppliers: {
                total: suppliers.length,
                approved: suppliers.filter(s => s.status === 'approved').length,
                pending: suppliers.filter(s => s.status === 'pending').length,
                suspended: suppliers.filter(s => s.status === 'suspended').length,
                averageRating: suppliers.length > 0 ? 
                    suppliers.reduce((sum, s) => sum + s.rating, 0) / suppliers.length : 0
            },
            payments: {
                total: payments.length,
                totalAmount: payments.reduce((sum, p) => sum + p.amount, 0),
                pending: payments.filter(p => p.status === 'pending').length,
                overdue: payments.filter(p => {
                    return p.status === 'pending' && new Date(p.dueDate) < new Date();
                }).length
            },
            contracts: {
                total: contracts.length,
                active: contracts.filter(c => c.status === 'active').length,
                expired: contracts.filter(c => c.status === 'expired').length,
                totalValue: contracts.reduce((sum, c) => sum + c.value, 0)
            },
            evaluations: {
                total: evaluations.length,
                averageScore: evaluations.length > 0 ? 
                    evaluations.reduce((sum, e) => sum + e.rating, 0) / evaluations.length : 0,
                thisMonth: evaluations.filter(e => {
                    const created = new Date(e.created);
                    const monthAgo = new Date();
                    monthAgo.setMonth(monthAgo.getMonth() - 1);
                    return created > monthAgo;
                }).length
            }
        };
    }

    // توليد رمز المورد
    generateSupplierCode() {
        const prefix = 'SUP';
        const timestamp = Date.now().toString().slice(-6);
        const random = Math.random().toString(36).substr(2, 3).toUpperCase();
        return `${prefix}${timestamp}${random}`;
    }

    // إعداد الفئات الافتراضية
    setupDefaultCategories() {
        const defaultCategories = [
            { name: 'مواد خام', description: 'موردو المواد الخام والمكونات الأساسية', color: '#28a745', icon: 'fas fa-industry' },
            { name: 'خدمات', description: 'مقدمو الخدمات المختلفة', color: '#007bff', icon: 'fas fa-handshake' },
            { name: 'تقنية', description: 'موردو الحلول التقنية والبرمجيات', color: '#6f42c1', icon: 'fas fa-laptop' },
            { name: 'لوجستيات', description: 'شركات الشحن والنقل', color: '#fd7e14', icon: 'fas fa-truck' },
            { name: 'مكتبية', description: 'موردو المستلزمات المكتبية', color: '#20c997', icon: 'fas fa-briefcase' }
        ];
        
        defaultCategories.forEach(categoryData => {
            if (!Array.from(this.supplierCategories.values()).find(c => c.name === categoryData.name)) {
                this.createSupplierCategory(categoryData);
            }
        });
    }

    // توليد معرف فريد
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // إعداد قاعدة البيانات
    setupDatabase() {
        console.log('📊 إعداد قاعدة بيانات الموردين');
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // سيتم إضافة مستمعي الأحداث هنا
    }

    // حفظ البيانات
    saveSuppliers() {
        try {
            const suppliersObj = {};
            this.suppliers.forEach((supplier, id) => {
                suppliersObj[id] = supplier;
            });
            localStorage.setItem('suppliers', JSON.stringify(suppliersObj));
        } catch (error) {
            console.error('خطأ في حفظ الموردين:', error);
        }
    }

    saveCategories() {
        try {
            const categoriesObj = {};
            this.supplierCategories.forEach((category, id) => {
                categoriesObj[id] = category;
            });
            localStorage.setItem('supplier_categories', JSON.stringify(categoriesObj));
        } catch (error) {
            console.error('خطأ في حفظ فئات الموردين:', error);
        }
    }

    saveEvaluations() {
        try {
            const evaluationsObj = {};
            this.supplierEvaluations.forEach((evaluation, id) => {
                evaluationsObj[id] = evaluation;
            });
            localStorage.setItem('supplier_evaluations', JSON.stringify(evaluationsObj));
        } catch (error) {
            console.error('خطأ في حفظ تقييمات الموردين:', error);
        }
    }

    saveContracts() {
        try {
            const contractsObj = {};
            this.supplierContracts.forEach((contract, id) => {
                contractsObj[id] = contract;
            });
            localStorage.setItem('supplier_contracts', JSON.stringify(contractsObj));
        } catch (error) {
            console.error('خطأ في حفظ عقود الموردين:', error);
        }
    }

    savePayments() {
        try {
            const paymentsObj = {};
            this.supplierPayments.forEach((payment, id) => {
                paymentsObj[id] = payment;
            });
            localStorage.setItem('supplier_payments', JSON.stringify(paymentsObj));
        } catch (error) {
            console.error('خطأ في حفظ دفعات الموردين:', error);
        }
    }

    saveData() {
        this.saveSuppliers();
        this.saveCategories();
        this.saveEvaluations();
        this.saveContracts();
        this.savePayments();
    }

    // تحميل البيانات
    loadData() {
        try {
            // تحميل الموردين
            const suppliers = JSON.parse(localStorage.getItem('suppliers') || '{}');
            Object.values(suppliers).forEach(supplier => {
                this.suppliers.set(supplier.id, supplier);
            });
            
            // تحميل الفئات
            const categories = JSON.parse(localStorage.getItem('supplier_categories') || '{}');
            Object.values(categories).forEach(category => {
                this.supplierCategories.set(category.id, category);
            });
            
            // تحميل التقييمات
            const evaluations = JSON.parse(localStorage.getItem('supplier_evaluations') || '{}');
            Object.values(evaluations).forEach(evaluation => {
                this.supplierEvaluations.set(evaluation.id, evaluation);
            });
            
            // تحميل العقود
            const contracts = JSON.parse(localStorage.getItem('supplier_contracts') || '{}');
            Object.values(contracts).forEach(contract => {
                this.supplierContracts.set(contract.id, contract);
            });
            
            // تحميل الدفعات
            const payments = JSON.parse(localStorage.getItem('supplier_payments') || '{}');
            Object.values(payments).forEach(payment => {
                this.supplierPayments.set(payment.id, payment);
            });
            
            console.log(`🏭 تم تحميل ${this.suppliers.size} مورد و ${this.supplierCategories.size} فئة`);
            
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
        }
    }
}

// تصدير الكلاس
window.SuppliersManager = SuppliersManager;
