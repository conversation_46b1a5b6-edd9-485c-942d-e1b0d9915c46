<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معالج الإعداد - Black Horse ERP</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .setup-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .setup-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .setup-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .setup-step {
            display: none;
            padding: 40px;
            min-height: 400px;
        }
        
        .setup-step.active {
            display: block;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .step-item {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .step-item.active {
            background: #007bff;
            color: white;
        }
        
        .step-item.completed {
            background: #28a745;
            color: white;
        }
        
        .step-connector {
            width: 50px;
            height: 2px;
            background: #e9ecef;
            margin-top: 19px;
        }
        
        .step-connector.completed {
            background: #28a745;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .btn-setup {
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-setup:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .setup-navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            border-color: #007bff;
            background: #e3f2fd;
        }
        
        .feature-card.selected {
            border-color: #007bff;
            background: #e3f2fd;
        }
        
        .progress-setup {
            height: 8px;
            border-radius: 4px;
            background: #e9ecef;
            margin-bottom: 20px;
        }
        
        .progress-bar-setup {
            height: 100%;
            border-radius: 4px;
            background: linear-gradient(45deg, #007bff, #0056b3);
            transition: width 0.5s ease;
        }
        
        .success-animation {
            text-align: center;
            padding: 40px;
        }
        
        .success-icon {
            font-size: 80px;
            color: #28a745;
            margin-bottom: 20px;
            animation: bounce 1s ease-in-out;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-20px);
            }
            60% {
                transform: translateY(-10px);
            }
        }
        
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-card">
            <!-- Header -->
            <div class="setup-header">
                <h1><i class="fas fa-horse-head me-3"></i>Black Horse ERP</h1>
                <p class="mb-0">معالج الإعداد الأولي للنظام</p>
            </div>
            
            <!-- Progress Bar -->
            <div class="px-4 pt-3">
                <div class="progress-setup">
                    <div id="setupProgress" class="progress-bar-setup" style="width: 20%"></div>
                </div>
            </div>
            
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step-item active" data-step="1">1</div>
                <div class="step-connector"></div>
                <div class="step-item" data-step="2">2</div>
                <div class="step-connector"></div>
                <div class="step-item" data-step="3">3</div>
                <div class="step-connector"></div>
                <div class="step-item" data-step="4">4</div>
                <div class="step-connector"></div>
                <div class="step-item" data-step="5">5</div>
            </div>
            
            <!-- Step 1: Welcome -->
            <div class="setup-step active" id="step1">
                <div class="text-center">
                    <h2><i class="fas fa-rocket text-primary me-2"></i>مرحباً بك في Black Horse ERP</h2>
                    <p class="lead text-muted">نظام إدارة الأعمال الشامل والمتطور</p>
                    
                    <div class="row mt-4">
                        <div class="col-md-4 mb-3">
                            <div class="text-center">
                                <i class="fas fa-warehouse fa-3x text-primary mb-3"></i>
                                <h5>إدارة المخزون</h5>
                                <p class="text-muted">إدارة شاملة للمنتجات والمخزون</p>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="text-center">
                                <i class="fas fa-chart-line fa-3x text-success mb-3"></i>
                                <h5>المبيعات والتقارير</h5>
                                <p class="text-muted">تتبع المبيعات وإنشاء التقارير</p>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="text-center">
                                <i class="fas fa-users fa-3x text-info mb-3"></i>
                                <h5>الموارد البشرية</h5>
                                <p class="text-muted">إدارة الموظفين والرواتب</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-4">
                        <i class="fas fa-info-circle me-2"></i>
                        سيستغرق الإعداد حوالي 5 دقائق لإكمال جميع الخطوات
                    </div>
                </div>
            </div>
            
            <!-- Step 2: Company Information -->
            <div class="setup-step" id="step2">
                <h3><i class="fas fa-building me-2"></i>معلومات الشركة</h3>
                <p class="text-muted">أدخل المعلومات الأساسية لشركتك</p>
                
                <form id="companyForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">اسم الشركة *</label>
                                <input type="text" class="form-control" id="companyName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">نوع النشاط</label>
                                <select class="form-control" id="businessType">
                                    <option value="">اختر نوع النشاط</option>
                                    <option value="retail">تجارة تجزئة</option>
                                    <option value="wholesale">تجارة جملة</option>
                                    <option value="manufacturing">تصنيع</option>
                                    <option value="services">خدمات</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="companyPhone">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="companyEmail">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">العنوان</label>
                        <textarea class="form-control" id="companyAddress" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">العملة الافتراضية</label>
                                <select class="form-control" id="defaultCurrency">
                                    <option value="EGP">جنيه مصري (EGP)</option>
                                    <option value="SAR">ريال سعودي (SAR)</option>
                                    <option value="AED">درهم إماراتي (AED)</option>
                                    <option value="USD">دولار أمريكي (USD)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">المنطقة الزمنية</label>
                                <select class="form-control" id="timezone">
                                    <option value="Africa/Cairo">القاهرة (GMT+2)</option>
                                    <option value="Asia/Riyadh">الرياض (GMT+3)</option>
                                    <option value="Asia/Dubai">دبي (GMT+4)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            
            <!-- Step 3: Features Selection -->
            <div class="setup-step" id="step3">
                <h3><i class="fas fa-puzzle-piece me-2"></i>اختيار الوحدات</h3>
                <p class="text-muted">اختر الوحدات التي تريد تفعيلها في نظامك</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="feature-card selected" data-module="inventory">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-warehouse fa-2x text-primary me-3"></i>
                                <div>
                                    <h5 class="mb-1">إدارة المخزون</h5>
                                    <small class="text-muted">إدارة المنتجات والمخزون</small>
                                </div>
                                <div class="ms-auto">
                                    <i class="fas fa-check-circle text-success"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="feature-card selected" data-module="sales">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-chart-line fa-2x text-success me-3"></i>
                                <div>
                                    <h5 class="mb-1">إدارة المبيعات</h5>
                                    <small class="text-muted">تسجيل وتتبع المبيعات</small>
                                </div>
                                <div class="ms-auto">
                                    <i class="fas fa-check-circle text-success"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="feature-card selected" data-module="pos">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-cash-register fa-2x text-info me-3"></i>
                                <div>
                                    <h5 class="mb-1">نقطة البيع</h5>
                                    <small class="text-muted">واجهة البيع السريع</small>
                                </div>
                                <div class="ms-auto">
                                    <i class="fas fa-check-circle text-success"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="feature-card" data-module="purchases">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-shopping-cart fa-2x text-warning me-3"></i>
                                <div>
                                    <h5 class="mb-1">إدارة المشتريات</h5>
                                    <small class="text-muted">تسجيل المشتريات والموردين</small>
                                </div>
                                <div class="ms-auto">
                                    <i class="fas fa-circle text-muted"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="feature-card" data-module="hr">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-users fa-2x text-purple me-3"></i>
                                <div>
                                    <h5 class="mb-1">الموارد البشرية</h5>
                                    <small class="text-muted">إدارة الموظفين والرواتب</small>
                                </div>
                                <div class="ms-auto">
                                    <i class="fas fa-circle text-muted"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="feature-card selected" data-module="reports">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-chart-bar fa-2x text-danger me-3"></i>
                                <div>
                                    <h5 class="mb-1">التقارير</h5>
                                    <small class="text-muted">تقارير شاملة ومفصلة</small>
                                </div>
                                <div class="ms-auto">
                                    <i class="fas fa-check-circle text-success"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info mt-3">
                    <i class="fas fa-lightbulb me-2"></i>
                    يمكنك تفعيل أو إلغاء تفعيل الوحدات لاحقاً من إعدادات النظام
                </div>
            </div>
            
            <!-- Step 4: Initial Data -->
            <div class="setup-step" id="step4">
                <h3><i class="fas fa-database me-2"></i>البيانات الأولية</h3>
                <p class="text-muted">إعداد البيانات الأساسية للنظام</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-user-plus fa-3x text-primary mb-3"></i>
                                <h5>إنشاء حساب المدير</h5>
                                <p class="text-muted">إنشاء حساب المدير الرئيسي</p>
                                <button class="btn btn-primary btn-setup" onclick="createAdminAccount()">
                                    إنشاء الحساب
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-box fa-3x text-success mb-3"></i>
                                <h5>بيانات تجريبية</h5>
                                <p class="text-muted">إضافة بيانات تجريبية للتجربة</p>
                                <button class="btn btn-success btn-setup" onclick="loadSampleData()">
                                    تحميل البيانات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4">
                    <h5>إعدادات إضافية</h5>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="enableNotifications" checked>
                        <label class="form-check-label" for="enableNotifications">
                            تفعيل الإشعارات
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="enableAutoBackup" checked>
                        <label class="form-check-label" for="enableAutoBackup">
                            تفعيل النسخ الاحتياطي التلقائي
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="enableAnalytics">
                        <label class="form-check-label" for="enableAnalytics">
                            تفعيل تحليلات الاستخدام
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- Step 5: Completion -->
            <div class="setup-step" id="step5">
                <div class="success-animation">
                    <div class="success-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h2>تم إعداد النظام بنجاح!</h2>
                    <p class="lead text-muted">نظام Black Horse ERP جاهز للاستخدام</p>
                    
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="fas fa-rocket fa-2x text-primary mb-2"></i>
                                <h6>النظام جاهز</h6>
                                <small class="text-muted">جميع الوحدات تم تفعيلها</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                                <h6>آمن ومحمي</h6>
                                <small class="text-muted">بياناتك محمية بالكامل</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="fas fa-headset fa-2x text-info mb-2"></i>
                                <h6>دعم فني</h6>
                                <small class="text-muted">دعم فني متاح 24/7</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <button class="btn btn-primary btn-lg btn-setup me-3" onclick="launchSystem()">
                            <i class="fas fa-play me-2"></i>تشغيل النظام
                        </button>
                        <button class="btn btn-outline-secondary btn-lg btn-setup" onclick="viewDocumentation()">
                            <i class="fas fa-book me-2"></i>دليل الاستخدام
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Navigation -->
            <div class="setup-navigation">
                <button id="prevBtn" class="btn btn-outline-secondary btn-setup" onclick="previousStep()" style="display: none;">
                    <i class="fas fa-arrow-right me-2"></i>السابق
                </button>
                <div></div>
                <button id="nextBtn" class="btn btn-primary btn-setup" onclick="nextStep()">
                    التالي<i class="fas fa-arrow-left ms-2"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let currentStep = 1;
        const totalSteps = 5;
        let selectedModules = ['inventory', 'sales', 'pos', 'reports'];
        
        // Initialize setup wizard
        document.addEventListener('DOMContentLoaded', function() {
            updateStepIndicator();
            updateProgress();
            
            // Feature card selection
            document.querySelectorAll('.feature-card').forEach(card => {
                card.addEventListener('click', function() {
                    const module = this.dataset.module;
                    const icon = this.querySelector('.ms-auto i');
                    
                    if (this.classList.contains('selected')) {
                        this.classList.remove('selected');
                        icon.className = 'fas fa-circle text-muted';
                        selectedModules = selectedModules.filter(m => m !== module);
                    } else {
                        this.classList.add('selected');
                        icon.className = 'fas fa-check-circle text-success';
                        selectedModules.push(module);
                    }
                });
            });
        });
        
        function nextStep() {
            if (currentStep < totalSteps) {
                if (validateCurrentStep()) {
                    document.getElementById(`step${currentStep}`).classList.remove('active');
                    currentStep++;
                    document.getElementById(`step${currentStep}`).classList.add('active');
                    updateStepIndicator();
                    updateProgress();
                    updateNavigation();
                }
            }
        }
        
        function previousStep() {
            if (currentStep > 1) {
                document.getElementById(`step${currentStep}`).classList.remove('active');
                currentStep--;
                document.getElementById(`step${currentStep}`).classList.add('active');
                updateStepIndicator();
                updateProgress();
                updateNavigation();
            }
        }
        
        function validateCurrentStep() {
            switch (currentStep) {
                case 2:
                    const companyName = document.getElementById('companyName').value;
                    if (!companyName.trim()) {
                        alert('يرجى إدخال اسم الشركة');
                        return false;
                    }
                    break;
                case 3:
                    if (selectedModules.length === 0) {
                        alert('يرجى اختيار وحدة واحدة على الأقل');
                        return false;
                    }
                    break;
            }
            return true;
        }
        
        function updateStepIndicator() {
            for (let i = 1; i <= totalSteps; i++) {
                const stepItem = document.querySelector(`.step-item[data-step="${i}"]`);
                const connector = stepItem.nextElementSibling;
                
                if (i < currentStep) {
                    stepItem.classList.add('completed');
                    stepItem.classList.remove('active');
                    if (connector) connector.classList.add('completed');
                } else if (i === currentStep) {
                    stepItem.classList.add('active');
                    stepItem.classList.remove('completed');
                } else {
                    stepItem.classList.remove('active', 'completed');
                    if (connector) connector.classList.remove('completed');
                }
            }
        }
        
        function updateProgress() {
            const progress = (currentStep / totalSteps) * 100;
            document.getElementById('setupProgress').style.width = `${progress}%`;
        }
        
        function updateNavigation() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            
            if (currentStep === 1) {
                prevBtn.style.display = 'none';
            } else {
                prevBtn.style.display = 'inline-block';
            }
            
            if (currentStep === totalSteps) {
                nextBtn.style.display = 'none';
            } else {
                nextBtn.style.display = 'inline-block';
            }
        }
        
        function createAdminAccount() {
            // Simulate admin account creation
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<span class="loading-spinner"></span> جاري الإنشاء...';
            btn.disabled = true;
            
            setTimeout(() => {
                btn.innerHTML = '<i class="fas fa-check me-2"></i>تم الإنشاء';
                btn.classList.remove('btn-primary');
                btn.classList.add('btn-success');
            }, 2000);
        }
        
        function loadSampleData() {
            // Simulate sample data loading
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<span class="loading-spinner"></span> جاري التحميل...';
            btn.disabled = true;
            
            setTimeout(() => {
                btn.innerHTML = '<i class="fas fa-check me-2"></i>تم التحميل';
                btn.classList.remove('btn-success');
                btn.classList.add('btn-success');
            }, 3000);
        }
        
        function launchSystem() {
            // Save setup configuration
            const setupConfig = {
                company: {
                    name: document.getElementById('companyName').value,
                    type: document.getElementById('businessType').value,
                    phone: document.getElementById('companyPhone').value,
                    email: document.getElementById('companyEmail').value,
                    address: document.getElementById('companyAddress').value,
                    currency: document.getElementById('defaultCurrency').value,
                    timezone: document.getElementById('timezone').value
                },
                modules: selectedModules,
                settings: {
                    notifications: document.getElementById('enableNotifications').checked,
                    autoBackup: document.getElementById('enableAutoBackup').checked,
                    analytics: document.getElementById('enableAnalytics').checked
                },
                setupCompleted: true,
                setupDate: new Date().toISOString()
            };
            
            localStorage.setItem('blackhorse_setup', JSON.stringify(setupConfig));
            
            // Redirect to main system
            window.location.href = 'index.html';
        }
        
        function viewDocumentation() {
            window.open('README.md', '_blank');
        }
    </script>
</body>
</html>
