<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص صحة النظام - Black Horse ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .health-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .status-healthy { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-critical { color: #dc3545; }
        .check-item {
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
        }
        .check-item.healthy { background: rgba(40, 167, 69, 0.1); border-left: 4px solid #28a745; }
        .check-item.warning { background: rgba(255, 193, 7, 0.1); border-left: 4px solid #ffc107; }
        .check-item.critical { background: rgba(220, 53, 69, 0.1); border-left: 4px solid #dc3545; }
        .progress-ring {
            transform: rotate(-90deg);
        }
        .progress-ring-circle {
            transition: stroke-dashoffset 0.35s;
            transform-origin: 50% 50%;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-12 col-lg-10">
                <!-- Header -->
                <div class="health-card p-4 mb-4">
                    <div class="text-center">
                        <h1><i class="fas fa-heartbeat me-2"></i>فحص صحة النظام</h1>
                        <p class="text-muted">تشخيص شامل لحالة نظام Black Horse ERP</p>
                    </div>
                    
                    <!-- Overall Health Score -->
                    <div class="row mt-4">
                        <div class="col-md-4 text-center">
                            <div class="position-relative d-inline-block">
                                <svg class="progress-ring" width="120" height="120">
                                    <circle class="progress-ring-circle" stroke="#e9ecef" stroke-width="8" fill="transparent" r="52" cx="60" cy="60"/>
                                    <circle class="progress-ring-circle" id="healthCircle" stroke="#28a745" stroke-width="8" fill="transparent" r="52" cx="60" cy="60" stroke-dasharray="327" stroke-dashoffset="327"/>
                                </svg>
                                <div class="position-absolute top-50 start-50 translate-middle">
                                    <h3 id="healthScore" class="mb-0">0%</h3>
                                    <small class="text-muted">صحة النظام</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-6 mb-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-check-circle fa-2x mb-2 status-healthy"></i>
                                            <h5 id="healthyCount">0</h5>
                                            <small>فحوصات سليمة</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-exclamation-triangle fa-2x mb-2 status-warning"></i>
                                            <h5 id="warningCount">0</h5>
                                            <small>تحذيرات</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-times-circle fa-2x mb-2 status-critical"></i>
                                            <h5 id="criticalCount">0</h5>
                                            <small>مشاكل حرجة</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="fas fa-clock fa-2x mb-2 text-info"></i>
                                            <h5 id="checkDuration">0s</h5>
                                            <small>مدة الفحص</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="text-center mt-4">
                        <button class="btn btn-primary btn-lg me-2" onclick="runHealthCheck()" id="checkButton">
                            <i class="fas fa-play me-2"></i>بدء الفحص
                        </button>
                        <button class="btn btn-success me-2" onclick="fixAllIssues()" id="fixButton" disabled>
                            <i class="fas fa-wrench me-2"></i>إصلاح المشاكل
                        </button>
                        <button class="btn btn-info" onclick="exportReport()">
                            <i class="fas fa-download me-2"></i>تصدير التقرير
                        </button>
                    </div>
                </div>

                <!-- Detailed Results -->
                <div class="health-card p-4">
                    <h4 class="mb-4"><i class="fas fa-list-check me-2"></i>نتائج الفحص التفصيلية</h4>
                    
                    <!-- Core System Checks -->
                    <div class="mb-4">
                        <h6><i class="fas fa-cogs me-2"></i>النظام الأساسي</h6>
                        <div id="coreChecks">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-hourglass-half fa-2x mb-3"></i>
                                <p>اضغط على "بدء الفحص" لبدء التشخيص</p>
                            </div>
                        </div>
                    </div>

                    <!-- Module Checks -->
                    <div class="mb-4">
                        <h6><i class="fas fa-puzzle-piece me-2"></i>الوحدات</h6>
                        <div id="moduleChecks">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-hourglass-half fa-2x mb-3"></i>
                                <p>في انتظار بدء الفحص...</p>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Checks -->
                    <div class="mb-4">
                        <h6><i class="fas fa-tachometer-alt me-2"></i>الأداء</h6>
                        <div id="performanceChecks">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-hourglass-half fa-2x mb-3"></i>
                                <p>في انتظار بدء الفحص...</p>
                            </div>
                        </div>
                    </div>

                    <!-- Security Checks -->
                    <div class="mb-4">
                        <h6><i class="fas fa-shield-alt me-2"></i>الأمان</h6>
                        <div id="securityChecks">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-hourglass-half fa-2x mb-3"></i>
                                <p>في انتظار بدء الفحص...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="health-card p-4 mt-4">
                    <h5 class="mb-3"><i class="fas fa-tools me-2"></i>إجراءات سريعة</h5>
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-primary w-100" onclick="clearCache()">
                                <i class="fas fa-broom me-2"></i>مسح التخزين المؤقت
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-success w-100" onclick="resetDatabase()">
                                <i class="fas fa-database me-2"></i>إعادة تعيين قاعدة البيانات
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-warning w-100" onclick="reloadModules()">
                                <i class="fas fa-sync me-2"></i>إعادة تحميل الوحدات
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-info w-100" onclick="openMainApp()">
                                <i class="fas fa-home me-2"></i>فتح التطبيق
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let healthData = {
            healthy: 0,
            warning: 0,
            critical: 0,
            total: 0,
            checks: []
        };

        let isChecking = false;

        function updateHealthScore() {
            const total = healthData.total;
            const score = total > 0 ? Math.round((healthData.healthy / total) * 100) : 0;
            
            document.getElementById('healthScore').textContent = score + '%';
            document.getElementById('healthyCount').textContent = healthData.healthy;
            document.getElementById('warningCount').textContent = healthData.warning;
            document.getElementById('criticalCount').textContent = healthData.critical;
            
            // Update progress ring
            const circle = document.getElementById('healthCircle');
            const circumference = 2 * Math.PI * 52;
            const offset = circumference - (score / 100) * circumference;
            circle.style.strokeDashoffset = offset;
            
            // Update color based on score
            if (score >= 80) {
                circle.style.stroke = '#28a745';
            } else if (score >= 60) {
                circle.style.stroke = '#ffc107';
            } else {
                circle.style.stroke = '#dc3545';
            }
            
            // Enable fix button if there are issues
            document.getElementById('fixButton').disabled = (healthData.warning + healthData.critical) === 0;
        }

        function addCheck(category, name, status, message, details = '') {
            healthData.total++;
            healthData[status]++;
            
            const check = { category, name, status, message, details };
            healthData.checks.push(check);
            
            const container = document.getElementById(category + 'Checks');
            if (container.querySelector('.text-muted')) {
                container.innerHTML = '';
            }
            
            const statusIcon = status === 'healthy' ? 'check-circle' : 
                             status === 'warning' ? 'exclamation-triangle' : 'times-circle';
            const statusText = status === 'healthy' ? 'سليم' : 
                             status === 'warning' ? 'تحذير' : 'خطأ';
            
            const checkElement = document.createElement('div');
            checkElement.className = `check-item ${status}`;
            checkElement.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-${statusIcon} me-2"></i>
                        <strong>${name}</strong>
                        <div class="small text-muted mt-1">${message}</div>
                        ${details ? `<div class="small mt-1">${details}</div>` : ''}
                    </div>
                    <span class="badge bg-${status === 'healthy' ? 'success' : status === 'warning' ? 'warning' : 'danger'}">
                        ${statusText}
                    </span>
                </div>
            `;
            
            container.appendChild(checkElement);
            updateHealthScore();
        }

        async function runHealthCheck() {
            if (isChecking) return;
            
            isChecking = true;
            const startTime = Date.now();
            
            // Reset data
            healthData = { healthy: 0, warning: 0, critical: 0, total: 0, checks: [] };
            
            // Clear previous results
            ['core', 'module', 'performance', 'security'].forEach(category => {
                document.getElementById(category + 'Checks').innerHTML = '';
            });
            
            // Update button
            const checkButton = document.getElementById('checkButton');
            checkButton.disabled = true;
            checkButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الفحص...';
            
            try {
                // Core system checks
                await checkCoreSystem();
                
                // Module checks
                await checkModules();
                
                // Performance checks
                await checkPerformance();
                
                // Security checks
                await checkSecurity();
                
            } catch (error) {
                console.error('Health check error:', error);
                addCheck('core', 'خطأ في الفحص', 'critical', 'حدث خطأ أثناء الفحص: ' + error.message);
            }
            
            // Update duration
            const duration = ((Date.now() - startTime) / 1000).toFixed(1);
            document.getElementById('checkDuration').textContent = duration + 's';
            
            // Reset button
            checkButton.disabled = false;
            checkButton.innerHTML = '<i class="fas fa-redo me-2"></i>إعادة الفحص';
            
            isChecking = false;
        }

        async function checkCoreSystem() {
            // Check main HTML file
            try {
                const response = await fetch('index.html');
                if (response.ok) {
                    addCheck('core', 'ملف التطبيق الرئيسي', 'healthy', 'ملف index.html متاح ويعمل بشكل صحيح');
                } else {
                    addCheck('core', 'ملف التطبيق الرئيسي', 'critical', 'ملف index.html غير متاح');
                }
            } catch (error) {
                addCheck('core', 'ملف التطبيق الرئيسي', 'critical', 'خطأ في الوصول لملف index.html: ' + error.message);
            }
            
            // Check core files
            const coreFiles = [
                { path: 'src/core/database.js', name: 'قاعدة البيانات' },
                { path: 'src/core/utils.js', name: 'الأدوات المساعدة' }
            ];
            
            for (const file of coreFiles) {
                try {
                    const response = await fetch(file.path);
                    if (response.ok) {
                        addCheck('core', file.name, 'healthy', `ملف ${file.path} متاح ويعمل بشكل صحيح`);
                    } else {
                        addCheck('core', file.name, 'critical', `ملف ${file.path} غير متاح`);
                    }
                } catch (error) {
                    addCheck('core', file.name, 'critical', `خطأ في الوصول لملف ${file.path}: ${error.message}`);
                }
            }
            
            // Check localStorage
            try {
                localStorage.setItem('health_test', 'test');
                localStorage.removeItem('health_test');
                addCheck('core', 'التخزين المحلي', 'healthy', 'التخزين المحلي يعمل بشكل صحيح');
            } catch (error) {
                addCheck('core', 'التخزين المحلي', 'critical', 'التخزين المحلي غير متاح: ' + error.message);
            }
        }

        async function checkModules() {
            const modules = [
                { path: 'src/modules/inventory/inventory.js', name: 'المخزون' },
                { path: 'src/modules/sales/sales.js', name: 'المبيعات' },
                { path: 'src/modules/pos/pos.js', name: 'نقاط البيع' },
                { path: 'src/modules/purchases/purchases.js', name: 'المشتريات' },
                { path: 'src/modules/cashier/cashier.js', name: 'الصندوق' },
                { path: 'src/modules/hr/hr.js', name: 'الموارد البشرية' },
                { path: 'src/modules/reports/reports.js', name: 'التقارير' },
                { path: 'src/modules/settings/settings.js', name: 'الإعدادات' }
            ];
            
            for (const module of modules) {
                try {
                    const response = await fetch(module.path);
                    if (response.ok) {
                        addCheck('module', `وحدة ${module.name}`, 'healthy', `وحدة ${module.name} متاحة وجاهزة للاستخدام`);
                    } else {
                        addCheck('module', `وحدة ${module.name}`, 'critical', `وحدة ${module.name} غير متاحة`);
                    }
                } catch (error) {
                    addCheck('module', `وحدة ${module.name}`, 'critical', `خطأ في تحميل وحدة ${module.name}: ${error.message}`);
                }
                
                // Small delay for visual effect
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }

        async function checkPerformance() {
            // Check page load time
            const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
            if (loadTime < 3000) {
                addCheck('performance', 'سرعة التحميل', 'healthy', `وقت التحميل: ${loadTime}ms - ممتاز`);
            } else if (loadTime < 5000) {
                addCheck('performance', 'سرعة التحميل', 'warning', `وقت التحميل: ${loadTime}ms - مقبول`);
            } else {
                addCheck('performance', 'سرعة التحميل', 'critical', `وقت التحميل: ${loadTime}ms - بطيء`);
            }
            
            // Check memory usage
            if (performance.memory) {
                const memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024;
                if (memoryUsage < 50) {
                    addCheck('performance', 'استخدام الذاكرة', 'healthy', `استخدام الذاكرة: ${memoryUsage.toFixed(1)}MB - ممتاز`);
                } else if (memoryUsage < 100) {
                    addCheck('performance', 'استخدام الذاكرة', 'warning', `استخدام الذاكرة: ${memoryUsage.toFixed(1)}MB - مقبول`);
                } else {
                    addCheck('performance', 'استخدام الذاكرة', 'critical', `استخدام الذاكرة: ${memoryUsage.toFixed(1)}MB - مرتفع`);
                }
            } else {
                addCheck('performance', 'استخدام الذاكرة', 'warning', 'معلومات الذاكرة غير متاحة في هذا المتصفح');
            }
        }

        async function checkSecurity() {
            // Check HTTPS
            if (location.protocol === 'https:') {
                addCheck('security', 'الاتصال الآمن', 'healthy', 'الموقع يستخدم HTTPS');
            } else {
                addCheck('security', 'الاتصال الآمن', 'warning', 'الموقع لا يستخدم HTTPS (مقبول للتطوير المحلي)');
            }
            
            // Check Content Security Policy
            const metaTags = document.querySelectorAll('meta[http-equiv="Content-Security-Policy"]');
            if (metaTags.length > 0) {
                addCheck('security', 'سياسة أمان المحتوى', 'healthy', 'سياسة أمان المحتوى مفعلة');
            } else {
                addCheck('security', 'سياسة أمان المحتوى', 'warning', 'سياسة أمان المحتوى غير مفعلة');
            }
            
            // Check for sensitive data in localStorage
            let sensitiveDataFound = false;
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (key.includes('password') || key.includes('token') || key.includes('secret'))) {
                    sensitiveDataFound = true;
                    break;
                }
            }
            
            if (!sensitiveDataFound) {
                addCheck('security', 'البيانات الحساسة', 'healthy', 'لا توجد بيانات حساسة في التخزين المحلي');
            } else {
                addCheck('security', 'البيانات الحساسة', 'warning', 'تم العثور على بيانات حساسة في التخزين المحلي');
            }
        }

        function fixAllIssues() {
            alert('وظيفة الإصلاح التلقائي قيد التطوير');
        }

        function exportReport() {
            const report = {
                timestamp: new Date().toISOString(),
                score: Math.round((healthData.healthy / healthData.total) * 100),
                summary: {
                    healthy: healthData.healthy,
                    warning: healthData.warning,
                    critical: healthData.critical,
                    total: healthData.total
                },
                checks: healthData.checks
            };
            
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `health-report-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        function clearCache() {
            localStorage.clear();
            sessionStorage.clear();
            alert('تم مسح التخزين المؤقت بنجاح');
        }

        function resetDatabase() {
            if (confirm('هل أنت متأكد من إعادة تعيين قاعدة البيانات؟ سيتم فقدان جميع البيانات.')) {
                localStorage.clear();
                alert('تم إعادة تعيين قاعدة البيانات بنجاح');
            }
        }

        function reloadModules() {
            location.reload();
        }

        function openMainApp() {
            window.open('index.html', '_blank');
        }

        // Auto-run health check on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runHealthCheck, 1000);
        });
    </script>
</body>
</html>
