<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الملفات - Black Horse ERP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/custom.css" rel="stylesheet">
    <style>
        .file-manager-container {
            height: calc(100vh - 200px);
            overflow: hidden;
        }
        
        .sidebar {
            background: #f8f9fa;
            border-left: 1px solid #dee2e6;
            height: 100%;
            overflow-y: auto;
        }
        
        .main-content {
            height: 100%;
            overflow-y: auto;
        }
        
        .toolbar {
            background: white;
            border-bottom: 1px solid #dee2e6;
            padding: 15px;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .breadcrumb-nav {
            background: #f8f9fa;
            padding: 10px 15px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .file-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            padding: 20px;
        }
        
        .file-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .file-item:hover {
            border-color: #007bff;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .file-item.selected {
            border-color: #007bff;
            background: #e7f3ff;
        }
        
        .file-icon {
            font-size: 3rem;
            margin-bottom: 10px;
            color: #6c757d;
        }
        
        .file-icon.image { color: #28a745; }
        .file-icon.pdf { color: #dc3545; }
        .file-icon.document { color: #007bff; }
        .file-icon.spreadsheet { color: #28a745; }
        .file-icon.folder { color: #ffc107; }
        
        .file-name {
            font-size: 0.9rem;
            font-weight: 500;
            margin-bottom: 5px;
            word-break: break-word;
        }
        
        .file-info {
            font-size: 0.75rem;
            color: #6c757d;
        }
        
        .file-list {
            padding: 0;
        }
        
        .file-list-item {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        
        .file-list-item:hover {
            background: #f8f9fa;
        }
        
        .file-list-item.selected {
            background: #e7f3ff;
        }
        
        .upload-zone {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 20px;
            transition: all 0.3s ease;
        }
        
        .upload-zone.dragover {
            border-color: #007bff;
            background: #f0f8ff;
        }
        
        .folder-tree {
            padding: 10px;
        }
        
        .folder-item {
            padding: 8px 12px;
            cursor: pointer;
            border-radius: 4px;
            margin-bottom: 2px;
            transition: background-color 0.2s ease;
        }
        
        .folder-item:hover {
            background: #e9ecef;
        }
        
        .folder-item.active {
            background: #007bff;
            color: white;
        }
        
        .folder-item i {
            margin-left: 8px;
            width: 16px;
        }
        
        .context-menu {
            position: fixed;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            z-index: 1000;
            min-width: 150px;
            display: none;
        }
        
        .context-menu-item {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .context-menu-item:hover {
            background: #f8f9fa;
        }
        
        .context-menu-item:last-child {
            border-bottom: none;
        }
        
        .progress-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .progress-card {
            background: white;
            padding: 30px;
            border-radius: 8px;
            min-width: 300px;
            text-align: center;
        }
        
        .search-results {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .empty-folder {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .empty-folder i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        .file-preview {
            max-width: 100%;
            max-height: 400px;
            border-radius: 8px;
        }
        
        .view-mode-buttons {
            display: flex;
            gap: 5px;
        }
        
        .view-mode-buttons .btn {
            padding: 8px 12px;
        }
        
        .storage-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .storage-bar {
            background: rgba(255,255,255,0.3);
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }
        
        .storage-used {
            background: white;
            height: 100%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- رأس الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-folder text-primary"></i> إدارة الملفات</h2>
                        <p class="text-muted">مركز إدارة جميع ملفات النظام</p>
                    </div>
                    <div>
                        <button class="btn btn-outline-primary me-2" onclick="showUploadModal()">
                            <i class="fas fa-upload"></i> رفع ملفات
                        </button>
                        <button class="btn btn-outline-success me-2" onclick="createNewFolder()">
                            <i class="fas fa-folder-plus"></i> مجلد جديد
                        </button>
                        <button class="btn btn-primary" onclick="showSettingsModal()">
                            <i class="fas fa-cog"></i> الإعدادات
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- حاوي إدارة الملفات -->
        <div class="row file-manager-container">
            <!-- الشريط الجانبي -->
            <div class="col-md-3 sidebar">
                <!-- معلومات التخزين -->
                <div class="storage-info">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">مساحة التخزين</h6>
                            <small id="storageUsed">0 بايت من 1 جيجابايت</small>
                        </div>
                        <i class="fas fa-hdd fa-2x"></i>
                    </div>
                    <div class="storage-bar">
                        <div class="storage-used" id="storageBar" style="width: 0%"></div>
                    </div>
                </div>

                <!-- شجرة المجلدات -->
                <div class="folder-tree">
                    <h6 class="mb-3">المجلدات</h6>
                    <div id="folderTree">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>

                <!-- الملفات الأخيرة -->
                <div class="mt-4">
                    <h6 class="mb-3">الملفات الأخيرة</h6>
                    <div id="recentFiles">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 main-content">
                <!-- شريط الأدوات -->
                <div class="toolbar">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchInput" 
                                       placeholder="البحث في الملفات..." onkeyup="searchFiles()">
                                <button class="btn btn-outline-secondary" onclick="showAdvancedSearch()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-end align-items-center">
                                <!-- أزرار العرض -->
                                <div class="view-mode-buttons me-3">
                                    <button class="btn btn-outline-secondary active" onclick="setViewMode('grid')" id="gridViewBtn">
                                        <i class="fas fa-th"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="setViewMode('list')" id="listViewBtn">
                                        <i class="fas fa-list"></i>
                                    </button>
                                </div>

                                <!-- أزرار الإجراءات -->
                                <div class="btn-group">
                                    <button class="btn btn-outline-secondary" onclick="selectAll()" title="تحديد الكل">
                                        <i class="fas fa-check-square"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="copySelected()" title="نسخ" disabled id="copyBtn">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="cutSelected()" title="قص" disabled id="cutBtn">
                                        <i class="fas fa-cut"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="pasteFiles()" title="لصق" disabled id="pasteBtn">
                                        <i class="fas fa-paste"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deleteSelected()" title="حذف" disabled id="deleteBtn">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- شريط التنقل -->
                <div class="breadcrumb-nav">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0" id="breadcrumb">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </ol>
                    </nav>
                </div>

                <!-- منطقة الرفع -->
                <div class="upload-zone" id="uploadZone" style="display: none;">
                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                    <h5>اسحب الملفات هنا أو انقر للاختيار</h5>
                    <p class="text-muted">يمكنك رفع ملفات متعددة في نفس الوقت</p>
                    <input type="file" id="fileInput" multiple style="display: none;">
                </div>

                <!-- عرض الملفات -->
                <div id="fileDisplay">
                    <!-- عرض الشبكة -->
                    <div id="gridView" class="file-grid">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>

                    <!-- عرض القائمة -->
                    <div id="listView" class="file-list" style="display: none;">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>

                    <!-- حالة فارغة -->
                    <div id="emptyFolder" class="empty-folder" style="display: none;">
                        <i class="fas fa-folder-open"></i>
                        <h4>المجلد فارغ</h4>
                        <p>لا توجد ملفات في هذا المجلد</p>
                        <button class="btn btn-primary" onclick="showUploadModal()">
                            <i class="fas fa-upload"></i> رفع ملفات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة رفع الملفات -->
    <div class="modal fade" id="uploadModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">رفع ملفات</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="upload-zone" id="modalUploadZone">
                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                        <h5>اختر الملفات للرفع</h5>
                        <p class="text-muted">أو اسحب الملفات هنا</p>
                        <button class="btn btn-primary" onclick="document.getElementById('modalFileInput').click()">
                            <i class="fas fa-file"></i> اختيار ملفات
                        </button>
                        <input type="file" id="modalFileInput" multiple style="display: none;">
                    </div>

                    <!-- قائمة الملفات المحددة -->
                    <div id="selectedFiles" style="display: none;">
                        <h6 class="mt-4 mb-3">الملفات المحددة:</h6>
                        <div id="filesList"></div>
                    </div>

                    <!-- شريط التقدم -->
                    <div id="uploadProgress" style="display: none;">
                        <h6 class="mt-4 mb-3">جاري الرفع...</h6>
                        <div class="progress">
                            <div class="progress-bar" id="progressBar" style="width: 0%"></div>
                        </div>
                        <small class="text-muted" id="progressText">0%</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="startUpload()" id="uploadBtn" disabled>
                        <i class="fas fa-upload"></i> رفع الملفات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إنشاء مجلد -->
    <div class="modal fade" id="folderModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إنشاء مجلد جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">اسم المجلد</label>
                        <input type="text" class="form-control" id="folderName" placeholder="أدخل اسم المجلد">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="createFolder()">
                        <i class="fas fa-folder-plus"></i> إنشاء
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة معاينة الملف -->
    <div class="modal fade" id="previewModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="previewTitle"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center" id="previewBody">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="downloadCurrentFile()">
                        <i class="fas fa-download"></i> تحميل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- القائمة السياقية -->
    <div class="context-menu" id="contextMenu">
        <div class="context-menu-item" onclick="openFile()">
            <i class="fas fa-eye me-2"></i> فتح
        </div>
        <div class="context-menu-item" onclick="downloadFile()">
            <i class="fas fa-download me-2"></i> تحميل
        </div>
        <div class="context-menu-item" onclick="renameFile()">
            <i class="fas fa-edit me-2"></i> إعادة تسمية
        </div>
        <div class="context-menu-item" onclick="copyFile()">
            <i class="fas fa-copy me-2"></i> نسخ
        </div>
        <div class="context-menu-item" onclick="cutFile()">
            <i class="fas fa-cut me-2"></i> قص
        </div>
        <div class="context-menu-item" onclick="deleteFile()">
            <i class="fas fa-trash me-2"></i> حذف
        </div>
        <div class="context-menu-item" onclick="showFileProperties()">
            <i class="fas fa-info-circle me-2"></i> خصائص
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../core/database.js"></script>
    <script src="../../core/utils.js"></script>
    <script src="file-manager.js"></script>

    <script>
        // متغيرات عامة
        let fileManager;
        let currentViewMode = 'grid';
        let selectedFiles = new Set();
        let currentFile = null;
        let dragCounter = 0;

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeFileManager();
            setupEventListeners();
            loadCurrentFolder();
            updateStorageInfo();
        });

        // تهيئة مدير الملفات
        function initializeFileManager() {
            fileManager = new FileManager();
            console.log('✅ تم تهيئة واجهة إدارة الملفات');
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // رفع الملفات بالسحب والإفلات
            const uploadZone = document.getElementById('uploadZone');
            const modalUploadZone = document.getElementById('modalUploadZone');

            [uploadZone, modalUploadZone].forEach(zone => {
                zone.addEventListener('dragenter', handleDragEnter);
                zone.addEventListener('dragover', handleDragOver);
                zone.addEventListener('dragleave', handleDragLeave);
                zone.addEventListener('drop', handleDrop);
            });

            // اختيار الملفات
            document.getElementById('fileInput').addEventListener('change', handleFileSelect);
            document.getElementById('modalFileInput').addEventListener('change', handleModalFileSelect);

            // القائمة السياقية
            document.addEventListener('contextmenu', handleContextMenu);
            document.addEventListener('click', hideContextMenu);

            // اختصارات لوحة المفاتيح
            document.addEventListener('keydown', handleKeyboard);

            // تغيير حجم النافذة
            window.addEventListener('resize', updateLayout);
        }

        // تحميل المجلد الحالي
        function loadCurrentFolder() {
            updateBreadcrumb();
            loadFolderTree();
            displayFiles();
            updateRecentFiles();
        }

        // عرض الملفات
        function displayFiles() {
            const files = fileManager.getFolderFiles(fileManager.currentPath);
            const folders = fileManager.getSubFolders(fileManager.currentPath);
            const allItems = [...folders, ...files];

            if (allItems.length === 0) {
                showEmptyFolder();
                return;
            }

            hideEmptyFolder();

            if (currentViewMode === 'grid') {
                displayGridView(allItems);
            } else {
                displayListView(allItems);
            }
        }

        // عرض الشبكة
        function displayGridView(items) {
            const gridView = document.getElementById('gridView');
            const listView = document.getElementById('listView');

            gridView.style.display = 'grid';
            listView.style.display = 'none';

            gridView.innerHTML = items.map(item => createGridItem(item)).join('');
        }

        // عرض القائمة
        function displayListView(items) {
            const gridView = document.getElementById('gridView');
            const listView = document.getElementById('listView');

            gridView.style.display = 'none';
            listView.style.display = 'block';

            listView.innerHTML = items.map(item => createListItem(item)).join('');
        }

        // إنشاء عنصر الشبكة
        function createGridItem(item) {
            const isFolder = !item.type;
            const icon = getFileIcon(item);
            const size = isFolder ? `${item.fileCount} ملف` : fileManager.formatFileSize(item.size);
            const selected = selectedFiles.has(item.id) ? 'selected' : '';

            return `
                <div class="file-item ${selected}" data-id="${item.id}" data-type="${isFolder ? 'folder' : 'file'}"
                     onclick="selectItem('${item.id}')" ondblclick="${isFolder ? `openFolder('${item.path}')` : `openFile('${item.id}')`}">
                    <div class="file-icon ${getFileIconClass(item)}">
                        <i class="${icon}"></i>
                    </div>
                    <div class="file-name" title="${item.name}">${truncateText(item.name, 20)}</div>
                    <div class="file-info">${size}</div>
                </div>
            `;
        }

        // إنشاء عنصر القائمة
        function createListItem(item) {
            const isFolder = !item.type;
            const icon = getFileIcon(item);
            const size = isFolder ? `${item.fileCount} ملف` : fileManager.formatFileSize(item.size);
            const date = new Date(item.modified).toLocaleDateString('ar-EG');
            const selected = selectedFiles.has(item.id) ? 'selected' : '';

            return `
                <div class="file-list-item ${selected}" data-id="${item.id}" data-type="${isFolder ? 'folder' : 'file'}"
                     onclick="selectItem('${item.id}')" ondblclick="${isFolder ? `openFolder('${item.path}')` : `openFile('${item.id}')`}">
                    <div class="me-3">
                        <i class="${icon}" style="width: 20px;"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="file-name">${item.name}</div>
                    </div>
                    <div class="me-3 text-muted" style="min-width: 80px;">${size}</div>
                    <div class="text-muted" style="min-width: 100px;">${date}</div>
                </div>
            `;
        }

        // الحصول على أيقونة الملف
        function getFileIcon(item) {
            if (!item.type) return 'fas fa-folder'; // مجلد

            if (item.type.startsWith('image/')) return 'fas fa-image';
            if (item.type === 'application/pdf') return 'fas fa-file-pdf';
            if (item.type.includes('word') || item.name.endsWith('.doc') || item.name.endsWith('.docx')) return 'fas fa-file-word';
            if (item.type.includes('excel') || item.name.endsWith('.xls') || item.name.endsWith('.xlsx')) return 'fas fa-file-excel';
            if (item.type.includes('powerpoint') || item.name.endsWith('.ppt') || item.name.endsWith('.pptx')) return 'fas fa-file-powerpoint';
            if (item.type.startsWith('text/')) return 'fas fa-file-alt';
            if (item.type.startsWith('video/')) return 'fas fa-file-video';
            if (item.type.startsWith('audio/')) return 'fas fa-file-audio';

            return 'fas fa-file';
        }

        // الحصول على فئة أيقونة الملف
        function getFileIconClass(item) {
            if (!item.type) return 'folder';

            if (item.type.startsWith('image/')) return 'image';
            if (item.type === 'application/pdf') return 'pdf';
            if (item.type.includes('word') || item.type.includes('excel') || item.type.includes('powerpoint')) return 'document';
            if (item.name.endsWith('.xls') || item.name.endsWith('.xlsx')) return 'spreadsheet';

            return 'file';
        }

        // تحديد عنصر
        function selectItem(itemId) {
            if (selectedFiles.has(itemId)) {
                selectedFiles.delete(itemId);
            } else {
                selectedFiles.add(itemId);
            }

            updateSelection();
            updateToolbar();
        }

        // تحديث التحديد
        function updateSelection() {
            document.querySelectorAll('.file-item, .file-list-item').forEach(item => {
                const itemId = item.dataset.id;
                if (selectedFiles.has(itemId)) {
                    item.classList.add('selected');
                } else {
                    item.classList.remove('selected');
                }
            });
        }

        // تحديث شريط الأدوات
        function updateToolbar() {
            const hasSelection = selectedFiles.size > 0;

            document.getElementById('copyBtn').disabled = !hasSelection;
            document.getElementById('cutBtn').disabled = !hasSelection;
            document.getElementById('deleteBtn').disabled = !hasSelection;
        }

        // فتح مجلد
        function openFolder(folderPath) {
            fileManager.currentPath = folderPath;
            selectedFiles.clear();
            loadCurrentFolder();
        }

        // فتح ملف
        function openFile(fileId) {
            const file = fileManager.files.get(fileId);
            if (!file) return;

            currentFile = file;

            if (file.type.startsWith('image/')) {
                showImagePreview(file);
            } else if (file.type === 'application/pdf') {
                showPdfPreview(file);
            } else if (file.type.startsWith('text/')) {
                showTextPreview(file);
            } else {
                // تحميل مباشر للملفات الأخرى
                fileManager.downloadFile(fileId);
            }
        }

        // عرض معاينة الصورة
        function showImagePreview(file) {
            document.getElementById('previewTitle').textContent = file.name;
            document.getElementById('previewBody').innerHTML = `
                <img src="data:${file.type};base64,${btoa(file.content)}" class="file-preview" alt="${file.name}">
            `;
            new bootstrap.Modal(document.getElementById('previewModal')).show();
        }

        // عرض معاينة PDF
        function showPdfPreview(file) {
            document.getElementById('previewTitle').textContent = file.name;
            document.getElementById('previewBody').innerHTML = `
                <embed src="data:${file.type};base64,${btoa(file.content)}" type="application/pdf" width="100%" height="600px">
            `;
            new bootstrap.Modal(document.getElementById('previewModal')).show();
        }

        // عرض معاينة النص
        function showTextPreview(file) {
            document.getElementById('previewTitle').textContent = file.name;
            document.getElementById('previewBody').innerHTML = `
                <pre class="text-start bg-light p-3 rounded" style="max-height: 500px; overflow-y: auto;">${file.content}</pre>
            `;
            new bootstrap.Modal(document.getElementById('previewModal')).show();
        }

        // تحديث شريط التنقل
        function updateBreadcrumb() {
            const breadcrumb = document.getElementById('breadcrumb');
            const pathParts = fileManager.currentPath.split('/').filter(part => part);

            let html = '<li class="breadcrumb-item"><a href="#" onclick="openFolder(\'/\')">الرئيسية</a></li>';

            let currentPath = '';
            pathParts.forEach((part, index) => {
                currentPath += '/' + part;
                if (index === pathParts.length - 1) {
                    html += `<li class="breadcrumb-item active">${part}</li>`;
                } else {
                    html += `<li class="breadcrumb-item"><a href="#" onclick="openFolder('${currentPath}')">${part}</a></li>`;
                }
            });

            breadcrumb.innerHTML = html;
        }

        // تحميل شجرة المجلدات
        function loadFolderTree() {
            const tree = document.getElementById('folderTree');
            const folders = Array.from(fileManager.folders.values());

            tree.innerHTML = folders.map(folder => {
                const active = folder.path === fileManager.currentPath ? 'active' : '';
                return `
                    <div class="folder-item ${active}" onclick="openFolder('${folder.path}')">
                        <i class="${folder.icon || 'fas fa-folder'}"></i>
                        ${folder.name}
                    </div>
                `;
            }).join('');
        }

        // تحديث الملفات الأخيرة
        function updateRecentFiles() {
            const recentFiles = document.getElementById('recentFiles');
            const files = Array.from(fileManager.files.values())
                .sort((a, b) => new Date(b.modified) - new Date(a.modified))
                .slice(0, 5);

            recentFiles.innerHTML = files.map(file => `
                <div class="folder-item" onclick="openFile('${file.id}')">
                    <i class="${getFileIcon(file)}"></i>
                    ${truncateText(file.name, 15)}
                </div>
            `).join('');
        }

        // تحديث معلومات التخزين
        function updateStorageInfo() {
            const totalSize = Array.from(fileManager.files.values())
                .reduce((total, file) => total + file.size, 0);

            const maxSize = 1024 * 1024 * 1024; // 1GB
            const percentage = (totalSize / maxSize) * 100;

            document.getElementById('storageUsed').textContent =
                `${fileManager.formatFileSize(totalSize)} من ${fileManager.formatFileSize(maxSize)}`;
            document.getElementById('storageBar').style.width = `${Math.min(percentage, 100)}%`;
        }

        // تعيين وضع العرض
        function setViewMode(mode) {
            currentViewMode = mode;

            document.querySelectorAll('.view-mode-buttons .btn').forEach(btn => {
                btn.classList.remove('active');
            });

            if (mode === 'grid') {
                document.getElementById('gridViewBtn').classList.add('active');
            } else {
                document.getElementById('listViewBtn').classList.add('active');
            }

            displayFiles();
        }

        // عرض المجلد الفارغ
        function showEmptyFolder() {
            document.getElementById('fileDisplay').style.display = 'none';
            document.getElementById('emptyFolder').style.display = 'block';
        }

        // إخفاء المجلد الفارغ
        function hideEmptyFolder() {
            document.getElementById('fileDisplay').style.display = 'block';
            document.getElementById('emptyFolder').style.display = 'none';
        }

        // عرض نافذة الرفع
        function showUploadModal() {
            new bootstrap.Modal(document.getElementById('uploadModal')).show();
        }

        // معالجة السحب والإفلات
        function handleDragEnter(e) {
            e.preventDefault();
            dragCounter++;
            e.currentTarget.classList.add('dragover');
        }

        function handleDragOver(e) {
            e.preventDefault();
        }

        function handleDragLeave(e) {
            e.preventDefault();
            dragCounter--;
            if (dragCounter === 0) {
                e.currentTarget.classList.remove('dragover');
            }
        }

        function handleDrop(e) {
            e.preventDefault();
            dragCounter = 0;
            e.currentTarget.classList.remove('dragover');

            const files = Array.from(e.dataTransfer.files);
            if (files.length > 0) {
                handleFileUpload(files);
            }
        }

        // معالجة اختيار الملفات
        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            handleFileUpload(files);
        }

        function handleModalFileSelect(e) {
            const files = Array.from(e.target.files);
            displaySelectedFiles(files);
        }

        // معالجة رفع الملفات
        async function handleFileUpload(files) {
            try {
                showUploadProgress();

                const results = await fileManager.uploadMultipleFiles(files, fileManager.currentPath);

                hideUploadProgress();

                if (results.success.length > 0) {
                    showNotification(`تم رفع ${results.success.length} ملف بنجاح`, 'success');
                    loadCurrentFolder();
                    updateStorageInfo();
                }

                if (results.errors.length > 0) {
                    showNotification(`فشل في رفع ${results.errors.length} ملف`, 'error');
                }

            } catch (error) {
                hideUploadProgress();
                showNotification('خطأ في رفع الملفات: ' + error.message, 'error');
            }
        }

        // عرض الملفات المحددة
        function displaySelectedFiles(files) {
            const selectedFilesDiv = document.getElementById('selectedFiles');
            const filesList = document.getElementById('filesList');

            if (files.length === 0) {
                selectedFilesDiv.style.display = 'none';
                document.getElementById('uploadBtn').disabled = true;
                return;
            }

            selectedFilesDiv.style.display = 'block';
            document.getElementById('uploadBtn').disabled = false;

            filesList.innerHTML = files.map(file => `
                <div class="d-flex justify-content-between align-items-center p-2 border rounded mb-2">
                    <div>
                        <i class="${getFileIconByType(file.type)}"></i>
                        <span class="ms-2">${file.name}</span>
                    </div>
                    <small class="text-muted">${fileManager.formatFileSize(file.size)}</small>
                </div>
            `).join('');
        }

        // الحصول على أيقونة الملف حسب النوع
        function getFileIconByType(type) {
            if (type.startsWith('image/')) return 'fas fa-image text-success';
            if (type === 'application/pdf') return 'fas fa-file-pdf text-danger';
            if (type.includes('word')) return 'fas fa-file-word text-primary';
            if (type.includes('excel')) return 'fas fa-file-excel text-success';
            if (type.startsWith('text/')) return 'fas fa-file-alt text-info';
            return 'fas fa-file text-muted';
        }

        // بدء الرفع
        async function startUpload() {
            const files = Array.from(document.getElementById('modalFileInput').files);
            if (files.length === 0) return;

            try {
                showModalUploadProgress();

                for (let i = 0; i < files.length; i++) {
                    await fileManager.uploadFile(files[i], fileManager.currentPath);
                    updateModalProgress((i + 1) / files.length * 100);
                }

                hideModalUploadProgress();
                bootstrap.Modal.getInstance(document.getElementById('uploadModal')).hide();

                showNotification(`تم رفع ${files.length} ملف بنجاح`, 'success');
                loadCurrentFolder();
                updateStorageInfo();

            } catch (error) {
                hideModalUploadProgress();
                showNotification('خطأ في رفع الملفات: ' + error.message, 'error');
            }
        }

        // عرض تقدم الرفع
        function showUploadProgress() {
            // تنفيذ عرض تقدم الرفع
        }

        function hideUploadProgress() {
            // تنفيذ إخفاء تقدم الرفع
        }

        function showModalUploadProgress() {
            document.getElementById('uploadProgress').style.display = 'block';
            document.getElementById('uploadBtn').disabled = true;
        }

        function hideModalUploadProgress() {
            document.getElementById('uploadProgress').style.display = 'none';
            document.getElementById('uploadBtn').disabled = false;
        }

        function updateModalProgress(percentage) {
            document.getElementById('progressBar').style.width = `${percentage}%`;
            document.getElementById('progressText').textContent = `${Math.round(percentage)}%`;
        }

        // دوال مساعدة
        function truncateText(text, maxLength) {
            return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
        }

        function showNotification(message, type = 'info') {
            const alertClass = {
                'success': 'alert-success',
                'error': 'alert-danger',
                'warning': 'alert-warning',
                'info': 'alert-info'
            }[type] || 'alert-info';

            const notification = document.createElement('div');
            notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // معالجة القائمة السياقية
        function handleContextMenu(e) {
            const fileItem = e.target.closest('.file-item, .file-list-item');
            if (fileItem) {
                e.preventDefault();
                showContextMenu(e.clientX, e.clientY, fileItem.dataset.id);
            }
        }

        function showContextMenu(x, y, itemId) {
            const contextMenu = document.getElementById('contextMenu');
            contextMenu.style.display = 'block';
            contextMenu.style.left = `${x}px`;
            contextMenu.style.top = `${y}px`;
            contextMenu.dataset.itemId = itemId;
        }

        function hideContextMenu() {
            document.getElementById('contextMenu').style.display = 'none';
        }

        // معالجة لوحة المفاتيح
        function handleKeyboard(e) {
            if (e.ctrlKey) {
                switch (e.key) {
                    case 'a':
                        e.preventDefault();
                        selectAll();
                        break;
                    case 'c':
                        e.preventDefault();
                        copySelected();
                        break;
                    case 'x':
                        e.preventDefault();
                        cutSelected();
                        break;
                    case 'v':
                        e.preventDefault();
                        pasteFiles();
                        break;
                }
            } else if (e.key === 'Delete') {
                deleteSelected();
            }
        }

        // دوال الإجراءات
        function selectAll() {
            document.querySelectorAll('.file-item, .file-list-item').forEach(item => {
                selectedFiles.add(item.dataset.id);
            });
            updateSelection();
            updateToolbar();
        }

        function copySelected() {
            if (selectedFiles.size === 0) return;
            fileManager.clipboard = { action: 'copy', items: Array.from(selectedFiles) };
            document.getElementById('pasteBtn').disabled = false;
            showNotification(`تم نسخ ${selectedFiles.size} عنصر`, 'success');
        }

        function cutSelected() {
            if (selectedFiles.size === 0) return;
            fileManager.clipboard = { action: 'cut', items: Array.from(selectedFiles) };
            document.getElementById('pasteBtn').disabled = false;
            showNotification(`تم قص ${selectedFiles.size} عنصر`, 'success');
        }

        async function pasteFiles() {
            if (!fileManager.clipboard) return;

            try {
                const { action, items } = fileManager.clipboard;

                for (const itemId of items) {
                    if (action === 'copy') {
                        await fileManager.copyFile(itemId, fileManager.currentPath);
                    } else if (action === 'cut') {
                        await fileManager.moveFile(itemId, fileManager.currentPath);
                    }
                }

                if (action === 'cut') {
                    fileManager.clipboard = null;
                    document.getElementById('pasteBtn').disabled = true;
                }

                loadCurrentFolder();
                showNotification(`تم ${action === 'copy' ? 'نسخ' : 'نقل'} ${items.length} عنصر`, 'success');

            } catch (error) {
                showNotification('خطأ في العملية: ' + error.message, 'error');
            }
        }

        async function deleteSelected() {
            if (selectedFiles.size === 0) return;

            if (confirm(`هل أنت متأكد من حذف ${selectedFiles.size} عنصر؟`)) {
                try {
                    for (const itemId of selectedFiles) {
                        await fileManager.deleteFile(itemId);
                    }

                    selectedFiles.clear();
                    loadCurrentFolder();
                    updateToolbar();
                    showNotification('تم حذف العناصر المحددة', 'success');

                } catch (error) {
                    showNotification('خطأ في الحذف: ' + error.message, 'error');
                }
            }
        }

        // إنشاء مجلد جديد
        function createNewFolder() {
            new bootstrap.Modal(document.getElementById('folderModal')).show();
        }

        function createFolder() {
            const folderName = document.getElementById('folderName').value.trim();
            if (!folderName) {
                showNotification('يرجى إدخال اسم المجلد', 'warning');
                return;
            }

            try {
                fileManager.createFolder(folderName, fileManager.currentPath);
                bootstrap.Modal.getInstance(document.getElementById('folderModal')).hide();
                document.getElementById('folderName').value = '';
                loadCurrentFolder();
                showNotification('تم إنشاء المجلد بنجاح', 'success');
            } catch (error) {
                showNotification('خطأ في إنشاء المجلد: ' + error.message, 'error');
            }
        }

        // البحث في الملفات
        function searchFiles() {
            const query = document.getElementById('searchInput').value.trim();
            if (!query) {
                loadCurrentFolder();
                return;
            }

            const results = fileManager.searchFiles(query);
            displaySearchResults(results);
        }

        function displaySearchResults(results) {
            if (currentViewMode === 'grid') {
                document.getElementById('gridView').innerHTML = results.map(item => createGridItem(item)).join('');
            } else {
                document.getElementById('listView').innerHTML = results.map(item => createListItem(item)).join('');
            }

            if (results.length === 0) {
                showEmptyFolder();
            } else {
                hideEmptyFolder();
            }
        }

        // تحميل الملف الحالي
        function downloadCurrentFile() {
            if (currentFile) {
                fileManager.downloadFile(currentFile.id);
            }
        }

        // تحديث التخطيط
        function updateLayout() {
            // تحديث التخطيط عند تغيير حجم النافذة
        }

        // دوال القائمة السياقية
        function openFile() {
            const itemId = document.getElementById('contextMenu').dataset.itemId;
            openFile(itemId);
            hideContextMenu();
        }

        function downloadFile() {
            const itemId = document.getElementById('contextMenu').dataset.itemId;
            fileManager.downloadFile(itemId);
            hideContextMenu();
        }

        function renameFile() {
            const itemId = document.getElementById('contextMenu').dataset.itemId;
            // تنفيذ إعادة التسمية
            hideContextMenu();
        }

        function copyFile() {
            const itemId = document.getElementById('contextMenu').dataset.itemId;
            selectedFiles.clear();
            selectedFiles.add(itemId);
            copySelected();
            hideContextMenu();
        }

        function cutFile() {
            const itemId = document.getElementById('contextMenu').dataset.itemId;
            selectedFiles.clear();
            selectedFiles.add(itemId);
            cutSelected();
            hideContextMenu();
        }

        function deleteFile() {
            const itemId = document.getElementById('contextMenu').dataset.itemId;
            selectedFiles.clear();
            selectedFiles.add(itemId);
            deleteSelected();
            hideContextMenu();
        }

        function showFileProperties() {
            const itemId = document.getElementById('contextMenu').dataset.itemId;
            // تنفيذ عرض الخصائص
            hideContextMenu();
        }

        // عرض البحث المتقدم
        function showAdvancedSearch() {
            // تنفيذ البحث المتقدم
        }

        // عرض الإعدادات
        function showSettingsModal() {
            // تنفيذ عرض الإعدادات
        }
    </script>
</body>
</html>
