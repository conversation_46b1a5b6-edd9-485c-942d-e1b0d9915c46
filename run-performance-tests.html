<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Black Horse POS - Performance Tests</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .test-controls {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(52, 152, 219, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(39, 174, 96, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(243, 156, 18, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .progress-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db 0%, #2980b9 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            font-weight: 600;
            color: #2c3e50;
        }

        .results-container {
            display: none;
        }

        .summary-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 5px solid #3498db;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stat-value {
            font-size: 2em;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
        }

        .grade-A { color: #27ae60; }
        .grade-B { color: #f39c12; }
        .grade-C { color: #e67e22; }
        .grade-D { color: #e74c3c; }
        .grade-F { color: #c0392b; }

        .test-category {
            background: white;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .category-header {
            background: #2c3e50;
            color: white;
            padding: 15px 20px;
            font-weight: 600;
        }

        .test-item {
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .test-item:last-child {
            border-bottom: none;
        }

        .test-name {
            font-weight: 600;
        }

        .test-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
        }

        .status-pass {
            background: #d4edda;
            color: #155724;
        }

        .status-fail {
            background: #f8d7da;
            color: #721c24;
        }

        .status-error {
            background: #fff3cd;
            color: #856404;
        }

        .recommendations {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
        }

        .recommendations h3 {
            color: #856404;
            margin-bottom: 15px;
        }

        .recommendations ul {
            list-style-type: none;
            padding: 0;
        }

        .recommendations li {
            padding: 8px 0;
            border-bottom: 1px solid #ffeaa7;
        }

        .recommendations li:last-child {
            border-bottom: none;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .footer {
            text-align: center;
            padding: 20px;
            color: #6c757d;
            border-top: 1px solid #e9ecef;
            margin-top: 30px;
        }

        @media (max-width: 768px) {
            .test-controls {
                flex-direction: column;
            }
            
            .summary-stats {
                grid-template-columns: 1fr;
            }
            
            .test-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐎 Black Horse POS</h1>
            <p>اختبارات الأداء الشاملة - Performance Testing Suite</p>
        </div>

        <div class="content">
            <div class="test-controls">
                <button id="runTestsBtn" class="btn btn-primary">
                    🚀 تشغيل جميع الاختبارات
                </button>
                <button id="exportReportBtn" class="btn btn-success" disabled>
                    📤 تصدير التقرير
                </button>
                <button id="openDashboardBtn" class="btn btn-warning">
                    📊 فتح لوحة المراقبة
                </button>
                <a href="src/index.html" class="btn btn-primary">
                    🏠 العودة للتطبيق الرئيسي
                </a>
            </div>

            <div id="progressContainer" class="progress-container">
                <div class="progress-bar">
                    <div id="progressFill" class="progress-fill"></div>
                </div>
                <div id="progressText" class="progress-text">جاري التحضير...</div>
            </div>

            <div id="resultsContainer" class="results-container">
                <div class="summary-card">
                    <h2>📊 ملخص النتائج</h2>
                    <div class="summary-stats">
                        <div class="stat-item">
                            <div id="totalTests" class="stat-value">0</div>
                            <div class="stat-label">إجمالي الاختبارات</div>
                        </div>
                        <div class="stat-item">
                            <div id="passedTests" class="stat-value">0</div>
                            <div class="stat-label">اختبارات ناجحة</div>
                        </div>
                        <div class="stat-item">
                            <div id="failedTests" class="stat-value">0</div>
                            <div class="stat-label">اختبارات فاشلة</div>
                        </div>
                        <div class="stat-item">
                            <div id="successRate" class="stat-value">0%</div>
                            <div class="stat-label">معدل النجاح</div>
                        </div>
                        <div class="stat-item">
                            <div id="overallGrade" class="stat-value grade-F">F</div>
                            <div class="stat-label">التقييم العام</div>
                        </div>
                        <div class="stat-item">
                            <div id="testDuration" class="stat-value">0ms</div>
                            <div class="stat-label">مدة الاختبار</div>
                        </div>
                    </div>
                </div>

                <div id="testResults"></div>

                <div id="recommendations" class="recommendations" style="display: none;">
                    <h3>💡 التوصيات</h3>
                    <ul id="recommendationsList"></ul>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>© 2024 Black Horse POS - نظام نقاط البيع المتطور</p>
            <p>تم التطوير بواسطة Augment Agent</p>
        </div>
    </div>

    <!-- تحميل المكتبات الأساسية -->
    <script src="src/utils/helpers.js"></script>
    <script src="src/utils/validators.js"></script>
    <script src="src/database/database-optimizer.js"></script>
    <script src="src/database/unified-storage.js"></script>
    <script src="src/core/performance-optimizer.js"></script>
    <script src="src/ui/ui-optimizer.js"></script>
    <script src="src/core/asset-optimizer.js"></script>
    
    <!-- تحميل الوحدات -->
    <script src="src/modules/products/products-manager.js"></script>
    <script src="src/modules/customers/customers-manager.js"></script>
    <script src="src/modules/sales/sales-manager.js"></script>
    <script src="src/modules/reports/reports-manager.js"></script>
    <script src="src/modules/expenses/expenses-manager.js"></script>
    
    <!-- تحميل أدوات الاختبار -->
    <script src="src/ui/performance-dashboard.js"></script>
    <script src="src/tests/performance-tests.js"></script>

    <script>
        // متغيرات عامة
        let currentReport = null;
        let isTestRunning = false;

        // عناصر DOM
        const runTestsBtn = document.getElementById('runTestsBtn');
        const exportReportBtn = document.getElementById('exportReportBtn');
        const openDashboardBtn = document.getElementById('openDashboardBtn');
        const progressContainer = document.getElementById('progressContainer');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const resultsContainer = document.getElementById('resultsContainer');

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('🚀 Performance Tests Page Loaded');
            
            // تهيئة الأنظمة الأساسية
            await initializeSystems();
            
            // إعداد معالجات الأحداث
            setupEventHandlers();
        });

        // تهيئة الأنظمة الأساسية
        async function initializeSystems() {
            try {
                // تهيئة نظام التخزين
                if (window.UnifiedStorage) {
                    await window.UnifiedStorage.init();
                }
                
                // تهيئة محسن الأداء
                if (window.PerformanceOptimizer) {
                    await window.PerformanceOptimizer.init();
                }
                
                // تهيئة محسن واجهة المستخدم
                if (window.UIOptimizer) {
                    await window.UIOptimizer.init();
                }
                
                // تهيئة محسن الأصول
                if (window.AssetOptimizer) {
                    await window.AssetOptimizer.init();
                }
                
                console.log('✅ Systems initialized for testing');
                
            } catch (error) {
                console.error('❌ Error initializing systems:', error);
            }
        }

        // إعداد معالجات الأحداث
        function setupEventHandlers() {
            runTestsBtn.addEventListener('click', runPerformanceTests);
            exportReportBtn.addEventListener('click', exportReport);
            openDashboardBtn.addEventListener('click', openPerformanceDashboard);
        }

        // تشغيل اختبارات الأداء
        async function runPerformanceTests() {
            if (isTestRunning) return;
            
            try {
                isTestRunning = true;
                runTestsBtn.disabled = true;
                runTestsBtn.innerHTML = '🔄 جاري التشغيل... <div class="loading-spinner"></div>';
                
                // إظهار شريط التقدم
                progressContainer.style.display = 'block';
                resultsContainer.style.display = 'none';
                
                // تشغيل الاختبارات
                if (window.PerformanceTests) {
                    // مراقبة التقدم
                    const progressInterval = setInterval(() => {
                        const status = window.PerformanceTests.getTestStatus();
                        updateProgress(status);
                    }, 500);
                    
                    // تشغيل الاختبارات
                    const report = await window.PerformanceTests.runAllTests();
                    
                    clearInterval(progressInterval);
                    
                    if (report) {
                        currentReport = report;
                        displayResults(report);
                        exportReportBtn.disabled = false;
                    } else {
                        throw new Error('فشل في إنشاء التقرير');
                    }
                } else {
                    throw new Error('نظام الاختبارات غير متاح');
                }
                
            } catch (error) {
                console.error('❌ Error running tests:', error);
                alert('حدث خطأ أثناء تشغيل الاختبارات: ' + error.message);
                
            } finally {
                isTestRunning = false;
                runTestsBtn.disabled = false;
                runTestsBtn.innerHTML = '🚀 تشغيل جميع الاختبارات';
                progressContainer.style.display = 'none';
            }
        }

        // تحديث شريط التقدم
        function updateProgress(status) {
            if (status.isRunning) {
                const progress = Math.min((status.progress / 7) * 100, 100); // 7 فئات اختبار
                progressFill.style.width = progress + '%';
                progressText.textContent = `جاري تشغيل الاختبارات... ${Math.round(progress)}%`;
            }
        }

        // عرض النتائج
        function displayResults(report) {
            // عرض الملخص
            document.getElementById('totalTests').textContent = report.summary.totalTests;
            document.getElementById('passedTests').textContent = report.summary.passedTests;
            document.getElementById('failedTests').textContent = report.summary.failedTests;
            document.getElementById('successRate').textContent = report.summary.successRate;
            document.getElementById('testDuration').textContent = report.duration;
            
            const gradeElement = document.getElementById('overallGrade');
            gradeElement.textContent = report.summary.overallGrade;
            gradeElement.className = `stat-value grade-${report.summary.overallGrade}`;
            
            // عرض تفاصيل الاختبارات
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = '';
            
            report.categories.forEach(category => {
                const categoryDiv = createCategoryElement(category);
                testResults.appendChild(categoryDiv);
            });
            
            // عرض التوصيات
            if (report.recommendations && report.recommendations.length > 0) {
                const recommendationsDiv = document.getElementById('recommendations');
                const recommendationsList = document.getElementById('recommendationsList');
                
                recommendationsList.innerHTML = '';
                report.recommendations.forEach(recommendation => {
                    const li = document.createElement('li');
                    li.textContent = recommendation;
                    recommendationsList.appendChild(li);
                });
                
                recommendationsDiv.style.display = 'block';
            }
            
            // إظهار النتائج
            resultsContainer.style.display = 'block';
        }

        // إنشاء عنصر فئة الاختبار
        function createCategoryElement(category) {
            const categoryDiv = document.createElement('div');
            categoryDiv.className = 'test-category';
            
            const header = document.createElement('div');
            header.className = 'category-header';
            header.textContent = category.name;
            categoryDiv.appendChild(header);
            
            if (category.tests) {
                category.tests.forEach(test => {
                    const testDiv = document.createElement('div');
                    testDiv.className = 'test-item';
                    
                    const nameDiv = document.createElement('div');
                    nameDiv.className = 'test-name';
                    nameDiv.textContent = test.name;
                    
                    const statusDiv = document.createElement('div');
                    statusDiv.className = `test-status status-${test.status.toLowerCase()}`;
                    statusDiv.textContent = test.status;
                    
                    testDiv.appendChild(nameDiv);
                    testDiv.appendChild(statusDiv);
                    categoryDiv.appendChild(testDiv);
                });
            }
            
            return categoryDiv;
        }

        // تصدير التقرير
        function exportReport() {
            if (currentReport && window.PerformanceTests) {
                window.PerformanceTests.exportReport(currentReport);
            }
        }

        // فتح لوحة مراقبة الأداء
        function openPerformanceDashboard() {
            if (window.PerformanceDashboard) {
                window.PerformanceDashboard.toggle();
            } else {
                alert('لوحة مراقبة الأداء غير متاحة');
            }
        }
    </script>
</body>
</html>
