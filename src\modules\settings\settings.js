/**
 * Settings Module - إعدادات النظام
 * System settings and configuration for Black Horse ERP
 */

class SettingsModule {
    constructor() {
        this.currentTab = 'general';
        this.settings = new Map();
        this.defaultSettings = {
            // General settings
            company_name: 'Black Horse',
            company_address: '',
            company_phone: '',
            company_email: '',
            company_website: '',
            company_logo: '',
            
            // System settings
            currency: 'EGP',
            language: 'ar',
            timezone: 'Africa/Cairo',
            date_format: 'dd/mm/yyyy',
            time_format: '24h',
            
            // Business settings
            tax_rate: 14,
            low_stock_threshold: 10,
            auto_backup: true,
            backup_frequency: 'daily',
            
            // POS settings
            receipt_printer: '',
            barcode_scanner: '',
            cash_drawer: '',
            receipt_template: 'default',
            
            // Payment methods
            payment_methods: {
                cash: { enabled: true, name: 'نقدي' },
                card: { enabled: true, name: 'بطاقة ائتمان' },
                vodafone_cash: { enabled: true, name: 'فودافون كاش' },
                instapay: { enabled: true, name: 'انستاباي' },
                bank_transfer: { enabled: false, name: 'تحويل بنكي' }
            },
            
            // Notifications
            notifications: {
                low_stock: true,
                new_order: true,
                payment_due: true,
                employee_attendance: false
            },
            
            // Security
            session_timeout: 30,
            password_policy: 'medium',
            two_factor_auth: false,
            
            // Theme
            theme: 'light',
            sidebar_color: 'primary',
            accent_color: 'success'
        };
    }

    /**
     * Initialize settings module
     */
    async initialize() {
        console.log('Initializing Settings Module...');
        await this.loadSettings();
    }

    /**
     * Show settings module
     */
    async show() {
        const container = document.getElementById('moduleContainer');
        if (!container) return;

        container.innerHTML = this.getSettingsHTML();
        await this.setupEventListeners();
        await this.loadCurrentSettings();
    }

    /**
     * Get settings HTML structure
     */
    getSettingsHTML() {
        return `
            <div class="settings-module">
                <div class="row">
                    <!-- Settings Navigation -->
                    <div class="col-md-3 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-cog me-2"></i>إعدادات النظام
                                </h6>
                            </div>
                            <div class="list-group list-group-flush">
                                <a href="#" class="list-group-item list-group-item-action active" 
                                   onclick="Settings.switchTab('general')">
                                    <i class="fas fa-building me-2"></i>الإعدادات العامة
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" 
                                   onclick="Settings.switchTab('business')">
                                    <i class="fas fa-briefcase me-2"></i>إعدادات الأعمال
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" 
                                   onclick="Settings.switchTab('pos')">
                                    <i class="fas fa-cash-register me-2"></i>إعدادات نقطة البيع
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" 
                                   onclick="Settings.switchTab('payments')">
                                    <i class="fas fa-credit-card me-2"></i>طرق الدفع
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" 
                                   onclick="Settings.switchTab('notifications')">
                                    <i class="fas fa-bell me-2"></i>الإشعارات
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" 
                                   onclick="Settings.switchTab('security')">
                                    <i class="fas fa-shield-alt me-2"></i>الأمان
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" 
                                   onclick="Settings.switchTab('backup')">
                                    <i class="fas fa-database me-2"></i>النسخ الاحتياطي
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" 
                                   onclick="Settings.switchTab('theme')">
                                    <i class="fas fa-palette me-2"></i>المظهر
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Settings Content -->
                    <div class="col-md-9">
                        <div id="settingsContent">
                            <!-- Dynamic content will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Switch between settings tabs
     */
    async switchTab(tab) {
        this.currentTab = tab;
        
        // Update active tab
        document.querySelectorAll('.list-group-item').forEach(item => {
            item.classList.remove('active');
        });
        event.target.classList.add('active');
        
        await this.renderCurrentTab();
    }

    /**
     * Render current tab content
     */
    async renderCurrentTab() {
        const content = document.getElementById('settingsContent');
        if (!content) return;

        switch (this.currentTab) {
            case 'general':
                content.innerHTML = this.renderGeneralSettings();
                break;
            case 'business':
                content.innerHTML = this.renderBusinessSettings();
                break;
            case 'pos':
                content.innerHTML = this.renderPOSSettings();
                break;
            case 'payments':
                content.innerHTML = this.renderPaymentSettings();
                break;
            case 'notifications':
                content.innerHTML = this.renderNotificationSettings();
                break;
            case 'security':
                content.innerHTML = this.renderSecuritySettings();
                break;
            case 'backup':
                content.innerHTML = this.renderBackupSettings();
                break;
            case 'theme':
                content.innerHTML = this.renderThemeSettings();
                break;
        }

        await this.loadCurrentSettings();
    }

    /**
     * Render general settings
     */
    renderGeneralSettings() {
        return `
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">معلومات الشركة</h6>
                </div>
                <div class="card-body">
                    <form id="generalSettingsForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم الشركة</label>
                                <input type="text" class="form-control" name="company_name" 
                                       value="${this.getSetting('company_name')}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" name="company_phone" 
                                       value="${this.getSetting('company_phone')}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" name="company_email" 
                                       value="${this.getSetting('company_email')}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الموقع الإلكتروني</label>
                                <input type="url" class="form-control" name="company_website" 
                                       value="${this.getSetting('company_website')}">
                            </div>
                            <div class="col-12 mb-3">
                                <label class="form-label">العنوان</label>
                                <textarea class="form-control" name="company_address" rows="3">${this.getSetting('company_address')}</textarea>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">العملة</label>
                                <select class="form-select" name="currency">
                                    <option value="EGP" ${this.getSetting('currency') === 'EGP' ? 'selected' : ''}>جنيه مصري (EGP)</option>
                                    <option value="USD" ${this.getSetting('currency') === 'USD' ? 'selected' : ''}>دولار أمريكي (USD)</option>
                                    <option value="EUR" ${this.getSetting('currency') === 'EUR' ? 'selected' : ''}>يورو (EUR)</option>
                                    <option value="SAR" ${this.getSetting('currency') === 'SAR' ? 'selected' : ''}>ريال سعودي (SAR)</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">المنطقة الزمنية</label>
                                <select class="form-select" name="timezone">
                                    <option value="Africa/Cairo" ${this.getSetting('timezone') === 'Africa/Cairo' ? 'selected' : ''}>القاهرة</option>
                                    <option value="Asia/Riyadh" ${this.getSetting('timezone') === 'Asia/Riyadh' ? 'selected' : ''}>الرياض</option>
                                    <option value="Asia/Dubai" ${this.getSetting('timezone') === 'Asia/Dubai' ? 'selected' : ''}>دبي</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">تنسيق التاريخ</label>
                                <select class="form-select" name="date_format">
                                    <option value="dd/mm/yyyy" ${this.getSetting('date_format') === 'dd/mm/yyyy' ? 'selected' : ''}>يوم/شهر/سنة</option>
                                    <option value="mm/dd/yyyy" ${this.getSetting('date_format') === 'mm/dd/yyyy' ? 'selected' : ''}>شهر/يوم/سنة</option>
                                    <option value="yyyy-mm-dd" ${this.getSetting('date_format') === 'yyyy-mm-dd' ? 'selected' : ''}>سنة-شهر-يوم</option>
                                </select>
                            </div>
                        </div>
                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;
    }

    /**
     * Render business settings
     */
    renderBusinessSettings() {
        return `
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">إعدادات الأعمال</h6>
                </div>
                <div class="card-body">
                    <form id="businessSettingsForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">معدل الضريبة (%)</label>
                                <input type="number" class="form-control" name="tax_rate"
                                       value="${this.getSetting('tax_rate')}" min="0" max="100" step="0.01">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">حد تنبيه المخزون المنخفض</label>
                                <input type="number" class="form-control" name="low_stock_threshold"
                                       value="${this.getSetting('low_stock_threshold')}" min="0">
                            </div>
                            <div class="col-12 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="auto_backup"
                                           ${this.getSetting('auto_backup') ? 'checked' : ''}>
                                    <label class="form-check-label">
                                        تفعيل النسخ الاحتياطي التلقائي
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">تكرار النسخ الاحتياطي</label>
                                <select class="form-select" name="backup_frequency">
                                    <option value="hourly" ${this.getSetting('backup_frequency') === 'hourly' ? 'selected' : ''}>كل ساعة</option>
                                    <option value="daily" ${this.getSetting('backup_frequency') === 'daily' ? 'selected' : ''}>يومياً</option>
                                    <option value="weekly" ${this.getSetting('backup_frequency') === 'weekly' ? 'selected' : ''}>أسبوعياً</option>
                                    <option value="monthly" ${this.getSetting('backup_frequency') === 'monthly' ? 'selected' : ''}>شهرياً</option>
                                </select>
                            </div>
                        </div>
                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;
    }

    /**
     * Render POS settings
     */
    renderPOSSettings() {
        return `
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">إعدادات نقطة البيع</h6>
                </div>
                <div class="card-body">
                    <form id="posSettingsForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">طابعة الإيصالات</label>
                                <select class="form-select" name="receipt_printer">
                                    <option value="">اختر الطابعة</option>
                                    <option value="thermal_58mm">طابعة حرارية 58mm</option>
                                    <option value="thermal_80mm">طابعة حرارية 80mm</option>
                                    <option value="laser">طابعة ليزر</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">قارئ الباركود</label>
                                <select class="form-select" name="barcode_scanner">
                                    <option value="">اختر القارئ</option>
                                    <option value="usb_scanner">قارئ USB</option>
                                    <option value="bluetooth_scanner">قارئ بلوتوث</option>
                                    <option value="camera">كاميرا الجهاز</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">درج النقود</label>
                                <select class="form-select" name="cash_drawer">
                                    <option value="">بدون درج</option>
                                    <option value="automatic">درج تلقائي</option>
                                    <option value="manual">درج يدوي</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">قالب الإيصال</label>
                                <select class="form-select" name="receipt_template">
                                    <option value="default">القالب الافتراضي</option>
                                    <option value="minimal">قالب مبسط</option>
                                    <option value="detailed">قالب مفصل</option>
                                </select>
                            </div>
                        </div>
                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;
    }

    /**
     * Render payment settings
     */
    renderPaymentSettings() {
        const paymentMethods = this.getSetting('payment_methods');

        return `
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">طرق الدفع</h6>
                </div>
                <div class="card-body">
                    <form id="paymentSettingsForm">
                        ${Object.entries(paymentMethods).map(([key, method]) => `
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox"
                                               name="payment_${key}" ${method.enabled ? 'checked' : ''}>
                                        <label class="form-check-label">
                                            ${method.name}
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <input type="text" class="form-control"
                                           name="payment_name_${key}" value="${method.name}"
                                           placeholder="اسم طريقة الدفع">
                                </div>
                            </div>
                        `).join('')}

                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;
    }

    /**
     * Render notification settings
     */
    renderNotificationSettings() {
        const notifications = this.getSetting('notifications');

        return `
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">إعدادات الإشعارات</h6>
                </div>
                <div class="card-body">
                    <form id="notificationSettingsForm">
                        ${Object.entries(notifications).map(([key, enabled]) => `
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox"
                                       name="notification_${key}" ${enabled ? 'checked' : ''}>
                                <label class="form-check-label">
                                    ${this.getNotificationLabel(key)}
                                </label>
                            </div>
                        `).join('')}

                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;
    }

    /**
     * Get notification label
     */
    getNotificationLabel(key) {
        const labels = {
            low_stock: 'تنبيه المخزون المنخفض',
            new_order: 'تنبيه الطلبات الجديدة',
            payment_due: 'تنبيه المدفوعات المستحقة',
            employee_attendance: 'تنبيه حضور الموظفين'
        };
        return labels[key] || key;
    }

    /**
     * Get setting value
     */
    getSetting(key) {
        return this.settings.get(key) || this.defaultSettings[key] || '';
    }

    /**
     * Load settings from storage
     */
    async loadSettings() {
        try {
            if (window.Database && window.Database.settings) {
                const settingsData = await window.Database.settings.getAll();
                settingsData.forEach(setting => {
                    this.settings.set(setting.key, setting.value);
                });
            }
            
            // Load default settings for missing keys
            Object.entries(this.defaultSettings).forEach(([key, value]) => {
                if (!this.settings.has(key)) {
                    this.settings.set(key, value);
                }
            });
            
        } catch (error) {
            console.error('Error loading settings:', error);
            // Load defaults
            Object.entries(this.defaultSettings).forEach(([key, value]) => {
                this.settings.set(key, value);
            });
        }
    }

    /**
     * Save setting
     */
    async saveSetting(key, value) {
        try {
            this.settings.set(key, value);
            
            if (window.Database && window.Database.settings) {
                await window.Database.settings.add({
                    id: key,
                    key: key,
                    value: value
                });
            }
            
        } catch (error) {
            console.error('Error saving setting:', error);
        }
    }

    /**
     * Setup event listeners
     */
    async setupEventListeners() {
        // Form submissions will be handled by individual forms
        console.log('Settings event listeners setup');
    }

    /**
     * Load current settings into forms
     */
    async loadCurrentSettings() {
        // This would populate form fields with current settings
        console.log('Loading current settings into forms');
    }
}

// Create global instance
window.Settings = new SettingsModule();
