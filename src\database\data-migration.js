/**
 * Black Horse POS - Data Migration System
 * نظام ترحيل البيانات
 * Developer: Augment Agent
 */

class DataMigration {
    constructor() {
        this.storage = window.UnifiedStorage;
        this.migrationStatus = {
            completed: false,
            startTime: null,
            endTime: null,
            migratedTables: [],
            errors: []
        };
        
        console.log('🔄 Data Migration System initialized');
    }

    // بدء عملية الترحيل الشاملة
    async startMigration() {
        try {
            console.log('🚀 Starting data migration process...');
            this.migrationStatus.startTime = new Date().toISOString();
            
            // التحقق من وجود بيانات في localStorage
            const hasLocalStorageData = this.checkLocalStorageData();
            
            if (!hasLocalStorageData) {
                console.log('ℹ️ No data found in localStorage to migrate');
                this.migrationStatus.completed = true;
                return true;
            }
            
            // تهيئة نظام التخزين الموحد
            await this.storage.init();
            
            // ترحيل كل جدول
            const tables = ['products', 'customers', 'sales', 'expenses', 'users', 'suppliers', 'settings'];
            
            for (const table of tables) {
                try {
                    await this.migrateTable(table);
                    this.migrationStatus.migratedTables.push(table);
                    console.log(`✅ Table migrated successfully: ${table}`);
                } catch (error) {
                    console.error(`❌ Error migrating table ${table}:`, error);
                    this.migrationStatus.errors.push({
                        table,
                        error: error.message,
                        timestamp: new Date().toISOString()
                    });
                }
            }
            
            // إنشاء نسخة احتياطية من localStorage قبل الحذف
            await this.createLocalStorageBackup();
            
            // حذف البيانات القديمة من localStorage (اختياري)
            if (this.migrationStatus.errors.length === 0) {
                this.cleanupLocalStorage();
            }
            
            this.migrationStatus.completed = true;
            this.migrationStatus.endTime = new Date().toISOString();
            
            console.log('🎉 Data migration completed successfully!');
            return true;
            
        } catch (error) {
            console.error('❌ Migration process failed:', error);
            this.migrationStatus.errors.push({
                general: error.message,
                timestamp: new Date().toISOString()
            });
            return false;
        }
    }

    // التحقق من وجود بيانات في localStorage
    checkLocalStorageData() {
        const tables = ['products', 'customers', 'sales', 'expenses', 'users', 'suppliers', 'settings'];
        
        for (const table of tables) {
            const data = localStorage.getItem(table);
            if (data && data !== '[]' && data !== 'null') {
                console.log(`📊 Found data in localStorage: ${table}`);
                return true;
            }
        }
        
        return false;
    }

    // ترحيل جدول واحد
    async migrateTable(tableName) {
        try {
            console.log(`🔄 Migrating table: ${tableName}`);
            
            // تحميل البيانات من localStorage
            const rawData = localStorage.getItem(tableName);
            if (!rawData || rawData === '[]' || rawData === 'null') {
                console.log(`ℹ️ No data to migrate for table: ${tableName}`);
                return;
            }
            
            let data;
            try {
                data = JSON.parse(rawData);
            } catch (parseError) {
                console.error(`❌ Error parsing data for ${tableName}:`, parseError);
                throw new Error(`Invalid JSON data in ${tableName}`);
            }
            
            if (!Array.isArray(data)) {
                console.warn(`⚠️ Data for ${tableName} is not an array, converting...`);
                data = [data];
            }
            
            // تنظيف وتحسين البيانات
            const cleanedData = this.cleanTableData(tableName, data);
            
            // حفظ البيانات في النظام الجديد
            for (const item of cleanedData) {
                await this.storage.save(tableName, item);
            }
            
            console.log(`✅ Migrated ${cleanedData.length} records for ${tableName}`);
            
        } catch (error) {
            console.error(`❌ Error migrating table ${tableName}:`, error);
            throw error;
        }
    }

    // تنظيف وتحسين بيانات الجدول
    cleanTableData(tableName, data) {
        return data.map(item => {
            // إضافة ID إذا لم يكن موجوداً
            if (!item.id) {
                item.id = this.generateId();
            }
            
            // إضافة timestamps
            if (!item.createdAt) {
                item.createdAt = new Date().toISOString();
            }
            if (!item.updatedAt) {
                item.updatedAt = new Date().toISOString();
            }
            
            // تنظيف حسب نوع الجدول
            switch (tableName) {
                case 'products':
                    return this.cleanProductData(item);
                case 'customers':
                    return this.cleanCustomerData(item);
                case 'sales':
                    return this.cleanSalesData(item);
                case 'expenses':
                    return this.cleanExpenseData(item);
                default:
                    return item;
            }
        }).filter(item => item !== null);
    }

    // تنظيف بيانات المنتجات
    cleanProductData(product) {
        try {
            return {
                id: product.id,
                name: product.name || 'منتج غير محدد',
                price: this.parseNumber(product.price, 0),
                costPrice: this.parseNumber(product.costPrice, product.price || 0),
                stock: this.parseNumber(product.stock, 0),
                category: product.category || 'عام',
                barcode: product.barcode || this.generateBarcode(),
                image: product.image || '📦',
                description: product.description || '',
                isActive: product.isActive !== false,
                createdAt: product.createdAt,
                updatedAt: product.updatedAt
            };
        } catch (error) {
            console.error('❌ Error cleaning product data:', error);
            return null;
        }
    }

    // تنظيف بيانات العملاء
    cleanCustomerData(customer) {
        try {
            return {
                id: customer.id,
                name: customer.name || 'عميل غير محدد',
                phone: customer.phone || '',
                email: customer.email || '',
                address: customer.address || '',
                type: customer.type || 'عادي',
                debt: this.parseNumber(customer.debt, 0),
                totalPurchases: this.parseNumber(customer.totalPurchases, 0),
                loyaltyPoints: this.parseNumber(customer.loyaltyPoints, 0),
                isActive: customer.isActive !== false,
                createdAt: customer.createdAt,
                updatedAt: customer.updatedAt,
                lastVisit: customer.lastVisit || customer.createdAt
            };
        } catch (error) {
            console.error('❌ Error cleaning customer data:', error);
            return null;
        }
    }

    // تنظيف بيانات المبيعات
    cleanSalesData(sale) {
        try {
            return {
                id: sale.id,
                invoiceNumber: sale.invoiceNumber || this.generateInvoiceNumber(),
                customerId: sale.customerId || null,
                customerName: sale.customerName || 'عميل نقدي',
                items: Array.isArray(sale.items) ? sale.items : [],
                subtotal: this.parseNumber(sale.subtotal, 0),
                tax: this.parseNumber(sale.tax, 0),
                discount: this.parseNumber(sale.discount, 0),
                total: this.parseNumber(sale.total, 0),
                paymentMethod: sale.paymentMethod || 'نقدي',
                date: sale.date || new Date().toISOString(),
                status: sale.status || 'مكتملة',
                createdAt: sale.createdAt,
                updatedAt: sale.updatedAt
            };
        } catch (error) {
            console.error('❌ Error cleaning sales data:', error);
            return null;
        }
    }

    // تنظيف بيانات المصروفات
    cleanExpenseData(expense) {
        try {
            return {
                id: expense.id,
                description: expense.description || 'مصروف غير محدد',
                amount: this.parseNumber(expense.amount, 0),
                category: expense.category || 'عام',
                date: expense.date || new Date().toISOString(),
                paymentMethod: expense.paymentMethod || 'نقدي',
                notes: expense.notes || '',
                createdAt: expense.createdAt,
                updatedAt: expense.updatedAt
            };
        } catch (error) {
            console.error('❌ Error cleaning expense data:', error);
            return null;
        }
    }

    // إنشاء نسخة احتياطية من localStorage
    async createLocalStorageBackup() {
        try {
            console.log('💾 Creating localStorage backup...');
            
            const backup = {
                timestamp: new Date().toISOString(),
                version: '1.0',
                data: {}
            };
            
            // نسخ جميع البيانات
            const tables = ['products', 'customers', 'sales', 'expenses', 'users', 'suppliers', 'settings'];
            
            for (const table of tables) {
                const data = localStorage.getItem(table);
                if (data) {
                    backup.data[table] = data;
                }
            }
            
            // حفظ النسخة الاحتياطية
            await this.storage.save('backups', {
                id: 'localStorage_backup_' + Date.now(),
                type: 'localStorage_migration',
                backup: backup,
                createdAt: new Date().toISOString()
            });
            
            console.log('✅ localStorage backup created successfully');
            
        } catch (error) {
            console.error('❌ Error creating localStorage backup:', error);
        }
    }

    // تنظيف localStorage (حذف البيانات القديمة)
    cleanupLocalStorage() {
        try {
            console.log('🧹 Cleaning up localStorage...');
            
            const tables = ['products', 'customers', 'sales', 'expenses', 'users', 'suppliers'];
            
            for (const table of tables) {
                localStorage.removeItem(table);
                console.log(`🗑️ Removed localStorage data: ${table}`);
            }
            
            // الاحتفاظ بالإعدادات
            console.log('ℹ️ Settings data preserved in localStorage');
            
        } catch (error) {
            console.error('❌ Error cleaning localStorage:', error);
        }
    }

    // الحصول على تقرير الترحيل
    getMigrationReport() {
        return {
            ...this.migrationStatus,
            duration: this.migrationStatus.startTime && this.migrationStatus.endTime 
                ? new Date(this.migrationStatus.endTime) - new Date(this.migrationStatus.startTime)
                : null
        };
    }

    // دوال مساعدة
    parseNumber(value, defaultValue = 0) {
        const parsed = parseFloat(value);
        return isNaN(parsed) ? defaultValue : parsed;
    }

    generateId() {
        return 'migrated_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    generateBarcode() {
        return Date.now().toString();
    }

    generateInvoiceNumber() {
        return 'INV-' + Date.now();
    }
}

// إنشاء instance عام
window.DataMigration = new DataMigration();

// تصدير للاستخدام في الوحدات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DataMigration;
}

console.log('✅ Data Migration System loaded successfully');
